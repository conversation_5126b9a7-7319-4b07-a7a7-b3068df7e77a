package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/jwt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"go.uber.org/zap"
)

// 定义中间件函数
func AdminActionLogger(operateType domain.OperateType, events string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取管理员ID，这里假设从请求头或上下文中获取
		bearerToken := c.GetHeader("x-auth-token")
		if bearerToken == "" {
			bearerToken = c.GetHeader("Authorization")
		}
		var creatorId string = "0"
		var creatorName string = "unknown"
		if bearerToken == "" {
			creatorId = "0"
			creatorName = "unknown"
			// 有未登录的

		} else {
			parts := strings.Split(bearerToken, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				creatorId = "0"
				creatorName = "unknown"
			} else {
				token := parts[1]
				claims, err := jwt.ParseToken(token)
				if err == nil {
					creatorId = claims.UserId
					creatorName = claims.Username
				}
			}
		}

		var requestData string
		params := make(map[string]any)
		if "GET" == c.Request.Method {
			p := c.Params
			for _, cParam := range p {
				params[cParam.Key] = cParam.Value
			}
		} else {
			if err := c.ShouldBind(&params); err != nil {
				fmt.Println(err)
			} else {
				marshal, _ := json.Marshal(params)
				c.Request.Body = io.NopCloser(bytes.NewBuffer(marshal))
			}
		}
		paramsByte, err := json.Marshal(params)
		if err == nil {
			requestData = string(paramsByte)
		}
		// 获取当前时间
		startTime := time.Now()

		logId := uuid.NewString()

		var operateStatus = ""
		if c.Writer.Status() == 200 {
			operateStatus = "COMPLETE"
		} else {
			operateStatus = "FAILED"
		}

		// 创建日志记录
		logEntry := domain.SysOperateLog{
			Events:        events,
			LogId:         logId,
			Type:          operateType,
			Uri:           c.Request.URL.String(), // c.Request.RequestURI,
			Ip:            c.ClientIP(),
			UserAgent:     c.Request.UserAgent(),
			ReferenceType: domain.REF_TYPE_LOG,
			Params:        requestData,
			OperateStatus: operateStatus,
			CreateTime:    startTime,
			CreatorId:     creatorId,
			CreatorName:   creatorName,
		}

		//global.Lg.Info("拦截操作日志", zap.Any("logEntry", logEntry))

		// 将日志写入到指定的存储中
		go logAction(logEntry)

		// 将log_id的信息保存到上下文中
		c.Set(consts.LogIdKey, logId)
		// 继续处理请求
		c.Next()
	}
}

// 日志存储函数
func logAction(log domain.SysOperateLog) {
	//global.Lg.Info("入库日志", zap.Any("log: ", log))
	err := global.DB.Model(&domain.SysOperateLog{}).Create(&log).Error
	if err != nil {
		fmt.Println("入DB日志error", err)
	}
}

// 黑白日志中间件函数
func BlackWhiteActionLogger() gin.HandlerFunc {
	svc := servicev2.BlackWhiteOperatorLogService{
		BlackWhiteItemRepo:        datav2.NewRmsBlackWhiteItem(),
		BlackWhiteListRepo:        datav2.NewRmsBlackWhiteList(),
		BlackWhiteAuditRepo:       datav2.NewRmsBlackWhiteAudit(),
		BlackWhiteOperatorLogRepo: datav2.NewRmsBlackWhiteOperatorLog(),
	}
	return func(c *gin.Context) {

		// 获取管理员ID，这里假设从请求头或上下文中获取

		bearerToken := c.GetHeader("x-auth-token")
		if bearerToken == "" {
			bearerToken = c.GetHeader("Authorization")
		}
		var creatorName string = "unknown"
		if bearerToken == "" {
			creatorName = "unknown"
			// 有未登录的

		} else {
			parts := strings.Split(bearerToken, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				creatorName = "unknown"
			} else {
				token := parts[1]
				claims, err := jwt.ParseToken(token)
				if err == nil {
					creatorName = claims.Username
				}
			}
		}
		// 继续处理请求
		c.Next()

		if c.Writer.Status() == 200 {
			bwLogger := svc.NewBlackWhiteOperatorLog(creatorName)

			var actions []servicev2.BwLogAction
			actionCtxs := c.Request.Context().Value(entity.BwOperatorLogSvc)
			if actionCtxs != nil {
				actions = actionCtxs.([]servicev2.BwLogAction)
			}
			for _, v := range actions {
				if v.Check(c.Request.Context(), bwLogger) {
					go v.Handle(context.Background(), bwLogger)
					return
				}
			}
			if bwLogger.Error() != nil {
				wlog.Error("黑白日志错误", zap.Any("error", bwLogger.Error()))
			}

		}

	}
}

// 日志存储函数
func bwlogAction(log *modelv2.RmsBlackWhiteOperatorLog) {
	//global.Lg.Info("入库日志", zap.Any("log: ", log))
	err := global.DB.Model(&modelv2.RmsBlackWhiteOperatorLog{}).Create(&log).Error
	if err != nil {
		fmt.Println("入DB日志error", err)
	}
}
