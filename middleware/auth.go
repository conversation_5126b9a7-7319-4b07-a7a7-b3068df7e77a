package middleware

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/jwt"
)

func JwtAuth(jwt jwt.IJWT) gin.HandlerFunc {
	return func(c *gin.Context) {
		bearerToken := c.Request.Header.Get("x-auth-token")
		if bearerToken == "" {
			bearerToken = c.Request.Header.Get("Authorization")
		}
		errResp := domain.Base{
			Code: er.AuthorizationError.Code(),
			Msg:  er.AuthorizationError.Msg(),
		}
		if bearerToken == "" {
			c.JSON(http.StatusForbidden, errResp)
			c.Abort()
			return
		}
		parts := strings.Split(bearerToken, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusForbidden, errResp)
			c.Abort()
			return
		}
		token := parts[1]
		claims, err := jwt.ParseToken(token)

		if err != nil {
			c.JSON(http.StatusForbidden, errResp)
			c.Abort()
			return
		}

		// 将token的信息保存到上下文中
		c.Set(consts.TokenKey, claims)

		c.Next()
	}
}
