/*
 Navicat Premium Data Transfer

 Source Server         : uqdev
 Source Server Type    : MySQL
 Source Server Version : 80031 (8.0.31-google)
 Source Host           : **************:3306
 Source Schema         : uq_risk_devv10

 Target Server Type    : MySQL
 Target Server Version : 80031 (8.0.31-google)
 File Encoding         : 65001

 Date: 17/06/2024 17:10:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS `casbin_rule`;
CREATE TABLE `casbin_rule`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `ptype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `v0` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `v1` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `v2` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `v3` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `v4` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `v5` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_casbin_rule`(`ptype` ASC, `v0` ASC, `v1` ASC, `v2` ASC, `v3` ASC, `v4` ASC, `v5` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_alarm_contacts
-- ----------------------------
DROP TABLE IF EXISTS `rms_alarm_contacts`;
CREATE TABLE `rms_alarm_contacts`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系人姓名',
  `mobile` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` int NOT NULL COMMENT '状态',
  `alarm_time_config_id` bigint NULL DEFAULT NULL COMMENT '时间配置表外键',
  `notice_time` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型',
  `open_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控预警联系人' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_alarm_time_conf
-- ----------------------------
DROP TABLE IF EXISTS `rms_alarm_time_conf`;
CREATE TABLE `rms_alarm_time_conf`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `result_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'ResultCode',
  `status` int NOT NULL COMMENT '状态',
  `start_time` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` int NOT NULL COMMENT '截止时间',
  `open_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控预警时间配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `rms_blacklist`;
CREATE TABLE `rms_blacklist`  (
  `rc_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `risk_key` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '键，BANK_CARD_NO/TRADE_IP',
  `risk_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '值，如卡号/ip',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`rc_id`) USING BTREE,
  UNIQUE INDEX `idx_rk_rv`(`risk_key` ASC, `risk_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '黑名单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_business_type
-- ----------------------------
DROP TABLE IF EXISTS `rms_business_type`;
CREATE TABLE `rms_business_type`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务编码',
  `business_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务名称',
  `table_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '与businessField相关，废弃',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '业务类型' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_check_point
-- ----------------------------
DROP TABLE IF EXISTS `rms_check_point`;
CREATE TABLE `rms_check_point`  (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `CODE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '编码',
  `LABEL` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
  `FILTER_FIELDS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '[]' COMMENT '过滤字段配置，区分更细化的业务数据（policy选取有关）',
  `PRE_ACTIONS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '[]' COMMENT '事件预处理配置，如根据ip获取省市',
  `BUSINESS_TYPES` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '业务类型配置，用于对接入业务（事件）的细分',
  `VOUCHER_CONFIGS` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '凭证读取配置，不启用',
  `DEFAULT_PK_FIELD_NAME` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '默认主键字段，与filterField相关',
  `CHECK_DUPLICATE` tinyint(1) NULL DEFAULT 1 COMMENT '数据重复检查',
  `MEMO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CREATED_AT` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `uidx_code`(`CODE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '检查点' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_event
-- ----------------------------
DROP TABLE IF EXISTS `rms_event`;
CREATE TABLE `rms_event`  (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `CHECK_POINT` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系人姓名',
  `STR_FIELD1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段1（建议主键），字段名见对应存储配置',
  `STR_FIELD2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段2，字段名见对应存储配置',
  `STR_FIELD3` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段3，字段名见对应存储配置',
  `STR_FIELD4` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段4，字段名见对应存储配置',
  `STR_FIELD5` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段5，字段名见对应存储配置',
  `NUM_FIELD1` decimal(16, 2) NULL DEFAULT NULL COMMENT '重要数值类型字段1(值=原始值*1000)，字段名见对应存储配置',
  `NUM_FIELD2` decimal(16, 2) NULL DEFAULT NULL COMMENT '重要数值类型字段2(值=原始值*1000)，字段名见对应存储配置',
  `EVENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'json显示存储的事件完整内容',
  `RESULT_CODE` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则结果代码',
  `RESULT_MESSAGE` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则结果内容',
  `RULE_NO` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则编号',
  `RULES_NOT_PASSED` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '未通过规则',
  `CREATED_AT` datetime NOT NULL COMMENT '创建时间',
  `UPDATED_AT` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_ckp_rcode`(`CHECK_POINT` ASC, `RESULT_CODE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风险事件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_event_field
-- ----------------------------
DROP TABLE IF EXISTS `rms_event_field`;
CREATE TABLE `rms_event_field`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `check_point` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '归属检查点（编码）',
  `field_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字段名',
  `inner_field_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内部字段名',
  `field_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字段类型，如String',
  `default_value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认值',
  `required` tinyint(1) NOT NULL COMMENT '是否必填',
  `required_condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '必填条件，依赖于其它字段',
  `memo` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ckp`(`check_point` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风险事件字段配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_event_store_cfg
-- ----------------------------
DROP TABLE IF EXISTS `rms_event_store_cfg`;
CREATE TABLE `rms_event_store_cfg`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `check_point` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查点',
  `persistent` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否存储',
  `send_to_scheduler` tinyint(1) NOT NULL DEFAULT 0 COMMENT '暂不使用',
  `save_to_business` tinyint(1) NOT NULL DEFAULT 0 COMMENT '暂不使用',
  `send_to_intra` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否发送到风控后台',
  `str_field1` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段1的字段名（建议主键）',
  `str_field2` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段2的字段名',
  `str_field3` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段3的字段名',
  `str_field4` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段4的字段名',
  `str_field5` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要字符串类型字段5的字段名',
  `num_field1` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要数值类型字段1的字段名',
  `num_field2` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '重要数值类型字段2的字段名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ckp`(`check_point` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '事件存储配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_filter_field
-- ----------------------------
DROP TABLE IF EXISTS `rms_filter_field`;
CREATE TABLE `rms_filter_field`  (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `FIELD_NAME` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字段名',
  `FIELD_ENUM` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '枚举类型列表的json，各枚举由编码和名称构成',
  `CHECK_POINT` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查点',
  `MEMO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CREATED_AT` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `idx_fname`(`FIELD_NAME` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '事件过滤字段配置（亦即枚举字段配置）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_operator_log
-- ----------------------------
DROP TABLE IF EXISTS `rms_operator_log`;
CREATE TABLE `rms_operator_log`  (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `USER_ID` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作员id',
  `USER_NAME` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员姓名',
  `METHOD` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '动作',
  `PARAMS` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数',
  `CREATED_AT` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_uid`(`USER_ID` ASC) USING BTREE,
  INDEX `idx_method`(`METHOD` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控操作日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_policy
-- ----------------------------
DROP TABLE IF EXISTS `rms_policy`;
CREATE TABLE `rms_policy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `policy_group` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'CMF' COMMENT '策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup',
  `policy_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '策略编号',
  `policy_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规则组名称',
  `check_point` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查点',
  `filters` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '策略匹配的过滤条件，需事先设置检查点的过滤字段',
  `policy_rules` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '策略中需要执行的规则id列表json',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态，Online/Offline',
  `memo` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `synced_at` datetime NULL DEFAULT NULL COMMENT '同步时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ckp`(`check_point` ASC) USING BTREE,
  INDEX `idx_pg`(`policy_group` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控规则策略（即规则组）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_risk_logs
-- ----------------------------
DROP TABLE IF EXISTS `rms_risk_logs`;
CREATE TABLE `rms_risk_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `partner_id` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商户号',
  `req_ip` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求IP',
  `project_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工程名',
  `alias` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '拦截别名 sql,xss,signature',
  `event_explain` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '事件说明',
  `ori_params` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '源参数',
  `illegal_param` int NOT NULL COMMENT '非法参数. 1-是，0-否',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_pid`(`partner_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_rule
-- ----------------------------
DROP TABLE IF EXISTS `rms_rule`;
CREATE TABLE `rms_rule`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `rule_group` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'CMF' COMMENT '规则组，即策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup',
  `rule_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则编号',
  `rule_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则描述',
  `check_point` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查点',
  `rule_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则类型',
  `rule_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则内容',
  `short_circuit` tinyint(1) NOT NULL COMMENT '是否支持短路',
  `params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数，注意与规则参数区分',
  `agenda_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Agenda，和规则内容中的package一致',
  `engine_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '引擎类型，有状态/无状态',
  `engine_instance` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '有状态类型情况下选择的引擎实例',
  `start_time` datetime NULL DEFAULT NULL COMMENT '生效时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '失效时间',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态，Online/Offline',
  `memo` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `synced_at` datetime NULL DEFAULT NULL COMMENT '同步时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_rno`(`rule_no` ASC) USING BTREE,
  INDEX `idx_rg`(`rule_group` ASC) USING BTREE,
  INDEX `idx_start_end`(`start_time` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '风控规则' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_rule_parameter
-- ----------------------------
DROP TABLE IF EXISTS `rms_rule_parameter`;
CREATE TABLE `rms_rule_parameter`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数编码',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数描述',
  `param_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes',
  `check_point` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查点(可多个,逗号分割)',
  `value_transfer` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer',
  `access_flag` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'apex' COMMENT '允许接口添加值，默认值apex',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则参数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_rule_parameter_group
-- ----------------------------
DROP TABLE IF EXISTS `rms_rule_parameter_group`;
CREATE TABLE `rms_rule_parameter_group`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数组编码',
  `param_ids` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '下属规则参数id(可多个,逗号分割)',
  `check_point` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '检查点',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数组描述',
  `default_flag` int NOT NULL COMMENT '默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否',
  `able` int NOT NULL COMMENT '状态，1-有效，0-失效',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则参数组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_rule_parameter_group_bind
-- ----------------------------
DROP TABLE IF EXISTS `rms_rule_parameter_group_bind`;
CREATE TABLE `rms_rule_parameter_group_bind`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `group_id` bigint NOT NULL COMMENT '参数组id',
  `check_point` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '检查点',
  `target_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '绑定目标id',
  `target_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '绑定目标类型，MERCHANT-商户，CHANNEL-渠道',
  `able` int NOT NULL COMMENT '状态，1-有效，0-失效',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_tid_gid`(`target_id` ASC, `group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则参数组绑定关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rms_rule_parameter_value
-- ----------------------------
DROP TABLE IF EXISTS `rms_rule_parameter_value`;
CREATE TABLE `rms_rule_parameter_value`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `param_id` bigint NOT NULL COMMENT 'RMS_RULE_PARAMETER外键',
  `group_id` bigint NOT NULL COMMENT 'RMS_RULE_PARAMETER_GROUP外键',
  `param_key` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '健，非键值对类型可空',
  `param_value` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '值',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_pid`(`param_id` ASC) USING BTREE,
  INDEX `idx_gid`(`group_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '规则参数值' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint NOT NULL COMMENT '用户ID',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '登录名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `group_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户角色名',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `last_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0.0.0.0' COMMENT '最后一次登录IP',
  `last_login_time` datetime NOT NULL COMMENT '最后一次登录时间',
  `salt` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '盐',
  `status` int NOT NULL DEFAULT 0 COMMENT '用户状态: 0=ACTIVE 活动, 1=INACTIVE 非活动',
  `reset_pwd_time` datetime NULL DEFAULT NULL COMMENT '重制登陆密码时间',
  `created_at` datetime NULL DEFAULT NULL,
  `update_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `un_username`(`user_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wp_handle_logs
-- ----------------------------
DROP TABLE IF EXISTS `wp_handle_logs`;
CREATE TABLE `wp_handle_logs`  (
  `hlid` int NOT NULL AUTO_INCREMENT,
  `puserid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员ID',
  `partner_id` varchar(48) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商户ID',
  `handle_code` varchar(48) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型: 前台商户=TYPE_PARTNER_HANDLE，后台管理=TYPE_SYSADMIN_HANDLE，网关日志=TYPE_GATEWAY_HANDLE，计划任务日志=TYPE_TASK_HANDLE',
  `handle_type` varchar(48) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型编码 PARTNER_LOGIN = 商户登陆 PARTNER_PAYOUT = 商户提现  PARTNER_PAYIN = 商户充值  PARTNER_SAFETY = 商户安全设置 SYSADMIN_LOG 后台设置日志 TASK_HANDLE_LOG=定时任务设置日志',
  `order_sn` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '订单号：针对订单流程',
  `handle_events` varchar(240) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作事件',
  `handle_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '动作参数[内部使用]',
  `handle_status` int NOT NULL DEFAULT 0 COMMENT '事件状态 0=失败 1=成功',
  `handle_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作IP',
  `created_at` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`hlid`) USING BTREE,
  INDEX `IDX_DATELINE`(`created_at` ASC) USING BTREE,
  INDEX `IDX_PUID_PARTNERID`(`puserid` ASC, `partner_id` ASC) USING BTREE,
  INDEX `IDX_STATUS`(`puserid` ASC, `partner_id` ASC, `handle_status` ASC) USING BTREE,
  INDEX `IDX_CODE`(`puserid` ASC, `partner_id` ASC, `handle_code` ASC, `handle_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户操作日志' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
