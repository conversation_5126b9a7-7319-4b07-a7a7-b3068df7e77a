package request

import (
	"bytes"
	"strconv"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"

	"github.com/gin-gonic/gin"
)

func GetRequestBodyJson[T any](ctx *gin.Context) (T, error) {
	var res T
	//判断body是否为空，为空直接返回空结构体
	if ctx.Request.Body == nil || ctx.Request.ContentLength == 0 {
		return res, nil
	}
	if err := ctx.ShouldBindJSON(&res); err != nil {
		InvalidArgumentErr := er.ValidatorErrorHandler(err)
		if InvalidArgumentErr != nil {
			return res, InvalidArgumentErr
		}
		return res, err
	}
	return res, nil
}
func GetRequestQueryBind[T any](ctx *gin.Context) (T, error) {
	var res T
	if err := ctx.ShouldBindQuery(&res); err != nil {
		InvalidArgumentErr := er.ValidatorErrorHandler(err)
		if InvalidArgumentErr != nil {
			return res, InvalidArgumentErr
		}

		return res, err
	}
	return res, nil
}

func GetRequestFormFile(c *gin.Context, name string, limitErr int64) (res *bytes.Buffer, filename string, err error) {
	file, err := c.FormFile(name)
	if err != nil {
		return
	}
	if limitErr > 0 && file.Size > limitErr {
		err = er.RequestFileTooLarge
		return
	}
	src, err := file.Open()
	if err != nil {
		return
	}
	defer src.Close()

	content := make([]byte, file.Size)
	if _, err = src.Read(content); err != nil {
		return
	}
	return bytes.NewBuffer(content), file.Filename, nil
}

func GetParamInt64(c *gin.Context, key string) (param int64, err error) {
	paramStr := c.Param(key)
	param, err = strconv.ParseInt(paramStr, 10, 64)
	if err != nil {
		err = er.InvalidArgument.WithErr(err)
		return
	}
	return
}

func GetParam(c *gin.Context, key string) (param string, err error) {
	param = c.Param(key)
	if param == "" {
		err = er.InvalidArgument
		return
	}
	return
}
