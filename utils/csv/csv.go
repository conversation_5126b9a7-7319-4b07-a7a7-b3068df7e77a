package csv

import (
	"bytes"
)

type CSV struct {
	dataByte []byte
}

func OpenBuffer(buffer *bytes.Buffer) *CSV {
	content := buffer.Bytes()
	if len(content) >= 3 && bytes.Equal(content[0:3], []byte{0xEF, 0xBB, 0xBF}) {
		content = content[3:]
	}

	return &CSV{dataByte: content}
}
func (c *CSV) ReadAll() ([][]string, error) {
	// 存储所有行的数据
	var rows [][]string
	// 当前行的数据
	var currentRow []string
	// 当前字段的数据
	var currentField []byte

	// 遍历字节数组
	for i := 0; i < len(c.dataByte); i++ {
		b := c.dataByte[i]

		switch b {
		case '\n':
			// 处理换行符，添加最后一个字段并结束当前行
			if len(currentField) > 0 {
				currentRow = append(currentRow, string(currentField))
				currentField = []byte{}
			}
			if len(currentRow) > 0 {
				rows = append(rows, currentRow)
				currentRow = []string{}
			}
		case '\r':
			// 跳过回车符
			continue
		case ',':
			// 处理逗号，添加当前字段到当前行
			currentRow = append(currentRow, string(currentField))
			currentField = []byte{}
		default:
			// 添加字符到当前字段
			currentField = append(currentField, b)
		}
	}

	// 处理最后一行数据（如果文件不以换行符结束）
	if len(currentField) > 0 {
		currentRow = append(currentRow, string(currentField))
	}
	if len(currentRow) > 0 {
		rows = append(rows, currentRow)
	}

	return rows, nil
}
