package db

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gorm.io/gorm"
)

func Transaction(ctx context.Context, session *gorm.DB, fn func(tx *gorm.DB) error) (err error) {
	if session == nil {
		engine := global.DB
		engine = engine.WithContext(ctx)
		tx := engine.Begin()
		defer func() {
			if err != nil {
				tx.Rollback()
			}
		}()
		err = fn(tx)
		if err != nil {
			return err
		}
		tx.Commit()
		return
	}
	err = fn(session)
	return
}
