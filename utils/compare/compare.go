package compare

type Compare[K comparable, T any] struct {
	uniqueKeyGetter func(T) K
	isUpdated       func(T, T) bool
	original        map[K]*CompareWarp[T]
}
type CompareWarp[T any] struct {
	Exists bool
	Data   T
}

func NewCompare[K comparable, T any](uniqueKeyGetter func(T) K, isUpdated func(T, T) bool, orig map[K]T) *Compare[K, T] {
	var original = make(map[K]*CompareWarp[T])
	for k, v := range orig {
		original[k] = &CompareWarp[T]{
			Exists: false,
			Data:   v,
		}
	}
	return &Compare[K, T]{
		uniqueKeyGetter: uniqueKeyGetter,
		isUpdated:       isUpdated,
		original:        original,
	}
}

func (c *Compare[K, T]) Compare(new []T) (ins, up, del []T) {
	for _, n := range new {
		uniqueKey := c.uniqueKeyGetter(n)
		if o, ok := c.original[uniqueKey]; ok {
			o.Exists = true
			if c.isUpdated(n, o.Data) {
				up = append(up, n)
			}
		} else {
			ins = append(ins, n)
		}
	}
	for _, o := range c.original {
		if !o.Exists {
			del = append(del, o.Data)
		}
	}

	return
}
