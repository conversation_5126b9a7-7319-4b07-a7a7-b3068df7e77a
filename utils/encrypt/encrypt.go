package encrypt

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
)

// AES加密密钥，必须是16、24或32字节
var aesKey = []byte("uqpayportal12345")

type Raw string

// 加密
func Encrypt(data Raw) Encrypted {
	// 创建AES加密块
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return Encrypted(data)
	}

	// 填充数据到16字节的倍数
	plaintext := pkcs7Padding([]byte(data), block.BlockSize())

	// 创建CBC加密模式
	iv := aesKey[:block.BlockSize()] // 使用密钥的前16字节作为IV
	mode := cipher.NewCBCEncrypter(block, iv)

	// 加密
	ciphertext := make([]byte, len(plaintext))
	mode.CryptBlocks(ciphertext, plaintext)

	// Base64编码
	return Encrypted(base64.StdEncoding.EncodeToString(ciphertext))
}

// PKCS7填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padtext...)
}

// PKCS7去除填充
func pkcs7UnPadding(data []byte) []byte {
	length := len(data)
	if length == 0 {
		return nil
	}
	padding := int(data[length-1])
	return data[:(length - padding)]
}
