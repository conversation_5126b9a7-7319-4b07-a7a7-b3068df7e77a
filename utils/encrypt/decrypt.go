package encrypt

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
)

type Encrypted string

func Decrypt(data Encrypted) Raw {
	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return ""
	}

	// 创建AES解密块
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return ""
	}

	// 创建CBC解密模式
	iv := aesKey[:block.BlockSize()]
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// 去除填充
	return Raw(pkcs7UnPadding(plaintext))
}
