package response

import (
	"bytes"
	"github.com/jinzhu/copier"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap/zapcore"
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
)

type BaseHandleErrInterface interface {
	Name() string
	Level() zapcore.Level
	ToMap() map[string]interface{}
	Code() uint32
	Msg() string
	SendToClient() bool // 是否发送给调用者
	ToZapField() []zapcore.Field
}

func HeadErr(c *gin.Context, err error) bool {
	if err == nil {
		return false
	}
	ReturnFailure(c, err)
	return true
}
func Copy(c *gin.Context, fromValue interface{}, toValue interface{}) bool {
	return HeadErr(c, copier.Copy(toValue, fromValue))
}
func CopyAndSend(c *gin.Context, fromValue interface{}, toValue interface{}) bool {
	if HeadErr(c, copier.Copy(toValue, fromValue)) {
		return true
	}
	ReturnSuccess(c, toValue)
	return false
}
func FeedBack(c *gin.Context, data interface{}, err error) bool {
	if HeadErr(c, err) {
		return true
	}
	ReturnSuccess(c, data)
	return false
}

type Base struct {
	Code uint32      `json:"code"`
	Msg  string      `json:"message"`
	Data interface{} `json:"data,omitempty"`
}

func ReturnFailure(ctx *gin.Context, err error) {
	var ok bool
	var errInf BaseHandleErrInterface
	if errInf, ok = err.(BaseHandleErrInterface); !ok {
		errInf = er.Unknown.WithErr(err)
	}
	wlog.Log(errInf.Level(), errInf.Msg(), errInf.ToZapField()...)

	if errInf.SendToClient() {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, Base{
			Code: errInf.Code(),
			Msg:  errInf.Msg(),
		})
	} else {
		ctx.AbortWithStatusJSON(http.StatusBadRequest, Base{
			Code: er.Unknown.Code(),
			Msg:  er.Unknown.Msg(),
		})
	}
}

func ReturnSuccess(ctx *gin.Context, data interface{}, msg ...string) {
	msgStr := ""
	if len(msg) > 0 {
		msgStr = msg[0]
	}

	m := gin.H{
		"code": 0,
		"msg":  msgStr,
		"data": data,
	}

	ctx.AbortWithStatusJSON(200, m)
}

func ReturnFileSuccess(ctx *gin.Context, data *bytes.Buffer, filename string) {
	// RFC 6266 compliant encoding for the filename
	// First provide the standard ASCII version
	asciiFilename := url.QueryEscape(filename)
	disposition := "attachment; filename=\"" + asciiFilename + "\""

	// 然后添加 UTF-8 编码版本供支持的浏览器使用
	// 使用 RFC 5987 编码
	disposition += "; filename*=UTF-8''" + url.QueryEscape(filename)
	// 添加CORS headers
	ctx.Header("Access-Control-Expose-Headers", "Content-Disposition, Filename,filename, Content-Transfer-Encoding")
	ctx.Header("Filename", asciiFilename)          // 也对 Filename 头进行编码
	ctx.Header("Content-Disposition", disposition) // 用来指定下载下来的文件名
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Data(200, "application/octet-stream", data.Bytes())
	ctx.Abort()
}
