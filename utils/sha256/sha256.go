package sha256

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"sort"
	"strings"
)

// Encode 对data降序排序后，计算sha256
func SortEncode(data ...string) []byte {
	//对data降序排序
	sort.Strings(data)
	h := sha256.New()
	h.Write([]byte(strings.Join(data, "")))
	return h.Sum(nil)
}
func Encode(str string) []byte {
	h := sha256.New()
	h.Write([]byte(str))
	return h.Sum(nil)
}
func Equal(a, b []byte) bool {
	return bytes.Equal(a, b)
}

// EncodeToHexString 将字符串进行SHA-256哈希计算，并返回十六进制字符串
func EncodeToHexString(str string) string {
	hashBytes := Encode(str)
	return BytesToHexString(hashBytes)
}

// BytesToHexString 将SHA-256哈希值（字节数组）转换为十六进制字符串
func BytesToHexString(hashBytes []byte) string {
	// 创建一个足够大的字符串构建器
	var hexString strings.Builder
	hexString.Grow(len(hashBytes) * 2)

	// 将每个字节转换为两个十六进制字符
	for _, b := range hashBytes {
		// %02x 表示将字节格式化为两位十六进制，不足两位在前面补0
		hexString.WriteString(strings.ToLower(string("0123456789abcdef"[b>>4])) +
			strings.ToLower(string("0123456789abcdef"[b&0x0F])))
	}

	return hexString.String()
}

// BytesToBase64String 将SHA-256哈希值转换为Base64字符串（更短的表示）
func BytesToBase64String(hashBytes []byte) string {
	return base64.StdEncoding.EncodeToString(hashBytes)
}
