package sha256

import (
	"fmt"
	"testing"
)

func TestEncodingMethods(t *testing.T) {
	testString := "Hello, <PERSON>!"

	// 获取原始哈希字节
	hashBytes := Encode(testString)

	// 测试不同的编码方法
	hexString := BytesToHexString(hashBytes)
	base64String := BytesToBase64String(hashBytes)
	// base64URLString := BytesToBase64URLString(hashBytes)
	// base62String := BytesToBase62String(hashBytes)

	// 打印结果和长度
	fmt.Printf("原始字符串: %s\n", testString)
	fmt.Printf("十六进制 (长度: %d): %s\n", len(hexString), hexString)
	fmt.Printf("Base64 (长度: %d): %s\n", len(base64String), base64String)
	// fmt.Printf("Base64URL (长度: %d): %s\n", len(base64URLString), base64URLString)
	// fmt.Printf("Base62 (长度: %d): %s\n", len(base62String), base62String)
}
