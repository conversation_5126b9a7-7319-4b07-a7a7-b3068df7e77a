package wlog

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func Fatal(msg string, fields ...zap.Field) {
	global.Logger.WithOptions(zap.AddCallerSkip(1)).Fatal(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	global.Logger.WithOptions(zap.AddCallerSkip(1)).Error(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	global.Logger.WithOptions(zap.AddCallerSkip(1)).Warn(msg, fields...)
}

func Info(msg string, fields ...zap.Field) {
	global.Logger.WithOptions(zap.AddCallerSkip(1)).Info(msg, fields...)
}

func Debug(msg string, fields ...zap.Field) {
	global.Logger.WithOptions(zap.AddCallerSkip(1)).Debug(msg, fields...)
}

func Log(level zapcore.Level, msg string, fields ...zap.Field) {
	global.Logger.WithOptions(zap.AddCallerSkip(1)).Log(level, msg, fields...)
}
