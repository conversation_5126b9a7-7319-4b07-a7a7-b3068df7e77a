package filter

import (
	"gorm.io/gorm"
	"time"
)

type Time struct {
	dateType  string
	startTime int64
	endTime   int64
}

func NewTime(dateType string, startTime, endTime int64) *Time {
	return &Time{
		dateType:  dateType,
		startTime: startTime,
		endTime:   endTime,
	}
}

func (t *Time) Filter(tx *gorm.DB) *gorm.DB {
	field := "created_at"
	switch t.dateType {
	case "created_at", "updated_at", "synced_at":
		field = t.dateType
	}

	if t.startTime > 0 {
		tx = tx.Where(field+" >= ?", time.Unix(t.startTime, 0))
	}
	if t.endTime > 0 {
		tx = tx.Where(field+" <= ?", time.Unix(t.endTime, 0))
	}
	return tx
}
