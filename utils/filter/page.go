package filter

import "gorm.io/gorm"

type Page struct {
	Page     int64 `json:"page"`
	PageSize int64 `json:"page_size"`
	Invalid  bool  `json:"invalid"`
}

func NewPaging(page, pageSize int64) *Page {
	if page == 0 {
		page = 1
	}
	if pageSize == 0 {
		pageSize = 10
	}
	return &Page{
		Page:     page,
		PageSize: pageSize,
		Invalid:  false,
	}
}
func NewInvalidPage() *Page {
	return &Page{
		Page:     0,
		PageSize: 0,
		Invalid:  true,
	}
}
func (p *Page) Valid() bool {
	return !p.Invalid
}
func (p *Page) SetInvalid() *Page {
	p.Invalid = true
	return p
}
func (p *Page) Offset() int64 {
	return (p.Page - 1) * p.PageSize
}

func (p *Page) Limit() int64 {
	return p.PageSize
}

func (p *Page) Filter(db *gorm.DB) *gorm.DB {
	if p.Invalid {
		return db
	}
	return db.Offset(int(p.Offset())).Limit(int(p.Limit()))
}
