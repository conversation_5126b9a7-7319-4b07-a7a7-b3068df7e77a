package filter

type DefaultFilter struct {
	Page *Page
	Time *Time
}
type Option func(*DefaultFilter) *DefaultFilter

// NewDefaultFilter
// PageRequest.Option
// TimeRequest.Option
func NewDefaultFilterByRequest(options ...Option) *DefaultFilter {
	var res = &DefaultFilter{}
	for _, option := range options {
		option(res)
	}
	return res

}

func NewDefaultFilter(o ...func(f *DefaultFilter) *DefaultFilter) *DefaultFilter {
	var res = &DefaultFilter{}
	for _, tmpO := range o {
		res = tmpO(res)
	}
	return res
}
