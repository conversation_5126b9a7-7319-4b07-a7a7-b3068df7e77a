package ztime

import "time"

func StartOfDayByStr(tstr string) (time.Time, error) {
	t, err := time.ParseInLocation(time.DateTime, tstr, time.Local)
	if err != nil {
		return time.Time{}, err
	}
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()), nil
}

func StartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func EndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location())
}
func EndOfDayByStr(tstr string) (time.Time, error) {
	t, err := time.ParseInLocation(time.DateTime, tstr, time.Local)
	if err != nil {
		return time.Time{}, err
	}
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location()), nil
}

func StartOfWeek(t time.Time) time.Time {
	// 获取当前是周几 (0是周日，1是周一，以此类推)
	weekday := int(t.Weekday())
	// 如果是周日，需要往前推6天；如果是其他日期，往前推(weekday-1)天
	daysToSubtract := 0
	if weekday == 0 {
		daysToSubtract = 6
	} else {
		daysToSubtract = weekday - 1
	}
	// 往前推到最近的周一
	t = t.AddDate(0, 0, -daysToSubtract)
	// 设置为当天的0点
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func StartOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

func StartOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
}

// func EndOfWeek(t time.Time) time.Time {
// 	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, t.Location())
// }
