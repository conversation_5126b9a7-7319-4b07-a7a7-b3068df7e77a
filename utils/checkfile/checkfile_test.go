package checkfile

import (
	"bytes"
	"os"
	"testing"
)

// utils/checkfile/test1_template.csv
// utils/checkfile/test1_template.htm
// utils/checkfile/test1_template.xls
// utils/checkfile/test1_template.xlsx
func TestCheckFile(t *testing.T) {
	// 测试Excel文件
	// 测试XLS文件
	xlsData, err := os.ReadFile("test1_template.xls")
	if err != nil {
		t.Fatal("Failed to read XLS file:", err)
	}
	xlsBuffer := bytes.NewBuffer(xlsData)
	if ok := Excel(xlsBuffer); !ok {
		t.Error("XLS file check failed")
	}

	// 测试XLSX文件
	xlsxData, err := os.ReadFile("test1_template.xlsx")
	if err != nil {
		t.Fatal("Failed to read XLSX file:", err)
	}
	xlsxBuffer := bytes.NewBuffer(xlsxData)
	if ok := Excel(xlsxBuffer); !ok {
		t.Error("XLSX file check failed")
	}

	// 测试HTM文件 - 应该返回false
	htmData, err := os.ReadFile("test1_template.htm")
	if err != nil {
		t.Fatal("Failed to read HTM file:", err)
	}
	htmBuffer := bytes.NewBuffer(htmData)
	if ok := Excel(htmBuffer); ok {
		t.Error("HTM file should not pass as Excel file")
	}

	// 测试空文件
	emptyBuffer := &bytes.Buffer{}
	if ok := Excel(emptyBuffer); ok {
		t.Error("Empty buffer should not pass the Excel check")
	}

	// 测试CSV方法
	// 读取有效的CSV文件
	csvData, err := os.ReadFile("test1_template.csv")
	if err != nil {
		t.Fatal("Failed to read CSV file:", err)
	}
	csvBuffer := bytes.NewBuffer(csvData)
	if ok := CSV(csvBuffer); !ok {
		t.Error("Valid CSV file check failed")
	}

	// 测试非CSV文件
	if ok := CSV(htmBuffer); !ok {
		t.Error("HTM file should not pass as CSV file")
	}

	// 测试空文件作为CSV
	emptyCSVBuffer := &bytes.Buffer{}
	if ok := CSV(emptyCSVBuffer); ok {
		t.Error("Empty buffer should not pass the CSV check")
	}
}
