package checkfile

import (
	"bytes"

	"github.com/gocarina/gocsv"

	"github.com/gabriel-vasile/mimetype"
)

const XlsxFile = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
const XlsFile = "application/vnd.ms-excel"

func Excel(file *bytes.Buffer) (isValid bool) {
	// 检测文件类型
	mtype := mimetype.Detect(file.Bytes())
	if mtype.Is(XlsxFile) || mtype.Is(XlsFile) {
		return true
	}

	return false
}

func CSV(file *bytes.Buffer) (isValid bool) {
	//替换掉文件内容开头的\uFEFF，不区分大小写
	content := file.Bytes()
	if len(content) >= 3 && bytes.Equal(content[0:3], []byte{0xEF, 0xBB, 0xBF}) {
		content = content[3:]
	}

	reader := bytes.NewBuffer(content)
	_, err := gocsv.CSVToMaps(reader)

	return err == nil
}
