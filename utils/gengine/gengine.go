package gengine

import (
	"strings"
	"time"
	"unicode"
)

func ParseDate(value string) (err error) {
	_, err = time.Parse("2006-01-02", value)
	return
}

// ValidateDigits checks if a string contains exactly length digits
func ValidateDigits(length int, data string) bool {
	// 首先检查长度是否为length
	if len(data) != length {
		return false
	}

	// 检查每个字符是否都是数字
	for _, char := range data {
		if char < '0' || char > '9' {
			return false
		}
	}
	return true
}

// GetStringLength 获取字符串长度
func GetStringLength(s string) int {
	return len(s)
}

// 检查字符是否为字母或空格
func IsAlphaOrSpace(char rune) bool {
	return unicode.IsLetter(char) || char == ' '
}
func IsEmpty(s string) bool {
	return len(strings.TrimSpace(s)) == 0
}

// ContainsOnlyAlphaAndSpace 检查字符串是否只包含字母和空格
func ContainsOnlyAlphaAndSpace(s string) bool {
	for _, char := range s {
		if !IsAlphaOrSpace(char) {
			return false
		}
	}
	return true
}

// HasSpaceAtBoundary 检查字符串开头和结尾是否有空格
func HasSpaceAtBoundary(s string) bool {
	if len(s) == 0 {
		return false
	}
	return s[0] == ' ' || s[len(s)-1] == ' '
}

// 检查字符是否为ASCII字符（ASCII码在0-127之间）
func IsASCII(char rune) bool {
	return char >= 0 && char <= 127
}

// 检查字符串是否只包含ASCII字符
func ContainsOnlyASCII(s string) bool {
	for _, char := range s {
		if !IsASCII(char) {
			return false
		}
	}
	return true
}

func StartsWith(s, prefix string) bool {
	return strings.HasPrefix(s, prefix)
}

// 排除以后都是数字
func RestAreDigits(s string, startIdx int) bool {
	if len(s) <= 1 {
		return false
	}

	for _, char := range s[startIdx:] {
		if !IsDigit(char) {
			return false
		}
	}
	return true
}

// 检查字符是否为数字
func IsDigit(char rune) bool {
	return unicode.IsDigit(char)
}

func IsUppercaseLetter(char rune) bool {
	return unicode.IsUpper(char) && unicode.IsLetter(char)
}

// 检查字符串是否只包含大写字母
func ContainsOnlyUppercaseLetters(s string) bool {
	for _, char := range s {
		if !IsUppercaseLetter(char) {
			return false
		}
	}
	return true
}
