package excel

import (
	"bytes"
	"strconv"

	excelize "github.com/xuri/excelize/v2"
)

type Excel struct {
	client       *excelize.File
	sheetIndex   map[string]int
	currentSheet string
}

func New() *Excel {
	return &Excel{
		client:     excelize.NewFile(),
		sheetIndex: make(map[string]int),
	}
}

func OpenBuffer(buffer *bytes.Buffer) (*Excel, error) {
	open, err := excelize.OpenReader(buffer)
	if err != nil {
		return nil, err
	}
	return &Excel{client: open, sheetIndex: make(map[string]int)}, nil
}
func (e *Excel) Sheets() []string {
	sheets := e.client.GetSheetList()
	for idx, sheet := range sheets {
		e.sheetIndex[sheet] = idx
	}
	return sheets
}
func (e *Excel) ReadAllByBuffer() ([][]string, error) {
	rows, err := e.client.GetRows(e.currentSheet)

	return rows, err
}
func (e *Excel) UseSheet(name string) *Excel {
	if _, ok := e.sheetIndex[name]; !ok {
		e.sheetIndex[name], _ = e.client.NewSheet(name)
	}
	e.currentSheet = name
	e.client.SetActiveSheet(e.sheetIndex[name])
	return e
}

func (e *Excel) SetTitle(title ...string) *Excel {
	for i, v := range title {
		tmpLine, _ := excelize.ColumnNumberToName(i + 1)
		e.client.SetCellValue(e.currentSheet, tmpLine+"1", v)
	}
	return e
}
func ColumnNumberToName(col int) string {
	colName, _ := excelize.ColumnNumberToName(col)
	return colName
}
func (e *Excel) SetColString(cols ...string) *Excel {
	a := "@"
	style, _ := e.client.NewStyle(&excelize.Style{
		CustomNumFmt: &a, // 使用"@"表示文本格式
	})
	for _, col := range cols {
		e.client.SetColStyle(e.currentSheet, col, style)
	}
	return e
}
func (e *Excel) SetCellValue(cells ...[]string) *Excel {
	for idx, cell := range cells {
		for i, v := range cell {
			tmpLine, _ := excelize.ColumnNumberToName(i + 1)

			e.client.SetCellValue(e.currentSheet, tmpLine+strconv.Itoa(idx+1), v)

		}
	}

	return e
}

//	func (e *Excel) SetCellStyle(topLeftCell, bottomRightCell string, styleID int) *Excel {
//		excelize.CellType()
//		style, err := e.client.NewStyle(&excelize.Style{
//			Protection: &excelize.Protection{
//				Hidden: true,
//				Locked: true,
//			},
//		})
//		e.client.SetCellStyle(e.currentSheet, topLeftCell, bottomRightCell, styleID)
//	}
func (e *Excel) WriteBuffer() (*bytes.Buffer, error) {
	return e.client.WriteToBuffer()
}
