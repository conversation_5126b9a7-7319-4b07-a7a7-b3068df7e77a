package cache

import (
	"bytes"
	"encoding/gob"
	"errors"
	"time"

	"github.com/go-redis/redis"
)

var _ ICache[any] = &LocalRedisCache[any]{}

type LocalRedisCache[T any] struct {
	client *redis.Client
}

func NewLocalRedisCache[t any](address, password string, db int) *LocalRedisCache[t] {
	return &LocalRedisCache[t]{
		client: redis.NewClient(&redis.Options{
			Addr:     address,
			Password: password,
			DB:       db,
		}),
	}
}

func (l *LocalRedisCache[T]) TTL(key string) (int, error) {
	ttl, err := l.client.TTL(key).Result()
	if err != nil {
		return 0, err
	}
	return int(ttl.Seconds()), nil
}

func (l *LocalRedisCache[T]) Has(key string) (bool, error) {
	if _, err := l.client.Get(key).Result(); err != nil {
		if errors.Is(err, redis.Nil) {
			return false, nil
		} else {
			return false, err
		}
	}
	return true, nil
}

func (l *LocalRedisCache[T]) Set(key string, val T, expireSeconds ...int) error {
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	err := enc.Encode(val)
	if err != nil {
		return err
	}
	if len(expireSeconds) > 0 {
		return l.client.Set(key, buf.Bytes(), time.Duration(expireSeconds[0])*time.Second).Err()
	}
	return l.client.Set(key, buf.Bytes(), 0).Err()
}

func (l *LocalRedisCache[T]) Get(key string) (T, error) {
	var result T
	val, err := l.client.Get(key).Bytes()
	if err != nil {
		return result, err
	}
	buf := bytes.NewBuffer(val)
	dec := gob.NewDecoder(buf)
	err = dec.Decode(&result)
	return result, err
}
