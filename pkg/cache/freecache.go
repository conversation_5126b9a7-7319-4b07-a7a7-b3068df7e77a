package cache

import (
	"bytes"
	"encoding/gob"
	"errors"

	"github.com/coocood/freecache"
)

var _ ICache[any] = &LocalFreeCache[any]{}

type LocalFreeCache[T any] struct {
	cache *freecache.Cache
}

func NewLocalFreeCache[t any](size int) *LocalFreeCache[t] {
	return &LocalFreeCache[t]{
		cache: freecache.NewCache(size),
	}
}

func (l *LocalFreeCache[T]) TTL(key string) (int, error) {
	ttl, err := l.cache.TTL([]byte(key))
	if err != nil {
		return 0, err
	}
	return int(ttl), nil
}

func (l *LocalFreeCache[T]) Has(key string) (bool, error) {
	if _, err := l.cache.Get([]byte(key)); err != nil {
		if errors.Is(err, freecache.ErrNotFound) {
			return false, nil
		} else {
			return false, err
		}
	}
	return true, nil
}

func (l *LocalFreeCache[T]) Set(key string, val T, expireSeconds ...int) error {
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	err := enc.Encode(val)
	if err != nil {
		return err
	}
	if len(expireSeconds) > 0 {
		return l.cache.Set([]byte(key), buf.Bytes(), expireSeconds[0])
	}
	return l.cache.Set([]byte(key), buf.Bytes(), 0)
}

func (l *LocalFreeCache[T]) Get(key string) (T, error) {
	var result T
	val, err := l.cache.Get([]byte(key))
	if err != nil {
		return result, err
	}
	buf := bytes.NewBuffer(val)
	dec := gob.NewDecoder(buf)
	err = dec.Decode(&result)
	return result, err
}
