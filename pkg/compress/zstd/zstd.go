package zstd

import (
	"bytes"
	"fmt"
	"github.com/klauspost/compress/zstd"
	"io"
)

func CompressStream(in io.Reader, out io.Writer) error {
	enc, err := zstd.NewWriter(out)
	if err != nil {
		return err
	}
	_, err = io.Copy(enc, in)
	defer enc.Close()
	if err != nil {

		return err
	}
	return nil
}

func CompressBytes(data []byte) ([]byte, error) {
	var buf = bytes.Buffer{}
	enc, err := zstd.NewWriter(&buf)
	if err != nil {
		return nil, err
	}
	if _, err := enc.Write(data); err != nil {
		return nil, fmt.Errorf("Failed to write to zstd writer.\n: %w", err)
	}
	if err := enc.Close(); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func DecompressBytes(data []byte) ([]byte, error) {
	var buf = bytes.Buffer{}
	dec, err := zstd.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("Failed to create zstd reader.\n: %w", err)
	}
	defer dec.Close()

	// 进行错误判断，如果解压失败就进行三次重试
	const maxRetries = 3
	for i := 0; i < maxRetries; i++ {
		buf.Reset() // 重置缓冲区，准备重新解压

		// 重置reader位置
		reader := bytes.NewReader(data)
		dec.Reset(reader)

		// 尝试解压
		_, err = io.Copy(&buf, dec)
		if err == nil {
			// 解压成功
			return buf.Bytes(), nil
		}

		// 如果是最后一次尝试，直接返回错误
		if i == maxRetries-1 {
			return nil, fmt.Errorf("Failed to decompress zstd data, retried %d times.\n: %w", maxRetries, err)
		}
	}
	return nil, nil
}
