package version

import (
	"flag"
	"fmt"
	"runtime"
)

// 通过flag包设置-version参数
var PrintVersion bool

func init() {
	flag.BoolVar(&PrintVersion, "version", false, "print program build version")
}

var (
	gitBranch string
	gitTag    string
	gitCommit string
	buildDate string
	gitStash  string
)

type Info struct {
	GitStash  string `json:"git_stash"`
	GitBranch string `json:"gitBranch"`
	GitTag    string `json:"gitTag"`
	GitCommit string `json:"gitCommit"`
	BuildDate string `json:"buildDate"`
	GoVersion string `json:"goVersion"`
	Compiler  string `json:"compiler"`
	Platform  string `json:"platform"`
}

func (info Info) String() string {
	return info.GitCommit
}

func GetVersion() Info {
	return Info{
		GitBranch: gitBranch,
		GitStash:  gitStash,
		GitTag:    gitTag,
		GitCommit: gitCommit,
		BuildDate: buildDate,
		GoVersion: runtime.Version(),
		Compiler:  runtime.Compiler,
		Platform:  fmt.Sprintf("%s/%s", runtime.GOOS, runtime.GOARCH),
	}
}
