package timeutil

import (
	"errors"
	"math"
	"net/http"
	"time"
)

var (
	cst *time.Location
)

// CSTLayout China Standard Time Layout
const (
	CSTLayout  = "2006-01-02 15:04:05"
	DateLayout = "2006-01-02"
)

const Day = time.Hour * 24

func UnixToTime(sec int64) time.Time {
	return time.Unix(sec, 0)
}

func DateStringToTime(date string) (t time.Time, err error) {
	t, err = time.Parse(DateLayout, date)
	return
}

func TimeToDate(t time.Time) string {
	return t.Format(DateLayout)
}

func TimeToCSTString(t time.Time) string {
	return t.Format(CSTLayout)
}

func init() {
	var err error
	if cst, err = time.LoadLocation("Asia/Shanghai"); err != nil {
		panic(err)
	}

	// 默认设置为中国时区
	time.Local = cst
}

// RFC3339ToCSTLayout convert rfc3339 value to china standard time layout
// 2020-11-08T08:18:46+08:00 => 2020-11-08 08:18:46
func RFC3339ToCSTLayout(value string) (string, error) {
	ts, err := time.Parse(time.RFC3339, value)
	if err != nil {
		return "", err
	}

	return ts.In(cst).Format(CSTLayout), nil
}

// CSTLayoutString 格式化时间
// 返回 "2006-01-02 15:04:05" 格式的时间
func CSTLayoutString() string {
	ts := time.Now()
	return ts.In(cst).Format(CSTLayout)
}

// ParseCSTInLocation 格式化时间
func ParseCSTInLocation(date string) (time.Time, error) {
	return time.ParseInLocation(CSTLayout, date, cst)
}

// CSTLayoutStringToUnix 返回 unix 时间戳
// 2020-01-24 21:11:11 => 1579871471
func CSTLayoutStringToUnix(cstLayoutString string) (int64, error) {
	stamp, err := time.ParseInLocation(CSTLayout, cstLayoutString, cst)
	if err != nil {
		return 0, err
	}
	return stamp.Unix(), nil
}

// GMTLayoutString 格式化时间
// 返回 "Mon, 02 Jan 2006 15:04:05 GMT" 格式的时间
func GMTLayoutString() string {
	return time.Now().In(cst).Format(http.TimeFormat)
}

// ParseGMTInLocation 格式化时间
func ParseGMTInLocation(date string) (time.Time, error) {
	return time.ParseInLocation(http.TimeFormat, date, cst)
}

// SubInLocation 计算时间差
func SubInLocation(ts time.Time) float64 {
	return math.Abs(time.Now().In(cst).Sub(ts).Seconds())
}

func StartDate(sec int64) string {
	return time.Unix(sec, 0).Format(DateLayout) + " 00:00:00"
}

func EndDate(sec int64) string {
	return time.Unix(sec, 0).Format(DateLayout) + " 23:59:59"
}

func TimeToShortDate(sec int64) string {
	return time.Unix(sec, 0).Format(DateLayout)
}

func IsEmptyTime(t time.Time) bool {
	return t.Unix() == -62135596800
}

// 是否在指定天数内
func IsWithinPastDays(days int, t time.Time) bool {
	daysAgo := time.Now().AddDate(0, 0, -days)
	return t.After(daysAgo)
}

func TimeStampToDate(timeStamp int64) string {
	return time.Unix(timeStamp, 0).Format(DateLayout)
}

func CalculateAge(birthDate string) (int, error) {
	layout := "2006-01-02"
	bd, err := time.Parse(layout, birthDate)
	if err != nil {
		return 0, errors.New("invalid date format")
	}
	now := time.Now()
	age := now.Year() - bd.Year()
	if now.Month() < bd.Month() || (now.Month() == bd.Month() && now.Day() < bd.Day()) {
		age--
	}
	return age, nil
}

func TimeToMMYY(time1 time.Time) string {
	mmYY := time1.Format("0106")
	return mmYY
}

func DateToString(date time.Time) string {
	return date.Format(DateLayout)
}
