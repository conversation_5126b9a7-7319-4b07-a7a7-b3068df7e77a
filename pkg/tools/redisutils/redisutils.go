package redisutils

import (
	"context"
	"fmt"
	"github.com/go-redis/redis/v8"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"time"
)

var ctx = context.Background()

// GetAllKeys 使用 SCAN 命令获取所有的键
func GetAllKeys(match string) ([]string, error) {
	var keys, result []string
	var cursor uint64
	var n int
	const batchSize = 100 // 每次扫描的键数量
	for {
		var err error
		keys, cursor, err = global.Rdb.Scan(ctx, cursor, "lambda"+match+"*", batchSize).Result()
		if err != nil {
			return nil, err
		}
		result = append(result, keys...)
		n += len(keys)
		if cursor == 0 {
			break
		}
	}
	return result, nil
}

// GetPrefixKeys 获取某个前缀的所有key
func GetPrefixKeys(prefix string) ([]string, error) {
	var keys, result []string
	var cursor uint64
	var n int
	const batchSize = 100 // 每次扫描的键数量
	for {
		var err error
		keys, cursor, err = global.Rdb.Scan(ctx, cursor, prefix+"*", batchSize).Result()
		if err != nil {
			return nil, err
		}
		result = append(result, keys...)
		n += len(keys)
		if cursor == 0 {
			break
		}
	}
	return result, nil
}

/*------------------------------------ 字符 操作 ------------------------------------*/

// Set 设置 key的值
func Set(key, value string) bool {
	result, err := global.Rdb.Set(ctx, key, value, 0).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result == "OK"
}

// SetEX 设置 key的值并指定过期时间
func SetEX(key, value string, ex time.Duration) bool {
	result, err := global.Rdb.Set(ctx, key, value, ex).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result == "OK"
}

// Get 获取 key的值
func Get(key string) (string, error) {
	result, err := global.Rdb.Get(ctx, key).Result()
	return result, err
}

func Exists(key string) (res int64, err error) {
	return global.Rdb.Exists(ctx, key).Result()
}

// GetSet 设置新值获取旧值
func GetSet(key, value string) (bool, string) {
	oldValue, err := global.Rdb.GetSet(ctx, key, value).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, oldValue
}

// Incr key值每次加一 并返回新值
func Incr(key string) int64 {
	val, err := global.Rdb.Incr(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// IncrBy key值每次加指定数值 并返回新值
func IncrBy(key string, incr int64) int64 {
	val, err := global.Rdb.IncrBy(ctx, key, incr).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// IncrByFloat key值每次加指定浮点型数值 并返回新值
func IncrByFloat(key string, incrFloat float64) float64 {
	val, err := global.Rdb.IncrByFloat(ctx, key, incrFloat).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// Decr key值每次递减 1 并返回新值
func Decr(key string) int64 {
	val, err := global.Rdb.Decr(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// DecrBy key值每次递减指定数值 并返回新值
func DecrBy(key string, incr int64) int64 {
	val, err := global.Rdb.DecrBy(ctx, key, incr).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// Del 删除 key
func Del(key string) bool {
	result, err := global.Rdb.Del(ctx, key).Result()
	if err != nil {
		return false
	}
	return result == 1
}

// Expire 设置 key的过期时间
func Expire(key string, ex time.Duration) bool {
	result, err := global.Rdb.Expire(ctx, key, ex).Result()
	if err != nil {
		return false
	}
	return result
}

/*------------------------------------ list 操作 ------------------------------------*/

// LPush 从列表左边插入数据，并返回列表长度
func LPush(key string, date ...interface{}) int64 {
	result, err := global.Rdb.LPush(ctx, key, date).Result()
	if err != nil {
		fmt.Println(err)
	}
	return result
}

// RPush 从列表右边插入数据，并返回列表长度
func RPush(key string, date ...interface{}) int64 {
	result, err := global.Rdb.RPush(ctx, key, date).Result()
	if err != nil {
		fmt.Println(err)
	}
	return result
}

// LPop 从列表左边删除第一个数据，并返回删除的数据
func LPop(key string) (bool, string) {
	val, err := global.Rdb.LPop(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, val
}

// RPop 从列表右边删除第一个数据，并返回删除的数据
func RPop(key string) (bool, string) {
	val, err := global.Rdb.RPop(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, val
}

// LIndex 根据索引坐标，查询列表中的数据
func LIndex(key string, index int64) (bool, string) {
	val, err := global.Rdb.LIndex(ctx, key, index).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, val
}

// LLen 返回列表长度
func LLen(key string) int64 {
	val, err := global.Rdb.LLen(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// LRange 返回列表的一个范围内的数据，也可以返回全部数据
func LRange(key string, start, stop int64) []string {
	vales, err := global.Rdb.LRange(ctx, key, start, stop).Result()
	if err != nil {
		fmt.Println(err)
	}
	return vales
}

// LRem 从列表左边开始，删除元素data， 如果出现重复元素，仅删除 count次
func LRem(key string, count int64, data interface{}) bool {
	_, err := global.Rdb.LRem(ctx, key, count, data).Result()
	if err != nil {
		fmt.Println(err)
	}
	return true
}

// LInsert 在列表中 pivot 元素的后面插入 data
func LInsert(key string, pivot int64, data interface{}) bool {
	err := global.Rdb.LInsert(ctx, key, "after", pivot, data).Err()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

/*------------------------------------ set 操作 ------------------------------------*/

// SAdd 添加元素到集合中
func SAdd(key string, data ...interface{}) bool {
	err := global.Rdb.SAdd(ctx, key, data).Err()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// SCard 获取集合元素个数
func SCard(key string) int64 {
	size, err := global.Rdb.SCard(ctx, "key").Result()
	if err != nil {
		fmt.Println(err)
	}
	return size
}

// SIsMember 判断元素是否在集合中
func SIsMember(key string, data interface{}) bool {
	ok, err := global.Rdb.SIsMember(ctx, key, data).Result()
	if err != nil {
		fmt.Println(err)
	}
	return ok
}

// SMembers 获取集合所有元素
func SMembers(key string) []string {
	es, err := global.Rdb.SMembers(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return es
}

// SRem 删除 key集合中的 data元素
func SRem(key string, data ...interface{}) bool {
	_, err := global.Rdb.SRem(ctx, key, data).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// SPopN 随机返回集合中的 count个元素，并且删除这些元素
func SPopN(key string, count int64) []string {
	vales, err := global.Rdb.SPopN(ctx, key, count).Result()
	if err != nil {
		fmt.Println(err)
	}
	return vales
}

/*------------------------------------ hash 操作 ------------------------------------*/

// HSet 根据 key和 field字段设置，field字段的值
func HSet(key, field, value string) bool {
	err := global.Rdb.HSet(ctx, key, field, value).Err()
	if err != nil {
		return false
	}
	return true
}

// HGet 根据 key和 field字段，查询field字段的值
func HGet(key, field string) (val string, err error) {
	val, err = global.Rdb.HGet(ctx, key, field).Result()
	if err != nil {
		return
	}
	return
}

// HMGet 根据key和多个字段名，批量查询多个 hash字段值
func HMGet(key string, fields ...string) ([]interface{}, error) {
	return global.Rdb.HMGet(ctx, key, fields...).Result()

}

// HGetAll 根据 key查询所有字段和值
func HGetAll(key string) (map[string]string, error) {
	data, err := global.Rdb.HGetAll(ctx, key).Result()
	return data, err
}

// HKeys 根据 key返回所有字段名
func HKeys(key string) []string {
	fields, err := global.Rdb.HKeys(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return fields
}

// HLen 根据 key，查询hash的字段数量
func HLen(key string) int64 {
	size, err := global.Rdb.HLen(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return size
}

// HMSet 根据 key和多个字段名和字段值，批量设置 hash字段值
func HMSet(key string, data map[string]interface{}) bool {
	result, err := global.Rdb.HMSet(ctx, key, data).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result
}

// HSetNX 如果 field字段不存在，则设置 hash字段值
func HSetNX(key, field string, value interface{}) bool {
	result, err := global.Rdb.HSetNX(ctx, key, field, value).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result
}

// HDel 根据 key和字段名，删除 hash字段，支持批量删除
func HDel(key string, fields ...string) bool {
	_, err := global.Rdb.HDel(ctx, key, fields...).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// HExists 检测 hash字段名是否存在
func HExists(key, field string) bool {
	result, err := global.Rdb.HExists(ctx, key, field).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result
}

// 其他操作

// Rename 重命名
func Rename(oldKey, newKey string) (string, error) {
	result, err := global.Rdb.Rename(ctx, oldKey, newKey).Result()
	return result, err
}

func Pipeline(f func(rdb redis.Pipeliner) error) (cmders []redis.Cmder, err error) {

	pipeline := global.Rdb.Pipeline()
	defer pipeline.Close()
	err = f(pipeline)
	if err != nil {
		return
	}
	cmders, err = pipeline.Exec(ctx)
	return
}
