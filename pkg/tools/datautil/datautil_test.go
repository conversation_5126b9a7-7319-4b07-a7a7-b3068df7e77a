package datautil

import (
	"reflect"
	"testing"
)

func TestCommaSeparated_AddInt64(t *testing.T) {
	type fields struct {
		sLi []string
	}
	type args struct {
		li []int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			"4",
			fields{[]string{"1", "2", "3"}},
			args{[]int64{4}},
			"1,2,3,4",
		}, {
			"repeat",
			fields{[]string{"1", "2", "3"}},
			args{[]int64{3, 3, 4, 4}},
			"1,2,3,4",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &CommaSeparated{
				sLi: tt.fields.sLi,
			}
			if got := d.AddInt64(tt.args.li...); got != tt.want {
				t.Errorf("AddInt64() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCommaSeparated_Marshal(t *testing.T) {
	type fields struct {
		sLi []string
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		{
			"1,2,3",
			fields{[]string{"1", "2", "3"}},
			"1,2,3",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &CommaSeparated{
				sLi: tt.fields.sLi,
			}
			if got := d.Marshal(); got != tt.want {
				t.Errorf("Marshal() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCommaSeparated_RemoveInt64(t *testing.T) {
	type fields struct {
		sLi []string
	}
	type args struct {
		li []int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			"2",
			fields{[]string{"1", "2", "3"}},
			args{[]int64{2}},
			"1,3",
		}, {
			"all",
			fields{[]string{"1", "2", "3"}},
			args{[]int64{1, 2, 3}},
			"",
		}, {
			"nil",
			fields{[]string{"1", "2", "3"}},
			args{[]int64{}},
			"1,2,3",
		}, {
			"not found",
			fields{[]string{"1", "2", "3"}},
			args{[]int64{4}},
			"1,2,3",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &CommaSeparated{
				sLi: tt.fields.sLi,
			}
			if got := d.RemoveInt64(tt.args.li...); got != tt.want {
				t.Errorf("RemoveInt64() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCommaSeparated_ToInt64(t *testing.T) {
	type fields struct {
		sLi []string
	}
	tests := []struct {
		name    string
		fields  fields
		want    []int64
		wantErr bool
	}{
		{
			"123",
			fields{[]string{"1", "2", "3"}},
			[]int64{1, 2, 3},
			false,
		},
		{
			"null",
			fields{[]string{}},
			[]int64{},
			false,
		},
		{
			"err",
			fields{[]string{"a"}},
			nil,
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &CommaSeparated{
				sLi: tt.fields.sLi,
			}
			got, err := d.ToInt64()
			if (err != nil) != tt.wantErr {
				t.Errorf("ToInt64() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ToInt64() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewCommaSeparated(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want *CommaSeparated
	}{
		{
			"123",
			args{"1,2,3"},
			&CommaSeparated{[]string{"1", "2", "3"}},
		}, {
			"empty",
			args{""},
			&CommaSeparated{[]string{}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCommaSeparated(tt.args.s); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewCommaSeparated() = %v, want %v", got, tt.want)
			}
		})
	}
}
