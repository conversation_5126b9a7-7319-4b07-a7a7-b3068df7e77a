package datautil

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"strconv"
	"strings"
)

// CommaSeparated 管理逗号分割的数据，提供类型转换接口
type CommaSeparated struct {
	sLi []string
}

func NewCommaSeparated(s string) *CommaSeparated {
	sLi := []string{}
	for _, v := range strings.Split(s, ",") {
		if v != "" {
			sLi = append(sLi, v)
		}
	}
	return &CommaSeparated{sLi: sLi}
}

func (d *CommaSeparated) Marshal() string {
	return strings.Join(d.sLi, ",")
}

func (d *CommaSeparated) AddInt64(li ...int64) string {
	exitMap := map[string]bool{}
	newLi := []string{}
	for _, s := range d.sLi {
		if !exitMap[s] {
			newLi = append(newLi, s)
			exitMap[s] = true
		}
	}
	for _, i := range li {
		s := strconv.FormatInt(i, 10)
		if !exitMap[s] {
			newLi = append(newLi, s)
			exitMap[s] = true
		}
	}
	d.sLi = newLi
	return d.Marshal()
}

func (d *CommaSeparated) ToInt64() ([]int64, error) {
	resp := []int64{}
	for _, s := range d.sLi {
		i, err := strconv.ParseInt(s, 10, 64)
		if err != nil {
			return nil, er.WSEF(err, zap.String("s", s))
		}
		resp = append(resp, i)
	}
	return resp, nil
}

func (d *CommaSeparated) RemoveInt64(li ...int64) string {
	newLi := []string{}
	removeLi := []string{}
	for _, i := range li {
		s := strconv.FormatInt(i, 10)
		removeLi = append(removeLi, s)
	}
	for _, v := range d.sLi {
		has := false
		for _, r := range removeLi {
			if r == v {
				has = true
				break
			}
		}
		if !has {
			newLi = append(newLi, v)
		}
	}
	d.sLi = newLi
	return d.Marshal()
}
