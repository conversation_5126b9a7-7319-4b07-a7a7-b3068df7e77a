package db

import (
	"context"
	"crypto/tls"
	"github.com/go-redis/redis/v8"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
)

func InitRedis(cfg config.RedisConfig) (rdb *redis.Client, err error) {
	var tlsConfig *tls.Config
	if cfg.Tls {
		tlsConfig = &tls.Config{
			MinVersion:         tls.VersionTLS12,
			InsecureSkipVerify: true,
			//ServerName: "your.domain.com",
			//Certificates: []tls.Certificate{cert}
		}
	} else {
		tlsConfig = nil

	}
	rdb = redis.NewClient(&redis.Options{
		Addr:      cfg.Host + ":" + cfg.Port,
		Password:  cfg.Password, // no password set
		DB:        cfg.Db,       // use default
		TLSConfig: tlsConfig,
		PoolSize:  8,
	})
	// 测试连接
	if _, err = rdb.Ping(context.Background()).Result(); err != nil {
		// 处理错误
		return nil, err
	}

	return
}
