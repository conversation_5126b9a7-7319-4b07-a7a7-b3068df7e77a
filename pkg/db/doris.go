package db

import (
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
)

var doris *gorm.DB

func Doris() *gorm.DB {
	return doris
}

func InitDoris(cfg config.<PERSON>) error {
	// Silent = 1
	// Error = 2
	// Warn = 3

	dsn := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8mb4&parseTime=True&loc=Local", cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Dbname)

	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // 输出到标准输出
		logger.Config{
			LogLevel: logger.Info, // 设置日志级别
			Colorful: false,       // 是否使用颜色
		},
	)
	doris, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		QueryFields: true,
		Logger:      newLogger,
	})

	if err != nil {
		return err
	}
	sqlDB, err := doris.DB()
	if err != nil {
		log.Fatalf("获取数据库实例失败: %v", err)
	}

	// 确保数据库连接正常
	err = sqlDB.Ping()
	if err != nil {
		log.Fatalf("无法 ping Doris 数据库: %v", err)
	} else {
		fmt.Println("成功连接到 Doris 数据库!")
	}

	return nil
}
