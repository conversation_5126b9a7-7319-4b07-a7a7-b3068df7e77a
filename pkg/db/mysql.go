package db

import (
	"fmt"
	"log"
	"os"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func InitDB(cfg config.Mysql) (*gorm.DB, error) {
	// Silent = 1
	// Error = 2
	// Warn = 3
	var db *gorm.DB
	dsn := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8mb4&parseTime=True&loc=Local", cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Dbname)

	var err error
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // 输出到标准输出
		logger.Config{
			SlowThreshold: time.Duration(cfg.SlowSql), // 设置慢查询时间阈值
			LogLevel:      logger.Info,                // 设置日志级别
			Colorful:      false,                      // 是否使用颜色
		},
	)
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
		QueryFields: true,
		Logger:      newLogger,
	})

	if err != nil {
		return nil, err
	}

	return db, nil
}
