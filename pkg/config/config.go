package config

import (
	"fmt"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
)

var cfg Config

type Http struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

func IsLocal() bool {
	return cfg.App.Mode == "local"
}
func (h *Http) Addr() string {
	return fmt.Sprintf("%s:%d", h.Host, h.Port)
}

type Mysql struct {
	Host     string
	Port     string
	User     string
	Password string
	Dbname   string
	SlowSql  int
}

type RedisConfig struct {
	Host     string
	Port     string
	Password string
	Db       int
	Tls      bool
}
type CraParamter struct {
	Individual []struct {
		FormField string `mapstructure:"form_field"`
		DataField string `mapstructure:"data_field"`
	} `mapstructure:"individual"`
	Company []struct {
		FormField string `mapstructure:"form_field"`
		DataField string `mapstructure:"data_field"`
	} `mapstructure:"company"`
}

type Log struct {
	Path              string `mapstructure:"path"`                // 文件路径
	Level             string `mapstructure:"level"`               // 日志级别
	DisableLogConsole bool   `mapstructure:"disable_console_log"` // 是否将日志输出在console
	MaxSize           int    `mapstructure:"max_size"`            // MaxSize 进行切割之前，日志文件的最大大小(MB为单位)，默认为100MB
	MaxAge            int    `mapstructure:"max_age"`             // MaxAge 是根据文件名中编码的时间戳保留旧日志文件的最大天数。
	MaxBackups        int    `mapstructure:"max_backups"`         // MaxBackups 是要保留的旧日志文件的最大数量。默认是保留所有旧的日志文件（尽管 MaxAge 可能仍会导致它们被删除。）
	LocalTime         bool   `mapstructure:"local_time"`          // 使用本地时间
	Compress          bool   `mapstructure:"compress"`            // 是否压缩
	Daily             bool   `mapstructure:"daily"`               // 是否按天分割
	Skip              int    `mapstructure:"skip"`                // 日志打印层级
}

// jwt配置
type Jwt struct {
	AccessExpire  int64  `mapstructure:"access_expire"`  // 访问过期时间
	RefreshExpire int64  `mapstructure:"refresh_expire"` // 刷新过期时间
	Secret        string `mapstructure:"secret"`         // 签名
	Issuer        string `mapstructure:"issuer"`         // 发行人
}

type App struct {
	NodeId      int64  `mapstructure:"node_id"`
	GatewayHost string `mapstructure:"gateway_host"`
	Mode        string `mapstructure:"mode"`
}

type Config struct {
	App         App          `mapstructure:"app"`
	Http        Http         `mapstructure:"http"`
	Mysql       Mysql        `mapstructure:"mysql"`
	Logs        Log          `mapstructure:"logs"`
	Jwt         Jwt          `mapstructure:"jwt"`
	Redis       RedisConfig  `mapstructure:"redis"`
	CraParamter *CraParamter `mapstructure:"cra_paramter"`
	Doris       Doris        `mapstructure:"doris"`
	Aws         AWSConfig    `mapstructure:"aws"`
}
type Doris struct {
	Host     string
	Port     string
	User     string
	Password string
	Dbname   string
}
type AWSConfig struct {
	SNS struct {
		Region             string `mapstructure:"region"`
		AccessKeyID        string `mapstructure:"access-key-id"`
		SecretAccessKey    string `mapstructure:"secret-access-key"`
		OnboardingTopicARN string `mapstructure:"onboarding-topic-arn"`
	} `mapstructure:"sns"`

	S3 struct {
		Bucket          string `mapstructure:"bucket"`
		Region          string `mapstructure:"region"`
		AccessKeyID     string `mapstructure:"access-key-id"`
		SecretAccessKey string `mapstructure:"secret-access-key"`
	} `mapstructure:"s3"`
}

func GetConfig() Config {
	return cfg
}

// 初始化配置
// 将配置文件的信息反序列化到结构体中
func ConfigInit(env string) error {
	pwd, _ := os.Getwd()
	cfgPath := filepath.Join(pwd, "configs")
	viper.AddConfigPath(cfgPath)
	viper.SetConfigName(env + "_config")
	// 读取配置信息
	if err := viper.ReadInConfig(); err != nil {
		return err
	}

	// 把读取到的信息反序列化到Conf变量中
	if err := viper.Unmarshal(&cfg); err != nil {
		return err
	}
	return nil
}
