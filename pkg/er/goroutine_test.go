package er

import (
	"context"
	"go.uber.org/zap/zapcore"
	"reflect"
	"testing"
)

func TestGo(t *testing.T) {
	type args struct {
		g      func()
		fields []zapcore.Field
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			Go(tt.args.g, tt.args.fields...)
		})
	}
}

func TestGoWithRestart(t *testing.T) {
	type args struct {
		ctx    context.Context
		g      func()
		fields []zapcore.Field
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			GoWithRestart(tt.args.ctx, tt.args.g, tt.args.fields...)
		})
	}
}

func TestRecoverWithStack(t *testing.T) {
	type args struct {
		msg    string
		fields []zapcore.Field
	}
	tests := []struct {
		name string
		args args
		want any
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RecoverWithStack(tt.args.msg, tt.args.fields...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RecoverWithStack() = %v, want %v", got, tt.want)
			}
		})
	}
}
