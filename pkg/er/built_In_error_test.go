package er

import (
	"fmt"
	"go.uber.org/zap/zapcore"
	"reflect"
	"testing"
)

func Test_BuiltInError_Level(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   zapcore.Level
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Level(); got != tt.want {
				t.Errorf("Level() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_Msg(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Msg(); got != tt.want {
				t.Errorf("Msg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_Code(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   uint32
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Code(); got != tt.want {
				t.Errorf("Code() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_ErrDesc(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Name(); got != tt.want {
				t.Errorf("Name() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_Error(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Error(); got != tt.want {
				t.Errorf("Error() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_Format(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	type args struct {
		s    fmt.State
		verb rune
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			e.Format(tt.args.s, tt.args.verb)
		})
	}
}

func Test_BuiltInError_Level1(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   zapcore.Level
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Level(); got != tt.want {
				t.Errorf("Level() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_Msg1(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.Msg(); got != tt.want {
				t.Errorf("Msg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_SendToClient(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.SendToClient(); got != tt.want {
				t.Errorf("SendToClient() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_String(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.String(); got != tt.want {
				t.Errorf("String() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_ToMap(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   map[string]interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.ToMap(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ToMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_WSEF(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	type args struct {
		oriErr error
		fields []zapcore.Field
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.WSEF(tt.args.oriErr, tt.args.fields...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WSEF() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_WSF(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	type args struct {
		fields []zapcore.Field
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.WSF(tt.args.fields...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WSF() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_WithErr(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	type args struct {
		err error
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.WithErr(tt.args.err); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WithErr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_WithMsg(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	type args struct {
		msg string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.WithMsg(tt.args.msg); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WithMsg() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_WithMsgf(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	type args struct {
		format string
		a      []any
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.WithMsgf(tt.args.format, tt.args.a...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WithMsgf() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_BuiltInError_new(t *testing.T) {
	type fields struct {
		code         uint32
		desc         string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name   string
		fields fields
		want   *BuiltInError
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &BuiltInError{
				code:         tt.fields.code,
				name:         tt.fields.desc,
				msg:          tt.fields.msg,
				level:        tt.fields.level,
				sendToClient: tt.fields.sendToClient,
			}
			if got := e.new(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("new() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_newErr(t *testing.T) {
	type args struct {
		code         uint32
		errDesc      string
		msg          string
		level        zapcore.Level
		sendToClient bool
	}
	tests := []struct {
		name string
		args args
		want *BuiltInError
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := newErr(tt.args.code, tt.args.errDesc, tt.args.msg, tt.args.level, tt.args.sendToClient); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("newErr() = %v, want %v", got, tt.want)
			}
		})
	}
}
