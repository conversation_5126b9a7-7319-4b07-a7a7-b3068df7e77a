package er

import (
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"reflect"
	"testing"
)

func TestAWSEF(t *testing.T) {
	type args struct {
		err    error
		fields []zapcore.Field
	}
	tests := []struct {
		name string
		args args
		want ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AWSEF(tt.args.err, tt.args.fields...); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON>("AWSEF() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToAddFields(t *testing.T) {
	type args struct {
		err    error
		fields []zapcore.Field
	}
	tests := []struct {
		name string
		args args
		want []zap.Field
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToAddFields(tt.args.err, tt.args.fields...); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("ToAddFields() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToJson(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToJson(tt.args.err); got != tt.want {
				t.Errorf("ToJson() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToMap(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToMap(tt.args.err); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ToMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToZapFields(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want []zap.Field
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToZapFields(tt.args.err); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ToZapFields() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWSEF(t *testing.T) {
	type args struct {
		oriErr error
		fields []zapcore.Field
	}
	tests := []struct {
		name string
		args args
		want ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := WSEF(tt.args.oriErr, tt.args.fields...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WSEF() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWithStack(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want ShellErrorInterface
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := WithStack(tt.args.err); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WithStack() = %v, want %v", got, tt.want)
			}
		})
	}
}
