package er

import (
	"errors"
	"fmt"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"strings"
	"time"
)

func ValidatorErrorTest(fieldError validator.FieldError) {
	fmt.Println(fieldError.Tag())
	fmt.Println(fieldError.ActualTag())
	fmt.Println(fieldError.Namespace())
	fmt.Println(fieldError.StructNamespace())
	fmt.Println(fieldError.Field())
	fmt.Println(fieldError.StructField())
	fmt.Println(fieldError.Value())
	fmt.Println(fieldError.Param())
	fmt.Println(fieldError.Kind())
	fmt.Println(fieldError.Type())
	fmt.Println(fieldError.Error())
}

func ValidatorErrorHandler(err error) error {
	if err == nil {
		return nil
	}

	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		tErr := new(time.ParseError)
		if errors.As(err, &tErr) {
			return InvalidArgument.WSEF(validationErrors, zap.Error(tErr)).WithMsg("InvalidArgument: The time format is incorrect.")
		}
		return InvalidArgument.WSEF(validationErrors, zap.Error(err)).WithMsg("The parameter is incorrect. Please check whether the information you entered is correct.")
	}

	var errTemp string
	errMsg := strings.Builder{}
	delimiter := ""
	for _, fieldError := range validationErrors {
		errMsg.WriteString(delimiter)
		delimiter = "\n"
		errMsg.WriteString(errTemp)
		switch fieldError.Tag() {
		case "oneof":
			errMsg.WriteString(fmt.Sprintf("%s must be one of: %s", fieldError.Field(), fieldError.Param()))
		case "required":
			errMsg.WriteString(fmt.Sprintf("%s is required", fieldError.Field()))
		case "alpha_num_underline":
			errMsg.WriteString(fmt.Sprintf("Please check the %s parameter.", fieldError.Field()))
		default:
			errMsg.WriteString(fieldError.Error())
		}

	}
	return InvalidArgument.WithStack().WithMsg(errMsg.String())
}
