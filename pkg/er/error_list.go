package er

// 声明内置异常,错误码用固定编码,避免代码更改时导致变动
var (
	// 系统状态
	Ok                  = newErr(0, "Ok", "ok", Error, true)                                 // 没有错误
	Cancelled           = newErr(1, "Cancelled", "cancelled", Error, true)                   // 操作被调用者取消
	Unknown             = newErr(2, "Unknown", "unknown", Error, true)                       // 未知错误（服务端错误）
	InvalidArgument     = newErr(3, "InvalidArgument", "invalid_argument", Info, true)       // 无效的参数，参数错误
	DeadlineExceeded    = newErr(4, "DeadlineExceeded", "deadline_exceeded", Error, true)    // 超时
	NotFound            = newErr(5, "NotFound", "not_found", Error, true)                    // 未找到
	AlreadyExists       = newErr(6, "AlreadyExists", "already_exists", Error, true)          // 已存在
	PermissionDenied    = newErr(7, "PermissionDenied", "permission_denied", Error, true)    // 没有权限
	ResourceExhausted   = newErr(8, "ResourceExhausted", "resource_exhausted", Error, true)  // 资源已耗尽
	FailedPrecondition  = newErr(9, "FailedPrecondition", "failed_precondition", Info, true) // 操作被拒绝
	Aborted             = newErr(10, "Aborted", "aborted", Error, true)                      // 终止
	OutOfRange          = newErr(11, "OutOfRange", "out_of_range", Error, true)              // 超出范围
	Unimplemented       = newErr(12, "Unimplemented", "unimplemented", Error, true)          // 未实现或不支持的操作
	Internal            = newErr(13, "Internal", "internal", Error, true)                    // 内部错误，为严重错误保留
	Unavailable         = newErr(14, "Unavailable", "unavailable", Error, true)              // 不可用
	DataLoss            = newErr(15, "DataLoss", "data_loss", Error, true)                   // 数据丢失
	Unauthenticated     = newErr(16, "Unauthenticated", "unauthenticated", Error, true)      // 该请求没有用于该操作的有效身份验证凭据。（未登录）
	InvalidParameter    = newErr(17, "InvalidParameter", "invalid_parameter", Error, true)   // 无效的参数，参数错误
	RequestFileTooLarge = newErr(18, "RequestFileTooLarge", "request_file_too_large", Error, true)
	// 请求文件过大
	UnsupportedOption           = newErr(100, "UnsupportedOption", "unsupported option", Error, false)                    // 不支持的选项
	IncorrectValueConversionErr = newErr(101, "IncorrectValueConversionErr", "Incorrect value conversion.", Error, false) // 不正确的数值转换
	RowsAffectedErr             = newErr(201, "RowsAffectedErr", "rows affected not match", Error, false)                 // 数据库操作，rows 数量与预期不同
	FirstErr                    = newErr(202, "FirstErr", "first error", Error, false)
	FindErr                     = newErr(203, "FindErr", "find error", Error, false)
	DuplicateEntry              = newErr(204, "DuplicateKey", "duplicate key", Info, true) // 重复的 key
	DataTooLongForColumn        = newErr(205, "DataTooLong", "Data too long", Info, false) // 字段的数据过长
	DatabaseException           = newErr(206, "DatabaseException", "database exception", Error, true)
	// 业务错误
	AuthorizationError          = newErr(401, "AuthorizationError", "authorization error", Info, true)                      // 验证错误
	DuplicateKeys               = newErr(424, "DuplicateKeys", "duplicate keys", Info, true)                                // 重复的键
	CompileFail                 = newErr(425, "CompileFail", "compile fail", Info, true)                                    // 编译失败
	DefaultPkFieldAlreadyExists = newErr(426, "DefaultPkFieldAlreadyExists", "default_pk_field_already_exists", Info, true) // 默认主键已存在

	//CRA
	CraParameterNameRepeated     = newErr(1001, "The parameter name cannot be repeated", "The parameter name cannot be repeated.", Info, true)                                           // 默认主键已存在
	CraParameterValInactiveError = newErr(1002, "The reference list is bound and its statuscan't be changed", "The reference list is bound and its status can't be changed", Info, true) // ParameterVal 已被使用无法更改

	//黑名单白名单
	BlackWhiteFileInvalidError      = newErr(2000, "Data processing exception", "Data processing exception", Info, true)                                                                                                   // 数据处理异常
	BlackWhiteFileInvalidColumn     = newErr(2001, "Field does not match expectations", "Field does not match expectations.", Info, true)                                                                                  // 文件包含无效的列
	BlackWhiteFileUnsupportedFormat = newErr(2002, "Please upload Excel (.xlsx, xls) or CSV files", "Please upload Excel (.xlsx, xls) or CSV files.", Info, true)                                                          // 文件格式不支持
	BlackWhiteFileDuplicateRecords  = newErr(2003, "Duplicate records found in the file.Please recove duplicates or re-upload.", "Duplicate records found in the file.Please recove duplicates or re-upload.", Info, true) // 文件包含重复的记录
	BlackWhiteDuplicateRecords      = newErr(2004, "Current record already exists, please confirm and add again", "Current record already exists, please confirm and add again", Info, true)                               // 当前记录已存在，请确认后新增
	BlackWhiteInvalidColumn         = newErr(2005, "Missing required data in column", "Missing required data in column.", Info, true)                                                                                      // 缺少部分字段
	BlackWhiteItemInvalid           = newErr(2006, "Internal error in blacklist/whitelist details.", "Internal error in blacklist/whitelist details.", Info, true)                                                         // 黑白名单明细内部错误

	//审核中的名单无法进行编辑操作
	BlackWhiteListIsAuditing = newErr(2006, "The list is being audited and cannot be edited", "The list is being audited and cannot be edited", Info, true)

	//数据源
)
