package logger

import (
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
	"os"
	"time"
)

var log *zap.SugaredLogger

// 时间格式化
func customTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	layout := fmt.Sprintf("[%s]", "2006-01-02 15:04:05")
	type appendTimeEncoder interface {
		AppendTimeLayout(time.Time, string)
	}
	if enc, ok := enc.(appendTimeEncoder); ok {
		enc.AppendTimeLayout(t, layout)
		return
	}
	enc.AppendString(t.Format(layout))
}

// 输出格式化
func customCallerEncoder(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString("[" + caller.TrimmedPath() + "]")
}

func getEncoder() zapcore.Encoder {
	encoderConfig := zapcore.EncoderConfig{
		MessageKey:       "msg",
		LevelKey:         "level",
		TimeKey:          "time",
		NameKey:          "logger",
		CallerKey:        "caller",
		EncodeLevel:      zapcore.CapitalLevelEncoder, // CapitalColorLevelEncoder 颜色输出
		EncodeTime:       customTimeEncoder,
		EncodeDuration:   zapcore.SecondsDurationEncoder,
		EncodeCaller:     customCallerEncoder,
		EncodeName:       zapcore.FullNameEncoder,
		ConsoleSeparator: " ",
		LineEnding:       "\r\n\r\n",
	}
	return zapcore.NewConsoleEncoder(encoderConfig)
}

func NewDevLogger(conf *config.Config) (*zap.Logger, error) {
	// 采用默认zap提供的日志打印设置
	encoderConfig := zap.NewProductionEncoderConfig()
	// 设置日志记录中时间的格式
	encoderConfig.EncodeTime = func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
		enc.AppendString(t.Format("2006-01-02 15:04:05"))
	}
	// 设置
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 将日志等级标识设置为大写并且有颜色
	encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	// 生成打印到console的encoder
	consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)

	core := zapcore.NewTee(
		zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapcore.DebugLevel),
	)
	// 返回调用栈
	return zap.New(core, zap.AddCaller()), nil
}

func NewLogger(conf config.Config) (*zap.Logger, error) {
	lc := conf.Logs
	level := zap.DebugLevel
	err := level.UnmarshalText([]byte(lc.Level))
	if err != nil {
		return nil, err
	}

	encoder := getEncoder()
	core := zapcore.NewCore(encoder, getLogWriter(lc), level)
	zapLogger := zap.New(core, zap.AddCaller())
	zapLogger = zapLogger.WithOptions(zap.AddCallerSkip(lc.Skip))
	return zapLogger, nil
}

func getLogWriter(lc config.Log) zapcore.WriteSyncer {
	// 文件写入,log文件的配置
	lumberJackLogger := &lumberjack.Logger{
		Filename:   lc.Path,       // 日志文件位置
		MaxSize:    lc.MaxSize,    // 进行切割之前，日志文件最大值(单位MB)
		MaxBackups: lc.MaxBackups, // 保留旧文件的最大个数
		MaxAge:     lc.MaxAge,     // 最大时间，默认单位 day
		LocalTime:  lc.LocalTime,  // 使用本地时间
		Compress:   lc.Compress,   // 是否压缩
	}
	// 按天分割日志
	if lc.Daily {
		go func() {
			for {
				nowTime := time.Now()
				nowTimeStr := nowTime.Format("2006-01-02")
				//使用Parse 默认获取为UTC时区 需要获取本地时区 所以使用ParseInLocation
				t2, _ := time.ParseInLocation("2006-01-02", nowTimeStr, time.Local)
				// 第二天零点时间戳
				next := t2.AddDate(0, 0, 1)
				after := next.UnixNano() - nowTime.UnixNano() - 1
				<-time.After(time.Duration(after) * time.Nanosecond)
				lumberJackLogger.Rotate()
			}
		}()
	}
	fileSyncer := zapcore.AddSync(lumberJackLogger)
	// 不在终端打印
	if lc.DisableLogConsole {
		return zapcore.NewMultiWriteSyncer(fileSyncer)
	}
	// 控制台输出
	consoleSyncer := zapcore.AddSync(os.Stdout)
	return zapcore.NewMultiWriteSyncer(consoleSyncer, fileSyncer)
}

func Info(msg string, keysAndValues ...interface{}) {
	log.Infow(msg, keysAndValues...)
}

func Error(msg string, err error, keysAndValues ...interface{}) {
	log.Errorw(msg, append([]interface{}{"error", err}, keysAndValues...)...)
}
