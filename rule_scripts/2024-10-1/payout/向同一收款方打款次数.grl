rule "BP002_Payout_to_same_recipient" "向同一收款方打款次数" salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])
	payee_id = toString(Event["payee_id"])
	resultCode = "000"
	resultMsg = "pass"

	// 获取打款次数
	times = Resource.GResourceService.HGet("lambda_payout:transaction:payee:" + account_id, payee_id)

	if toString(times) != ""{
		// 获取规则参数
		groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
		// number 向同一收款方打款次数
		payment_to_same_recipient_count = Resource.GParamService.Get(groupCode, "payment_to_same_recipient_count")

		if stringUtils.ParseInt(toString(times), 10, 64) +1 > payment_to_same_recipient_count.GetIntegerValue(){
			resultCode = "002"
			resultMsg = "向同一收款方打款超过设定次数 [" + toString(payment_to_same_recipient_count.GetIntegerValue()) + "]"
		}
	} 

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    if resultCode == "000" && ShortCircuit[result.RuleNo] {
        result.ShortCircuit = true
        Stag.StopTag = true
    } else if resultCode == "001" || resultCode == "002" {
        Result.AddRuleNotPassed(@name, resultCode)
        if !Result.ShortCircuit {
            Stag.StopTag = true
        }
    }

    Result.Add(result)
end