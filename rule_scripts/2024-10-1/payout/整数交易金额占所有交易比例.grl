rule "BP002_Payout_integer_tran_ratio" "整数交易金额占所有交易比例" salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])

	resultCode = "000"
	resultMsg = "pass"

	// 获取交易数据
	transactionData = Resource.GResourceService.HGet("lambda_payout:transaction", account_id)

	if toString(transactionData) != ""{
		transactionMap = toMap(transactionData)

		// 整数交易次数
		integer_counts_month = stringUtils.ParseFloat(toString(transactionMap["IntegerCountsMonth"]), 64)
		// 当月交易次数
		account_counts_month = stringUtils.ParseFloat(toString(transactionMap["AccountCountsMonth"]), 64)

		// 获取规则参数
		groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
		// number 整数交易金额占所有交易比例
		integer_transaction_ratio = Resource.GParamService.Get(groupCode, "integer_transaction_ratio")

		if account_counts_month > 0{
			if (integer_counts_month * 100 / account_counts_month) > integer_transaction_ratio.GetIntegerValue(){
				resultCode = "002"
				resultMsg = "整数交易金额比例过高"
			}
		}
	} else {
		resultCode = "002"
		resultMsg = "BP002_Payout_integer_tran_ratio: 无交易统计数据"
	}



    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    if resultCode == "000" && ShortCircuit[result.RuleNo] {
        result.ShortCircuit = true
        Stag.StopTag = true
    } else if resultCode == "001" || resultCode == "002" {
        Result.AddRuleNotPassed(@name, resultCode)
        if !Result.ShortCircuit {
            Stag.StopTag = true
        }
    }

    Result.Add(result)
end