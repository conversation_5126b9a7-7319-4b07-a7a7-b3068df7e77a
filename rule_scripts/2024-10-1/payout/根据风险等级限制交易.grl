rule "BP002_Payout_transaction_limit" "根据风险等级限制交易" salience  1000
begin
    account_id = toString(Event["account_id"])
    checkPoint = toString(Event["checkPoint"])
    risk_level = toString(Event["risk_level"])
    amount = stringUtils.ParseDecimal(toString(Event["amount"]))

    resultCode = "000"
    resultMsg = "pass"

    //获取整个交易数据
    transactionData = Resource.GResourceService.HGet("lambda_payout:transaction", account_id)
    if toString(transactionData) != ""{
        transactionMap = toMap(transactionData)
        // 当日累计交易额
        AmountTotalDayUSD = stringUtils.ParseDecimal(toString(transactionMap["AmountTotalDayUSD"]))
        // 当日交易成功笔数
        AccountCountsDay = stringUtils.ParseDecimal(toString(transactionMap["AccountCountsDay"]))

        // 获取规则参数
        // 注意配置参数组绑定
        groupCode = Resource.GParamService.GetGroupCode(checkPoint, risk_level, "RISK_LEVEL")

        // map: transaction_limit 交易限额
        // single_amt 单笔交易额
        // total_daily_amt 当日累计交易额
        // daily_succ_trans 当日交易成功笔数
        param = Resource.GParamService.Get(groupCode, "transaction_limit")
        single_amt = stringUtils.ParseDecimal(param.Get("single_amt"))
        total_daily_amt = stringUtils.ParseDecimal(param.Get("total_daily_amt"))
        daily_succ_trans = stringUtils.ParseDecimal(param.Get("daily_succ_trans"))

        if amount.GreaterThanOrEqual(single_amt){
            resultCode = "002"
            resultMsg = "单笔交易额受限"
        }

        if resultCode == "000"{
            AmountTotalDayUSD = AmountTotalDayUSD.Add(amount)
            if AmountTotalDayUSD.GreaterThanOrEqual(total_daily_amt){
                resultCode = "002"
                resultMsg = "当日累计交易额受限"
            }
        }

        if resultCode == "000"{
            if AccountCountsDay.GreaterThanOrEqual(daily_succ_trans){
                resultCode = "002"
                resultMsg = "当日交易成功笔数受限"
            }
        }
    } else {
		resultCode = "002"
		resultMsg = "BP002_Payout_transaction_limit 无交易统计数据"
    }

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    if resultCode == "000" && ShortCircuit[result.RuleNo] {
        result.ShortCircuit = true
        Stag.StopTag = true
    } else if resultCode == "001" || resultCode == "002" {
        Result.AddRuleNotPassed(@name, resultCode)
        if !Result.ShortCircuit {
            Stag.StopTag = true
        }
    }

    Result.Add(result)
end