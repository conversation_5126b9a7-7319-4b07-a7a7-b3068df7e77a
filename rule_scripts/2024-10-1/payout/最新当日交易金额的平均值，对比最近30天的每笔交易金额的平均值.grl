rule "BP002_Payout_compare_avg_daily" "最新当日交易金额的平均值，对比最近30天的每笔交易金额的平均值 " salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])
	amount = stringUtils.ParseFloat(toString(Event["amount"]), 64)

	resultCode = "000"
	resultMsg = "pass"

	// 获取交易数据
	transactionData = Resource.GResourceService.HGet("lambda_payout:transaction", account_id)
	if toString(transactionData) != ""{
		transactionMap = toMap(transactionData)

		// 每日交易
		AmountTotalDayUSD = stringUtils.ParseFloat(toString(transactionMap["AmountTotalDayUSD"]), 64)
		AmountTotalDayUSD += amount
		AccountCountsDay = stringUtils.ParseFloat(toString(transactionMap["AccountCountsDay"]), 64)
		AccountCountsDay += 1
		// 每月交易
		AmountTotalMonthUSD = stringUtils.ParseFloat(toString(transactionMap["AmountTotalMonthUSD"]), 64)
		AccountCountsMonth = stringUtils.ParseFloat(toString(transactionMap["AccountCountsMonth"]), 64)

		// 占月均交易比例
		groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
		monthly_average_transaction_percentage = Resource.GParamService.Get(groupCode, "monthly_average_transaction_percentage")

		if AccountCountsDay != 0 && AccountCountsMonth != 0{
			daily_average = AmountTotalDayUSD / AccountCountsDay
			monthly_daily_average = AmountTotalMonthUSD / AccountCountsMonth
			if monthly_daily_average != 0{
				if (daily_average * 100 / monthly_daily_average) > monthly_average_transaction_percentage.GetIntegerValue(){
					resultCode = "002"
					resultMsg = "最新当日交易金额的平均值大于最近30天的日均交易金额的百分之" + " [" + toString(monthly_average_transaction_percentage.GetIntegerValue()) + "]"
				}
			}
		}
	} else {
		resultCode = "002"
		resultMsg = "BP002_Payout_compare_avg_daily: 无交易统计数据"
	}

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    if resultCode == "000" && ShortCircuit[result.RuleNo] {
        result.ShortCircuit = true
        Stag.StopTag = true
    } else if resultCode == "001" || resultCode == "002" {
        Result.AddRuleNotPassed(@name, resultCode)
        if !Result.ShortCircuit {
            Stag.StopTag = true
        }
    }

    Result.Add(result)
end