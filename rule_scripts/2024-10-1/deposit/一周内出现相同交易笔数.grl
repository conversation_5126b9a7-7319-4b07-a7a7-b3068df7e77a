rule "BP001_Deposit_same_amount" "一周内出现相同交易笔数" salience  1000
begin
    account_id = toString(Event["account_id"])
    checkPoint = toString(Event["checkPoint"])
    amount = toString(Event["amount"])

    resultCode = "000"
    resultMsg = "pass"

    // 获取相同交易金额笔数
    times = Resource.GResourceService.HGet("lambda_deposit:transaction:same_amount:" + account_id, amount)
    if toString(times) != ""{
        // 获取规则参数
        groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
        // number 相同交易金额笔数
        same_amount_transactions = Resource.GParamService.Get(groupCode, "same_amount_transactions")

        if stringUtils.ParseInt(toString(times), 10, 64) > same_amount_transactions.GetIntegerValue(){
            resultCode = "002"
            resultMsg = "相同交易金额笔数超过设定次数"
        }
    } 

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    if resultCode == "000" && ShortCircuit[result.RuleNo] {
        result.ShortCircuit = true
        Stag.StopTag = true
    } else if resultCode == "001" || resultCode == "002" {
        Result.AddRuleNotPassed(@name, resultCode)
        if !Result.ShortCircuit {
            Stag.StopTag = true
        }
    }

    Result.Add(result)
end