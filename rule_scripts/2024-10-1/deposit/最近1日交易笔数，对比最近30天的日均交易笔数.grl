rule "BP001_Deposit_exceeds_avg_daily" "最近1日交易笔数，对比最近30天的日均交易笔数" salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])

	resultCode = "000"
	resultMsg = "pass"

	// 获取交易数据
	transactionData = Resource.GResourceService.HGet("lambda_deposit:transaction", account_id)
	if toString(transactionData) != ""{
		transactionMap = toMap(transactionData)

		// 每日交易
		AccountCountsDay = stringUtils.ParseFloat(toString(transactionMap["AccountCountsDay"]), 64)
		AccountCountsDay += 1
		// 每月交易
		AccountCountsMonth = stringUtils.ParseFloat(toString(transactionMap["AccountCountsMonth"]), 64)

		// 占月均交易比例
		groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
		// number 最近1日交易笔数对最近30天的日均交易笔数的占比
		percentage_daily_transactions_over_avg_last_30_days = Resource.GParamService.Get(groupCode, "percentage_daily_transactions_over_avg_last_30_days")

		if (AccountCountsDay * 100 * 30 / AccountCountsMonth ) > percentage_daily_transactions_over_avg_last_30_days.GetIntegerValue(){
			resultCode = "002"
			resultMsg = "日交易笔数超过最近30天的日均交易笔数的百分之" + " [" + toString(percentage_daily_transactions_over_avg_last_30_days.GetIntegerValue()) + "]"
		}
	} else {
		resultCode = "002"
		resultMsg = "BP001_Deposit_exceeds_avg_daily: 无交易统计数据"
	}

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    if resultCode == "000" && ShortCircuit[result.RuleNo] {
        result.ShortCircuit = true
        Stag.StopTag = true
    } else if resultCode == "001" || resultCode == "002" {
        Result.AddRuleNotPassed(@name, resultCode)
        if !Result.ShortCircuit {
            Stag.StopTag = true
        }
    }

    Result.Add(result)
end