rule "BP002_Payout_non_identical_limit" "非同名付款" salience  1000
begin

	//月限额
    account_name = toString(Event["account_name"])
	//年限额
	payee_name = toString(Event["payee_name"])

	//交易金额
	amount = stringUtils.ParseDecimal(toString(Event["amount"]))

	//非同名限额
	non_identical_limit=20000
	
    resultCode = "000"
    resultMsg = "pass"


    if (amount.IntPart() >= non_identical_limit ) && ( account_name != payee_name ){
          resultCode = "002"
          resultMsg = "Non-same name transfer amount limit exceeds"
    }
       

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc

    Result.Add(result)
end