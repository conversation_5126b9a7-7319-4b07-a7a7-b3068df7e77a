rule "BP002_Payout_account_limit" "根据用户配置限制交易" salience  1000
begin
    account_id = toString(Event["account_id"])
	//月限额
	m_limit=toString(Event["monthly_limit"])
	//年限额
	a_limit = toString(Event["annual_limit"])
	//总限额
    t_limit = toString(Event["total_limit"])

	amount = Event["amount"]

    resultCode = "000"
    resultMsg = ""

	//月交易数据
	if(m_limit != ""){
	  monthly_limit = stringUtils.ParseDecimal(m_limit)
	  monthly_key="lambda_payout:transaction:"+timeUtils.GetToDayString()
      monthly_transaction = Resource.GRedisService.HGet(monthly_key, account_id)
	  monthly_transaction_map = stringUtils.ToMap(monthly_transaction)
	  monthly_amount= decimalUtils.Add(amount,monthly_transaction_map["AmountTotalMonthUSD"])
      if monthly_amount.GreaterThanOrEqual(monthly_limit){
          resultCode = "002"
          resultMsg = "Monthly accumulated transaction amount limit exceeds."
      }
	}

	//年交易数据
	if(a_limit != ""){
        annual_limit = stringUtils.ParseDecimal(a_limit)
        annual_key="lambda_payout:transaction:"+timeUtils.GetYearString()
        annual_transaction = Resource.GRedisService.HGet(annual_key, account_id)
        annual_amount = decimalUtils.Add(amount,annual_transaction)
        if annual_amount.GreaterThanOrEqual(annual_limit){
              resultCode = "002"
              resultMsg = resultMsg+"Annual accumulated transaction amount limit exceeds."
        }
    }


	//总交易数据
	if(t_limit != ""){
        total_limit = stringUtils.ParseDecimal(t_limit)
        total_key="lambda_payout:transaction:total"
        total_transaction = Resource.GRedisService.HGet(total_key, account_id)
        total_annual = decimalUtils.Add(amount,annual_transaction)
        if total_annual.GreaterThanOrEqual(total_limit){
              resultCode = "002"
              resultMsg = resultMsg+"Accumulated transaction amount limit exceeds"
        }
	}
	if len(resultMsg) == 0  {
		resultMsg = "pass"
	}

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc

    Result.Add(result)
end