rule "BP002_Payout_to_same_recipient" "向同一收款方打款次数" salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])
	payee_id = toString(Event["payee_id"])
	resultCode = "000"
	resultMsg = "pass"

	// 获取打款次数
	counts = Resource.GRedisService.HGet("lambda_payout:transaction:payee:" + account_id, payee_id)

	
	// 获取规则参数
	groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
	// number 向同一收款方打款次数
	count_limit = Resource.GParamService.Get(groupCode, "payment_to_same_recipient_count")
	num1=decimalUtils.Add(counts,1)
	if num1.IntPart() >= count_limit.GetIntegerValue(){
		resultCode = "002"
		resultMsg = "Same Recipient in 1 week limit exceeds"
	}
    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc

    Result.Add(result)
end