rule "BP002_Payout_amount_avg_daily" "最新当日交易金额，对比最近30天的日均交易金额" salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])
	amount = Event["amount"]
    resultCode = "000"
    resultMsg = "pass"
	
    create_time = toString(Event["create_time"])
    //判断是否30天内
    if !timeUtils.IsWithinThirtyDay(create_time){

        redisKey="lambda_payout:transaction:"+timeUtils.GetToDayString()

        // 获取交易数据
        transactionData = Resource.GRedisService.HGet(redisKey, account_id)
        if toString(transactionData) != ""{
            transactionMap = stringUtils.ToMap(transactionData)

            // 最近1日交易金额
            amount_day = decimalUtils.Add(transactionMap["AmountTotalDayUSD"],amount)
            // 30天的金额
            amount_thirty_day = decimalUtils.Add(transactionMap["AmountTotalThirtyDay"],amount)
            // 30天内交易天数
            days_thirty_day = decimalUtils.Add(Resource.GRedisService.GetBitmap(30, account_id),1)
            // 占月均交易比例
            groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
            // number 最近1日交易笔数对最近30天的日均交易笔数的占比
            daily_transactions_limit = Resource.GParamService.Get(groupCode, "percentage_daily_transactions_over_avg_last_30_days")

            //30天日均交易额
            amount_thirty_avg=amount_thirty_day.Div(days_thirty_day)

            printf("30天日均交易额： %v -----------",amount_thirty_day)
            printf("30天内交易天数： %v -----------",days_thirty_day)

            printf("最近1日交易金额： %v -----------",amount_day.IntPart())
            printf("30天日均交易额： %v -----------",amount_thirty_avg)
            printf("交易限额： %v -----------",daily_transactions_limit.GetValue())
            num1 = amount_day.Div(amount_thirty_avg)
            num2 = decimalUtils.Mul(num1.String(),"100")
            num3 = stringUtils.ToDecimal(daily_transactions_limit.GetValue())
            printf("num1： %v -----------",num1)
            printf("num2： %v -----------",num2)
            printf("num3： %v -----------",num3)

            if num2.GreaterThanOrEqual(num3) {
                resultCode = "002"
                resultMsg = "Daliy accumulated transaction amount increase limit exceeds. " + " [" + amount_thirty_avg.String() + "]"
            }
        } else {
				resultCode = "002"
				resultMsg = "BP002_Payout_exceeds_avg_daily:  No transaction statistics risk control trigger"
		}

    }
    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc


    Result.Add(result)
end