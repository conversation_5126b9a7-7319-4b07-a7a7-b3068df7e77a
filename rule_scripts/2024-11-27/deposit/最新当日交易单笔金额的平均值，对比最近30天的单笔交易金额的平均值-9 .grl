rule "BP001_Deposit_compare_avg_daily" "最新当日交易金额的平均值，对比最近30天的每笔交易金额的平均值 " salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])
	amount = Event["amount"]
    create_time = toString(Event["create_time"])
    //判断是否30天内
    if !timeUtils.IsWithinThirtyDay(create_time){
        resultCode = "000"
        resultMsg = "pass"
        redisKey="lambda_deposit:transaction:"+timeUtils.GetToDayString()

        // 获取交易数据
        transactionData = Resource.GRedisService.HGet(redisKey, account_id)

        if toString(transactionData) != ""{
            transactionMap = stringUtils.ToMap(transactionData)
            // 当日交易金额

            AmountTotalDayUSD = decimalUtils.Add(amount,transactionMap["AmountTotalDayUSD"])
            CountsDay = decimalUtils.Add(1,transactionMap["CountsDay"])
            // 最近30天的交易金额
            AmountTotalThirtyDay =decimalUtils.Add(transactionMap["AmountTotalThirtyDay"],amount)
            // 最近30天的次数
            CountsThirtyDay =decimalUtils.Add(transactionMap["CountsThirtyDay"],1)
            // 占月均交易比例
            groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
            month_avg_percent = Resource.GParamService.Get(groupCode, "monthly_average_transaction_percentage")
            daily_average = decimalUtils.Div(AmountTotalDayUSD,CountsDay)
            month_average = decimalUtils.Div(AmountTotalThirtyDay,CountsThirtyDay)
            num1=daily_average.Div(month_average)
            num2=decimalUtils.Mul(month_avg_percent.GetValue(),"0.01")

        if num1.GreaterThan(num2){
                    resultCode = "002"
                    resultMsg = "Daily average single transaction amount increase limit exceeds.["+month_average.String()+"]"
            }

        } else {
            resultCode = "002"
            resultMsg = "BP002_Payout_compare_avg_daily:  No transaction statistics risk control trigger"
        }
    }
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    Result.Add(result)
end