rule "BP001_Deposit_integer_tran_ratio" "整数交易金额占所有交易比例" salience  1000
begin
	account_id = toString(Event["account_id"])
	checkPoint = toString(Event["checkPoint"])
	source_amount = toString(Event["source_amount"])
	create_time = toString(Event["create_time"])
    //判断是否30天内
    if !timeUtils.IsWithinThirtyDay(create_time){
        resultCode = "000"
        resultMsg = "pass"

        redisKey="lambda_deposit:transaction:"+timeUtils.GetToDayString()
        // 获取交易数据
        transactionData = Resource.GRedisService.HGet(redisKey, account_id)

        if toString(transactionData) != ""{
            transactionMap = toMap(transactionData)

            // 30天内整数交易次数
            integer_counts_month = transactionMap["IntegerCountsThirtyDay"]
            // 30天内交易次数
            counts_thirty_day = transactionMap["CountsThirtyDay"]
            printf("transactionData %v \n",transactionData)


            // 获取规则参数
            groupCode = Resource.GParamService.GetDefaultGroupCode(checkPoint)
            // number 整数交易金额占所有交易比例
            integer_transaction_ratio = Resource.GParamService.Get(groupCode, "integer_transaction_ratio")
            isInteger = decimalUtils.IsIntegerOfThousands(source_amount)
            if(isInteger){
				integer_counts_month=decimalUtils.Add(integer_counts_month,1)
            }
			counts_thirty_day=decimalUtils.Add(counts_thirty_day,1)
			num1=decimalUtils.Div(integer_counts_month,counts_thirty_day)
			num2=decimalUtils.Mul(integer_transaction_ratio.GetValue(),"0.01")
            if num1.GreaterThanOrEqual(num2) {
                    resultCode = "002"
                    resultMsg = "integer_tran_ratio: Round amount transaction ratio in last 30 days limit exceeds"
            }

        } else {
            resultCode = "002"
            resultMsg = "BP002_Payout_integer_tran_ratio: No transaction statistics risk control trigger."
        }
    }
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc
    Result.Add(result)
end