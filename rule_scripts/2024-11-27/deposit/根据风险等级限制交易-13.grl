rule "BP001_Deposit_transaction_limit" "根据风险等级限制交易" salience  1000
begin
    account_id = toString(Event["account_id"])
    checkPoint = toString(Event["checkPoint"])
    risk_level = toString(Event["risk_level"])
    amount = Event["amount"]

    resultCode = "000"
    resultMsg = "pass"
	redisKey="lambda_deposit:transaction:"+timeUtils.GetToDayString()

    //获取整个交易数据
    transactionData = Resource.GRedisService.HGet(redisKey, account_id)
    if toString(transactionData) != ""{
		transactionMap = stringUtils.ToMap(transactionData)
        // 当日累计交易额
		AmountTotalDayUSD = decimalUtils.Add(amount,transactionMap["AmountTotalDayUSD"])
        // 当日交易笔数
		CountsDay = decimalUtils.Add(1,transactionMap["CountsDay"])

        // 获取规则参数
        // 注意配置参数组绑定
        groupCode = Resource.GParamService.GetGroupCode(checkPoint, risk_level, "RISK_LEVEL")

        // map: transaction_limit 交易限额
       
        
        param = Resource.GParamService.Get(groupCode, "transaction_limit")
		 // single_amt 单笔交易限额
        single_amt = stringUtils.ParseDecimal(param.Get("single_amt"))
		printf("单笔交易限额： %v  ",single_amt)

		// total_daily_amt 当日累计交易额
        total_daily_amt = stringUtils.ParseDecimal(param.Get("total_daily_amt"))
		printf("当日累计交易额： %v ",total_daily_amt)

		// daily_succ_trans 当日交易成功笔数
        daily_succ_trans = stringUtils.ParseDecimal(param.Get("daily_succ_trans"))
		printf("当日交易成功笔数： %v ",daily_succ_trans)

        num=stringUtils.ParseDecimal(toString(amount))
        if num.GreaterThanOrEqual(single_amt){
            resultCode = "002"
            resultMsg = "Per transaction amount limit exceeds."
        }

        
        if AmountTotalDayUSD.GreaterThanOrEqual(total_daily_amt){
                resultCode = "002"
                resultMsg = resultMsg+"Daily accumulated transaction amount limit exceeds"
        }
        

        if CountsDay.GreaterThanOrEqual(daily_succ_trans){
                resultCode = "002"
                resultMsg = resultMsg+"Daily successful transaction count limit exceeds"
        }
        
    } else {
		resultCode = "002"
		resultMsg = "BP002_Payout_transaction_limit: No transaction statistics risk control trigger"
    }

    // 处理返回值和短路
    result = newVerifyResultWithMsg(resultCode, resultMsg)
    result.RuleNo = @name
    result.RuleName = @desc

    Result.Add(result)
end