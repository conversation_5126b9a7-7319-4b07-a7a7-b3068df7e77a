gitBranch=$(git rev-parse --abbrev-ref HEAD)
gitTag=$(shell git log --pretty=format:'%h' -n 1)
gitBranch=$(shell git rev-parse --abbrev-ref HEAD)
buildDate=$(shell TZ=Asia/Shanghai date +%FT%T%z)
gitCommit=$(shell git rev-parse --short HEAD)
gitStash=$(shell git diff --quiet || echo 'dirty')

ldflags="-s -w  -X gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/version.gitBranch=${gitBranch} -X 'gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/version.gitTag=${gitTag}' -X 'gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/version.gitCommit=${gitCommit}' -X 'gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/version.gitStash=${gitStash}' -X 'gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/version.buildDate=${buildDate}'"
pwd=$(shell pwd)
init_column:
	go run tools/gen_column/gen_model_columns.go
# 生成swagger
generate_swagger:
	swag init -g main.go --parseDependency --parseInternal && sg warp ./docs/swagger.json

# 构建mac arm64
build_mac_arm64:
	GOOS=darwin GOARCH=arm64 go build -ldflags $(ldflags) -o bin/uqpay-risk-portal-api_mac_arm64  ./

# 构建mac amd64
build_mac_amd64:
	GOOS=darwin GOARCH=amd64 go build -ldflags $(ldflags) -o bin/uqpay-risk-portal-api_mac_amd64  ./

# 构建linux amd64
build_linux_amd64:
	GOOS=linux GOARCH=amd64 go build -ldflags $(ldflags) -o bin/uqpay-risk-portal-api_linux_amd64  ./

# 构建linux arm64
build_linux_arm64:
	GOOS=linux GOARCH=arm64 go build -ldflags $(ldflags) -o bin/uqpay-risk-portal-api_linux_arm64  ./

