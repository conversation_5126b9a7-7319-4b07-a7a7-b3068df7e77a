CREATE TABLE `rms_data_source_table` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `data_source_id` bigint(20) NOT NULL COMMENT '数据源ID',
    `table_name` varchar(255) NOT NULL COMMENT '表名',
    `name` varchar(255) NOT NULL COMMENT '表别名',
    `desc` varchar(500) DEFAULT NULL COMMENT '介绍',
    `business_type` int(11) NOT NULL COMMENT '业务类型：1000=Banking，2000=Foreign，3000=Acquiring，4000=Issuing，5000=Ramp',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_data_source_id` (`data_source_id`),
    KEY `idx_business_type` (`business_type`),
    CONSTRAINT `fk_data_source_table_data_source_id` FOREIGN KEY (`data_source_id`) REFERENCES `rms_data_source` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据源表信息表'; 