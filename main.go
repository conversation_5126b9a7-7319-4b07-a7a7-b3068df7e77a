package main

import (
	"flag"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/version"
	"log"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/server/pro"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
)

// 启动服务
func main() {
	var err error
	env := flag.String("env", "loc", "Set the environment")

	flag.Parse()
	if version.PrintVersion {
		v := version.GetVersion()
		fmt.Printf("GitBranch: %s\nCommitId: %s\nGitStash: %s\nBuild Date: %s\nGo Version: %s\nOS/Arch: %s\n", v.GitBranch, v.GitCommit, v.GitStash, v.BuildDate, v.GoVersion, v.Platform)
		return
	}
	//初始化时区
	var cstZone, _ = time.LoadLocation("Asia/Shanghai") // 东八
	time.Local = cstZone

	//初始化配置文件
	if err = config.ConfigInit(*env); err != nil {
		fmt.Printf("configs init failed, error:%v\n", err)
		panic(err)
	}

	err = pro.Run()
	if err != nil {
		log.Panic(err)
	}
}
