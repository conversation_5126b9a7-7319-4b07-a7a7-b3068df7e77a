// Code generated by swaggo/swag. DO NOT EDIT.

package docs

import "github.com/swaggo/swag/v2"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},"swagger":"2.0","info":{"description":"{{escape .Description}}","title":"{{.Title}}","contact":{},"version":"{{.Version}}"},"host":"{{.Host}}","basePath":"{{.BasePath}}","paths":{"/access_point/all_name":{"post":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"AllName is list of all names of CheckPoint","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{"200":{"description":"OK","schema":{"type":"array","items":{"$ref":"#/definitions/domain.CheckPointNameLabel"}}}}}},"/access_point/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"Create CheckPoint","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.CheckPointCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.CheckPointResp"}}}}},"/access_point/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"17","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/access_point/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"List CheckPoint","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.CheckPointList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.CheckPointListResp"}}}}},"/access_point/retrieve/{code}":{"get":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"Retrieve CheckPoint","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"access_point code","name":"code","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.CheckPointResp"}}}}},"/access_point/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"Update CheckPoint","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.CheckPointUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.CheckPointResp"}}}}},"/access_point/update_business_config":{"post":{"security":[{"x-auth-token":[]}],"tags":["CheckPoint 接入点"],"summary":"UpdateBusinessConfig update CheckPoint.BusinessTypesLi","parameters":[{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.CheckPointBusinessConfigReq"}}],"responses":{"200":{"description":"OK","schema":{"type":"array","items":{"$ref":"#/definitions/domain.CheckPointNameLabel"}}}}}},"/alarm_contact/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["AlarmContact 风控预警联系人"],"summary":"Create AlarmContact","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.AlarmContactCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmContact"}}}}},"/alarm_contact/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["AlarmContact 风控预警联系人"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/alarm_contact/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["AlarmContact 风控预警联系人"],"summary":"List AlarmContact","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.AlarmContactList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmContact"}}}}},"/alarm_contact/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["AlarmContact 风控预警联系人"],"summary":"Retrieve AlarmContact","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmContact"}}}}},"/alarm_contact/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["AlarmContact 风控预警联系人"],"summary":"Update AlarmContact","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.AlarmContactUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmContact"}}}}},"/alarm_time_conf/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["AlarmTimeConf 风控预警时间配置"],"summary":"Create AlarmTimeConf","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.AlarmTimeConfCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmTimeConf"}}}}},"/alarm_time_conf/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["AlarmTimeConf 风控预警时间配置"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/alarm_time_conf/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["AlarmTimeConf 风控预警时间配置"],"summary":"List AlarmTimeConf","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.AlarmTimeConfList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmTimeConf"}}}}},"/alarm_time_conf/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["AlarmTimeConf 风控预警时间配置"],"summary":"Retrieve AlarmTimeConf","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmTimeConf"}}}}},"/alarm_time_conf/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["AlarmTimeConf 风控预警时间配置"],"summary":"Update AlarmTimeConf","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.AlarmTimeConfUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsAlarmTimeConf"}}}}},"/api/v1/rule_group/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["risk-portal-api/Policy 策略/风控规则策略（即规则组）"],"summary":"Create Policy 创建策略组","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v1/rule_group/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["risk-portal-api/Policy 策略/风控规则策略（即规则组）"],"summary":"Update Policy 更新策略组","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/access_point/all_name":{"post":{"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/CheckPoint 接入点"],"summary":"AllName CheckPoint 获取所有检查点名称和编码","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{"200":{"description":"名称列表","schema":{"type":"array","items":{"$ref":"#/definitions/response_parameters.CheckPointNameLabel"}}}}}},"/api/v2/access_point/create":{"post":{"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/CheckPoint 接入点"],"summary":"Create CheckPoint 创建检查点","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"请求体","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CheckPointCreate"}}],"responses":{"200":{"description":"创建成功","schema":{"$ref":"#/definitions/response_parameters.CheckPointResp"}}}}},"/api/v2/access_point/delete/{id}":{"get":{"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/CheckPoint 接入点"],"summary":"Delete CheckPoint 删除检查点","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"检查点ID","name":"id","in":"path","required":true}],"responses":{"200":{"description":"删除成功","schema":{"type":"object","additionalProperties":true}}}}},"/api/v2/access_point/list":{"post":{"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/CheckPoint 接入点"],"summary":"List CheckPoint 检查点列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"请求体","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CheckPointList"}}],"responses":{"200":{"description":"列表数据","schema":{"$ref":"#/definitions/response_parameters.CheckPointListResp"}}}}},"/api/v2/access_point/retrieve/{code}":{"get":{"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/CheckPoint 接入点"],"summary":"Retrieve CheckPoint 获取单个检查点","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"检查点编码","name":"code","in":"path","required":true}],"responses":{"200":{"description":"详情数据","schema":{"$ref":"#/definitions/response_parameters.CheckPointResp"}}}}},"/api/v2/access_point/update":{"post":{"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/CheckPoint 接入点"],"summary":"Update CheckPoint 更新检查点","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"请求体","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CheckPointUpdate"}}],"responses":{"200":{"description":"更新成功","schema":{"$ref":"#/definitions/response_parameters.CheckPointResp"}}}}},"/api/v2/black_white/audit":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Audit 审核管理"],"summary":"Audit 审核操作","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.BlackWhiteAudit"}}],"responses":{}}},"/api/v2/black_white/audit/list":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Audit 审核管理"],"summary":"List 获取审核列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"页码,type=3时不生效","name":"page_num","in":"query","required":true},{"type":"integer","description":"每页条数,type=3时不生效","name":"page_size","in":"query"},{"type":"integer","description":"类型 0:所有 1.未处理审核 2.已处理审核 3.待提醒","name":"type","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_BlackWhiteAuditList"}}}}},"/api/v2/black_white/audit/mark":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Audit 审核管理"],"summary":"Mark 标记审核提醒为已提醒","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{}}},"/api/v2/black_white/audit/{id}":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Audit 审核管理"],"summary":"Detail 获取审核详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{"200":{"description":"OK","schema":{"type":"array","items":{"$ref":"#/definitions/response_parameters.BlackWhiteAuditDetail"}}}}}},"/api/v2/black_white/fields":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Field 黑白名单字段管理"],"summary":"黑白名单字段列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"黑白名单字段列表","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.ListBlackWhiteField"}}],"responses":{"200":{"description":"黑白名单字段列表","schema":{"type":"array","items":{"type":"string"}}}}}},"/api/v2/black_white/flush":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Flush 黑白名数据刷新环境"],"summary":"risk-portal-api/BlackWhite 黑白名单/Flush 黑白名数据刷新环境","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{"200":{"description":"黑白名单进度","schema":{"type":"array","items":{"$ref":"#/definitions/entity.Progress"}}}}}},"/api/v2/black_white/info":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Get BlackWhiteList 获取黑白名单详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"黑白名单记录ID","name":"id","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.GetBlackWhiteRecord"}}}}},"/api/v2/black_white/item":{"put":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Update BlackWhiteListItem 更新黑白名单记录明细","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.UpdateBlackWhiteItem"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.CreateBlackWhiteItem"}}}},"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Create BlackWhiteListItem 创建黑白名单记录明细","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.AddBlackWhiteItem"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.CreateBlackWhiteItem"}}}}},"/api/v2/black_white/item/upload":{"post":{"security":[{"x-auth-token":[]}],"consumes":["multipart/form-data"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Upload BlackWhiteListItemFile 上传黑白名单明细文件","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"黑白名单ID","name":"bwl_id","in":"formData","required":true},{"type":"file","description":"Excel or CSV file","name":"file","in":"formData","required":true},{"type":"boolean","description":"是否覆盖重复项","name":"recover","in":"formData"}],"responses":{}}},"/api/v2/black_white/item/{bwi_id}":{"delete":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Delete BlackWhiteListItem 删除黑白名单记录明细","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"黑白名单记录明细ID","name":"bwi_id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.CreateBlackWhiteItem"}}}}},"/api/v2/black_white/items":{"get":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Get BlackWhiteItems 获取黑白名单明细列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"页码","name":"page_num","in":"query","required":true},{"type":"integer","description":"每页条数","name":"page_size","in":"query"},{"type":"integer","description":"黑白名单列表ID","name":"bwl_id","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_BlackWhiteItems"}}}}},"/api/v2/black_white/items/view":{"get":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Get BlackWhiteItemsView 获取黑白名单明细列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"页码","name":"page_num","in":"query","required":true},{"type":"integer","description":"每页条数","name":"page_size","in":"query"},{"type":"integer","description":"黑白名单列表ID","name":"bwl_id","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_BlackWhiteItems"}}}}},"/api/v2/black_white/list":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Get BlackWhiteList 获取黑白名单列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.GetBlackWhiteList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_BlackWhiteList"}}}}},"/api/v2/black_white/operator_logs":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/OperatorLog 操作日志"],"summary":"List 获取黑白名单操作日志列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"页码","name":"page_num","in":"query","required":true},{"type":"integer","description":"每页条数","name":"page_size","in":"query"},{"type":"integer","description":"黑白名单列表ID","name":"bwl_id","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_BlackWhiteOperatorLogs"}}}}},"/api/v2/black_white/print":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Print 黑白名单数据打印","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"黑白名单记录ID","name":"bwi_id","in":"query","required":true}],"responses":{"200":{"description":"黑白名单进度","schema":{"type":"array","items":{"$ref":"#/definitions/entity.Progress"}}}}}},"/api/v2/black_white/progress/{bwi_id}":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Progress 黑白名单进度","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"黑白名单记录ID","name":"bwi_id","in":"query","required":true}],"responses":{"200":{"description":"黑白名单进度","schema":{"type":"array","items":{"$ref":"#/definitions/entity.Progress"}}}}}},"/api/v2/black_white/record":{"put":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Update BlackWhiteList 更新黑白名单","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.UpdateBlackWhiteRecord"}}],"responses":{"200":{"description":"黑白名单成功","schema":{"$ref":"#/definitions/response_parameters.NormalCreateResponse"}}}},"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Create BlackWhiteList 创建黑白名单","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CreateBlackWhiteRecord"}}],"responses":{"200":{"description":"黑白名单保存成功","schema":{"$ref":"#/definitions/response_parameters.NormalCreateResponse"}}}},"delete":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"Remove BlackWhiteList 删除黑白名单","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"黑白名单记录ID","name":"id","in":"query","required":true}],"responses":{"200":{"description":"黑白名单删除成功","schema":{"type":"string"}}}}},"/api/v2/black_white/status":{"put":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"SetStatus 设置黑白名单状态","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.SetBlackWhiteStatus"}}],"responses":{"200":{"description":"黑白名单状态设置成功","schema":{"$ref":"#/definitions/response_parameters.NormalCreateResponse"}}}}},"/api/v2/black_white/submit":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理"],"summary":"SubmitAudit 提交审核","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.SubmitAudit"}}],"responses":{"200":{"description":"黑白名单提交审核成功"}}}},"/api/v2/black_white/template/{bwl_id}":{"get":{"security":[{"x-auth-token":[]}],"produces":["application/json"],"tags":["risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理"],"summary":"Download BlackWhiteListItemTemplate 下载黑白名单明细Excel模板","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"模板ID","name":"bwl_id","in":"path","required":true}],"responses":{}}},"/api/v2/config_progress/create_or_update":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/ConfigProgress 配置进度/ConfigProgress 配置进度管理"],"summary":"CreateOrUpdate ConfigProgress 创建或更新配置进度","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.ConfigProgressCreate"}}],"responses":{}}},"/api/v2/config_progress/retrieve/{code}":{"get":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/ConfigProgress 配置进度/ConfigProgress 配置进度管理"],"summary":"Retrieve ConfigProgress 获取配置进度详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"access_point code","name":"code","in":"path","required":true}],"responses":{"200":{"description":"配置进度详情","schema":{"$ref":"#/definitions/response_parameters.ConfigProgressResp"}}}}},"/api/v2/data_source":{"put":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Update DataSource 更新数据源","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"数据源更新信息","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.DataSourceUpdate"}}],"responses":{"200":{"description":"数据源更新成功","schema":{"allOf":[{"$ref":"#/definitions/domain.Base"},{"type":"object","properties":{"data":{"$ref":"#/definitions/response_parameters.DataSourceDetail"}}}]}}}},"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Create DataSource 创建数据源","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"数据源信息","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.DataSourceCreate"}}],"responses":{"200":{"description":"数据源创建成功","schema":{"allOf":[{"$ref":"#/definitions/domain.Base"},{"type":"object","properties":{"data":{"$ref":"#/definitions/response_parameters.DataSourceDetail"}}}]}}}}},"/api/v2/data_source/:id":{"get":{"security":[{"x-auth-token":[]}],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Get DataSource Detail 获取数据源详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"数据源ID","name":"id","in":"path","required":true}],"responses":{"200":{"description":"数据源详情","schema":{"allOf":[{"$ref":"#/definitions/domain.Base"},{"type":"object","properties":{"data":{"$ref":"#/definitions/response_parameters.DataSourceDetail"}}}]}}}},"delete":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Delete DataSource 删除数据源","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"数据源ID","name":"id","in":"path","required":true}],"responses":{"200":{"description":"数据源删除成功","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/data_source/list":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Get DataSource List 获取数据源列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"查询条件","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.DataSourceList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_DataSourceDetail"}}}}},"/api/v2/data_source/tables/:data_source_id":{"get":{"security":[{"x-auth-token":[]}],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Get DataSource Tables 获取数据源表列表","parameters":[{"type":"integer","description":"数据源ID","name":"data_source_id","in":"path","required":true}],"responses":{"200":{"description":"表列表","schema":{"type":"array","items":{"type":"string"}}}}}},"/api/v2/data_source/test_connection":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Config 数据源配置"],"summary":"Test DataSource Connection 测试数据源连接","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"数据源连接信息","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.DataSourceTest"}}],"responses":{"200":{"description":"连接测试结果","schema":{"allOf":[{"$ref":"#/definitions/domain.Base"},{"type":"object","properties":{"data":{"$ref":"#/definitions/response_parameters.DataSourceTestResp"}}}]}}}}},"/api/v2/data_source_table":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Table 表信息管理"],"summary":"Create DataSourceTable 创建数据源表信息","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CreateDataSourceTable"}}],"responses":{"200":{"description":"获取成功","schema":{"$ref":"#/definitions/response_parameters.NormalCreateResponse"}}}}},"/api/v2/data_source_table/columns":{"get":{"security":[{"x-auth-token":[]}],"tags":["risk-portal-api/DataSource 数据源管理/Table 表信息管理"],"summary":"Get DataSource Columns 获取数据源表字段列表","parameters":[{"type":"string","description":"表ID","name":"table_id","in":"query","required":true},{"type":"string","description":"字段类型 DateTime：时间类型","name":"column_type","in":"query","required":true}],"responses":{"200":{"description":"字段列表","schema":{"type":"array","items":{"$ref":"#/definitions/entity.DataSourceColumn"}}}}}},"/api/v2/data_source_table/list":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Table 表信息管理"],"summary":"Get DataSourceTable List 获取数据源表信息列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.GetDataSourceTableList"}}],"responses":{"200":{"description":"获取成功","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_DataSourceTableList"}}}}},"/api/v2/data_source_table/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["risk-portal-api/DataSource 数据源管理/Table 表信息管理"],"summary":"Get DataSourceTable Detail 获取数据源表信息详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"数据源表信息ID","name":"id","in":"path","required":true}],"responses":{"200":{"description":"获取成功","schema":{"$ref":"#/definitions/response_parameters.DataSourceTableDetail"}}}},"delete":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/DataSource 数据源管理/Table 表信息管理"],"summary":"Delete DataSourceTable 删除数据源表信息","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"数据源表信息ID","name":"id","in":"path","required":true}],"responses":{}}},"/api/v2/filter_field/create":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理"],"summary":"Create FilterField 创建过滤字段","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.FilterFieldCreate"}}],"responses":{"200":{"description":"过滤字段创建成功","schema":{"$ref":"#/definitions/response_parameters.FilterFieldResp"}}}}},"/api/v2/filter_field/list":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理"],"summary":"Get FilterField List 获取过滤字段列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.FilterFieldList"}}],"responses":{"200":{"description":"过滤字段列表","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_FilterFieldResp"}}}}},"/api/v2/filter_field/retrieve":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理"],"summary":"Retrieve FilterField 获取过滤字段详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.FilterFieldRetrieve"}}],"responses":{"200":{"description":"过滤字段详情","schema":{"$ref":"#/definitions/response_parameters.FilterFieldResp"}}}}},"/api/v2/filter_field/update":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理"],"summary":"Update FilterField 更新过滤字段","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.FilterFieldUpdate"}}],"responses":{"200":{"description":"过滤字段更新成功","schema":{"$ref":"#/definitions/response_parameters.FilterFieldResp"}}}}},"/api/v2/indicator":{"get":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Get Indicator Detail 获取指标配置详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"指标ID","name":"id","in":"query","required":true},{"type":"integer","description":"版本短ID","name":"version_id","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.IndicatorDetail"}}}},"put":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Update Indicator 更新指标配置","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.UpdateIndicator"}}],"responses":{"200":{"description":"指标配置更新成功"}}},"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Create Indicator 创建指标配置","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CreateIndicator"}}],"responses":{"200":{"description":"指标配置创建成功","schema":{"$ref":"#/definitions/response_parameters.IndicatorCreateResponse"}}}}},"/api/v2/indicator/:id":{"delete":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Delete Indicator 删除指标配置","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"指标ID","name":"id","in":"path","required":true}],"responses":{"200":{"description":"指标配置删除成功"}}}},"/api/v2/indicator/generate_sql":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Generate Indicator SQL 生成指标SQL","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.GenerateIndicatorSql"}}],"responses":{"200":{"description":"指标SQL生成成功","schema":{"$ref":"#/definitions/response_parameters.GenerateIndicatorSqlResponse"}}}}},"/api/v2/indicator/list":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Get Indicator List 获取指标配置列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.GetIndicatorList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_IndicatorList"}}}}},"/api/v2/indicator/status":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Update Indicator Status 更新指标状态","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.UpdateIndicatorStatus"}}],"responses":{"200":{"description":"指标状态更新成功"}}}},"/api/v2/indicator/testing":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Test Indicator SQL 测试指标SQL","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.TestIndicatorSql"}}],"responses":{"200":{"description":"指标SQL测试成功","schema":{"type":"array","items":{"type":"object","additionalProperties":true}}}}}},"/api/v2/indicator/version/release":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Version 版本管理"],"summary":"Release Indicator Version 发布指标版本","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.ReleaseIndicator"}}],"responses":{"200":{"description":"发布成功"}}}},"/api/v2/indicator/versions":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Version 版本管理"],"summary":"Get Indicator Version List 获取指标配置版本列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.GetIndicatorVersion"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_IndicatorVersionList"}}}}},"/api/v2/indicator_script":{"get":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Script Indicator Script指标"],"summary":"Get Indicator  Detail 获取指标配置详情","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"指标ID","name":"id","in":"query","required":true},{"type":"integer","description":"版本短ID","name":"version_id","in":"query","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.IndicatorScriptDetail"}}}},"put":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Script Indicator Script指标"],"summary":"Update Indicator 更新指标配置","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.UpdateScriptIndicator"}}],"responses":{"200":{"description":"指标配置更新成功"}}},"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Script Indicator Script指标"],"summary":"Create Indicator 创建指标配置","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.CreateScriptIndicator"}}],"responses":{"200":{"description":"指标配置创建成功","schema":{"$ref":"#/definitions/response_parameters.IndicatorCreateResponse"}}}}},"/api/v2/indicator_script/:id":{"delete":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Script Indicator Script指标"],"summary":"Delete Indicator 删除指标配置","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"integer","description":"指标ID","name":"id","in":"path","required":true}],"responses":{"200":{"description":"指标配置删除成功"}}}},"/api/v2/indicator_script/list":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Script Indicator Script指标"],"summary":"Get Indicator List 获取指标配置列表","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.GetIndicatorScriptList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/response_parameters.ListResponsePointer-response_parameters_IndicatorScriptList"}}}}},"/api/v2/indicator_script/status":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Script Indicator Script指标"],"summary":"Update Indicator Status 更新指标状态","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.UpdateIndicatorStatus"}}],"responses":{"200":{"description":"指标状态更新成功"}}}},"/api/v2/indicator_script/testing":{"post":{"security":[{"x-auth-token":[]}],"consumes":["application/json"],"produces":["application/json"],"tags":["risk-portal-api/Indicator 指标/Visual Indicator 可视化指标"],"summary":"Test Script Indicator SQL 测试Script指标SQL","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"data","in":"body","required":true,"schema":{"$ref":"#/definitions/request_parameters.TestScriptIndicatorSql"}}],"responses":{"200":{"description":"指标SQL测试成功","schema":{"type":"object","additionalProperties":true}}}}},"/api/v2/operator_log/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["OperatorLog 风控操作日志"],"summary":"List OperatorLog","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.OperatorLogList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/modelv2.RmsOperatorLog"}}}}},"/api/v2/operator_log/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["OperatorLog 风控操作日志"],"summary":"Retrieve OperatorLog","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/modelv2.RmsOperatorLog"}}}}},"/api/v2/rule/compile":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Compile Rule, Returns whether the rule has errors.","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleCompile"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Create Rule","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/api/v2/rule/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"List Rule","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/api/v2/rule/refresh":{"get":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Refresh Rule Cache","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Retrieve Rule","parameters":[{"type":"string","description":"17","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/api/v2/rule/set_status":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"SetStatus Rule, one of Online Offline","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleSetStatus"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Update Rule","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/api/v2/rule_group/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"Create Policy","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/rule_group/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule_group/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"List Policy","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyListResp"}}}}},"/api/v2/rule_group/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"Retrieve Policy","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/rule_group/set_status":{"post":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"SetStatus Policy, one of Online Offline","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicySetStatus"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule_group/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"Update Policy","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/rule_param/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameter 规则参数"],"summary":"Create RuleParameter","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/rule_param/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameter 规则参数"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule_param/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameter 规则参数"],"summary":"List RuleParameter","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterListResp"}}}}},"/api/v2/rule_param/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameter 规则参数"],"summary":"Retrieve RuleParameter","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/rule_param/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameter 规则参数"],"summary":"Update RuleParameter","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/api/v2/rule_parameter_group/all":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"All RuleParameterGroup","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/api/v2/rule_parameter_group/all_exclude_default":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"all_exclude_default RuleParameterGroup","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/api/v2/rule_parameter_group/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"Create RuleParameterGroup","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/api/v2/rule_parameter_group/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule_parameter_group/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"List RuleParameterGroup","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupListResp"}}}}},"/api/v2/rule_parameter_group/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"Retrieve RuleParameterGroup","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/api/v2/rule_parameter_group/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroup 规则参数组"],"summary":"Update RuleParameterGroup","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/api/v2/rule_parameter_group_bind/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Create RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}},"/api/v2/rule_parameter_group_bind/create_list":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Create_list Add multiple RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindCreateList"}}],"responses":{"200":{"description":"OK","schema":{"type":"array","items":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}}},"/api/v2/rule_parameter_group_bind/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule_parameter_group_bind/delete_list":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"DeleteList batch deletion RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"values","in":"body","required":true,"schema":{"type":"array","items":{"type":"integer"}}}],"responses":{"200":{"description":"OK"}}}},"/api/v2/rule_parameter_group_bind/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"List RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindListResp"}}}}},"/api/v2/rule_parameter_group_bind/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Retrieve RuleParameterGroupBind","parameters":[{"type":"string","description":"id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}},"/api/v2/rule_parameter_group_bind/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Update RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}},"/api/v2/rule_parameter_value/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterValue 规则参数值"],"summary":"Create RuleParameterValue","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/api/v2/rule_parameter_value/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterValue 规则参数值"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/api/v2/rule_parameter_value/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterValue 规则参数值"],"summary":"List RuleParameterValue","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterValueList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/api/v2/rule_parameter_value/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterValue 规则参数值"],"summary":"Retrieve RuleParameterValue","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/api/v2/rule_parameter_value/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterValue 规则参数值"],"summary":"Update RuleParameterValue","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/api/v2/rule_parameter_value/update_multiple":{"post":{"security":[{"x-auth-token":[]}],"tags":["V2 RuleParameterValue 规则参数值"],"summary":"UpdateMultiple RuleParameterValue 更新多个","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdateMultiple"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdateMultipleResp"}}}}},"/black_list/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["Blacklist 黑名单"],"summary":"Create Blacklist","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.BlacklistCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.BlacklistResp"}}}}},"/black_list/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Blacklist 黑名单"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/black_list/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["Blacklist 黑名单"],"summary":"List Blacklist","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.BlacklistList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.BlacklistListResp"}}}}},"/black_list/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Blacklist 黑名单"],"summary":"Retrieve Blacklist","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.BlacklistResp"}}}}},"/black_list/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["Blacklist 黑名单"],"summary":"Update Blacklist","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.BlacklistUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.BlacklistResp"}}}}},"/business_type/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["BusinessType 业务类型"],"summary":"Create BusinessType","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.BusinessTypeCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsBusinessType"}}}}},"/business_type/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["BusinessType 业务类型"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/business_type/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["BusinessType 业务类型"],"summary":"List BusinessType","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.BusinessTypeList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsBusinessType"}}}}},"/business_type/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["BusinessType 业务类型"],"summary":"Retrieve BusinessType","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsBusinessType"}}}}},"/business_type/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["BusinessType 业务类型"],"summary":"Update BusinessType","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.BusinessTypeUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsBusinessType"}}}}},"/config_progress/create_or_update":{"post":{"security":[{"x-auth-token":[]}],"tags":["PlatConfigProgress 配置进度"],"summary":"CreateOrUpdate ConfigProgress","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.ConfigProgressCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/config_progress/retrieve/{code}":{"get":{"security":[{"x-auth-token":[]}],"tags":["PlatConfigProgress 配置进度"],"summary":"Retrieve ConfigProgress","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"access_point code","name":"code","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.PlatConfigProgress"}}}}},"/event/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["Event 风险事件 事件查询"],"summary":"List Event","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsEvent"}}}}},"/event/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Event 风险事件 事件查询"],"summary":"Retrieve Event","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsEvent"}}}}},"/event_field/check_next":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventField 事件字段"],"summary":"CheckNext Check if you can click Next","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventFieldCheckNext"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventFieldListResp"}}}}},"/event_field/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventField 事件字段"],"summary":"Create EventField","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventFieldCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventFieldResp"}}}}},"/event_field/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["EventField 事件字段"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/event_field/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventField 事件字段"],"summary":"List EventField","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventFieldList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventFieldListResp"}}}}},"/event_field/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["EventField 事件字段"],"summary":"Retrieve EventField","parameters":[{"type":"string","description":"EventField Id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventFieldResp"}}}}},"/event_field/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventField 事件字段"],"summary":"Update EventField","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventFieldUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventFieldResp"}}}}},"/event_store_cfg/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventStoreCfg 事件存储配置"],"summary":"Create EventStoreCfg","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventStoreCfgCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventStoreCfgResp"}}}}},"/event_store_cfg/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["EventStoreCfg 事件存储配置"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/event_store_cfg/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventStoreCfg 事件存储配置"],"summary":"List EventStoreCfg","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventStoreCfgList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventStoreCfgResp"}}}}},"/event_store_cfg/retrieve/{access_point_code}":{"get":{"security":[{"x-auth-token":[]}],"tags":["EventStoreCfg 事件存储配置"],"summary":"Retrieve EventStoreCfg","parameters":[{"type":"string","description":"access point code","name":"access_point_code","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventStoreCfgResp"}}}}},"/event_store_cfg/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["EventStoreCfg 事件存储配置"],"summary":"Update EventStoreCfg","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.EventStoreCfgUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.EventStoreCfgResp"}}}}},"/filter_field/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["FilterField 过滤字段"],"summary":"Create FilterField","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.FilterFieldCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.FilterFieldResp"}}}}},"/filter_field/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["FilterField 过滤字段"],"summary":"List FilterField","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.FilterFieldList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.FilterFieldListResp"}}}}},"/filter_field/retrieve":{"post":{"security":[{"x-auth-token":[]}],"tags":["FilterField 过滤字段"],"summary":"Retrieve FilterField","parameters":[{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.FilterFieldRetrieve"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.FilterFieldResp"}}}}},"/filter_field/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["FilterField 过滤字段"],"summary":"Update FilterField","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.FilterFieldUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.FilterFieldResp"}}}}},"/handle_log/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["HandleLog 用户操作日志"],"summary":"List HandleLog","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.HandleLogList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.WpHandleLog"}}}}},"/handle_log/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["HandleLog 用户操作日志"],"summary":"Retrieve HandleLog","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.WpHandleLog"}}}}},"/operator_log/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["OperatorLog 风控操作日志"],"summary":"List OperatorLog","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.OperatorLogList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsOperatorLog"}}}}},"/operator_log/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["OperatorLog 风控操作日志"],"summary":"Retrieve OperatorLog","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsOperatorLog"}}}}},"/page_data/business_config/{access_point_code}":{"get":{"security":[{"x-auth-token":[]}],"tags":["PageData 对应前端页面"],"summary":"BusinessConfig 接入点配置-\u003e 业务配置页面","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"access point code","name":"access_point_code","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyConfigPageResp"}}}}},"/page_data/policy_config_page/{rules_group_code}":{"get":{"security":[{"x-auth-token":[]}],"tags":["PageData 对应前端页面"],"summary":"PolicyConfigPage 规则组配置所需数据","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"access point code","name":"access_point_code","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyConfigPageResp"}}}}},"/page_data/policy_config_page_create/{access_point_code}":{"get":{"security":[{"x-auth-token":[]}],"tags":["PageData 对应前端页面"],"summary":"PolicyConfigPageCreate 创建规则组配置所需数据","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"type":"string","description":"access point code","name":"access_point_code","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyConfigPageResp"}}}}},"/page_data/rule_parameter_group_bind":{"post":{"security":[{"x-auth-token":[]}],"tags":["PageData 对应前端页面"],"summary":"RmsRuleParameterGroupBind 规则配置-\u003e 参数组 -\u003e 参数组绑定","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyConfigPageList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RmsRuleParameterGroupBindResp"}}}}},"/risk_log/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RiskLog 风控日志"],"summary":"List RiskLog","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RiskLogList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRiskLog"}}}}},"/risk_log/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RiskLog 风控日志"],"summary":"Retrieve RiskLog","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRiskLog"}}}}},"/rule/compile":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Compile Rule, Returns whether the rule has errors.","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleCompile"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Create Rule","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/rule/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"List Rule","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/rule/refresh":{"get":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Refresh Rule Cache","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Retrieve Rule","parameters":[{"type":"string","description":"17","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/rule/set_status":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"SetStatus Rule, one of Online Offline","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleSetStatus"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["Rule 风控规则 规则配置"],"summary":"Update Rule","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleResp"}}}}},"/rule_group/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule_group/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"List Policy","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicyList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyListResp"}}}}},"/rule_group/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"Retrieve Policy","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/rule_group/set_status":{"post":{"security":[{"x-auth-token":[]}],"tags":["Policy 风控规则策略（即规则组）"],"summary":"SetStatus Policy, one of Online Offline","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.PolicySetStatus"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule_param/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameter 规则参数"],"summary":"Create RuleParameter","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/rule_param/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameter 规则参数"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule_param/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameter 规则参数"],"summary":"List RuleParameter","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterListResp"}}}}},"/rule_param/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameter 规则参数"],"summary":"Retrieve RuleParameter","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/rule_param/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameter 规则参数"],"summary":"Update RuleParameter","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.PolicyResp"}}}}},"/rule_parameter_group/all":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"All RuleParameterGroup","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/rule_parameter_group/all_exclude_default":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"all_exclude_default RuleParameterGroup","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/rule_parameter_group/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"Create RuleParameterGroup","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/rule_parameter_group/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule_parameter_group/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"List RuleParameterGroup","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupListResp"}}}}},"/rule_parameter_group/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"Retrieve RuleParameterGroup","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/rule_parameter_group/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroup 规则参数组"],"summary":"Update RuleParameterGroup","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}}}}},"/rule_parameter_group_bind/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Create RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}},"/rule_parameter_group_bind/create_list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Create_list Add multiple RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindCreateList"}}],"responses":{"200":{"description":"OK","schema":{"type":"array","items":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}}},"/rule_parameter_group_bind/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule_parameter_group_bind/delete_list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"DeleteList batch deletion RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"values","in":"body","required":true,"schema":{"type":"array","items":{"type":"integer"}}}],"responses":{"200":{"description":"OK"}}}},"/rule_parameter_group_bind/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"List RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindListResp"}}}}},"/rule_parameter_group_bind/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Retrieve RuleParameterGroupBind","parameters":[{"type":"string","description":"id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}},"/rule_parameter_group_bind/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterGroupBind 规则参数组绑定关系"],"summary":"Update RuleParameterGroupBind","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RuleParameterGroupBindResp"}}}}},"/rule_parameter_value/create":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterValue 规则参数值"],"summary":"Create RuleParameterValue","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueCreate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/rule_parameter_value/delete/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterValue 规则参数值"],"summary":"Delete role record by ID","parameters":[{"type":"string","description":"unique id","name":"id","in":"path","required":true}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.Base"}}}}},"/rule_parameter_value/list":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterValue 规则参数值"],"summary":"List RuleParameterValue","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RuleParameterValueList"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/rule_parameter_value/retrieve/{id}":{"get":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterValue 规则参数值"],"summary":"Retrieve RuleParameterValue","responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/rule_parameter_value/update":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterValue 规则参数值"],"summary":"Update RuleParameterValue","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdate"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/model.RmsRuleParameterValue"}}}}},"/rule_parameter_value/update_multiple":{"post":{"security":[{"x-auth-token":[]}],"tags":["RuleParameterValue 规则参数值"],"summary":"UpdateMultiple RuleParameterValue 更新多个","parameters":[{"type":"string","description":"jwt token","name":"x-auth-token","in":"header","required":true},{"description":"Request body","name":"body","in":"body","required":true,"schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdateMultiple"}}],"responses":{"200":{"description":"OK","schema":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdateMultipleResp"}}}}}},"definitions":{"domain.AlarmContactCreate":{"type":"object","properties":{"alarm_time_config_id":{"type":"integer"},"email":{"type":"string","example":"a@a.c"},"mobile":{"type":"string","example":"13333333333"},"name":{"type":"string","example":"联系人名"},"notice_time":{"type":"string"},"open_id":{"type":"string","example":"1"},"status":{"type":"integer","example":1}}},"domain.AlarmContactList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.AlarmContactUpdate":{"type":"object","required":["id"],"properties":{"alarm_time_config_id":{"type":"integer"},"email":{"type":"string","example":"a@a.c"},"id":{"type":"integer","minimum":0,"example":1},"mobile":{"type":"string","example":"13333333333"},"name":{"type":"string","example":"联系人名"},"notice_time":{"type":"string"},"open_id":{"type":"string","example":"1"},"status":{"type":"integer","example":1}}},"domain.AlarmTimeConfCreate":{"type":"object","properties":{"end_time":{"description":"截止时间","type":"integer"},"open_id":{"description":"操作员","type":"string"},"result_code":{"description":"ResultCode","type":"string"},"start_time":{"description":"开始时间","type":"string"},"status":{"description":"状态","type":"integer"}}},"domain.AlarmTimeConfList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.AlarmTimeConfUpdate":{"type":"object","required":["id"],"properties":{"end_time":{"description":"截止时间","type":"integer"},"id":{"type":"integer","minimum":0,"example":1},"open_id":{"description":"操作员","type":"string"},"result_code":{"description":"ResultCode","type":"string"},"start_time":{"description":"开始时间","type":"string"},"status":{"description":"状态","type":"integer"}}},"domain.Base":{"type":"object","properties":{"code":{"type":"integer"},"data":{},"message":{"type":"string"}}},"domain.BlacklistCreate":{"type":"object","required":["blacklist_value"],"properties":{"blacklist_type":{"description":"键，BANK_CARD_NO/TRADE_IP","type":"string","enum":["COMPANY_NAME","COMPANY_UEN","COMPANY_ADDRESS","INDIVIDUAL_NAME","INDIVIDUAL_NATIONAL_ID","INDIVIDUAL_PASSPORT","INDIVIDUAL_DRIVERS_LICENSE","RESIDENTIAL_ADDRESS","MOBILE","EMAIL_DOMAIN","EMAIL_ADDRESS","CUSTOMER_COUNTRY"],"example":"TRADE_IP"},"blacklist_value":{"description":"值，如卡号/ip","type":"string","example":"***********"},"remark":{"description":"备注","type":"string","example":"test"}}},"domain.BlacklistList":{"type":"object","required":["page_num","page_size"],"properties":{"blacklist_type":{"description":"键，BANK_CARD_NO/TRADE_IP","type":"string","enum":["COMPANY_NAME","COMPANY_UEN","COMPANY_ADDRESS","INDIVIDUAL_NAME","INDIVIDUAL_NATIONAL_ID","INDIVIDUAL_PASSPORT","INDIVIDUAL_DRIVERS_LICENSE","RESIDENTIAL_ADDRESS","MOBILE","EMAIL_DOMAIN","EMAIL_ADDRESS","CUSTOMER_COUNTRY"],"example":"TRADE_IP"},"blacklist_value":{"description":"值，如卡号/ip","type":"string","example":"***********"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"remark":{"description":"备注","type":"string","example":"test"}}},"domain.BlacklistListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.BlacklistResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.BlacklistResp":{"type":"object","required":["blacklist_value","rc_id"],"properties":{"blacklist_type":{"description":"键，BANK_CARD_NO/TRADE_IP","type":"string","enum":["COMPANY_NAME","COMPANY_UEN","COMPANY_ADDRESS","INDIVIDUAL_NAME","INDIVIDUAL_NATIONAL_ID","INDIVIDUAL_PASSPORT","INDIVIDUAL_DRIVERS_LICENSE","RESIDENTIAL_ADDRESS","MOBILE","EMAIL_DOMAIN","EMAIL_ADDRESS","CUSTOMER_COUNTRY"],"example":"TRADE_IP"},"blacklist_value":{"description":"值，如卡号/ip","type":"string","example":"***********"},"rc_id":{"type":"integer","minimum":0,"example":1},"remark":{"description":"备注","type":"string","example":"test"},"time":{"type":"string"}}},"domain.BlacklistUpdate":{"type":"object","required":["blacklist_value","rc_id"],"properties":{"blacklist_type":{"description":"键，BANK_CARD_NO/TRADE_IP","type":"string","enum":["COMPANY_NAME","COMPANY_UEN","COMPANY_ADDRESS","INDIVIDUAL_NAME","INDIVIDUAL_NATIONAL_ID","INDIVIDUAL_PASSPORT","INDIVIDUAL_DRIVERS_LICENSE","RESIDENTIAL_ADDRESS","MOBILE","EMAIL_DOMAIN","EMAIL_ADDRESS","CUSTOMER_COUNTRY"],"example":"TRADE_IP"},"blacklist_value":{"description":"值，如卡号/ip","type":"string","example":"***********"},"rc_id":{"type":"integer","minimum":0,"example":1},"remark":{"description":"备注","type":"string","example":"test"}}},"domain.BusinessTypeCreate":{"type":"object","required":["business_code","business_name"],"properties":{"business_code":{"description":"业务编码","type":"string","example":"DEPOSIT"},"business_name":{"description":"业务名称","type":"string","example":"充值"}}},"domain.BusinessTypeList":{"type":"object","required":["page_num","page_size"],"properties":{"business_name":{"type":"string"},"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.BusinessTypeUpdate":{"type":"object","required":["business_code","business_name","id"],"properties":{"business_code":{"description":"业务编码","type":"string","example":"DEPOSIT"},"business_name":{"description":"业务名称","type":"string","example":"充值"},"id":{"type":"integer","minimum":0,"example":1}}},"domain.CheckPointBusinessConfigReq":{"type":"object","required":["code"],"properties":{"check_point_business_config":{"type":"array","items":{"$ref":"#/definitions/model.CheckPointBusinessConfig"}},"code":{"description":"编码","type":"string","example":"CP003"}}},"domain.CheckPointCreate":{"type":"object","required":["code","name"],"properties":{"always_run":{"type":"integer"},"check_duplicate":{"description":"数据重复检查","type":"boolean","example":true},"code":{"description":"编码","type":"string","example":"CP003"},"name":{"description":"名称","type":"string","example":"CP003-CMF"},"remark":{"description":"备注","type":"string","example":"test"}}},"domain.CheckPointList":{"type":"object","required":["page_num","page_size"],"properties":{"code":{"description":"编码","type":"string","example":"CP003"},"label":{"description":"名称","type":"string","example":"CP003-CMF"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"domain.CheckPointListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.CheckPointResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.CheckPointNameLabel":{"type":"object","properties":{"code":{"type":"string"},"name":{"type":"string"}}},"domain.CheckPointResp":{"type":"object","required":["code","id","name"],"properties":{"always_run":{"type":"integer"},"business_type":{"description":"业务类型配置，用于对接入业务（事件）的细分","type":"integer","example":1000},"check_duplicate":{"description":"数据重复检查","type":"boolean","example":true},"code":{"description":"编码","type":"string","example":"CP003"},"config_progress":{"description":"配置进度","type":"integer"},"default_primary_key":{"description":"默认主键字段，与filterField相关","type":"string","example":"orderSn"},"filter_fields":{"description":"过滤字段配置，区分更细化的业务数据（policy选取有关）","type":"string","example":"[]"},"id":{"type":"integer","minimum":0,"example":1},"name":{"description":"名称","type":"string","example":"CP003-CMF"},"remark":{"description":"备注","type":"string","example":"test"}}},"domain.CheckPointUpdate":{"type":"object","required":["code","id","name"],"properties":{"always_run":{"type":"integer"},"check_duplicate":{"description":"数据重复检查","type":"boolean","example":true},"code":{"description":"编码","type":"string","example":"CP003"},"id":{"type":"integer","minimum":0,"example":1},"name":{"description":"名称","type":"string","example":"CP003-CMF"},"remark":{"description":"备注","type":"string","example":"test"}}},"domain.ConfigProgressCreate":{"type":"object","properties":{"access_point":{"type":"string","example":"CP003"},"progress":{"type":"integer","example":1}}},"domain.EventFieldCheckNext":{"type":"object","required":["access_point"],"properties":{"access_point":{"description":"归属检查点（编码）","type":"string","example":"CP003"}}},"domain.EventFieldCreate":{"type":"object","required":["access_point","item_type","name_item"],"properties":{"access_point":{"description":"归属检查点（编码）","type":"string","example":"CP003"},"filter":{"description":"是否为过滤字段","type":"boolean","example":false},"item_type":{"description":"字段类型，如String","type":"string","example":"String"},"name_item":{"description":"字段名","type":"string","example":"orderSn"},"primary_key":{"description":"是否为主键","type":"boolean","example":false},"remark":{"description":"备注","type":"string","example":"订单号"},"required":{"description":"是否必填","type":"boolean","example":true}}},"domain.EventFieldList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"归属检查点（编码）","type":"string","example":"CP003"},"field_name":{"description":"字段名","type":"string","example":"orderSn"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"domain.EventFieldListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.EventFieldResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.EventFieldResp":{"type":"object","required":["access_point","id","item_type","name_item"],"properties":{"access_point":{"description":"归属检查点（编码）","type":"string","example":"CP003"},"filter":{"description":"是否为过滤字段","type":"boolean","example":false},"id":{"type":"integer","minimum":0,"example":1},"item_type":{"description":"字段类型，如String","type":"string","example":"String"},"name_item":{"description":"字段名","type":"string","example":"orderSn"},"primary_key":{"description":"是否为主键","type":"boolean","example":false},"remark":{"description":"备注","type":"string","example":"订单号"},"required":{"description":"是否必填","type":"boolean","example":true}}},"domain.EventFieldUpdate":{"type":"object","required":["access_point","id","item_type","name_item"],"properties":{"access_point":{"description":"归属检查点（编码）","type":"string","example":"CP003"},"filter":{"description":"是否为过滤字段","type":"boolean","example":false},"id":{"type":"integer","minimum":0,"example":1},"item_type":{"description":"字段类型，如String","type":"string","example":"String"},"name_item":{"description":"字段名","type":"string","example":"orderSn"},"primary_key":{"description":"是否为主键","type":"boolean","example":false},"remark":{"description":"备注","type":"string","example":"订单号"},"required":{"description":"是否必填","type":"boolean","example":true}}},"domain.EventList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"联系人姓名","type":"string","example":""},"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"field":{"description":"字段名","type":"string","enum":["STR_FIELD1","STR_FIELD2","STR_FIELD3","STR_FIELD4","STR_FIELD5","NUM_FIELD1","NUM_FIELD2"],"example":"str_field1"},"field_value":{"description":"字段值","type":"string","example":"111"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"result_code":{"description":"规则结果代码","type":"string","example":""},"start_time":{"type":"integer","example":**********}}},"domain.EventStoreCfgCreate":{"type":"object","required":["access_point","whether_store"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"num_field1":{"description":"重要数值类型字段1的字段名","type":"string","maxLength":32,"example":"amount"},"num_field2":{"description":"重要数值类型字段2的字段名","type":"string","maxLength":32,"example":"te"},"str_field1":{"description":"重要字符串类型字段1的字段名（建议主键）","type":"string","maxLength":32,"example":"orderSn"},"str_field2":{"description":"重要字符串类型字段2的字段名","type":"string","maxLength":32,"example":"cardNo"},"str_field3":{"description":"重要字符串类型字段3的字段名","type":"string","maxLength":32,"example":"tradeHash"},"str_field4":{"description":"重要字符串类型字段4的字段名","type":"string","maxLength":32,"example":"ip"},"str_field5":{"description":"重要字符串类型字段5的字段名","type":"string","maxLength":32,"example":"te"},"whether_store":{"description":"是否存储","type":"boolean","example":true}}},"domain.EventStoreCfgList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.EventStoreCfgResp":{"type":"object","required":["access_point","id","whether_store"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"id":{"type":"integer","minimum":0,"example":1},"num_field1":{"description":"重要数值类型字段1的字段名","type":"string","maxLength":32,"example":"amount"},"num_field2":{"description":"重要数值类型字段2的字段名","type":"string","maxLength":32,"example":"te"},"str_field1":{"description":"重要字符串类型字段1的字段名（建议主键）","type":"string","maxLength":32,"example":"orderSn"},"str_field2":{"description":"重要字符串类型字段2的字段名","type":"string","maxLength":32,"example":"cardNo"},"str_field3":{"description":"重要字符串类型字段3的字段名","type":"string","maxLength":32,"example":"tradeHash"},"str_field4":{"description":"重要字符串类型字段4的字段名","type":"string","maxLength":32,"example":"ip"},"str_field5":{"description":"重要字符串类型字段5的字段名","type":"string","maxLength":32,"example":"te"},"whether_store":{"description":"是否存储","type":"boolean","example":true}}},"domain.EventStoreCfgUpdate":{"type":"object","required":["access_point","id","whether_store"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"id":{"type":"integer","minimum":0,"example":1},"num_field1":{"description":"重要数值类型字段1的字段名","type":"string","maxLength":32,"example":"amount"},"num_field2":{"description":"重要数值类型字段2的字段名","type":"string","maxLength":32,"example":"te"},"str_field1":{"description":"重要字符串类型字段1的字段名（建议主键）","type":"string","maxLength":32,"example":"orderSn"},"str_field2":{"description":"重要字符串类型字段2的字段名","type":"string","maxLength":32,"example":"cardNo"},"str_field3":{"description":"重要字符串类型字段3的字段名","type":"string","maxLength":32,"example":"tradeHash"},"str_field4":{"description":"重要字符串类型字段4的字段名","type":"string","maxLength":32,"example":"ip"},"str_field5":{"description":"重要字符串类型字段5的字段名","type":"string","maxLength":32,"example":"te"},"whether_store":{"description":"是否存储","type":"boolean","example":true}}},"domain.FilterFieldCreate":{"type":"object","required":["access_point","enumeration_value","field_name"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"enumeration_value":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"string","example":"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"remark":{"description":"备注","type":"string","example":"业务类型"}}},"domain.FilterFieldList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"domain.FilterFieldListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.FilterFieldResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.FilterFieldResp":{"type":"object","required":["access_point","enumeration_value","field_name","id"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"enumeration_value":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"string","example":"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"id":{"type":"integer","minimum":0,"example":1},"remark":{"description":"备注","type":"string","example":"业务类型"}}},"domain.FilterFieldRetrieve":{"type":"object","properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"field_name":{"description":"字段名","type":"string","example":"bizType"}}},"domain.FilterFieldUpdate":{"type":"object","required":["access_point","enumeration_value","field_name","id"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"enumeration_value":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"string","example":"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"id":{"type":"integer","minimum":0,"example":1},"remark":{"description":"备注","type":"string","example":"业务类型"}}},"domain.HandleLogList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"handle_events":{"description":"操作事件","type":"string","example":""},"handle_params":{"description":"动作参数[内部使用]","type":"string","example":""},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"puserid":{"description":"操作员ID","type":"string","example":""},"start_time":{"type":"integer","example":**********}}},"domain.OperatorLogList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.PolicyConfigPageAllFilterFieldResp":{"type":"object","properties":{"field_enum":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"array","items":{"type":"integer"}},"field_name":{"description":"字段名","type":"string"}}},"domain.PolicyConfigPageList":{"type":"object","required":["page_num","page_size"],"properties":{"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"rule_parameter_group_id":{"description":"规则参数组id","type":"integer","example":79}}},"domain.PolicyConfigPageResp":{"type":"object","properties":{"allFilterField":{"type":"array","items":{"$ref":"#/definitions/domain.PolicyConfigPageAllFilterFieldResp"}},"policy":{"$ref":"#/definitions/domain.PolicyConfigPolicyResp"},"ruleLi":{"type":"array","items":{"$ref":"#/definitions/domain.RuleResp"}}}},"domain.PolicyConfigPolicyResp":{"type":"object","required":["access_point","id","rule_group_code","rule_group_name","status"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"filter_field_map":{"description":"example:\"{\\\"orderNo\\\":[\\\"CREDIT\\\"]}\" 策略匹配的过滤条件，需事先设置检查点的过滤字段","type":"array","items":{"type":"integer"}},"id":{"type":"integer","minimum":0,"example":1},"number_of_rules":{"description":"规则组数量","type":"integer"},"policy_group":{"description":"策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup","type":"string","example":"CMF"},"remark":{"description":"备注","type":"string","example":""},"rule_group_code":{"description":"策略编号","type":"string","example":"CMF"},"rule_group_name":{"description":"规则组名称","type":"string","example":"CMF"},"rule_id_list":{"description":"example:\"[1,2]\" 策略中需要执行的规则id列表json","type":"array","items":{"type":"integer"}},"status":{"description":"状态，Online/Offline","type":"string","example":"Online"},"synced_at":{"description":"同步时间","type":"string"},"white_list_id_list":{"description":"白名单id列表","type":"array","items":{"type":"integer"}}}},"domain.PolicyCreate":{"type":"object","required":["access_point","rule_group_code","rule_group_name","status"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"filter_field_map":{"description":"example:\"{\\\"orderNo\\\":[\\\"CREDIT\\\"]}\" 策略匹配的过滤条件，需事先设置检查点的过滤字段","type":"array","items":{"type":"integer"}},"policy_group":{"description":"策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup","type":"string","example":"CMF"},"remark":{"description":"备注","type":"string","example":""},"rule_group_code":{"description":"策略编号","type":"string","example":"CMF"},"rule_group_name":{"description":"规则组名称","type":"string","example":"CMF"},"rule_id_list":{"description":"example:\"[1,2]\" 策略中需要执行的规则id列表json","type":"array","items":{"type":"integer"}},"status":{"description":"状态，Online/Offline","type":"string","example":"Online"},"white_list_id_list":{"description":"白名单id列表","type":"array","items":{"type":"integer"}}}},"domain.PolicyList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"policy_no":{"description":"策略编号","type":"string","example":"CMF"},"rule_group_name":{"description":"规则组名","type":"string","example":"CMF"},"start_time":{"type":"integer","example":**********}}},"domain.PolicyListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.PolicyResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.PolicyResp":{"type":"object","required":["access_point","id","rule_group_code","rule_group_name","status"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"filter_field_map":{"description":"example:\"{\\\"orderNo\\\":[\\\"CREDIT\\\"]}\" 策略匹配的过滤条件，需事先设置检查点的过滤字段","type":"array","items":{"type":"integer"}},"id":{"type":"integer","minimum":0,"example":1},"number_of_rules":{"description":"规则组数量","type":"integer"},"policy_group":{"description":"策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup","type":"string","example":"CMF"},"remark":{"description":"备注","type":"string","example":""},"rule_group_code":{"description":"策略编号","type":"string","example":"CMF"},"rule_group_name":{"description":"规则组名称","type":"string","example":"CMF"},"rule_id_list":{"description":"example:\"[1,2]\" 策略中需要执行的规则id列表json","type":"array","items":{"type":"integer"}},"status":{"description":"状态，Online/Offline","type":"string","example":"Online"},"synced_at":{"description":"同步时间","type":"string"},"white_list_id_list":{"description":"白名单id列表","type":"array","items":{"type":"integer"}}}},"domain.PolicySetStatus":{"type":"object","required":["id"],"properties":{"id":{"type":"integer"},"status":{"type":"string","enum":["Online","Offline"]}}},"domain.PolicyUpdate":{"type":"object","required":["access_point","id","rule_group_code","rule_group_name","status"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"filter_field_map":{"description":"example:\"{\\\"orderNo\\\":[\\\"CREDIT\\\"]}\" 策略匹配的过滤条件，需事先设置检查点的过滤字段","type":"array","items":{"type":"integer"}},"id":{"type":"integer","minimum":0,"example":1},"policy_group":{"description":"策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup","type":"string","example":"CMF"},"remark":{"description":"备注","type":"string","example":""},"rule_group_code":{"description":"策略编号","type":"string","example":"CMF"},"rule_group_name":{"description":"规则组名称","type":"string","example":"CMF"},"rule_id_list":{"description":"example:\"[1,2]\" 策略中需要执行的规则id列表json","type":"array","items":{"type":"integer"}},"status":{"description":"状态，Online/Offline","type":"string","example":"Online"},"white_list_id_list":{"description":"白名单id列表","type":"array","items":{"type":"integer"}}}},"domain.RiskLogList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.RmsRuleParameterGroupBindResp":{"type":"object","properties":{"item_list":{"description":"规则参数","type":"array","items":{"$ref":"#/definitions/domain.RmsRuleParameterGroupBindRuleParameter"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.RmsRuleParameterGroupBindRuleParameter":{"type":"object","properties":{"bind":{"description":"是否绑定，根据 rms_rule_parameter_value 是否有值调用 SetIsBind 或 SetNotBind","type":"string","example":"YES"},"id":{"type":"integer"},"parameter_code":{"description":"参数编码","type":"string","example":"CP003_DEFAULT_ACTION"},"parameter_description":{"description":"参数描述","type":"string","example":"默认操作"},"parameter_type":{"description":"参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes","type":"string","example":"STRING"},"parameter_value":{"description":"参数值","type":"array","items":{"$ref":"#/definitions/domain.RmsRuleParameterGroupBindRuleParameterParameterValue"}}}},"domain.RmsRuleParameterGroupBindRuleParameterParameterValue":{"type":"object","properties":{"description":{"description":"描述","type":"string"},"id":{"type":"integer"},"param_key":{"description":"健，非键值对类型可空","type":"string"},"param_value":{"description":"值","type":"string"}}},"domain.RmsRuleParameterValueCreate":{"type":"object","required":["group_id","param_id","param_key","param_value"],"properties":{"description":{"description":"描述","type":"string","example":""},"group_id":{"description":"RMS_RULE_PARAMETER_GROUP外键","type":"integer","example":1},"param_id":{"description":"RMS_RULE_PARAMETER外键","type":"integer","example":1},"param_key":{"description":"健，非键值对类型可空","type":"string","example":""},"param_value":{"description":"值","type":"string","example":"001"}}},"domain.RmsRuleParameterValueUpdate":{"type":"object","required":["group_id","id","param_id","param_key","param_value"],"properties":{"description":{"description":"描述","type":"string","example":""},"group_id":{"description":"RMS_RULE_PARAMETER_GROUP外键","type":"integer","example":1},"id":{"type":"integer","minimum":0,"example":1},"param_id":{"description":"RMS_RULE_PARAMETER外键","type":"integer","example":1},"param_key":{"description":"健，非键值对类型可空","type":"string","example":""},"param_value":{"description":"值","type":"string","example":"001"}}},"domain.RmsRuleParameterValueUpdateMultiple":{"type":"object","required":["rule_param_group_id","rule_param_id","rule_param_value_list"],"properties":{"rule_param_group_id":{"description":"RMS_RULE_PARAMETER_GROUP外键","type":"integer","example":1},"rule_param_id":{"description":"RMS_RULE_PARAMETER外键","type":"integer","example":1},"rule_param_value_list":{"type":"array","items":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdateMultipleItem"}}}},"domain.RmsRuleParameterValueUpdateMultipleItem":{"type":"object","required":["param_key","param_value"],"properties":{"description":{"description":"描述","type":"string","example":""},"id":{"type":"integer","example":1},"param_key":{"description":"健，非键值对类型可空","type":"string","example":""},"param_value":{"description":"值","type":"string","example":"001"}}},"domain.RmsRuleParameterValueUpdateMultipleResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.RmsRuleParameterValueUpdateMultipleRespItem"}}}},"domain.RmsRuleParameterValueUpdateMultipleRespItem":{"type":"object","properties":{"description":{"description":"描述","type":"string"},"id":{"type":"integer","example":8},"param_key":{"description":"健，非键值对类型可空","type":"string"},"param_value":{"description":"值","type":"string"}}},"domain.RuleCompile":{"type":"object","required":["priority","rule_code","rule_content","rule_name"],"properties":{"priority":{"type":"integer"},"rule_code":{"type":"string"},"rule_content":{"description":"规则内容","type":"string"},"rule_name":{"type":"string"}}},"domain.RuleCreate":{"type":"object","required":["access_point","deploy_method","priority","rule_code","rule_content","rule_name"],"properties":{"access_point":{"description":"接入点","type":"string","example":"CP003"},"deploy_method":{"description":"上下线方式，manual/auto","type":"string","enum":["manual","auto"],"example":"manual"},"effective_time":{"description":"生效时间","type":"string","example":"2023-06-07T17:07:20+08:00"},"expired_time":{"description":"失效时间","type":"string","example":"2023-06-07T17:07:20+08:00"},"priority":{"type":"integer"},"remark":{"description":"备注","type":"string","example":"黑名单"},"rule_code":{"description":"规则编号","type":"string","example":"Cp003BlackListCheck"},"rule_content":{"description":"规则内容","type":"string","example":"end"},"rule_name":{"description":"规则名","type":"string","example":"黑名单"},"short_circuit":{"description":"是否支持短路","type":"boolean","example":false},"status":{"description":"状态，Online/Offline","type":"string","enum":["Online","Offline"],"example":"Online"}}},"domain.RuleList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"rule_name":{"description":"规则描述","type":"string","example":"黑名单"},"start_time":{"type":"integer","example":**********}}},"domain.RuleParameterCreate":{"type":"object","required":["access_point_list","parameter_code"],"properties":{"access_point_list":{"description":"检查点(可多个,逗号分割)","type":"string","example":"CP003"},"parameter_code":{"description":"参数编码","type":"string","example":"CP003_DEFAULT_ACTION"},"parameter_description":{"description":"参数描述","type":"string","example":"默认操作"},"parameter_type":{"description":"参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes","type":"string","enum":["STRING","NUMBER","LIST","MAP","NUMBER_RANGE","IP_RANGE","BANK_CARD_LIST"],"example":"STRING"},"value_transfer":{"description":"值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer","type":"string","example":""}}},"domain.RuleParameterGroupBindCreate":{"type":"object","required":["parameter_group_id","target_type"],"properties":{"parameter_group_id":{"description":"参数组id","type":"integer","example":1},"state":{"description":"状态，Yes-1-有效，No-0-失效","type":"string","enum":["Yes","No"],"example":"Yes"},"target_type":{"description":"绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级","type":"string","enum":["MERCHANT","CHANNEL","CARD_NUMBER","RISK_LEVEL"],"example":"MERCHANT"},"target_value":{"description":"绑定目标id","type":"string","example":""}}},"domain.RuleParameterGroupBindCreateList":{"type":"object","required":["parameter_group_id_list","state","target_type","target_value"],"properties":{"parameter_group_id_list":{"description":"参数组id 数组","type":"array","items":{"type":"integer"},"example":[1,2]},"state":{"description":"状态，Yes-1-有效，No-0-失效","type":"string","enum":["Yes","No"],"example":"Yes"},"target_type":{"description":"绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级","type":"string","enum":["MERCHANT","CHANNEL","CARD_NUMBER","RISK_LEVEL"],"example":"MERCHANT"},"target_value":{"description":"绑定目标id","type":"string","example":""}}},"domain.RuleParameterGroupBindList":{"type":"object","required":["page_num","page_size"],"properties":{"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"parameter_group_code":{"description":"参数组编码","type":"string","example":"te"},"parameter_group_id":{"description":"参数组id","type":"integer"},"target_type":{"description":"绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级","type":"string","enum":["MERCHANT","CHANNEL","CARD_NUMBER","RISK_LEVEL"],"example":"MERCHANT"},"target_value":{"description":"绑定目标id","type":"string","example":""}}},"domain.RuleParameterGroupBindListItem":{"type":"object","required":["parameter_group_bind_id","parameter_group_id","target_type"],"properties":{"parameter_group_bind_id":{"description":"在前端对应界面， id 是关键字会导致报错。配合前端修改成 parameters-group-id","type":"integer","minimum":0,"example":1},"parameter_group_code":{"description":"参数组编码","type":"string","example":"te"},"parameter_group_id":{"description":"参数组id","type":"integer","example":1},"state":{"description":"状态，Yes-1-有效，No-0-失效","type":"string","enum":["Yes","No"],"example":"Yes"},"target_type":{"description":"绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级","type":"string","enum":["MERCHANT","CHANNEL","CARD_NUMBER","RISK_LEVEL"],"example":"MERCHANT"},"target_value":{"description":"绑定目标id","type":"string","example":""}}},"domain.RuleParameterGroupBindListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.RuleParameterGroupBindListItem"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.RuleParameterGroupBindResp":{"type":"object","required":["id","parameter_group_id","target_type"],"properties":{"id":{"type":"integer","minimum":0,"example":1},"parameter_group_code":{"description":"参数组编码","type":"string","example":"te"},"parameter_group_id":{"description":"参数组id","type":"integer","example":1},"state":{"description":"状态，Yes-1-有效，No-0-失效","type":"string","enum":["Yes","No"],"example":"Yes"},"target_type":{"description":"绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级","type":"string","enum":["MERCHANT","CHANNEL","CARD_NUMBER","RISK_LEVEL"],"example":"MERCHANT"},"target_value":{"description":"绑定目标id","type":"string","example":""}}},"domain.RuleParameterGroupBindUpdate":{"type":"object","required":["id","parameter_group_id","target_type"],"properties":{"id":{"type":"integer","minimum":0,"example":1},"parameter_group_id":{"description":"参数组id","type":"integer","example":1},"state":{"description":"状态，Yes-1-有效，No-0-失效","type":"string","enum":["Yes","No"],"example":"Yes"},"target_type":{"description":"绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级","type":"string","enum":["MERCHANT","CHANNEL","CARD_NUMBER","RISK_LEVEL"],"example":"MERCHANT"},"target_value":{"description":"绑定目标id","type":"string","example":""}}},"domain.RuleParameterGroupCreate":{"type":"object","required":["access_point","parameter_group_code"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"default_parameter_group":{"description":"默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否","type":"integer","example":1},"parameter_group_code":{"description":"参数组编码","type":"string","example":"CP003_DEFAULT"},"parameter_group_description":{"description":"参数组描述","type":"string","example":"默认参数组"},"rule_param_id_list":{"description":"下属规则参数id(可多个,逗号分割)","type":"string","example":"1,2,3"},"state":{"description":"状态，1-有效，0-失效","type":"integer","example":1}}},"domain.RuleParameterGroupList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"parameter_group_code":{"description":"参数组编码","type":"string","example":"CP003_DEFAULT"}}},"domain.RuleParameterGroupListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.RuleParameterGroupResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.RuleParameterGroupResp":{"type":"object","required":["access_point","id","parameter_group_code"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"default_parameter_group":{"description":"默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否","type":"integer","example":1},"id":{"type":"integer","minimum":0,"example":1},"parameter_group_code":{"description":"参数组编码","type":"string","example":"CP003_DEFAULT"},"parameter_group_description":{"description":"参数组描述","type":"string","example":"默认参数组"},"rule_param_id_list":{"description":"下属规则参数id(可多个,逗号分割)","type":"string","example":"1,2,3"},"state":{"description":"状态，1-有效，0-失效","type":"integer","example":1}}},"domain.RuleParameterGroupUpdate":{"type":"object","required":["access_point","id","parameter_group_code"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP003"},"default_parameter_group":{"description":"默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否","type":"integer","example":1},"id":{"type":"integer","minimum":0,"example":1},"parameter_group_code":{"description":"参数组编码","type":"string","example":"CP003_DEFAULT"},"parameter_group_description":{"description":"参数组描述","type":"string","example":"默认参数组"},"rule_param_id_list":{"description":"下属规则参数id(可多个,逗号分割)","type":"string","example":"1,2,3"},"state":{"description":"状态，1-有效，0-失效","type":"integer","example":1}}},"domain.RuleParameterList":{"type":"object","required":["page_num","page_size"],"properties":{"access_point":{"description":"检查点(可多个,逗号分割)","type":"string","example":"CP003"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"parameter_code":{"description":"参数编码","type":"string","example":"CP003_DEFAULT_ACTION"},"parameter_description":{"description":"参数描述","type":"string","example":"默认操作"},"parameter_type":{"description":"参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes","type":"string","example":"STRING"}}},"domain.RuleParameterListResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/domain.RuleParameterResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"domain.RuleParameterResp":{"type":"object","required":["access_point_list","id","parameter_code"],"properties":{"access_point_list":{"description":"检查点(可多个,逗号分割)","type":"string","example":"CP003"},"id":{"type":"integer","minimum":0,"example":1},"parameter_code":{"description":"参数编码","type":"string","example":"CP003_DEFAULT_ACTION"},"parameter_description":{"description":"参数描述","type":"string","example":"默认操作"},"parameter_type":{"description":"参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes","type":"string","enum":["STRING","NUMBER","LIST","MAP","NUMBER_RANGE","IP_RANGE","BANK_CARD_LIST"],"example":"STRING"},"value_transfer":{"description":"值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer","type":"string","example":""}}},"domain.RuleParameterUpdate":{"type":"object","required":["access_point_list","id","parameter_code"],"properties":{"access_point_list":{"description":"检查点(可多个,逗号分割)","type":"string","example":"CP003"},"id":{"type":"integer","minimum":0,"example":1},"parameter_code":{"description":"参数编码","type":"string","example":"CP003_DEFAULT_ACTION"},"parameter_description":{"description":"参数描述","type":"string","example":"默认操作"},"parameter_type":{"description":"参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes","type":"string","enum":["STRING","NUMBER","LIST","MAP","NUMBER_RANGE","IP_RANGE","BANK_CARD_LIST"],"example":"STRING"},"value_transfer":{"description":"值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer","type":"string","example":""}}},"domain.RuleParameterValueList":{"type":"object","required":["page_num","page_size"],"properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"domain.RuleResp":{"type":"object","required":["access_point","deploy_method","id","priority","rule_code","rule_content","rule_name"],"properties":{"access_point":{"description":"接入点","type":"string","example":"CP003"},"deploy_method":{"description":"上下线方式，manual/auto","type":"string","enum":["manual","auto"],"example":"manual"},"effective_time":{"description":"生效时间","type":"string","example":"2023-06-07T17:07:20+08:00"},"expired_time":{"description":"失效时间","type":"string","example":"2023-06-07T17:07:20+08:00"},"id":{"type":"integer","minimum":0,"example":1},"priority":{"type":"integer"},"remark":{"description":"备注","type":"string","example":"黑名单"},"rule_code":{"description":"规则编号","type":"string","example":"Cp003BlackListCheck"},"rule_content":{"description":"规则内容","type":"string","example":"end"},"rule_name":{"description":"规则名","type":"string","example":"黑名单"},"short_circuit":{"description":"是否支持短路","type":"boolean","example":false},"status":{"description":"状态，Online/Offline","type":"string","enum":["Online","Offline"],"example":"Online"},"synced_at":{"type":"string"}}},"domain.RuleSetStatus":{"type":"object","required":["id"],"properties":{"id":{"type":"integer","example":1},"status":{"type":"string","enum":["Online","Offline"],"example":"Online"}}},"domain.RuleUpdate":{"type":"object","required":["access_point","deploy_method","id","priority","rule_code","rule_content","rule_name"],"properties":{"access_point":{"description":"接入点","type":"string","example":"CP003"},"deploy_method":{"description":"上下线方式，manual/auto","type":"string","enum":["manual","auto"],"example":"manual"},"effective_time":{"description":"生效时间","type":"string","example":"2023-06-07T17:07:20+08:00"},"expired_time":{"description":"失效时间","type":"string","example":"2023-06-07T17:07:20+08:00"},"id":{"type":"integer","minimum":0,"example":1},"priority":{"type":"integer"},"remark":{"description":"备注","type":"string","example":"黑名单"},"rule_code":{"description":"规则编号","type":"string","example":"Cp003BlackListCheck"},"rule_content":{"description":"规则内容","type":"string","example":"end"},"rule_name":{"description":"规则名","type":"string","example":"黑名单"},"short_circuit":{"description":"是否支持短路","type":"boolean","example":false},"status":{"description":"状态，Online/Offline","type":"string","enum":["Online","Offline"],"example":"Online"}}},"entity.DataSourceColumn":{"type":"object","properties":{"column_id":{"type":"string"},"column_name":{"type":"string"},"column_type":{"type":"string"},"comment":{"type":"string"},"data_type":{"type":"string"},"field_type":{"type":"string"},"table_id":{"type":"integer"}}},"entity.Field":{"type":"object","properties":{"is_required":{"type":"boolean"},"name":{"description":"field name","type":"string"}}},"entity.IndicatorRule":{"type":"object","properties":{"column_id":{"description":"列名","type":"string"},"connector":{"description":"连接符(and, or)","type":"string"},"has_left_brackets":{"description":"是否包含左括号","type":"boolean"},"has_right_brackets":{"description":"是否包含右括号","type":"boolean"},"id":{"description":"规则ID,创建时为空","type":"integer"},"operator":{"description":"操作符 ne(!=),gt(\u003e),lt(\u003c),like(Contains),eq(=),lte(\u003c=),gte(\u003e=),ne(!=),in,notin,is_true,is_false","type":"string"},"value":{"description":"右侧比较值信息","type":"string"},"value_type":{"description":"比较值类型(fixed: 固定值, field: 字段比较)","type":"string"}}},"entity.Progress":{"type":"object","properties":{"completed":{"description":"已完成","type":"boolean"},"execute_at":{"description":"执行时间,时间戳","type":"integer"},"executor":{"description":"执行人","type":"string"},"name":{"description":"进度名称","type":"string"}}},"model.CheckPointBusinessConfig":{"type":"object","required":["businessCode"],"properties":{"businessCode":{"type":"string","example":"AUTH"},"pkFieldName":{"description":"主键字段","type":"string","example":"ip"},"secondFieldName":{"description":"第二匹配条件","type":"string","example":"num"},"secondFieldValue":{"type":"string","example":"zz"}}},"model.PlatConfigProgress":{"type":"object","properties":{"check_point_code":{"type":"string"},"id":{"type":"integer"},"progress":{"type":"integer"}}},"model.RmsAlarmContact":{"type":"object","properties":{"alarm_time_config_id":{"description":"时间配置表外键","type":"integer"},"created_at":{"description":"创建时间","type":"string"},"email":{"description":"邮箱","type":"string"},"id":{"description":"主键","type":"integer"},"mobile":{"description":"手机号","type":"string"},"name":{"description":"联系人姓名","type":"string"},"notice_time":{"description":"通知类型","type":"string"},"open_id":{"description":"操作员","type":"string"},"status":{"description":"状态","type":"integer"},"updated_at":{"description":"更新时间","type":"string"}}},"model.RmsAlarmTimeConf":{"type":"object","properties":{"created_at":{"description":"创建时间","type":"string"},"end_time":{"description":"截止时间","type":"integer"},"id":{"description":"主键","type":"integer"},"open_id":{"description":"操作员","type":"string"},"result_code":{"description":"ResultCode","type":"string"},"start_time":{"description":"开始时间","type":"string"},"status":{"description":"状态","type":"integer"},"updated_at":{"description":"更新时间","type":"string"}}},"model.RmsBusinessType":{"type":"object","properties":{"business_code":{"description":"业务编码","type":"string"},"business_name":{"description":"业务名称","type":"string"},"created_at":{"description":"创建时间","type":"string"},"id":{"description":"主键","type":"integer"},"table_name":{"description":"与businessField相关，废弃","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"model.RmsEvent":{"type":"object","properties":{"check_point":{"description":"联系人姓名","type":"string"},"created_at":{"description":"创建时间","type":"string"},"event":{"description":"json显示存储的事件完整内容","type":"string"},"id":{"description":"主键","type":"integer"},"num_field1":{"description":"重要数值类型字段1(值=原始值*1000)，字段名见对应存储配置","type":"number"},"num_field2":{"description":"重要数值类型字段2(值=原始值*1000)，字段名见对应存储配置","type":"number"},"result_code":{"description":"规则结果代码","type":"string"},"result_message":{"description":"规则结果内容","type":"string"},"rule_no":{"description":"规则编号","type":"string"},"rules_not_passed":{"description":"未通过规则","type":"string"},"str_field1":{"description":"重要字符串类型字段1（建议主键），字段名见对应存储配置","type":"string"},"str_field2":{"description":"重要字符串类型字段2，字段名见对应存储配置","type":"string"},"str_field3":{"description":"重要字符串类型字段3，字段名见对应存储配置","type":"string"},"str_field4":{"description":"重要字符串类型字段4，字段名见对应存储配置","type":"string"},"str_field5":{"description":"重要字符串类型字段5，字段名见对应存储配置","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"model.RmsOperatorLog":{"type":"object","properties":{"created_at":{"description":"创建时间","type":"string"},"id":{"description":"主键","type":"integer"},"method":{"description":"动作","type":"string"},"params":{"description":"参数","type":"string"},"updated_at":{"description":"更新时间","type":"string"},"user_id":{"description":"操作员id","type":"string"},"user_name":{"description":"操作员姓名","type":"string"}}},"model.RmsRiskLog":{"type":"object","properties":{"alias":{"description":"拦截别名 sql,xss,signature","type":"string"},"created_at":{"description":"创建时间","type":"string"},"event_explain":{"description":"事件说明","type":"string"},"id":{"description":"主键","type":"integer"},"illegal_param":{"description":"非法参数. 1-是，0-否","type":"integer"},"ori_params":{"description":"源参数","type":"string"},"partner_id":{"description":"商户号","type":"string"},"project_name":{"description":"工程名","type":"string"},"req_ip":{"description":"请求IP","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"model.RmsRuleParameterValue":{"type":"object","properties":{"created_at":{"description":"创建时间","type":"string"},"description":{"description":"描述","type":"string"},"group_id":{"description":"RMS_RULE_PARAMETER_GROUP外键","type":"integer"},"id":{"description":"主键","type":"integer"},"param_id":{"description":"RMS_RULE_PARAMETER外键","type":"integer"},"param_key":{"description":"健，非键值对类型可空","type":"string"},"param_value":{"description":"值","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"model.WpHandleLog":{"type":"object","properties":{"created_at":{"description":"创建时间","type":"string"},"handle_code":{"description":"日志类型: 前台商户=TYPE_PARTNER_HANDLE，后台管理=TYPE_SYSADMIN_HANDLE，网关日志=TYPE_GATEWAY_HANDLE，计划任务日志=TYPE_TASK_HANDLE","type":"string"},"handle_events":{"description":"操作事件","type":"string"},"handle_ip":{"description":"操作IP","type":"string"},"handle_params":{"description":"动作参数[内部使用]","type":"string"},"handle_status":{"description":"事件状态 0=失败 1=成功","type":"integer"},"handle_type":{"description":"日志类型编码 PARTNER_LOGIN = 商户登陆 PARTNER_PAYOUT = 商户提现  PARTNER_PAYIN = 商户充值  PARTNER_SAFETY = 商户安全设置 SYSADMIN_LOG 后台设置日志 TASK_HANDLE_LOG=定时任务设置日志","type":"string"},"hlid":{"type":"integer"},"order_sn":{"description":"订单号：针对订单流程","type":"string"},"partner_id":{"description":"商户ID","type":"string"},"puserid":{"description":"操作员ID","type":"string"}}},"modelv2.BlackWhiteItemExtends":{"type":"object","additionalProperties":true},"modelv2.RmsOperatorLog":{"type":"object","properties":{"created_at":{"type":"string"},"id":{"description":"主键","type":"integer"},"method":{"description":"动作","type":"string"},"params":{"description":"参数","type":"string"},"updated_at":{"type":"string"},"user_id":{"description":"操作员id","type":"string"},"user_name":{"description":"操作员姓名","type":"string"}}},"modelv2.RmsRiskLog":{"type":"object","properties":{"alias":{"description":"拦截别名 sql,xss,signature","type":"string"},"created_at":{"type":"string"},"event_explain":{"description":"事件说明","type":"string"},"id":{"description":"主键","type":"integer"},"illegal_param":{"description":"非法参数. 1-是，0-否","type":"integer"},"ori_params":{"description":"源参数","type":"string"},"partner_id":{"description":"商户号","type":"string"},"project_name":{"description":"工程名","type":"string"},"req_ip":{"description":"请求IP","type":"string"},"updated_at":{"type":"string"}}},"request_parameters.AddBlackWhiteItem":{"type":"object","required":["bwl_id"],"properties":{"bwl_id":{"description":"黑白名单ID","type":"integer","example":1},"end_date":{"description":"结束日期，时间戳","type":"integer","example":1},"extends":{"description":"扩展字段,一个map，key为字段ID,int64类型，value类型不定义","type":"object","additionalProperties":true},"permanent":{"description":"是否永久","type":"boolean","example":true},"start_date":{"description":"开始日期，时间戳","type":"integer","example":1}}},"request_parameters.BlackWhiteAudit":{"type":"object","required":["id"],"properties":{"id":{"description":"审核记录ID","type":"integer","example":1},"pass":{"description":"是否通过 true:通过 false：拒绝","type":"boolean","example":true},"reject_reason":{"type":"string","example":"拒绝原因"}}},"request_parameters.CheckPointCreate":{"type":"object","required":["business_type","code","name"],"properties":{"always_run":{"type":"integer"},"business_type":{"description":"业务类型 1000-banking, 3000-acquiring, 4000-issuing","type":"integer","enum":[1000,3000,4000]},"check_duplicate":{"description":"数据重复检查","type":"boolean"},"code":{"description":"编码","type":"string"},"name":{"description":"名称","type":"string"},"remark":{"description":"备注","type":"string"}}},"request_parameters.CheckPointList":{"type":"object","properties":{"code":{"description":"编码","type":"string"},"label":{"description":"名称","type":"string"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"request_parameters.CheckPointUpdate":{"type":"object","required":["code","id","name"],"properties":{"always_run":{"type":"integer"},"check_duplicate":{"description":"数据重复检查","type":"boolean"},"code":{"description":"编码","type":"string"},"id":{"type":"integer","minimum":0},"name":{"description":"名称","type":"string"},"remark":{"description":"备注","type":"string"}}},"request_parameters.ConfigProgressCreate":{"type":"object","required":["access_point","progress"],"properties":{"access_point":{"description":"接入点编码","type":"string","example":"CP003"},"progress":{"description":"填写进度","type":"integer","example":1}}},"request_parameters.CreateBlackWhiteRecord":{"type":"object","required":["name","type"],"properties":{"access_points":{"description":"接入点名称","type":"array","items":{"type":"string"},"example":["access_point_name"]},"fields":{"description":"字段，更新时候不处理了字段","type":"array","items":{"$ref":"#/definitions/entity.Field"}},"name":{"description":"名单名称，更新时候不处理了字段","type":"string","example":"test"},"note":{"description":"备注","type":"string","maxLength":500,"example":"test note"},"type":{"description":"名单类型 1.黑名单 2.白名单 ，更新时候不处理了字段","type":"integer","example":1}}},"request_parameters.CreateDataSourceTable":{"type":"object","required":["alias","business_type","data_source_id","name"],"properties":{"alias":{"description":"表别名","type":"string"},"business_type":{"description":"业务类型 1000=Banking，3000=Acquiring，4000=Issuing","type":"integer"},"data_source_id":{"description":"数据源ID","type":"integer"},"desc":{"description":"介绍","type":"string"},"name":{"description":"表名","type":"string"}}},"request_parameters.CreateIndicator":{"type":"object","required":["access_point","indicator_name","measure_type","table_id"],"properties":{"access_point":{"description":"接入点","type":"string"},"indicator_name":{"description":"指标名称","type":"string"},"measure_type":{"description":"度量类型 -agg select","type":"string"},"measures":{"description":"度量列表","type":"array","items":{"$ref":"#/definitions/request_parameters.Measure"}},"remark":{"description":"备注","type":"string"},"rule_preview":{"type":"string"},"rules":{"description":"规则列表","type":"array","items":{"$ref":"#/definitions/entity.IndicatorRule"}},"table_id":{"description":"关联的表名","type":"integer"},"time_window":{"description":"时间窗口","allOf":[{"$ref":"#/definitions/request_parameters.TimeWindow"}]}}},"request_parameters.CreateScriptIndicator":{"type":"object","required":["access_point","data_source_id","indicator_name","script"],"properties":{"access_point":{"description":"接入点","type":"string"},"data_source_id":{"description":"数据源ID","type":"integer"},"indicator_name":{"description":"指标名称","type":"string"},"remark":{"description":"备注","type":"string"},"script":{"description":"关联的表名","type":"string"}}},"request_parameters.DataSourceCreate":{"type":"object","required":["address","database_name","password","port","source_name","source_type","username"],"properties":{"address":{"description":"服务器地址","type":"string","maxLength":255,"example":"********"},"database_name":{"description":"数据库名称","type":"string","maxLength":50,"example":"crm_db"},"description":{"description":"描述","type":"string","maxLength":255,"example":"CRM系统数据库"},"password":{"description":"密码","type":"string","maxLength":255,"example":"password123"},"port":{"description":"端口","type":"integer","example":3306},"source_name":{"description":"数据源名称","type":"string","maxLength":50,"example":"CRM_KYC_Master"},"source_type":{"description":"数据源类型：mysql,doris","type":"string","maxLength":50,"example":"Mysql"},"username":{"description":"用户名","type":"string","maxLength":50,"example":"root"}}},"request_parameters.DataSourceList":{"type":"object","properties":{"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"source_name":{"description":"数据源名称","type":"string"},"source_type":{"description":"数据源类型：mysql,doris","type":"string"},"valid":{"description":"是否有效，0：全部 1:有效 2:无效","type":"integer"}}},"request_parameters.DataSourceTest":{"type":"object","required":["address","database_name","password","port","source_name","source_type","username"],"properties":{"address":{"description":"服务器地址","type":"string","maxLength":255,"example":"********"},"database_name":{"description":"数据库名称","type":"string","maxLength":50,"example":"crm_db"},"password":{"description":"密码","type":"string","maxLength":255,"example":"password123"},"port":{"description":"端口","type":"integer","example":3306},"source_name":{"description":"数据源名称","type":"string","maxLength":50,"example":"CRM_KYC_Master"},"source_type":{"description":"数据源类型：mysql,doris","type":"string","maxLength":50,"example":"Mysql"},"username":{"description":"用户名","type":"string","maxLength":50,"example":"root"}}},"request_parameters.DataSourceUpdate":{"type":"object","required":["address","database_name","id","password","port","source_name","source_type","username"],"properties":{"address":{"description":"服务器地址","type":"string","maxLength":255,"example":"********"},"database_name":{"description":"数据库名称","type":"string","maxLength":50,"example":"crm_db"},"description":{"description":"描述","type":"string","maxLength":255,"example":"CRM系统数据库"},"id":{"description":"数据源ID","type":"integer","example":1},"password":{"description":"密码","type":"string","maxLength":255,"example":"password123"},"port":{"description":"端口","type":"integer","example":3306},"source_name":{"description":"数据源名称","type":"string","maxLength":50,"example":"CRM_KYC_Master"},"source_type":{"description":"数据源类型：mysql,doris","type":"string","maxLength":50,"example":"Mysql"},"username":{"description":"用户名","type":"string","maxLength":50,"example":"root"}}},"request_parameters.FilterFieldCreate":{"type":"object","required":["access_point","enumeration_value","field_name"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"enumeration_value":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"string","example":"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"remark":{"description":"备注","type":"string","example":"业务类型"}}},"request_parameters.FilterFieldList":{"type":"object","properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"request_parameters.FilterFieldRetrieve":{"type":"object","properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"field_name":{"description":"字段名","type":"string","example":"bizType"}}},"request_parameters.FilterFieldUpdate":{"type":"object","required":["access_point","enumeration_value","field_name","id"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"enumeration_value":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"string","example":"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"id":{"type":"integer","minimum":0,"example":1},"remark":{"description":"备注","type":"string","example":"业务类型"}}},"request_parameters.GenerateIndicatorSql":{"type":"object","required":["indicator_id"],"properties":{"indicator_id":{"description":"指标ID","type":"integer"},"version_id":{"description":"版本ID","type":"integer"}}},"request_parameters.GetBlackWhiteList":{"type":"object","required":["type"],"properties":{"access_points":{"description":"接入点：access_point_name","type":"array","items":{"type":"string"}},"name":{"description":"名单名称","type":"string","example":"test"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"required_fields":{"description":"必填字段筛选，值为字段对应的ID","type":"array","items":{"type":"string"},"example":["account_id"]},"type":{"description":"名单类型 1.黑名单 2.白名单","type":"integer","example":1},"valid":{"description":"0:忽略 1.有效","type":"integer"}}},"request_parameters.GetDataSourceTableList":{"type":"object","properties":{"alias":{"description":"表别名","type":"string"},"business_type":{"description":"业务类型","type":"integer"},"data_source_id":{"description":"数据源ID","type":"integer"},"name":{"description":"表名","type":"string"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"request_parameters.GetIndicatorList":{"type":"object","properties":{"access_point":{"description":"接入点","type":"string"},"indicator_name":{"description":"指标名称","type":"string"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"table_id":{"description":"关联的表名","type":"integer"}}},"request_parameters.GetIndicatorScriptList":{"type":"object","properties":{"access_point":{"description":"接入点","type":"string"},"indicator_name":{"description":"指标名称","type":"string"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"request_parameters.GetIndicatorVersion":{"type":"object","required":["indicator_id"],"properties":{"indicator_id":{"description":"指标ID","type":"integer"},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10}}},"request_parameters.ListBlackWhiteField":{"type":"object","required":["access_points"],"properties":{"access_points":{"description":"接入点名称","type":"array","items":{"type":"string"},"example":["access_point_name"]}}},"request_parameters.Measure":{"type":"object","properties":{"agg_type":{"description":"聚合类型 - count, sum, avg, max, min","type":"string"},"condition_name":{"description":"条件名称","type":"string"},"id":{"description":"更新带上原始ID,新增ID为空","type":"integer"},"measure_field":{"description":"度量字段","type":"string"}}},"request_parameters.ReleaseIndicator":{"type":"object","required":["id"],"properties":{"id":{"description":"指标ID","type":"integer"}}},"request_parameters.RiskLogList":{"type":"object","properties":{"date_type":{"type":"string","example":"created_at"},"end_time":{"type":"integer","example":**********},"page_num":{"type":"integer","example":1},"page_size":{"type":"integer","example":10},"start_time":{"type":"integer","example":**********}}},"request_parameters.SetBlackWhiteStatus":{"type":"object","required":["id"],"properties":{"id":{"description":"黑白名单记录ID","type":"integer","example":1},"status":{"description":"状态 0.禁用 1.启用","type":"integer","example":0}}},"request_parameters.SubmitAudit":{"type":"object","required":["id"],"properties":{"id":{"description":"黑白名单记录ID","type":"integer","example":1}}},"request_parameters.TestIndicatorSql":{"type":"object","required":["indicator_id"],"properties":{"indicator_id":{"description":"指标ID","type":"integer"},"test_data":{"description":"测试数据","type":"string"},"version_id":{"description":"版本ID","type":"integer"}}},"request_parameters.TestScriptIndicatorSql":{"type":"object","required":["indicator_id"],"properties":{"indicator_id":{"description":"指标ID","type":"integer"},"version_id":{"description":"版本ID","type":"integer"}}},"request_parameters.TimeWindow":{"type":"object","properties":{"column_id":{"type":"string"},"end":{"description":"当type=4,5生效，数据为类似00:00数据","type":"string"},"excluding":{"description":"是否排除,当时间单位位天、周、月时生效，勾选不包含当前时间,当type=2,3,4,5时不生效","type":"boolean"},"start":{"description":"当type=3，5生效，数据为类似00:00数据","type":"string"},"type":{"description":"时间窗口类型 1.Sliding Window 2.Fixed Window 3.Before 4.After 5.Between","type":"integer"},"unit":{"description":"时间窗口单位,当type为1时，1:Minutes/2:Hours/3:Days/4:Weeks/5:Months,当type为2时，1:Daily 2:Monthly 3:Annual,当type=3,4,5时不生效","type":"integer"},"value":{"description":"时间窗口值 当type为1时，表示时间窗口值，当type=2,3，4,5时不生效","type":"integer"}}},"request_parameters.UpdateBlackWhiteItem":{"type":"object","required":["bwl_id","id"],"properties":{"bwl_id":{"description":"黑白名单ID","type":"integer","example":1},"end_date":{"description":"结束日期，时间戳","type":"integer","example":1},"extends":{"description":"扩展字段,一个map，key为字段ID,int64类型，value类型不定义","type":"object","additionalProperties":true},"id":{"description":"黑白名单记录明细ID","type":"integer","example":1},"permanent":{"description":"是否永久","type":"boolean","example":true},"start_date":{"description":"开始日期，时间戳","type":"integer","example":1}}},"request_parameters.UpdateBlackWhiteRecord":{"type":"object","properties":{"access_points":{"description":"接入点名称","type":"array","items":{"type":"string"},"example":["access_point_name"]},"id":{"description":"名单ID,更新时必填","type":"integer","example":1},"note":{"description":"备注","type":"string","maxLength":500,"example":"test note"}}},"request_parameters.UpdateIndicator":{"type":"object","required":["id"],"properties":{"id":{"description":"指标ID","type":"integer"},"indicator_name":{"description":"指标名称","type":"string"},"measures":{"description":"度量列表","type":"array","items":{"$ref":"#/definitions/request_parameters.Measure"}},"remark":{"description":"备注","type":"string"},"rule_preview":{"type":"string"},"rules":{"description":"规则列表","type":"array","items":{"$ref":"#/definitions/entity.IndicatorRule"}},"time_window":{"description":"时间窗口","allOf":[{"$ref":"#/definitions/request_parameters.TimeWindow"}]}}},"request_parameters.UpdateIndicatorStatus":{"type":"object","required":["id"],"properties":{"disable":{"description":"是否禁用","type":"boolean"},"id":{"description":"指标ID","type":"integer"}}},"request_parameters.UpdateScriptIndicator":{"type":"object","required":["id","script"],"properties":{"id":{"description":"指标ID","type":"integer"},"indicator_name":{"type":"string"},"remark":{"description":"备注","type":"string"},"script":{"description":"关联的表名","type":"string"}}},"response_parameters.BlackWhiteAuditDetail":{"type":"object","properties":{"bwl_id":{"description":"黑白名单ID","type":"integer","example":1},"detail":{"description":"提交详情","type":"string","example":"test"},"list_name":{"description":"名单名称","type":"string","example":"test"},"list_type":{"description":"名单类型 1.黑名单 2.白名单","type":"integer","example":1},"mode":{"description":"操作类型 1.创建操作 2.编辑操作 3.删除名单操作 4.开启状态操作 5.关闭状态操作","type":"integer","example":1},"state":{"description":"审核状态:1.待审核 2.审核通过 3.审核拒绝","type":"integer"},"submission_time":{"description":"提交时间,时间戳","type":"integer","example":1672531200},"submitter":{"description":"提交人","type":"string","example":"test"}}},"response_parameters.BlackWhiteAuditList":{"type":"object","properties":{"audit_time":{"description":"审核时间,时间戳","type":"integer","example":1672531200},"auditor":{"description":"审核人","type":"string","example":"test"},"bwl_id":{"description":"黑白名单ID","type":"integer","example":1},"id":{"description":"审核记录ID","type":"integer","example":1},"list_name":{"description":"名单名称","type":"string","example":"test"},"list_type":{"description":"名单类型 1.黑名单 2.白名单","type":"integer","example":1},"mode":{"description":"操作类型 1.创建操作 2.编辑操作 3.删除名单操作 4.开启状态操作 5.关闭状态操作","type":"integer","example":1},"state":{"description":"状态 1.待审核 2.审核通过 3.审核拒绝","type":"integer","example":1},"submission_time":{"description":"提交时间,时间戳","type":"integer","example":1672531200},"submitter":{"description":"提交人","type":"string","example":"test"}}},"response_parameters.BlackWhiteItems":{"type":"object","properties":{"bwi_id":{"description":"黑白名单记录ID","type":"integer","example":1},"expiration_date":{"description":"结束日期，时间戳","type":"integer"},"extends":{"description":"扩展字段","allOf":[{"$ref":"#/definitions/modelv2.BlackWhiteItemExtends"}]},"permanent":{"description":"是否永久","type":"boolean","example":true},"remaining_day":{"description":"剩余时间","type":"integer","example":1},"start_date":{"description":"开始日期，时间戳","type":"integer"}}},"response_parameters.BlackWhiteList":{"type":"object","properties":{"access_points":{"description":"接入点名称","type":"array","items":{"type":"string"}},"created_at":{"description":"创建时间,秒级时间戳","type":"integer","example":1717728000},"created_by":{"description":"最后编辑人","type":"string","example":"test"},"id":{"description":"名单ID","type":"integer","example":1},"name":{"description":"名单名称","type":"string","example":"test"},"required_fields":{"description":"必填字段，值为字段对应的名称","type":"array","items":{"type":"string"},"example":["1","2"]},"state":{"description":"状态 0.草稿 1.编辑状态草稿 2.待审核 3.审核通过 4.审核拒绝 5.移除","type":"integer","example":1},"status":{"description":"状态开关 0:关闭 1:开启","type":"integer","example":1},"total_items":{"description":"名单当前总数","type":"integer","example":1},"type":{"description":"名单类型 1.黑名单 2.白名单","type":"integer","example":1},"valid_items":{"description":"名单当前生效数","type":"integer","example":1}}},"response_parameters.BlackWhiteOperatorLogs":{"type":"object","properties":{"created_at":{"description":"创建时间,时间戳","type":"integer","example":1672531200},"log":{"description":"日志","type":"string","example":"test"},"operator":{"description":"操作员","type":"string","example":"test"}}},"response_parameters.CheckPointListResp":{"type":"object","properties":{"items":{"type":"array","items":{"$ref":"#/definitions/response_parameters.CheckPointResp"}},"total":{"type":"integer"}}},"response_parameters.CheckPointNameLabel":{"type":"object","properties":{"code":{"type":"string"},"label":{"type":"string"}}},"response_parameters.CheckPointResp":{"type":"object","properties":{"always_run":{"type":"integer"},"business_type":{"type":"integer"},"check_duplicate":{"type":"boolean"},"code":{"type":"string"},"config_progress":{"type":"integer"},"default_primary_key":{"type":"string"},"filter_fields":{"type":"string"},"id":{"type":"integer"},"name":{"type":"string"},"remark":{"type":"string"}}},"response_parameters.ConfigProgressResp":{"type":"object","properties":{"access_point":{"description":"接入点编码","type":"string","example":"CP003"},"id":{"type":"integer","example":1},"progress":{"description":"填写进度","type":"integer","example":1}}},"response_parameters.CreateBlackWhiteItem":{"type":"object","properties":{"bwi_id":{"description":"黑白名单记录ID","type":"integer","example":1}}},"response_parameters.DataSourceDetail":{"type":"object","properties":{"address":{"description":"服务器地址","type":"string"},"conn_status":{"description":"连接状态","type":"string"},"database_name":{"description":"数据库名称","type":"string"},"description":{"description":"描述","type":"string"},"fail_reason":{"type":"string"},"id":{"description":"主键","type":"integer"},"last_check_time":{"description":"最后检查时间","type":"string"},"password":{"description":"密码","type":"string"},"port":{"description":"端口","type":"integer"},"source_name":{"description":"数据源名称","type":"string"},"source_type":{"description":"数据源类型","type":"string"},"updated_at":{"description":"更新时间","type":"string"},"username":{"description":"用户名","type":"string"}}},"response_parameters.DataSourceTableDetail":{"type":"object","properties":{"alias":{"description":"表别名","type":"string"},"associated_indicator":{"description":"关联的指标","type":"array","items":{"type":"string"}},"business_type":{"description":"业务类型","type":"integer"},"business_type_name":{"description":"业务类型名称","type":"string"},"data_source_name":{"description":"数据源名称","type":"string"},"desc":{"description":"介绍","type":"string"},"fields":{"description":"字段列表","type":"array","items":{"$ref":"#/definitions/response_parameters.DataSourceTableField"}},"id":{"description":"主键ID","type":"integer"},"last_modified_by":{"description":"最后修改人","type":"string"},"name":{"description":"表名","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"response_parameters.DataSourceTableField":{"type":"object","properties":{"column_type":{"description":"字段类型","type":"string"},"desc":{"description":"字段描述","type":"string"},"name":{"description":"字段名称","type":"string"}}},"response_parameters.DataSourceTableList":{"type":"object","properties":{"alias":{"description":"表别名","type":"string"},"associated_indicator":{"description":"关联的指标","type":"array","items":{"type":"string"}},"business_type":{"description":"业务类型","type":"integer"},"business_type_name":{"description":"业务类型名称","type":"string"},"data_source_id":{"description":"数据源ID","type":"integer"},"id":{"description":"主键ID","type":"integer"},"name":{"description":"表名","type":"string"}}},"response_parameters.DataSourceTestResp":{"type":"object","properties":{"connected":{"description":"是否连接成功","type":"boolean"},"message":{"description":"连接信息","type":"string"}}},"response_parameters.FilterFieldResp":{"type":"object","required":["access_point","enumeration_value","field_name","id"],"properties":{"access_point":{"description":"检查点","type":"string","example":"CP004"},"enumeration_value":{"description":"枚举类型列表的json，各枚举由编码和名称构成","type":"string","example":"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]"},"field_name":{"description":"字段名","type":"string","example":"bizType"},"id":{"type":"integer","minimum":0,"example":1},"remark":{"description":"备注","type":"string","example":"业务类型"}}},"response_parameters.GenerateIndicatorSqlResponse":{"type":"object","properties":{"placeholders":{"description":"占位符","type":"array","items":{"type":"string"}},"sql":{"description":"指标SQL","type":"string"}}},"response_parameters.GetBlackWhiteRecord":{"type":"object","required":["id","name","type"],"properties":{"access_points":{"description":"接入点名称","type":"array","items":{"type":"string"}},"fields":{"description":"字段","type":"array","items":{"$ref":"#/definitions/entity.Field"}},"id":{"description":"名单ID","type":"integer","example":1},"last_audit_state":{"description":"最后一次审核状态 1.待审核 2.审核通过 3.审核拒绝","type":"integer","example":1},"list_effective_time":{"description":"审批通过的时间，时间戳","type":"integer"},"name":{"description":"名单名称","type":"string","example":"test"},"note":{"description":"备注","type":"string","example":"test note"},"state":{"description":"状态 0.草稿 1.编辑状态草稿 2.待审核 3.审核通过 4.审核拒绝 5.移除","type":"integer","example":1},"status":{"description":"状态开关 0:关闭 1:开启","type":"integer","example":1},"submitter":{"description":"最后一次提交人名字","type":"string"},"type":{"description":"名单类型 1.黑名单 2.白名单","type":"integer","example":1}}},"response_parameters.IndicatorCreateResponse":{"type":"object","properties":{"indicator_id":{"type":"integer"},"version_id":{"type":"integer"}}},"response_parameters.IndicatorDetail":{"type":"object","properties":{"access_point":{"description":"访问点列表","type":"string"},"current_version":{"description":"当前版本","type":"string"},"current_version_id":{"description":"当前版本ID","type":"integer"},"id":{"description":"指标ID","type":"integer"},"indicator_name":{"description":"指标名称","type":"string"},"measure_type":{"description":"度量类型 -agg select","type":"string"},"measures":{"description":"度量列表","type":"array","items":{"$ref":"#/definitions/response_parameters.Measure"}},"remark":{"description":"备注","type":"string"},"rule_preview":{"type":"string"},"rules":{"description":"规则列表","type":"array","items":{"$ref":"#/definitions/entity.IndicatorRule"}},"table_id":{"description":"关联的表名","type":"integer"},"time_window":{"description":"时间窗口","allOf":[{"$ref":"#/definitions/response_parameters.TimeWindow"}]}}},"response_parameters.IndicatorList":{"type":"object","properties":{"current_version":{"description":"当前版本","type":"string"},"current_version_id":{"description":"当前版本ID","type":"integer"},"has_draft":{"description":"是否存在草稿","type":"boolean"},"id":{"description":"指标ID","type":"integer"},"indicator_name":{"description":"指标名称","type":"string"},"measures":{"description":"度量","type":"string"},"status":{"description":"状态  online,offline,draft","type":"string"},"table_name":{"description":"关联的表名","type":"string"},"time_window":{"description":"时间窗口","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"response_parameters.IndicatorScriptDetail":{"type":"object","properties":{"current_version":{"description":"当前版本","type":"string"},"current_version_id":{"description":"当前版本ID","type":"integer"},"data_source_id":{"description":"数据源ID","type":"integer"},"id":{"description":"指标ID","type":"integer"},"indicator_name":{"description":"指标名称","type":"string"},"remark":{"description":"备注","type":"string"},"script":{"description":"规则代码","type":"string"}}},"response_parameters.IndicatorScriptList":{"type":"object","properties":{"current_version":{"description":"当前版本","type":"string"},"has_draft":{"description":"是否存在草稿","type":"boolean"},"id":{"description":"指标ID","type":"integer"},"indicator_name":{"description":"指标名称","type":"string"},"script":{"type":"string"},"status":{"description":"状态 online,offline,draft","type":"string"},"updated_at":{"description":"更新时间","type":"string"}}},"response_parameters.IndicatorVersionList":{"type":"object","properties":{"desc":{"description":"描述","type":"string"},"indicator_id":{"description":"指标ID","type":"integer"},"last_modified_at":{"description":"最后修改时间","type":"string"},"modify_by":{"description":"修改人","type":"string"},"status":{"description":"状态  Pending changes,Curerent version,Archived","type":"string"},"version":{"description":"版本号","type":"string"},"version_id":{"description":"版本ID","type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_BlackWhiteAuditList":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.BlackWhiteAuditList"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_BlackWhiteItems":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.BlackWhiteItems"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_BlackWhiteList":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.BlackWhiteList"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_BlackWhiteOperatorLogs":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.BlackWhiteOperatorLogs"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_DataSourceDetail":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.DataSourceDetail"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_DataSourceTableList":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.DataSourceTableList"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_FilterFieldResp":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.FilterFieldResp"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_IndicatorList":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.IndicatorList"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_IndicatorScriptList":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.IndicatorScriptList"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.ListResponsePointer-response_parameters_IndicatorVersionList":{"type":"object","properties":{"item_list":{"type":"array","items":{"$ref":"#/definitions/response_parameters.IndicatorVersionList"}},"total_item":{"type":"integer"},"total_page":{"type":"integer"}}},"response_parameters.Measure":{"type":"object","properties":{"agg_type":{"description":"度量类型 - count, sum, avg, max, min","type":"string"},"condition_name":{"description":"条件名称","type":"string"},"id":{"description":"度量ID","type":"integer"},"measure_field":{"description":"度量字段","type":"string"}}},"response_parameters.NormalCreateResponse":{"type":"object","properties":{"id":{"type":"integer"}}},"response_parameters.TimeWindow":{"type":"object","properties":{"column_id":{"type":"string"},"end":{"description":"当type=4,5生效，数据为类似2025-01-01 00:00:00数据","type":"string"},"excluding":{"description":"是否排除,当时间单位位天、周、月时生效，勾选不包含当前时间,当type=2,3,4,5时不生效","type":"boolean"},"start":{"description":"当type=3，5生效，数据为类似2025-01-01 00:00:00数据","type":"string"},"type":{"description":"时间窗口类型 1.Sliding Window 2.Fixed Window 3.Before 4.After 5.Between","type":"integer"},"unit":{"description":"时间窗口单位,当type为1时，1:Minutes/2:Hours/3:Days/4:Weeks/5:Months,当type=2,3,4,5时不生效","type":"integer"},"value":{"description":"时间窗口值 当type为1时，表示时间窗口值，当type为2时，1:Daily 2:Monthly 3:Annual,当type=3，4,5时不生效","type":"integer"}}}}}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "",
	Description:      "",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
