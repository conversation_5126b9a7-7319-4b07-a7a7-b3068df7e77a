package consts

const (
	IndicatorMeasureTypeAgg    = "agg"
	IndicatorMeasureTypeSelect = "select"

	//1.Sliding Window 2.Fixed Window 3.Before 4.After 5.Between
	IndicatorMeasureWindowTypeSliding = 1
	IndicatorMeasureWindowTypeFixed   = 2
	IndicatorMeasureWindowTypeBefore  = 3
	IndicatorMeasureWindowTypeAfter   = 4
	IndicatorMeasureWindowTypeBetween = 5

	IndicatorRuleValueTypeFixed = "fixed"
	IndicatorRuleValueTypeField = "field"
)

var IndicatorVersionTimeWindowTypes = map[int]string{
	IndicatorMeasureWindowTypeSliding: "sliding window",
	IndicatorMeasureWindowTypeFixed:   "fixed window",
	IndicatorMeasureWindowTypeBefore:  "before",
	IndicatorMeasureWindowTypeAfter:   "after",
	IndicatorMeasureWindowTypeBetween: "between",
}

const (
	IndicatorTimeWindowUnitMinutes = 1
	IndicatorTimeWindowUnitHours   = 2
	IndicatorTimeWindowUnitDays    = 3
	IndicatorTimeWindowUnitWeeks   = 4
	IndicatorTimeWindowUnitMonths  = 5

	IndicatorTimeWindowUnitDaily   = 1
	IndicatorTimeWindowUnitMonthly = 2
	IndicatorTimeWindowUnitAnnual  = 3
)

var IndicatorVersionSlidingTimeWindowUnits = map[int]string{
	IndicatorTimeWindowUnitMinutes: "minutes",
	IndicatorTimeWindowUnitHours:   "hours",
	IndicatorTimeWindowUnitDays:    "days",
	IndicatorTimeWindowUnitWeeks:   "weeks",
	IndicatorTimeWindowUnitMonths:  "months",
}

var IndicatorVersionFixedTimeWindowUnits = map[int]string{
	IndicatorTimeWindowUnitDaily:   "Daily",
	IndicatorTimeWindowUnitMonthly: "Monthly",
	IndicatorTimeWindowUnitAnnual:  "Annual",
}
var IndicatorMeasureAggTypeMap = map[string]bool{
	"count": true,
	"sum":   true,
	"avg":   true,
	"max":   true,
	"min":   true,
}
var IndicatorRuleOperatorMap = map[string]bool{
	"gt":       true,
	"lt":       true,
	"eq":       true,
	"lte":      true,
	"gte":      true,
	"ne":       true,
	"in":       true,
	"notin":    true,
	"like":     true,
	"is_true":  true,
	"is_false": true,
}

var IndicatorRuleValueTypeMap = map[string]bool{
	"fixed": true,
	"field": true,
}
