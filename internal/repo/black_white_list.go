package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type BlackWhiteListRepo interface {
	TableName() string
	// GenerateBlackWhiteCache(ctx context.Context, maxAuditID int64, dayTime time.Time, bwl *RmsBlackWhiteList) (bwlCache *entity.BlackWhiteCacheMetadata, err error)
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error)
	GetBlackList(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error)
	GetWhiteList(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error)
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteList, error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsBlackWhiteList) error
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	UpdateChangeData(ctx context.Context, session *gorm.DB, id int64, submitter string, changeData string) error
	AuditPass(ctx context.Context, session *gorm.DB, id int64) (err error)
	AuditReject(ctx context.Context, session *gorm.DB, id int64) (err error)
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsBlackWhiteList) (err error)
	Fields(ctx context.Context, bwlID int64) (fields entity.Fields, err error)
	Remove(ctx context.Context, session *gorm.DB, id int64, submitter string) error
}
