package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// IndicatorRepo 指标配置仓库接口
type IndicatorRepo interface {
	// TableName 表名
	TableName() string
	// Create 创建指标配置
	Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicator) error
	// Update 更新指标配置
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicator) error
	// Updates 更新指标配置版本
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	// First 获取单个指标配置
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicator, error)
	// List 获取指标配置列表
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicator, err error)
	// Delete 删除指标配置
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
}

// IndicatorVersionRepo 指标配置版本仓库接口
type IndicatorVersionRepo interface {
	// TableName 表名
	TableName() string
	// Create 创建指标配置版本
	Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicatorVersion) error
	// Updates 更新指标配置版本
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	// First 获取单个指标配置版本
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicatorVersion, error)
	// List 获取指标配置版本列表
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicatorVersion, err error)
	// Count 获取指标配置版本数量
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (int64, error)
	// NewVersion 创建新的版本号
	NewVersion(ctx context.Context, indicatorID int64) string
	// Delete 删除指标配置
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
}

// IndicatorMeasureRepo 指标配置度量仓库接口
type IndicatorMeasureRepo interface {
	// TableName 表名
	TableName() string
	// Update 更新指标配置
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorMeasure) error
	// BatchCreate 批量创建指标配置度量
	BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorMeasure) error
	// List 获取指标配置度量列表
	List(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) ([]*modelv2.RmsIndicatorMeasure, error)
	// Delete 删除指标配置度量
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
}

// IndicatorRuleRepo 指标配置规则仓库接口
type IndicatorRuleRepo interface {
	// TableName 表名
	TableName() string
	// Update 更新指标配置
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorRule) error
	// Updates 更新指标配置
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	// BatchCreate 批量创建指标配置规则
	BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorRule) error
	// List 获取指标配置规则列表
	List(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) ([]*modelv2.RmsIndicatorRule, error)
	// Delete 删除指标配置规则
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
}

// IndicatorVersionHistoryRepo 指标配置版本历史仓库接口
type IndicatorVersionHistoryRepo interface {
	// TableName 表名
	TableName() string
	// Create 创建指标配置版本历史
	Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicatorVersionHistory) error
	// BatchCreate 批量创建指标配置版本历史
	BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorVersionHistory) error
	// Update 更新指标配置版本历史
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorVersionHistory) error
	// Updates 更新指标配置版本历史
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	// First 获取单个指标配置版本历史
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicatorVersionHistory, error)
	// List 获取指标配置版本历史列表
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicatorVersionHistory, err error)
	// Delete 删除指标配置版本历史
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	// AnalyzeVersionAndSave 分析版本并保存
	AnalyzeVersionAndSave(ctx context.Context, is_script bool, user string, oldVersion *modelv2.RmsIndicatorVersion, newVersion *modelv2.RmsIndicatorVersion) (err error)
}
