// internal/repo/data_source.go
package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// DataSourceRepo 数据源仓库接口
type DataSourceRepo interface {
	// TableName 返回表名
	TableName() string
	// Create 创建数据源
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsDataSource) error
	// Update 更新数据源
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsDataSource) error
	// Updates 批量更新数据源字段
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	// Delete 删除数据源
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	// List 获取数据源列表
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsDataSource, err error)
	// Count 统计数据源数量
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	// First 获取单条数据源记录
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsDataSource, error)
	// TestConnection 测试数据源连接
	TestConnection(ctx context.Context, dataSource *modelv2.RmsDataSource) (bool, error)
	GetConnection(dataSource *modelv2.RmsDataSource) (*gorm.DB, error)
}
