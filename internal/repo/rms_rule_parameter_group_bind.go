package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type RmsRuleParameterGroupBindRepo interface {
	Create(ctx context.Context, session *gorm.DB, models ...*modelv2.RmsRuleParameterGroupBind) (err error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRuleParameterGroupBind) error
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRuleParameterGroupBind, err error)
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRuleParameterGroupBind, error)
	GetByGroupID(ctx context.Context, groupID int64) (*modelv2.RmsRuleParameterGroupBind, error)
	ListByTargetType(ctx context.Context, targetType string) ([]*modelv2.RmsRuleParameterGroupBind, error)
	ListByTargetID(ctx context.Context, targetID string) ([]*modelv2.RmsRuleParameterGroupBind, error)
}
