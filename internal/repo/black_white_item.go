package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type BlackWhiteItemRepo interface {
	GroupCount(ctx context.Context, idColumn string, fn func(tx *gorm.DB) *gorm.DB) (res []*modelv2.GroupIdCount, err error)
	GetBlackWhiteItems(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteItem, err error) //获取黑白名单列表
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteItem, error)
	GetParentItems(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (res map[int64]*modelv2.RmsBlackWhiteItem, err error) //获取单条名单记录
	Create(ctx context.Context, session *gorm.DB, m *modelv2.RmsBlackWhiteItem) (int64, error)                               //创建名单记录
	BatchCreate(ctx context.Context, session *gorm.DB, m []*modelv2.RmsBlackWhiteItem) error                                 //批量创建名单记录
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error                                       //删除名单记录
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	Remove(ctx context.Context, session *gorm.DB, id int64) (err error)
	AuditPass(ctx context.Context, session *gorm.DB, bwlID int64) error
	AuditReject(ctx context.Context, session *gorm.DB, bwlID int64) error //更新名单记录
	Count(ctx context.Context, fn func(tx *gorm.DB) *gorm.DB) (total int64, err error)
}
