package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type ConfigProgressRepo interface {
	// TableName 返回表名
	TableName() string
	// Create 创建配置进度
	Create(ctx context.Context, session *gorm.DB, model *modelv2.PlatConfigProgress) error
	// Update 更新配置进度
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.PlatConfigProgress) error
	// Updates 批量更新配置进度字段
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	// Delete 删除配置进度
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	// List 获取配置进度列表
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.PlatConfigProgress, err error)
	// Count 统计配置进度数量
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	// First 获取单条配置进度记录
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.PlatConfigProgress, error)
}
