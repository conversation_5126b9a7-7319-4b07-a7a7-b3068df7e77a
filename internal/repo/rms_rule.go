package repo

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type RmsRule interface {
	Create(ctx context.Context, session *gorm.DB, models ...*modelv2.RmsRule) (err error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRule) error
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRule, err error)
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRule, error)
	GetByRuleNo(ctx context.Context, ruleNo string) (*modelv2.RmsRule, error)
	ListByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsRule, error)
	ListByStatus(ctx context.Context, status string) ([]*modelv2.RmsRule, error)
	ListByRuleGroup(ctx context.Context, ruleGroup string) ([]*modelv2.RmsRule, error)
	SetStatus(ctx context.Context, session *gorm.DB, id int64, cp *modelv2.RmsRule) error
}
