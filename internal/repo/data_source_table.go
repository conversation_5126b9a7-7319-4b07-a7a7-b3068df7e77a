package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// DataSourceTableRepo 数据源表信息表仓库接口
type DataSourceTableRepo interface {
	// TableName 返回表名
	TableName() string
	// Count 获取记录总数
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (int64, error)
	// List 获取列表数据
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsDataSourceTable, err error)

	// First 获取单条记录
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsDataSourceTable, error)

	// GetDataSourceByTableID 获取单条记录
	GetDataSourceByTableID(ctx context.Context, tableID int64) (*modelv2.RmsDataSource, error)

	// Create 创建记录
	Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsDataSourceTable) error

	// Update 更新记录
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsDataSourceTable) error

	// Updates 批量更新记录
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error

	// Delete 删除记录
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error

	// BatchCreate 批量创建记录
	BatchCreate(ctx context.Context, session *gorm.DB, items []*modelv2.RmsDataSourceTable) error

	// Tables 获取表名
	Tables(ctx context.Context, dataSource *modelv2.RmsDataSource) ([]string, error)

	// Columns 获取表字段
	Columns(ctx context.Context, dataSource *modelv2.RmsDataSource, tableName string) ([]*entity.Column, error)
}
