package repo

import (
	"context"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type BlackWhiteCacheRepo interface {
	IsBlack() bool
	GetBlackWhiteCache(ctx context.Context, day string) (res *modelv2.BlackWhiteCache, err error)
	GenerateBlackWhiteCache(ctx context.Context, maxAuditID int64, dayTime time.Time, bwl *modelv2.RmsBlackWhiteList) (bwlCache *modelv2.BlackWhiteCacheMetadata, err error)
	Flush(model *modelv2.BlackWhiteCache) (err error)
	ChangeStatus(bwlID int64, status bool) (err error)
	RemoveBlackWhite(bwlID int64) (err error)
	IncUpdate(ctx context.Context, bwlID int64) (err error)
}
