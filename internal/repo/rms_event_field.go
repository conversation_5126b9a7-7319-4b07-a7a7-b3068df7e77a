package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type RmsEventField interface {
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEventField) (err error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsEventField) error
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEventField, err error)
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsEventField, error)
	FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsEventField, error)
	FindByFieldName(ctx context.Context, fieldName string) ([]*modelv2.RmsEventField, error)
	FindByCheckPointAndFieldName(ctx context.Context, checkPoint, fieldName string) (*modelv2.RmsEventField, error)
	GetIntersectionFields(ctx context.Context, checkPoints []string) (fields []string, err error)
	CheckFiledExists(c context.Context, checkPoints []string, fields []string) (exists bool, field string, err error)
}
