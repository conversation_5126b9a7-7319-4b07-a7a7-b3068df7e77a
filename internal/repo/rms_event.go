package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type RmsEvent interface {
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEvent) (err error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsEvent) error
	Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEvent, err error)
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsEvent, error)
	FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsEvent, error)
	FindByResultCode(ctx context.Context, resultCode string) ([]*modelv2.RmsEvent, error)
	FindByCheckPointAndResultCode(ctx context.Context, checkPoint, resultCode string) ([]*modelv2.RmsEvent, error)
}
