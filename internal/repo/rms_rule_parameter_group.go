package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type RmsRuleParameterGroupRepo interface {
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsRuleParameterGroup) (id int64, err error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRuleParameterGroup) error
	UpdateField(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, field string, value interface{}) error
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRuleParameterGroup, err error)
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRuleParameterGroup, error)
	FindByCode(ctx context.Context, code string) (*modelv2.RmsRuleParameterGroup, error)
	FindByCheckPoint(ctx context.Context, checkPoint string) (*modelv2.RmsRuleParameterGroup, error)
	ListAll(ctx context.Context) ([]*modelv2.RmsRuleParameterGroup, error)
	ListExcludeDefault(ctx context.Context) ([]*modelv2.RmsRuleParameterGroup, error)
}
