package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type BlackWhiteAuditRepo interface {
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsBlackWhiteAudit) error
	First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteAudit, error)
	Updates(ctx context.Context, session *gorm.DB, fn func(tx *gorm.DB) *gorm.DB, data map[string]interface{}) error
	Audit(ctx context.Context, session *gorm.DB, auditModel *modelv2.RmsBlackWhiteAudit, state int, approver string, reason string) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteAudit, err error)
}
