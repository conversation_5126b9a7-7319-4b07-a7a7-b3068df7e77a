package repo

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type HandleLogRepo interface {
	Create(ctx context.Context, session *gorm.DB, model *modelv2.WpHandleLog) (id int32, err error)
	Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.WpHandleLog) error
	Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.WpHandleLog, err error)
	Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error)
	Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.WpHandleLog, error)
}
