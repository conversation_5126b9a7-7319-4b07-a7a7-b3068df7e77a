package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type RmsEventFieldBinding interface {
	TableName() string
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEventFieldBinding, err error)
	Remove(ctx context.Context, session *gorm.DB, fn func(db2 *gorm.DB) *gorm.DB) error
	Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEventFieldBinding) (err error)
	BatchCreate(ctx context.Context, session *gorm.DB, models []*modelv2.RmsEventFieldBinding) (err error)
	Bound(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (bound bool, err error)
}
