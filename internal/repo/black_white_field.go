package repo

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gorm.io/gorm"
)

type BlackWhiteFieldRepo interface {
	TableName() string
	List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlack<PERSON>hiteField, err error)
	BatchCreate(ctx context.Context, session *gorm.DB, m []*modelv2.RmsBlackWhiteField) error
	GetBlackWhiteFieldsBriefing(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (res []*modelv2.RmsBlackWhiteField, err error)
}
