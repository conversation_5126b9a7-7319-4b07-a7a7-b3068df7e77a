package service

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/tidwall/gjson"
	risk_sdk "gitv2.uqpaytech.com/infra-group/uqpay-risk-sdk"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/jwt"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"time"
)

type CraParameterService struct {
	Logger             *zap.Logger
	ICraFramework      ICraFramework
	ICraParameter      ICraParameter
	ICraParameterVal   IcraParameterVal
	ICraScoreReference ICraScoreReference
	Conf               config.Config
}

func (s *CraParameterService) Create(c *gin.Context, cp *model.RmsCraParameter) (string, *er.BuiltInError) {
	uid := uuid.NewString()
	cp.CraParameterId = uid
	cp.CreateTime = time.Now()
	cp1 := &model.RmsCraParameter{
		CraParameterName: cp.CraParameterName,
		CraFrameworkId:   cp.CraFrameworkId,
	}
	if _, err := s.ICraParameter.First(c, cp1); err == nil {
		return "", er.CraParameterNameRepeated
	}
	if err := s.ICraParameter.Create(c, cp); err != nil {
		return "", er.Unknown
	}
	return uid, nil
}

func (s *CraParameterService) Update(c *gin.Context, cra_framework_id string, cp *model.RmsCraParameter) error {
	cp.UpdateTime = time.Now()

	return s.ICraParameter.Update(c, cra_framework_id, cp)
}

func (s *CraParameterService) Delete(c *gin.Context, cra_framework_id string) error {
	return s.ICraParameter.Delete(c, cra_framework_id)
}

func (s *CraParameterService) List(c *gin.Context, data *model.RmsCraParameter) (*domain.CraParameterListRes, error) {
	list, err := s.ICraParameter.List(c, data)
	if err != nil {
		return nil, err
	}
	res := &domain.CraParameterListRes{
		AmlList:     make([]*domain.CraParameter, 0),
		CountryList: make([]*domain.CraParameter, 0),
		CustomList:  make([]*domain.CraParameter, 0),
	}
	reslist := make([]*domain.CraParameter, 0)
	for _, parameter := range list {
		CraParameter := &domain.CraParameter{
			CraParameterId:     parameter.CraParameterId,                      // 唯一ID
			CraFrameworkId:     parameter.CraFrameworkId,                      // 唯一ID
			CraParameterName:   parameter.CraParameterName,                    // 参数名
			CraParameterStatus: parameter.CraParameterStatus,                  // 状态：1=active, 0=默认, -1=inactive
			CraParameterType:   parameter.CraParameterType,                    // AML; COUNTRY; CUSTOM
			Description:        parameter.Description,                         // 描述
			Weight:             parameter.Weight.Mul(decimal.NewFromInt(100)), // 重量，权重
			ParamValIds:        make([]string, 0),                             // 关联参考id
			ParamValNames:      make([]string, 0),
		}

		var result []*domain.ParameterInfo
		// 将 JSON 数据解码到 map 中
		parameter.ParamInfo = strings.Replace(parameter.ParamInfo, `\"`, `\\\"`, -1)

		err := json.Unmarshal([]byte(parameter.ParamInfo), &result)
		if err != nil {
			return nil, err
		}

		for _, v := range result {
			CraParameter.ParamValIds = append(CraParameter.ParamValIds, v.Value)
			key, _ := global.CraParamterMap[v.Key]
			CraParameter.FormField = append(CraParameter.FormField, key)
		}
		reslist = append(reslist, CraParameter)

	}
	nameIdList := make([]string, 0)
	for _, parameter := range reslist {
		nameIdList = append(nameIdList, parameter.ParamValIds...)
	}
	//查询所有对应的名称
	FormFieldList, err := s.ICraParameterVal.GetParameterName(nameIdList)
	if err != nil {
		return nil, err
	}
	nameId := make(map[string]string, 0)
	for _, s2 := range FormFieldList {
		nameId[s2.ParamValId] = s2.ParamValName
	}
	for _, parameter := range reslist {
		parameter.ParamValNames = make([]string, 0)
		for _, id := range parameter.ParamValIds {
			name, _ := nameId[id]
			parameter.ParamValNames = append(parameter.ParamValNames, name)
		}
		//CraParameter.FormField = FormFieldList
		switch parameter.CraParameterType {
		case "AML":

			res.AmlList = append(res.AmlList, parameter)

		case "COUNTRY":
			res.CountryList = append(res.CountryList, parameter)
		case "CUSTOM":
			res.CustomList = append(res.CustomList, parameter)

		}
	}
	return res, nil
}

func (s *CraParameterService) Retrieve(c *gin.Context, cra_framework_id string) (*domain.RmsCraParameterInesRes, error) {
	info, err := s.ICraParameter.Retrieve(c, cra_framework_id)
	if err != nil {
		return nil, err
	}
	res := &domain.RmsCraParameterInesRes{
		CraFrameworkId:     info.CraFrameworkId,
		CraParameterId:     info.CraParameterId,
		CraParameterName:   info.CraParameterName,
		CraParameterStatus: info.CraParameterStatus,
		CraParameterType:   info.CraParameterType,
		Description:        info.Description,
		Weight:             decimal.NewFromFloat(info.Weight).Mul(decimal.NewFromInt(100)),
		CreateTime:         info.CreateTime,
		UpdateTime:         info.UpdateTime,
		ParamInfo:          info.ParamInfo,
	}
	return res, nil
}

func (s *CraParameterService) BatchUpdate(c *gin.Context, req *domain.CraParameterBatchUpdateReq) error {
	return s.ICraParameter.BatchUpdate(c, req)

}
func (s *CraParameterService) GetParameter(c *gin.Context) (*config.CraParamter, error) {
	return global.CraParamter, nil
}

type CraReq struct {
	CraParameterId string        `json:"cra_parameter_id"`
	Value          []*CraReqInfo `json:"value"`
}
type CraReqInfo struct {
	ParameterValId string   `json:"parameter_val_id"`
	ParaList       []string `json:"list"`
}

func (s *CraParameterService) CraAuditing(c *gin.Context, req *domain.CraAuditingReq) (*model.RmsCraScoreReference, error) {
	if req.CustomerId == "" && (req.BusinessCode == 0 && req.EntityId == "") {
		return nil, errors.New("parameter error\n")
	}

	//特殊处理字段
	CompanySizeMap := map[string]string{
		"BS001": "0-1 Employee",
		"BS002": "2-10 Employees",
		"BS003": "11-50 Employees",
		"BS004": "51-200 Employees",
		"BS005": ">200 Employees",
	}
	MonthlyTurnoverMap := map[string]string{
		"TM001": "Less than 50,000",
		"TM002": "50,000 to 100,000",
		"TM003": "100,000 to 250,000",
		"TM004": "250,000 to 500,000",
		"TM005": "Over 500,000",
	}
	//行业
	IndustryMap := map[string]string{
		"ICCV3_0000XX": "Digital and tech/Crypto and blockchain related products and services",
		"ICCV3_0001XX": "Digital and tech/Digital content (e.g., Books, music, movies)",
		"ICCV3_0002XX": "Digital and tech/Financial data and research",
		"ICCV3_000300": "Digital and tech/Games",
		"ICCV3_000301": "Digital and tech/Games",
		"ICCV3_0007XX": "Digital and tech/ICCV3_4477",
		"ICCV3_0004XX": "Digital and tech/Matchmaking and dating",
		"ICCV3_000701": "Digital and tech/Others",
		"ICCV3_000500": "Digital and tech/Social media",
		"ICCV3_000501": "Digital and tech/Social media",
		"ICCV3_0006XX": "Digital and tech/Software development",
		"ICCV3_000601": "Digital and tech/Software development",
		"ICCV3_0100XX": "Education/Child care",
		"ICCV3_0101XX": "Education/Colleges and universities",
		"ICCV3_0102XX": "Education/Elementary and secondary schools",
		"ICCV3_0103XX": "Education/Online education platforms",
		"ICCV3_0105XX": "Education/Others",
		"ICCV3_0104XX": "Education/Vocational schools and trade schools",
		"ICCV3_0200XX": "Entertainment, recreation and social/Bands, orchestras, actors, and other entertainers",
		"ICCV3_0201XX": "Entertainment, recreation and social/Country, sports and membership clubs",
		"ICCV3_0202XX": "Entertainment, recreation and social/Event ticketing",
		"ICCV3_0203XX": "Entertainment, recreation and social/Gambling",
		"ICCV3_0204XX": "Entertainment, recreation and social/Lotteries",
		"ICCV3_0205XX": "Entertainment, recreation and social/Movie theaters",
		"ICCV3_0206XX": "Entertainment, recreation and social/NGOs, charities and social organizations",
		"ICCV3_0212XX": "Entertainment, recreation and social/Others",
		"ICCV3_0207XX": "Entertainment, recreation and social/Political organizations",
		"ICCV3_0208XX": "Entertainment, recreation and social/Professional sport teams and leagues",
		"ICCV3_0209XX": "Entertainment, recreation and social/Recreational camps",
		"ICCV3_0210XX": "Entertainment, recreation and social/Religious organizations",
		"ICCV3_0211XX": "Entertainment, recreation and social/Tourist attractions",
		"ICCV3_0300XX": "Financial services/Digital wallet",
		"ICCV3_0301XX": "Financial services/Insurance",
		"ICCV3_0302XX": "Financial services/Investment",
		"ICCV3_0303XX": "Financial services/Loans or lending",
		"ICCV3_0304XX": "Financial services/Money services or transmission",
		"ICCV3_0305XX": "Financial services/Mortgage",
		"ICCV3_0307XX": "Financial services/Others",
		"ICCV3_0306XX": "Financial services/Security brokers and dealers",
		"ICCV3_0400XX": "Manufacturing/Alcohol production",
		"ICCV3_0401XX": "Manufacturing/Clothing and accessories",
		"ICCV3_040201": "Manufacturing/Computer and electronics",
		"ICCV3_0402XX": "Manufacturing/Computer and electronics",
		"ICCV3_0403XX": "Manufacturing/Equipment rental and leasing",
		"ICCV3_0404XX": "Manufacturing/Food and beverage (non-alcoholic)",
		"ICCV3_0405XX": "Manufacturing/Furniture",
		"ICCV3_0406XX": "Manufacturing/Home appliances",
		"ICCV3_0407XX": "Manufacturing/Industrial machinery and equipment",
		"ICCV3_0408XX": "Manufacturing/Lumber and wood products (except furniture)",
		"ICCV3_0409XX": "Manufacturing/Medical devices",
		"ICCV3_0414XX": "Manufacturing/Others",
		"ICCV3_0410XX": "Manufacturing/Paper products",
		"ICCV3_0411XX": "Manufacturing/Pharmaceuticals",
		"ICCV3_0412XX": "Manufacturing/Rubber and plastics products",
		"ICCV3_041501": "Manufacturing/Supplements and nutraceuticals",
		"ICCV3_041502": "Manufacturing/Supplements and nutraceuticals",
		"ICCV3_0413XX": "Manufacturing/Tobacco and marijuana related products",
		"ICCV3_0500XX": "Medical services/Clinics",
		"ICCV3_0501XX": "Medical services/Hospitals",
		"ICCV3_0502XX": "Medical services/Mental health services",
		"ICCV3_050201": "Medical services/Mental health services",
		"ICCV3_0503XX": "Medical services/Nursing home and personal care facilities",
		"ICCV3_0505XX": "Medical services/Others",
		"ICCV3_050501": "Medical services/Others",
		"ICCV3_0504XX": "Medical services/Veterinary services",
		"ICCV3_0600XX": "Others/Adult content and services",
		"ICCV3_0601XX": "Others/Others",
		"ICCV3_0603XX": "Others/Weapons and munitions",
		"ICCV3_0700XX": "Personal services/Automotive repair and maintenance",
		"ICCV3_0701XX": "Personal services/Beauty salons and barbers",
		"ICCV3_0702XX": "Personal services/Devices and electronics repair",
		"ICCV3_070301": "Personal services/Health and wellness counselling",
		"ICCV3_0703XX": "Personal services/Health and wellness counselling",
		"ICCV3_0704XX": "Personal services/Laundry and cleaning services",
		"ICCV3_0706XX": "Personal services/Others",
		"ICCV3_0705XX": "Personal services/Physical fitness facilities",
		"ICCV3_0800XX": "Professional services/Accounting, auditing and tax",
		"ICCV3_0801XX": "Professional services/Administrative services",
		"ICCV3_080101": "Professional services/Administrative services",
		"ICCV3_0802XX": "Professional services/Art and design",
		"ICCV3_0803XX": "Professional services/Courier and shipping",
		"ICCV3_0804XX": "Professional services/Government and public services",
		"ICCV3_0805XX": "Professional services/Human resources solutions",
		"ICCV3_0806XX": "Professional services/IT consulting",
		"ICCV3_080601": "Professional services/IT consulting",
		"ICCV3_0807XX": "Professional services/Legal services",
		"ICCV3_0808XX": "Professional services/Management consulting",
		"ICCV3_080801": "Professional services/Management consulting",
		"ICCV3_0809XX": "Professional services/Marketing and advertising",
		"ICCV3_0813XX": "Professional services/Others",
		"ICCV3_081301": "Professional services/Others",
		"ICCV3_0810XX": "Professional services/Photography",
		"ICCV3_0811XX": "Professional services/Publishing and printing",
		"ICCV3_0812XX": "Professional services/Warehousing and distribution",
		"ICCV3_081201": "Professional services/Warehousing and distribution",
		"ICCV3_0900XX": "Property and real estate/Architectural and engineering services",
		"ICCV3_0901XX": "Property and real estate/Cleaning and sanitization",
		"ICCV3_0902XX": "Property and real estate/Construction",
		"ICCV3_0903XX": "Property and real estate/Electrical work",
		"ICCV3_0908XX": "Property and real estate/Others",
		"ICCV3_0904XX": "Property and real estate/Plumbing, heating and air-conditioning",
		"ICCV3_0905XX": "Property and real estate/Real estate agents and managers",
		"ICCV3_0906XX": "Property and real estate/Special trade contractors",
		"ICCV3_0907XX": "Property and real estate/Utilities",
		"ICCV3_1000XX": "Retail/Adult products",
		"ICCV3_1001XX": "Retail/Auto parts and accessories",
		"ICCV3_1002XX": "Retail/Automotive dealers",
		"ICCV3_1003XX": "Retail/Beauty and cosmetics",
		"ICCV3_1004XX": "Retail/Clothing and accessories",
		"ICCV3_100501": "Retail/Devices and electronics",
		"ICCV3_1005XX": "Retail/Devices and electronics",
		"ICCV3_100600": "Retail/Drugstore and personal care",
		"ICCV3_100601": "Retail/Drugstore and personal care",
		"ICCV3_1007XX": "Retail/Eyeglasses and other optical goods",
		"ICCV3_1008XX": "Retail/Florists",
		"ICCV3_1009XX": "Retail/Food and beverage",
		"ICCV3_1010XX": "Retail/Furniture",
		"ICCV3_1011XX": "Retail/Gardening",
		"ICCV3_1012XX": "Retail/Grocery stores",
		"ICCV3_1013XX": "Retail/Home appliances",
		"ICCV3_1014XX": "Retail/Household goods",
		"ICCV3_101501": "Retail/Jewelry, watches, precious stones and metals",
		"ICCV3_101500": "Retail/Jewelry, watches, precious stones and metals",
		"ICCV3_1016XX": "Retail/Medical devices",
		"ICCV3_1017XX": "Retail/Mother and baby goods",
		"ICCV3_1018XX": "Retail/Musical instruments",
		"ICCV3_1019XX": "Retail/Office and school supplies",
		"ICCV3_1020XX": "Retail/Online marketplaces",
		"ICCV3_1027XX": "Retail/Others",
		"ICCV3_1021XX": "Retail/Pet supplies",
		"ICCV3_1022XX": "Retail/Restaurants and bars",
		"ICCV3_1023XX": "Retail/Sports and outdoor",
		"ICCV3_102801": "Retail/Supplements and nutraceuticals",
		"ICCV3_102802": "Retail/Supplements and nutraceuticals",
		"ICCV3_1024XX": "Retail/Tobacco and marijuana related products",
		"ICCV3_1025XX": "Retail/Toys and hobbies",
		"ICCV3_1026XX": "Retail/Vapes, e-cigarettes, e-juice and related products",
		"ICCV3_1100XX": "Travel and lodging/Air transportation",
		"ICCV3_1101XX": "Travel and lodging/Car rental",
		"ICCV3_1102XX": "Travel and lodging/Hotels, inns, and motels",
		"ICCV3_1107XX": "Travel and lodging/Others",
		"ICCV3_1103XX": "Travel and lodging/Ridesharing",
		"ICCV3_1104XX": "Travel and lodging/Taxis and limos",
		"ICCV3_1105XX": "Travel and lodging/Travel agency",
		"ICCV3_1106XX": "Travel and lodging/Water transportation",
		"ICCV3_1200XX": "Wholesale/Adult products",
		"ICCV3_1201XX": "Wholesale/Auto parts and accessories",
		"ICCV3_1202XX": "Wholesale/Automotive",
		"ICCV3_1203XX": "Wholesale/Beauty and cosmetics",
		"ICCV3_1204XX": "Wholesale/Clothing and accessories",
		"ICCV3_1205XX": "Wholesale/Devices and electronics",
		"ICCV3_120601": "Wholesale/Drugstore and personal care",
		"ICCV3_120600": "Wholesale/Drugstore and personal care",
		"ICCV3_1207XX": "Wholesale/Eyeglasses and other optical goods",
		"ICCV3_1208XX": "Wholesale/Florists",
		"ICCV3_1209XX": "Wholesale/Food and beverage",
		"ICCV3_1210XX": "Wholesale/Furniture",
		"ICCV3_1211XX": "Wholesale/Gardening",
		"ICCV3_1212XX": "Wholesale/Groceries",
		"ICCV3_1213XX": "Wholesale/Home appliances",
		"ICCV3_1214XX": "Wholesale/Household goods",
		"ICCV3_121501": "Wholesale/Jewelry, watches, precious stones and metals",
		"ICCV3_121500": "Wholesale/Jewelry, watches, precious stones and metals",
		"ICCV3_1216XX": "Wholesale/Medical devices",
		"ICCV3_1217XX": "Wholesale/Mother and baby goods",
		"ICCV3_1218XX": "Wholesale/Musical instruments",
		"ICCV3_1219XX": "Wholesale/Office and school supplies",
		"ICCV3_1224XX": "Wholesale/Others",
		"ICCV3_1220XX": "Wholesale/Pet supplies",
		"ICCV3_1221XX": "Wholesale/Sports and outdoor",
		"ICCV3_122501": "Wholesale/Supplements and nutraceuticals",
		"ICCV3_122502": "Wholesale/Supplements and nutraceuticals",
		"ICCV3_1222XX": "Wholesale/Tobacco and marijuana related products",
		"ICCV3_1223XX": "Wholesale/Toys and hobbies",
	}

	AllMap := map[string]map[string]string{
		"business_details.entity_data.business_scale":    CompanySizeMap,
		"business_details.entity_data.turnover_monthly":  MonthlyTurnoverMap,
		"expected_activity.entity_data.turnover_monthly": MonthlyTurnoverMap,
		"business_details.entity_data.industry_code":     IndustryMap,
	}

	//成立时间判断
	RmsCraFramework, err := s.ICraFramework.First(c, &model.RmsCraFramework{CraType: req.EntityType})
	if err != nil {
		return nil, err
	}
	list, err := s.ICraParameter.List(c, &model.RmsCraParameter{CraFrameworkId: RmsCraFramework.CraFrameworkId, CraParameterStatus: 1})
	if err != nil {
		return nil, err
	}

	craReq := make([]*CraReq, 0)
	//遍历所有的parameter
	for _, parameter := range list {

		crareq := &CraReq{
			CraParameterId: parameter.CraParameterId,
			Value:          make([]*CraReqInfo, 0),
		}

		infoList := []*domain.ParameterInfo{}
		err := json.Unmarshal([]byte(parameter.ParamInfo), &infoList)
		if err != nil {
			return nil, err
		}
		//遍历所有的info的参数
		for _, parameterInfo := range infoList {

			info := &domain.ParameterInfoKey{}
			err := json.Unmarshal([]byte(parameterInfo.Key), info)
			if err != nil {
				return nil, err
			}

			craReqInfo := &CraReqInfo{
				ParameterValId: parameterInfo.Value,
				ParaList:       make([]string, 0),
			}
			//每一个参数可能自身由多个组成
			for _, i2 := range info.Key {
				vlist := gjson.Get(req.Profile, i2).Array()
				for _, v := range vlist {
					value := v.String()
					//时间单独判断
					if i2 == "company_info.entity_data.incorporate_date" && value != "" {
						// 解析日期字符串
						t, err := time.Parse("2006-01-02", value)
						if err != nil {
							return nil, err
						}
						// 获取当前时间
						currentTime := time.Now()
						// 计算日期差值
						years := currentTime.Year() - t.Year()

						// 如果当前时间还没有到达今年的指定日期，那么减去1年
						if currentTime.YearDay() < t.YearDay() {
							years--
						}
						value = strconv.Itoa(years)
					}
					//下拉映射
					if keyMap, ok := AllMap[i2]; ok {
						if va, ok := keyMap[v.String()]; ok {
							value = va
						}
					}
					craReqInfo.ParaList = append(craReqInfo.ParaList, value)
				}

			}
			crareq.Value = append(crareq.Value, craReqInfo)
		}
		craReq = append(craReq, crareq)

	}

	//总分
	prohibit_grade_score, err := s.ICraFramework.RiskScoreCount(c, &domain.CraFrameworkCount{CraType: RmsCraFramework.CraType})
	if err != nil {
		return nil, err
	}
	jsonCraReq, _ := json.Marshal(craReq)
	lastmap := map[string]any{
		"parameter":            string(jsonCraReq),
		"rms_cra_framework":    RmsCraFramework.CraFrameworkId,
		"prohibit_grade_score": prohibit_grade_score,
	}

	u := &risk_sdk.RiskRes{
		Parameter:  lastmap,
		CheckPoint: "ap_cra", //接入点
	}
	verifyResult, err := u.Verify()
	if err != nil {
		return nil, err
	}

	if verifyResult.Result != "000" {
		return nil, errors.New("gateway error")
	}
	res := &domain.RiskCraRes{}
	if err = json.Unmarshal([]byte(verifyResult.Msg), res); err != nil {
		return nil, err
	}
	infojson, _ := json.Marshal(res.Info)
	RmsCraScoreReference := &model.RmsCraScoreReference{
		CraScoreReferenceId: uuid.NewString(),
		CustomerId:          req.CustomerId,
		EntityId:            req.EntityId,
		BusinessCode:        req.BusinessCode,
		Info:                string(infojson),
		AssessmentScore:     res.AssessmentScore,
		AssessmentGrade:     res.AssessmentGrade,
		CreateTime:          time.Now(),
	}
	claims, _ := c.Get(consts.TokenKey)
	cclaims := claims.(*jwt.MyClaims)
	RmsCraScoreReference.Uid = cclaims.UserId
	RmsCraScoreReference.Nickname = cclaims.Username
	RmsCraScoreReference.Email = cclaims.Email

	if err = s.ICraScoreReference.Create(c, RmsCraScoreReference); err != nil {
		return nil, err
	}
	return RmsCraScoreReference, err
}
