package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

type RuleParameterGroupBindService struct {
	Logger                     *zap.Logger
	RuleParameterGroupBindRepo IRuleParameterGroupBindRepo
	RuleParameterGroup         IRuleParameterGroupRepo
	BaseServer
}

func (s *RuleParameterGroupBindService) Create(c *gin.Context, req *domain.RuleParameterGroupBindCreate) (*model.RmsRuleParameterGroupBind, error) {
	// 前端的组件不支持 bool 值
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	data := new(model.RmsRuleParameterGroupBind)
	if err := s.Copy(req, data); err != nil {
		return nil, err
	}
	pg, err := s.RuleParameterGroup.Retrieve(c, req.GroupID)
	if err != nil {
		return nil, err
	}
	data.CheckPoint = &pg.CheckPoint
	return data, s.RuleParameterGroupBindRepo.Create(c, data)
}

func (s *RuleParameterGroupBindService) CreateList(c *gin.Context, req *domain.RuleParameterGroupBindCreateList) ([]*model.RmsRuleParameterGroupBind, error) {
	resp := []*model.RmsRuleParameterGroupBind{}
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	for _, gid := range req.GroupIDList {
		data := new(model.RmsRuleParameterGroupBind)
		if err := s.Copy(req, data); err != nil {
			return nil, err
		}
		data.GroupID = gid
		pg, err := s.RuleParameterGroup.Retrieve(c, data.GroupID)
		if err != nil {
			return nil, err
		}
		data.CheckPoint = &pg.CheckPoint
		err = s.RuleParameterGroupBindRepo.Create(c, data)
		if err != nil {
			return nil, err
		}
		resp = append(resp, data)
	}
	return resp, nil
}

func (s *RuleParameterGroupBindService) Update(c *gin.Context, id int64, req *domain.RuleParameterGroupBindUpdate) (*model.RmsRuleParameterGroupBind, error) {
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	data := new(model.RmsRuleParameterGroupBind)
	if err := s.Copy(req, data); err != nil {
		return nil, err
	}
	pg, err := s.RuleParameterGroup.Retrieve(c, data.GroupID)
	if err != nil {
		return nil, err
	}
	data.CheckPoint = &pg.CheckPoint
	return data, s.RuleParameterGroupBindRepo.Update(c, id, data)
}

func (s *RuleParameterGroupBindService) Delete(c *gin.Context, id int64) error {
	return s.RuleParameterGroupBindRepo.Delete(c, id)
}

func (s *RuleParameterGroupBindService) DeleteList(c *gin.Context, idLi []int64) error {
	for _, id := range idLi {
		err := s.RuleParameterGroupBindRepo.Delete(c, id)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *RuleParameterGroupBindService) List(c *gin.Context, req *domain.RuleParameterGroupBindList) (*domain.PageData, error) {
	if req.ParamGroupCode != "" && req.ParamGroupId == 0 {
		pg, err := s.RuleParameterGroup.RetrieveByCode(c, req.ParamGroupCode)
		if err != nil {
			if errors.Is(err, er.NotFound) {
				return new(domain.PageData), nil
			}
			return nil, err
		}
		req.ParamGroupId = pg.ID
	}
	li, err := s.RuleParameterGroupBindRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	resp := []*domain.RuleParameterGroupBindListItem{}
	if err := s.Copy(li, &resp); err != nil {
		return nil, err
	}
	for _, i := range resp {
		// 前端的组件不支持 bool 值
		if i.Able == 1 {
			i.State = "Yes"
		} else {
			i.State = "No"
		}
		rpg, err := s.RuleParameterGroup.Retrieve(c, i.GroupID)
		if err != nil {
			return nil, err
		}
		i.ParamGroupCode = rpg.Code
	}
	count, err := s.RuleParameterGroupBindRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, resp), nil
}

func (s *RuleParameterGroupBindService) Retrieve(c *gin.Context, id int64) (*domain.RuleParameterGroupBindResp, error) {
	data, err := s.RuleParameterGroupBindRepo.Retrieve(c, id)
	if err != nil {
		return nil, err
	}
	resp := new(domain.RuleParameterGroupBindResp)
	err = s.Copy(data, resp)
	if err != nil {
		return nil, err
	}
	rpg, err := s.RuleParameterGroup.Retrieve(c, data.GroupID)
	if err != nil {
		return nil, err
	}
	resp.ParamGroupCode = rpg.Code
	// 前端的组件不支持 bool 值
	if resp.Able == 1 {
		resp.State = "Yes"
	} else {
		resp.State = "No"
	}
	return resp, nil
}
