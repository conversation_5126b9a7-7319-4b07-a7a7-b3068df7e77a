package service

import (
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
	"time"
)

type CraFrameWorkService struct {
	Logger       *zap.Logger
	CraFramework ICraFramework
}

func (s *CraFrameWorkService) Update(c *gin.Context, cra_framework_id string, cp *domain.CraFrameworkUpdate) error {
	cp.UpdateTime = time.Now()
	return s.CraFramework.Update(c, cra_framework_id, cp)
}
func (s *CraFrameWorkService) Retrieve(c *gin.Context, cra_framework_id string) (*model.RmsCraFramework, error) {
	return s.CraFramework.Retrieve(c, cra_framework_id)
}
func (s *CraFrameWorkService) RiskScoreCount(c *gin.Context, req *domain.CraFrameworkCount) (string, error) {
	return s.CraFramework.RiskScoreCount(c, req)
}
func (s *CraFrameWorkService) List(c *gin.Context) ([]domain.RmsCraFrameworkInfo, error) {

	list, err := s.CraFramework.List(c)
	if err != nil {
		return nil, err
	}
	infolist := make([]domain.RmsCraFrameworkInfo, 0)
	for _, framework := range list {
		info := domain.RmsCraFrameworkInfo{
			CraFrameworkId:           framework.CraFrameworkId,                                                              // 唯一ID
			CraType:                  framework.CraType,                                                                     // cra业务：KYC，KYB
			LowHighestPercentage:     decimal.NewFromFloat(framework.LowHighestPercentage).Mul(decimal.NewFromInt(100)),     // low最高分数（百分比）
			LowLowestPercentage:      decimal.NewFromFloat(framework.LowLowestPercentage).Mul(decimal.NewFromInt(100)),      // low最低分数（百分比）
			LowGradeScore:            framework.LowGradeScore,                                                               // low分数
			MediumHighestPercentage:  decimal.NewFromFloat(framework.MediumHighestPercentage).Mul(decimal.NewFromInt(100)),  // medium最高分数（百分比）
			MediumLowestPercentage:   decimal.NewFromFloat(framework.MediumLowestPercentage).Mul(decimal.NewFromInt(100)),   // medium最低分数（百分比）
			MediumGradeScore:         framework.MediumGradeScore,                                                            // medium分数
			HighHighestPercentage:    decimal.NewFromFloat(framework.HighHighestPercentage).Mul(decimal.NewFromInt(100)),    // high最高分数（百分比）
			HighLowestPercentage:     decimal.NewFromFloat(framework.HighLowestPercentage).Mul(decimal.NewFromInt(100)),     // high最低分数（百分比）
			HighGradeScore:           framework.HighGradeScore,                                                              // high分数
			ProhibitLowestPercentage: decimal.NewFromFloat(framework.ProhibitLowestPercentage).Mul(decimal.NewFromInt(100)), // prohibit最低分数（百分比）
			Status:                   framework.Status,                                                                      // 状态：1=active, 0=默认, -1=inactive
			CreateTime:               framework.CreateTime,
			UpdateTime:               framework.UpdateTime,
		}
		infolist = append(infolist, info)
	}

	return infolist, nil
}
