package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type RuleParameterService struct {
	Logger            *zap.Logger
	RuleParameterRepo IRuleParameterRepo
}

func (s *RuleParameterService) Create(c *gin.Context, cp *model.RmsRuleParameter) error {
	return s.RuleParameterRepo.Create(c, cp)
}

func (s *RuleParameterService) Update(c *gin.Context, id int64, cp *model.RmsRuleParameter) error {
	return s.RuleParameterRepo.Update(c, id, cp)
}

func (s *RuleParameterService) Delete(c *gin.Context, id int64) error {
	return s.RuleParameterRepo.Delete(c, id)
}

func (s *RuleParameterService) List(c *gin.Context, req *domain.RuleParameterList) (*domain.PageData, error) {
	li, err := s.RuleParameterRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.RuleParameterRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *RuleParameterService) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameter, error) {
	return s.RuleParameterRepo.Retrieve(c, id)
}
