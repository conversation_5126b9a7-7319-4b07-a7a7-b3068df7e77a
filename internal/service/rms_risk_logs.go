package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type RiskLogService struct {
	Logger      *zap.Logger
	RiskLogRepo IRiskLogRepo
}

func (s *RiskLogService) Create(c *gin.Context, cp *model.RmsRiskLog) error {
	return s.RiskLogRepo.Create(c, cp)
}

func (s *RiskLogService) Update(c *gin.Context, id int64, cp *model.RmsRiskLog) error {
	return s.RiskLogRepo.Update(c, id, cp)
}

func (s *RiskLogService) Delete(c *gin.Context, id int64) error {
	return s.RiskLogRepo.Delete(c, id)
}

func (s *RiskLogService) List(c *gin.Context, req *domain.RiskLogList) (*domain.PageData, error) {
	li, err := s.RiskLogRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.RiskLogRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *RiskLogService) Retrieve(c *gin.Context, id int64) (*model.RmsRiskLog, error) {
	return s.RiskLogRepo.Retrieve(c, id)
}
