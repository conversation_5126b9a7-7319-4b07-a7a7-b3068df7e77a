package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type AlarmTimeConfService struct {
	Logger            *zap.Logger
	AlarmTimeConfRepo IAlarmTimeConfRepo
}

func (s *AlarmTimeConfService) Create(c *gin.Context, cp *model.RmsAlarmTimeConf) error {
	return s.AlarmTimeConfRepo.Create(c, cp)
}

func (s *AlarmTimeConfService) Update(c *gin.Context, id int64, cp *model.RmsAlarmTimeConf) error {
	return s.AlarmTimeConfRepo.Update(c, id, cp)
}

func (s *AlarmTimeConfService) Delete(c *gin.Context, id int64) error {
	return s.AlarmTimeConfRepo.Delete(c, id)
}

func (s *AlarmTimeConfService) List(c *gin.Context, req *domain.AlarmTimeConfList) (*domain.PageData, error) {
	li, err := s.AlarmTimeConfRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.AlarmTimeConfRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *AlarmTimeConfService) Retrieve(c *gin.Context, id int64) (*model.RmsAlarmTimeConf, error) {
	return s.AlarmTimeConfRepo.Retrieve(c, id)
}
