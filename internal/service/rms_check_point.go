package service

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type CheckPointService struct {
	Logger         *zap.Logger
	CheckPointRepo ICheckPointRepo
	ConfigProgress IConfigProgressRepo
	BusinessType   IBusinessTypeRepo
	FilterField    IFilterFieldRepo
	BaseServer
}

func (s *CheckPointService) Check(c *gin.Context, cp *model.RmsCheckPoint) error {
	return nil
}

func (s *CheckPointService) Create(c *gin.Context, cp *model.RmsCheckPoint) error {
	if err := s.Check(c, cp); err != nil {
		return err
	}
	err := s.CheckPointRepo.Create(c, cp)
	if errors.Is(err, er.DuplicateEntry) {
		return er.DuplicateKeys.WithMsgf("code duplicate: %s", cp.Code)
	}
	return err
}

func (s *CheckPointService) Update(c *gin.Context, id int64, cp *model.RmsCheckPoint) error {
	if err := s.Check(c, cp); err != nil {
		return err
	}
	return s.CheckPointRepo.Update(c, id, cp)
}

func (s *CheckPointService) Delete(c *gin.Context, id int64) error {
	cp, err := datav2.NewRmsCheckPoint().First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsCheckPoint().IDField(), id)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if err != nil {
		return err
	}

	return db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {
		innerErr = datav2.NewRmsIndicator().Delete(c, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicator().AccessPointField(), cp.Code)
		})
		if innerErr != nil {
			return
		}

		innerErr = datav2.NewRmsCheckPoint().Delete(c, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsCheckPoint().IDField()+" = ?", id)
		})
		return innerErr
	})
}

func (s *CheckPointService) List(c *gin.Context, req *domain.CheckPointList) (*domain.PageData, error) {
	li, err := s.CheckPointRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.CheckPointRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	respLi := []*domain.CheckPointResp{}
	for _, i := range li {
		resp := new(domain.CheckPointResp)
		err = s.Copy(i, resp)
		if err != nil {
			return nil, err
		}

		p, err := s.ConfigProgress.Retrieve(c, i.Code)
		if err != nil {
			if !errors.Is(err, er.NotFound) {
				s.Logger.Warn("ConfigProgress.Retrieve", zap.Error(err))
			}
		} else {
			resp.ConfigProgress = p.Progress
		}
		FilterField := []string{}
		fli, err := s.FilterField.AllByAPCode(c, i.Code)
		if err != nil {
			return nil, err
		}
		for _, v := range fli {
			FilterField = append(FilterField, v.FieldName)
		}
		fj, err := json.Marshal(FilterField)
		if err != nil {
			return nil, er.WSEF(err, zap.Any("FilterField", FilterField))
		}
		resp.FilterFields = string(fj)
		respLi = append(respLi, resp)
	}
	return domain.FillInPageResponseData(req.PageSearch, count, respLi), nil
}

func (s *CheckPointService) Retrieve(c *gin.Context, id int64) (*model.RmsCheckPoint, error) {
	resp, err := s.CheckPointRepo.Retrieve(c, id)

	return resp, err
}

func (s *CheckPointService) RetrieveByCode(c *gin.Context, code string) (*domain.CheckPointResp, error) {
	data, err := s.CheckPointRepo.RetrieveByCode(c, code)
	if errors.Is(err, er.NotFound) {
		return nil, er.NotFound
	}
	resp := new(domain.CheckPointResp)
	err = s.Copy(data, resp)
	if err != nil {
		return nil, err
	}
	p, err := s.ConfigProgress.Retrieve(c, code)
	if err != nil {
		if !errors.Is(err, er.NotFound) {
			s.Logger.Warn("ConfigProgress.Retrieve", zap.Error(err))
		}
	} else {
		resp.ConfigProgress = p.Progress
	}
	return resp, nil
}

func (s *CheckPointService) AllNameCodeList(c *gin.Context) ([]*model.RmsCheckPoint, error) {
	return s.CheckPointRepo.AllNameCodeList(c)
}

func (s *CheckPointService) UpdateBusinessConfig(c *gin.Context, req *domain.CheckPointBusinessConfigReq) error {
	exitCheckPointBusinessConfig := []model.CheckPointBusinessConfig{}
	for _, v := range req.CheckPointBusinessConfig {
		// SecondFieldName SecondFieldValue 不能重复
		if v.SecondFieldName == "" || v.SecondFieldValue == "" {
			return er.InvalidArgument.WithStack().WithMsg("Second match condition can not be empty.")
		}
		for _, e := range exitCheckPointBusinessConfig {
			if v.SecondFieldName == e.SecondFieldName && v.SecondFieldValue == e.SecondFieldValue {
				return er.InvalidArgument.WithStack().WithMsg("Second match condition cannot be repeated.")
			}
		}
		exitCheckPointBusinessConfig = append(exitCheckPointBusinessConfig, v)
		if _, err := s.BusinessType.RetrieveByCode(c, v.BusinessCode); err != nil {
			if errors.Is(err, er.NotFound) {
				return er.NotFound.WithStack().WithMsgf("businessCode not found: %s", v.BusinessCode)
			}
			return err
		}
	}

	return nil
}
