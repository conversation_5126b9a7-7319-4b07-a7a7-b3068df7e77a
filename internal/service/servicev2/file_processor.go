package servicev2

import (
	"bytes"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/sha256"

	"sort"
	"strings"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/ztime"
	"go.uber.org/zap"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/csv"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/excel"
)

var BlackWhiteFileMaxLine = 20000

// 文件处理
type BlackWhiteFileProcessor struct {
	Fields                entity.Fields //所有字段
	ExistsRecover         bool
	ExistsItems           map[string]modelv2.RmsBlackWhiteItem //已存在的黑白名单记录 sha256=>modelv2.RmsBlackWhiteItem
	RequiredFields        []string                             //用户必填字段，不包含默认两个时间字段
	requiredFileMap       map[string]bool                      //用户必填字段映射，不包含默认两个时间字段
	fields                []string                             //所有字段，包含默认两个时间字段
	defaultRequiredFields map[string]bool                      //默认必填字段映射，包含默认两个时间字段
	fieldMap              map[string]bool                      //所有字段映射
}

func NewBlackWhiteFileProcessor(fields entity.Fields, existsItems map[string]modelv2.RmsBlackWhiteItem) *BlackWhiteFileProcessor {
	var res = &BlackWhiteFileProcessor{
		Fields:          fields,
		fieldMap:        map[string]bool{modelv2.BusinessFieldStartDate: true, modelv2.BusinessFieldExpirationDate: true}, //所有字段映射
		requiredFileMap: make(map[string]bool),                                                                            //用户必填字段映射
		defaultRequiredFields: map[string]bool{
			modelv2.BusinessFieldStartDate:      true,
			modelv2.BusinessFieldExpirationDate: true,
		}, //默认必填字段映射
		ExistsItems: make(map[string]modelv2.RmsBlackWhiteItem),
	}
	if existsItems != nil {
		res.ExistsItems = existsItems
	}

	for _, v := range fields {
		res.fields = append(res.fields, v.Name)
		res.fieldMap[v.Name] = true
		if v.IsRequired {
			res.RequiredFields = append(res.RequiredFields, v.Name)
			res.requiredFileMap[v.Name] = true
		}

	}
	res.fields = append(res.fields, modelv2.BusinessFieldExpirationDate, modelv2.BusinessFieldStartDate)

	sort.Slice(res.RequiredFields, func(i, j int) bool {
		return res.RequiredFields[i] < res.RequiredFields[j]
	})

	return res
}

// SetExistsRecover 字段重复时候是否需要覆盖
func (p *BlackWhiteFileProcessor) SetExistsRecover(existsRecover bool) *BlackWhiteFileProcessor {
	p.ExistsRecover = existsRecover
	return p
}

// ProcessExcelFile reads and validates excel/csv file data
func (p *BlackWhiteFileProcessor) ProcessExcelFile(file *bytes.Buffer) (res []map[string]interface{}, existsItems map[string]*ExistsBwItem, err error) {
	// 读取Excel文件数据
	var rows [][]string
	excelClient, err := excel.OpenBuffer(file)
	if err != nil {
		err = er.BlackWhiteFileInvalidError.WithMsg("The file is corrupted or cannot be read. Please check and re-upload.").WithErr(err)
		return
	}
	sheets := excelClient.Sheets()
	if len(sheets) == 0 {
		err = er.BlackWhiteFileInvalidError.WithMsg("Unable to read files\nThe uploaded file is empty.Please ensure the file contains data.")
		return
	}

	rows, err = excelClient.UseSheet(sheets[0]).ReadAllByBuffer()
	if err != nil {
		err = er.InvalidArgument.WithMsg("The file is corrupted or cannot be read.Please check and re-upload.").WithErr(err)
		return
	}
	wlog.Info("Read data from black and white list excel", zap.Any("rows", len(rows)))

	// 处理数据
	return p.Handle(rows)
}
func (p *BlackWhiteFileProcessor) ProcessCSVFile(file *bytes.Buffer) (res []map[string]interface{}, existsItems map[string]*ExistsBwItem, err error) {
	var rows [][]string
	rows, err = csv.OpenBuffer(file).ReadAll()
	if err != nil {
		err = er.BlackWhiteFileInvalidError.WithMsg("The file is corrupted or cannot be read. Please check and re-upload.").WithErr(err)
		return
	}

	return p.Handle(rows)
}

// Handle 处理数据
// 返回值:
// 1. 处理后的数据
// 2. 已存在的黑白名单记录的sha256=>id
// 3. 错误
func (p *BlackWhiteFileProcessor) Handle(rows [][]string) (res []map[string]interface{}, existsItems map[string]*ExistsBwItem, err error) {
	var tidyExcelData []map[string]interface{}

	tidyExcelData, err = p.valid(rows)
	if err != nil {
		return nil, nil, err
	}
	existsItems, err = p.checkItemExists(tidyExcelData)
	if err != nil {
		return nil, nil, err
	}

	return tidyExcelData, existsItems, nil
}
func (p *BlackWhiteFileProcessor) validTitle(data []string) (titleIndex map[int]string, err error) {
	titleIndex = make(map[int]string)
	// 标题行
	var requiredFieldStat = make(map[string]interface{}, len(p.RequiredFields)+modelv2.DefaultRequiredFiledCount)
	for colIdx, colCell := range data {
		colCell = strings.ReplaceAll(colCell, "(置空默认值9999年)", "")
		colCell = strings.TrimSpace(colCell) //删除数据空格
		colCell = strings.Trim(colCell, "*") //删除必填标志
		titleIndex[colIdx] = colCell
		if _, ok := p.fieldMap[colCell]; !ok {
			//陌生字段
			err = er.BlackWhiteFileInvalidColumn.WithMsg(fmt.Sprintf("Error - Column names do not match the required format. Expected columns: [%s].", strings.Join(p.fields, ",")))
			return
		}
		_, ok1 := p.defaultRequiredFields[colCell]
		_, ok2 := p.requiredFileMap[colCell]
		if ok1 || ok2 {
			requiredFieldStat[colCell] = struct{}{}
		}
	}
	if len(requiredFieldStat) > len(p.RequiredFields)+modelv2.DefaultRequiredFiledCount {
		//缺少必填字段
		err = er.BlackWhiteFileInvalidColumn.WithMsg(fmt.Sprintf("Error format - Column names do not match the required format. Expected columns: [%s].", strings.Join(p.fields, ",")))
		return
	}
	if len(requiredFieldStat) < len(p.RequiredFields)+modelv2.DefaultRequiredFiledCount {
		//缺少必填字段
		err = er.BlackWhiteFileInvalidColumn.WithMsg(fmt.Sprintf("Error - Missing required data in column [%s].", strings.Join(p.RequiredFields, ",")))
		return
	}
	return
}

// valid 校验文件内容
func (p *BlackWhiteFileProcessor) valid(data [][]string) (res []map[string]interface{}, err error) {
	var hashExists = make(map[string]bool)
	//验证文件内容
	if len(data) <= 1 {
		//文件没有数据
		err = er.BlackWhiteFileInvalidError.WithMsg("Unable to read files\nThe uploaded file is empty.Please ensure the file contains data.")
		return
	}
	//单次最多添加20000行
	if len(data) > BlackWhiteFileMaxLine+1 {
		err = er.BlackWhiteFileInvalidError.WithMsg(fmt.Sprintf("The number of records exceeds the limit of [%d].Please split the file and re-upload.", BlackWhiteFileMaxLine))
		return
	}
	var titleIndex = make(map[int]string)
	var requiredFieldIdx = make(map[string]int)
	for idx, row := range data {
		if idx == 0 {
			titleIndex, err = p.validTitle(row)
			if err != nil {
				return
			}
			for idx, title := range titleIndex {
				if p.requiredFileMap[title] {
					requiredFieldIdx[title] = idx
				}
			}
			wlog.Info("Read the title data from the whitelist and blacklist", zap.Any("titleIndex", titleIndex))

		} else {
			var blackWhiteItemExtends = modelv2.BlackWhiteItemExtends{
				modelv2.BusinessFieldStartDate:      ztime.StartOfDay(time.Now().Local()).Format(time.DateOnly),
				modelv2.BusinessFieldExpirationDate: modelv2.ForeverTime.Format(time.DateOnly),
			}
			//首先校验必填字段是否为空，防止必填字段右边字段均为空值导致提前完成校验
			var emptyFields []string
			for fieldName, idx := range requiredFieldIdx {
				if idx >= len(row) || strings.TrimSpace(row[idx]) == "" {
					emptyFields = append(emptyFields, fieldName)
				}
			}
			if len(emptyFields) > 0 {
				//必填字段为空
				err = er.InvalidArgument.WithMsg(fmt.Sprintf("Line number [%d] is missing required field data:[%s]", idx+1, strings.Join(emptyFields, ",")))
				return
			}
			for colIdx, colCell := range row {
				var (
					tmpOk2 bool
				)
				//判断当前数据是否为必填字段数据
				fieldName := titleIndex[colIdx]
				colCell = strings.TrimSpace(colCell) //删除数据空格
				if p.requiredFileMap[fieldName] && colCell == "" {
					//必填字段为空
					err = er.InvalidArgument.WithMsg(fmt.Sprintf("Missing required data in column [%s].", fieldName))
					return
				}

				_, tmpOk2 = titleIndex[colIdx] //检查数据表中是否存在该列数据,存在无标题的列

				if !p.fieldMap[fieldName] || !tmpOk2 {
					err = er.InvalidArgument.WithMsg(fmt.Sprintf("Column names do not match the required format. Expected columns: [%s]", strings.Join(p.fields, ",")))
					return
				}
				if !(p.defaultRequiredFields[fieldName] && colCell == "") {
					blackWhiteItemExtends[fieldName] = colCell
				}
			}
			blackWhiteItemExtends[modelv2.BusinessFieldRequiredSha256] = GenerateBlackWhiteItemSha256(p.Fields, blackWhiteItemExtends)
			if hashExists[sha256.BytesToBase64String(blackWhiteItemExtends[modelv2.BusinessFieldRequiredSha256].([]byte))] {
				continue
			}
			hashExists[sha256.BytesToBase64String(blackWhiteItemExtends[modelv2.BusinessFieldRequiredSha256].([]byte))] = true
			var start, end time.Time
			start, err = time.ParseInLocation("2006-01-02", blackWhiteItemExtends[modelv2.BusinessFieldStartDate].(string), time.Local)
			if err != nil {
				err = er.InvalidArgument.WithMsg("effective_start_date format error, for example:[2006-01-02].")
				return
			}
			end, err = time.ParseInLocation("2006-01-02", blackWhiteItemExtends[modelv2.BusinessFieldExpirationDate].(string), time.Local)
			if err != nil {
				err = er.InvalidArgument.WithMsg("effective_date format error, for example:[2006-01-02].")
				return
			}
			blackWhiteItemExtends[modelv2.BusinessFieldStartDate] = ztime.StartOfDay(start)
			blackWhiteItemExtends[modelv2.BusinessFieldExpirationDate] = ztime.EndOfDay(end)
			if end.Before(start) {
				err = er.InvalidArgument.WithMsgf("Line:[%d] Expiration date is earlier than start date.\n", idx+1)
				return
			}
			if start.Before(ztime.StartOfDay(time.Now().Local())) {
				err = er.InvalidArgument.WithMsgf("Line:[%d] Start date is earlier than current date.\n", idx+1)
				return
			}
			res = append(res, blackWhiteItemExtends)

		}
	}
	wlog.Info("Successfully read the data from the whitelist and blacklist", zap.Any("res_count", len(res)))

	return res, nil
}

type ExistsBwItem struct {
	ItemID int64
	Status int
}

// checkItemExists 检查数据当前数据库是否存在，开启Recove模式就覆盖重复数据，否则返回错误
func (p *BlackWhiteFileProcessor) checkItemExists(tidyExcelData []map[string]interface{}) (existsItems map[string]*ExistsBwItem, err error) {
	existsItems = make(map[string]*ExistsBwItem)
	for _, data := range tidyExcelData {
		var (
			ok   bool
			hash []byte
		)
		if hash, ok = data[modelv2.BusinessFieldRequiredSha256].([]byte); !ok || hash == nil {
			err = er.InvalidArgument.WithMsg("Data processing exception")
			return
		}
		if item, ok := p.ExistsItems[string(hash)]; ok {
			if p.ExistsRecover {
				existsItems[string(hash)] = &ExistsBwItem{
					ItemID: item.ID,
					Status: item.Status,
				}
			} else {
				err = er.BlackWhiteFileDuplicateRecords.WithMsg("Duplicate records found in the file.Please recove duplicates or re-upload.")
				return
			}
		}

	}
	return
}
