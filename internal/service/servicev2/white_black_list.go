package servicev2

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/json"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

type BlackWhiteListService struct {
	BlackWhiteListRepo        repo.BlackWhiteListRepo
	BlackWhiteItemRepo        repo.BlackWhiteItemRepo
	BlackWhiteFieldRepo       repo.BlackWhiteFieldRepo
	BlackWhiteAuditRepo       repo.BlackWhiteAuditRepo
	BlackWhiteAuditDetailRepo repo.BlackWhiteAuditDetailRepo
	RmsEventFieldRepo         repo.RmsEventField
	RmsEventFieldBindingRepo  repo.RmsEventFieldBinding
	BlackWhiteAuditService    *BlackWhiteAuditService
}

func BlockPendingChanges(ctx context.Context, repo repo.BlackWhiteListRepo, bwID int64) (bwl *modelv2.RmsBlackWhiteList, err error) {
	bwl, err = repo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", bwID)
	})
	if errors.Is(err, er.NotFound) {
		return
	}
	if err != nil {
		return
	}
	if bwl.State == modelv2.BlackWhiteListStatePendingApproval {
		err = er.BlackWhiteListIsAuditing.WithMsg("The list is being audited, please wait for the audit to complete.")

	}
	return
}
func (s *BlackWhiteListService) CheckFiledExists(c context.Context, accessPoints []string, fields []string) (exists bool, field string, err error) {
	exists, field, err = s.RmsEventFieldRepo.CheckFiledExists(c, accessPoints, fields)
	if err != nil {
		return
	}
	if !exists {
		return false, field, nil
	}
	return true, "", nil
}

// Save 保存黑白名单记录
func (s *BlackWhiteListService) Save(c context.Context, req *entity.SaveBlackWhiteRecord) (id int64, err error) {
	var bwl *modelv2.RmsBlackWhiteList
	if len(req.AccessPoints) == 0 {
		return 0, er.InvalidArgument.WithMsg("access_points is empty")
	}
	if req.ID > 0 {
		//处理更新逻辑
		bwl, err = BlockPendingChanges(c, s.BlackWhiteListRepo, int64(req.ID))
		if err != nil {
			return
		}
		// 校验字段是否存在
		var fieldModels []*modelv2.RmsBlackWhiteField
		var fieldNames []string
		var exists bool
		var field string
		_, fieldModels, err = s.BlackWhiteFieldRepo.List(c, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwl.ID)
		})
		if err != nil {
			return
		}
		for _, field := range fieldModels {
			fieldNames = append(fieldNames, field.FieldName)
		}
		exists, field, err = s.CheckFiledExists(c, req.AccessPoints, fieldNames)
		if err != nil {
			return
		}
		if !exists {
			return 0, er.InvalidArgument.WithMsg("field:" + field + " not exists")
		}
		bwl.Note = req.Note
		bwl.AccessPoints = json.JsonMarshal(req.AccessPoints)
		if bwl.State == modelv2.BlackWhiteListStateDraft {
			err = s.BlackWhiteListRepo.Update(c, nil, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", bwl.ID)
			}, bwl)
		} else {
			err = s.BlackWhiteListRepo.UpdateChangeData(c, nil, req.ID, req.Submitter, json.JsonMarshal(bwl))
		}
		if err != nil {
			return
		}
		id = bwl.ID
	} else {
		_, err = s.BlackWhiteListRepo.First(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("name = ?", req.Name).Where("type = ?", req.Type)
		})
		if err != nil && !errors.Is(err, er.NotFound) {
			return
		}
		if err == nil {
			// 名单记录已存在
			err = er.InvalidArgument.WithMsg("The list name already exists").WithErr(err)
			return
		}
		err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {
			var hasRequiredField bool
			bwl = &modelv2.RmsBlackWhiteList{
				Name:         req.Name,
				Type:         req.Type,
				AccessPoints: json.JsonMarshal(req.AccessPoints),
				Note:         req.Note,
				CreatedBy:    req.Submitter,
				Status:       modelv2.BlackWhiteListStatusTurnOff,
				State:        modelv2.BlackWhiteListStateDraft,
			}
			innerErr = s.BlackWhiteListRepo.Create(c, tx, bwl)
			if innerErr != nil {
				return
			}
			id = bwl.ID
			var fieldNames []string
			var fieldsModels []*modelv2.RmsBlackWhiteField
			for _, field := range req.Fields {
				if field.IsRequired {
					hasRequiredField = true
				}
				fieldNames = append(fieldNames, field.Name)
				fieldsModels = append(fieldsModels, &modelv2.RmsBlackWhiteField{
					BwlID:     id,
					FieldName: field.Name,
					Required:  field.IsRequired,
				})
			}
			if !hasRequiredField {
				return er.InvalidArgument.WithMsg("At least one field is required")
			}
			// 校验字段是否存在
			var exists bool
			var field string

			exists, field, err = s.CheckFiledExists(c, req.AccessPoints, fieldNames)
			if err != nil {
				return
			}
			if !exists {
				return er.InvalidArgument.WithMsg("field:" + field + " not exists")
			}
			// 写入字段绑定关系
			var bindingModels []*modelv2.RmsEventFieldBinding
			for _, field := range fieldsModels {
				bindingModels = append(bindingModels, &modelv2.RmsEventFieldBinding{
					CheckPoint:    field.FieldName,
					FieldName:     field.FieldName,
					BindTableName: s.BlackWhiteListRepo.TableName(),
					BindTableID:   id,
				})
			}
			if len(bindingModels) > 0 {
				innerErr = s.RmsEventFieldBindingRepo.BatchCreate(c, tx, bindingModels)
				if innerErr != nil {
					return
				}
			}

			if len(fieldsModels) > 0 {
				innerErr = s.BlackWhiteFieldRepo.BatchCreate(c, tx, fieldsModels)
			}
			return
		})

	}
	return
}

// Remove 删除黑白名单记录
func (s *BlackWhiteListService) Remove(c context.Context, id int64, submitter string) (err error) {
	var bwl *modelv2.RmsBlackWhiteList

	bwl, err = BlockPendingChanges(c, s.BlackWhiteListRepo, id)
	if err != nil {
		return
	}
	if bwl.State == modelv2.BlackWhiteListStateRemoved {
		//已删除直接返回
		err = er.BlackWhiteListIsAuditing.WithMsg("This list has already been deleted. Please do not delete it again.")
		return err
	}
	if bwl.State == modelv2.BlackWhiteListStateModifyDraft {
		err = er.BlackWhiteListIsAuditing.WithMsg("This list currently being edited, cannot be deleted")
		return err
	}
	if bwl.State == modelv2.BlackWhiteListStateDraft {
		//草稿状态直接删除
		return s.BlackWhiteListRepo.Remove(c, nil, id, submitter)
	}
	var delCount int64
	delCount, err = s.BlackWhiteItemRepo.Count(c, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("bwl_id = ?", id).Where("status = ?", modelv2.BlackWhiteItemStatusPass)
	})
	if err != nil {
		return
	}
	// 直接通知审核人员进行审核，审核通过直接删除
	err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {
		auditModel := &modelv2.RmsBlackWhiteAudit{
			BwlID:     id,
			Mode:      modelv2.BlackWhiteAuditModeDelete,
			Submitter: submitter,
			State:     modelv2.BlackWhiteAuditStatePending,
		}
		innerErr = s.BlackWhiteAuditRepo.Create(c, tx, auditModel)
		if innerErr != nil {
			return
		}
		innerErr = s.BlackWhiteListRepo.Updates(c, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", id)
		}, map[string]interface{}{
			"change_data": json.JsonMarshal(bwl),
			"state":       modelv2.BlackWhiteListStatePendingApproval,
			"created_by":  submitter,
		})
		if innerErr != nil {
			return
		}
		innerErr = s.BlackWhiteAuditDetailRepo.Create(c, tx, &modelv2.RmsBlackWhiteAuditDetail{
			BwlID:   id,
			AuditID: auditModel.ID,
			Log:     fmt.Sprintf("Deleted: %d records", delCount),
		})
		return
	})
	return
}

// SetStatus 设置黑白名单状态
func (s *BlackWhiteListService) SetStatus(ctx context.Context, id int64, status int, submitter string) (auditModel *modelv2.RmsBlackWhiteAudit, err error) {
	var bwl *modelv2.RmsBlackWhiteList
	bwl, err = s.BlackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if errors.Is(err, er.NotFound) {
		return
	}
	if err != nil {
		return
	}
	if bwl.State == modelv2.BlackWhiteListStateDraft || bwl.State == modelv2.BlackWhiteListStateRemoved {
		err = er.BlackWhiteFileInvalidError.WithMsg("List status abnormal, unable to set status")
		return
	}
	var mode int
	if bwl.Status != status {
		switch status {
		case modelv2.BlackWhiteListStatusTurnOn:
			mode = modelv2.BlackWhiteAuditModeTurnOn
		case modelv2.BlackWhiteListStatusTurnOff:
			mode = modelv2.BlackWhiteAuditModeTurnOff
		}
	}
	if mode > 0 {
		err = db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
			auditModel = &modelv2.RmsBlackWhiteAudit{
				BwlID:     id,
				Mode:      mode,
				Submitter: submitter,
				State:     modelv2.BlackWhiteAuditStatePending,
			}
			innerErr = s.BlackWhiteAuditRepo.Create(ctx, tx, auditModel)
			if innerErr != nil {
				return
			}
			innerErr = s.BlackWhiteListRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", id)
			}, map[string]interface{}{
				"change_data": json.JsonMarshal(bwl),
				"state":       modelv2.BlackWhiteListStatePendingApproval,
				"created_by":  submitter,
			})
			return
		})
	}
	return
}

// SubmitAudit 提交审核
func (s *BlackWhiteListService) SubmitAudit(c context.Context, id int64, submitter string) (err error) {
	var bwl *modelv2.RmsBlackWhiteList

	bwl, err = BlockPendingChanges(c, s.BlackWhiteListRepo, id)
	if err != nil {
		return
	}
	if bwl.State != modelv2.BlackWhiteListStateDraft && bwl.State != modelv2.BlackWhiteListStateModifyDraft {
		err = er.BlackWhiteListIsAuditing.WithMsg("The list is not in draft status, and the submission operation cannot be performed.")
		return
	}
	var mode int
	switch bwl.State {
	case modelv2.BlackWhiteListStateDraft:
		// 第一次提交，默认所有数据都是草稿状态
		mode = modelv2.BlackWhiteAuditModeCreate

	case modelv2.BlackWhiteListStateModifyDraft:
		// 非第一次提交，涉及明细有新增、编辑、删除操作
		mode = modelv2.BlackWhiteAuditModeEdit
	}
	audit := &modelv2.RmsBlackWhiteAudit{
		BwlID:     id,
		Mode:      mode,
		State:     modelv2.BlackWhiteAuditStatePending,
		Submitter: submitter,
	}
	err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {

		innerErr = s.BlackWhiteAuditRepo.Create(c, tx, audit)
		if innerErr != nil {
			return
		}

		return innerErr
	})
	if err != nil {
		return
	}
	var history string
	history, err = s.BlackWhiteAuditService.getItemChangeHistory(c, audit)
	if err != nil {
		return
	}

	return s.BlackWhiteAuditDetailRepo.Create(c, nil, &modelv2.RmsBlackWhiteAuditDetail{
		BwlID:   audit.BwlID,
		AuditID: audit.ID,
		Log:     history,
	})
}

// Detail 创建黑白名单记录
func (s *BlackWhiteListService) Detail(c context.Context, id int64) (bwlEntity *entity.BlackWhiteListDetail, err error) {
	var bwl *modelv2.RmsBlackWhiteList
	bwl, err = s.BlackWhiteListRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if errors.Is(err, er.NotFound) {
		return
	}
	if err != nil {
		// 数据库异常
		return
	}
	var fields entity.Fields
	fields, err = s.BlackWhiteListRepo.Fields(c, bwl.ID)
	if err != nil {
		return
	}
	if bwl.ChangeData != "" {
		cgBwl := json.JsonUnmarshal[modelv2.RmsBlackWhiteList](bwl.ChangeData)
		bwl.AccessPoints = cgBwl.AccessPoints
		bwl.Note = cgBwl.Note
	}
	bwlEntity = &entity.BlackWhiteListDetail{
		ID:           int(bwl.ID),
		Name:         bwl.Name,
		Type:         bwl.Type,
		AccessPoints: json.JsonUnmarshal[[]string](bwl.AccessPoints),
		Note:         bwl.Note,
		Status:       bwl.Status,
		State:        bwl.State,
		Fields:       fields,
		CreatedBy:    bwl.CreatedBy,
	}
	var audit *modelv2.RmsBlackWhiteAudit
	audit, err = s.BlackWhiteAuditRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", id).Where("mode in(?)", []int{modelv2.BlackWhiteAuditModeEdit, modelv2.BlackWhiteAuditModeCreate}).Order("id desc")
	})
	if errors.Is(err, er.NotFound) {
		err = nil
	} else if err == nil {
		bwlEntity.LastAuditState = audit.State
	}

	return
}
func (s *BlackWhiteListService) Print(c context.Context) (result map[string]interface{}, err error) {
	cacheKey := datav2.NewCacheKey(true)
	result = make(map[string]interface{})
	data, err := redisutils.Get(cacheKey.UpdateKey)
	if err != nil {
		return nil, er.Internal.WithMsg("failed to get update time").WithErr(err).WithStack()
	}
	result["update_time"] = data
	// 获取AccessPoint的hash map
	accessPointMap, err := redisutils.HGetAll(cacheKey.GetAccessPointKey())
	if err != nil {
		return nil, er.Internal.WithMsg("failed to get access point map").WithErr(err).WithStack()
	}
	result["access_points"] = accessPointMap

	// 获取所有ListPrefix前缀的hash map
	listKeys, err := global.Rdb.Keys(c, cacheKey.ListPrefix+"*").Result()
	if err != nil {
		return nil, er.Internal.WithMsg("failed to get list keys").WithErr(err).WithStack()
	}

	listMaps := make(map[string]map[string]string)
	for _, key := range listKeys {
		listMap, err := global.Rdb.HGetAll(c, key).Result()
		if err != nil {
			return nil, er.Internal.WithMsg("failed to get list map").WithErr(err).WithStack()
		}
		listMaps[key] = listMap
	}
	result["lists"] = listMaps

	// 获取所有ItemPrefix前缀的hash map
	itemKeys, err := global.Rdb.Keys(c, cacheKey.ItemPrefix+"*").Result()
	if err != nil {
		return nil, er.Internal.WithMsg("failed to get item keys").WithErr(err).WithStack()
	}

	itemMaps := make(map[string]map[string]string)
	for _, key := range itemKeys {
		itemMap, err := global.Rdb.HGetAll(c, key).Result()
		if err != nil {
			return nil, er.Internal.WithMsg("failed to get item map").WithErr(err).WithStack()
		}
		itemMaps[key] = itemMap
	}
	result["items"] = itemMaps

	return result, nil
}

// 获取当前黑白名单操作进度
func (s *BlackWhiteListService) Progress(c context.Context, id int64) (progress []*entity.Progress, err error) {
	_, err = s.BlackWhiteListRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return
	}
	var audit *modelv2.RmsBlackWhiteAudit
	audit, err = s.BlackWhiteAuditRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", id).Where("mode in(?)", []int{modelv2.BlackWhiteAuditModeEdit, modelv2.BlackWhiteAuditModeCreate}).Order("updated_at desc")
	})
	if errors.Is(err, er.NotFound) {
		err = nil
		return
	}
	if err != nil {
		return
	}
	var name string
	switch audit.Mode {

	case modelv2.BlackWhiteAuditModeCreate:
		name = "Create list"
	case modelv2.BlackWhiteAuditModeEdit:
		name = "Edit"
	case modelv2.BlackWhiteAuditModeDelete:
		name = "Delete list"
	case modelv2.BlackWhiteAuditModeTurnOn:
		name = "Turn on list"
	case modelv2.BlackWhiteAuditModeTurnOff:
		name = "Turn off list"
	}
	progress = append(progress, &entity.Progress{
		Name:      name,
		Completed: true,
		Executor:  audit.Submitter,
		ExecuteAt: audit.CreatedAt.Unix(),
	})
	var secondCompleted bool
	switch audit.State {
	case modelv2.BlackWhiteAuditStatePending:
		name = "Pending"
	case modelv2.BlackWhiteAuditStatePass:
		name = "Passed"
		secondCompleted = true
	case modelv2.BlackWhiteAuditStateReject:
		name = "Rejected"
		secondCompleted = true
	}

	progress = append(progress, &entity.Progress{
		Name:      "Under approval",
		Completed: secondCompleted,
		Executor:  audit.Approver,
		ExecuteAt: audit.UpdatedAt.Unix(),
	})
	progress = append(progress, &entity.Progress{
		Name:      name,
		Completed: secondCompleted,
	})

	return
}

// List 获取黑白名单列表
func (s *BlackWhiteListService) List(ctx context.Context, req *request_parameters.GetBlackWhiteList) (total int64, res []*response_parameters.BlackWhiteList, err error) {
	var nowUnix = time.Now().Local().Unix()
	var resMap = make(map[int64]*response_parameters.BlackWhiteList) //bwl_id => 返回结果
	var bwIds []int64
	total, items, err := s.BlackWhiteListRepo.List(ctx, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.Valid == 1 {
			db = db.Where("id in (select bwl_id from rms_black_white_audit t where t.mode= ? and t.state= ?) and state in ?", modelv2.BlackWhiteAuditModeCreate, modelv2.BlackWhiteAuditStatePass, []int64{
				modelv2.BlackWhiteListStateModifyDraft,
				modelv2.BlackWhiteListStatePendingApproval,
				modelv2.BlackWhiteListStatePass,
				modelv2.BlackWhiteListStateReject,
				modelv2.BlackWhiteListStateDisable,
			})
		}
		if req.Name != "" {
			db = db.Where("name like ?", "%"+req.Name+"%")
		}
		if len(req.RequiredFields) > 0 {
			// 联表查询，获取必填字段
			db = db.InnerJoins(fmt.Sprintf("inner join %[1]s on %[1]s.bwl_id=%[2]s.id and %[1]s.required=1", s.BlackWhiteFieldRepo.TableName(), s.BlackWhiteListRepo.TableName())).
				Where(fmt.Sprintf("%s.field_name in (?)", s.BlackWhiteFieldRepo.TableName()), req.RequiredFields)
		}
		if len(req.AccessPoints) > 0 {
			conditions := make([]string, len(req.AccessPoints))
			for i, point := range req.AccessPoints {
				conditions[i] = fmt.Sprintf("JSON_CONTAINS(access_points, '\"%s\"', '$')", point)
			}
			db = db.Where("(" + strings.Join(conditions, " OR ") + ")")
		}
		return db.Where("type = ?", req.Type).Order("updated_at desc,name asc")
	}, true)
	if err != nil {
		return
	}
	for _, v := range items {
		bwIds = append(bwIds, v.ID)
		resMap[v.ID] = &response_parameters.BlackWhiteList{
			ID:           v.ID,
			Name:         v.Name,
			Type:         v.Type,
			AccessPoints: json.JsonUnmarshal[[]string](v.AccessPoints),
			Status:       v.Status,
			State:        v.State,
			CreatedBy:    v.CreatedBy,
			CreatedAt:    v.UpdatedAt.Local().Unix(),
		}
		res = append(res, resMap[v.ID])
	}
	var eg errgroup.Group
	eg.Go(func() (innerErr error) {
		// 获取有效名单数
		var groupCount []*modelv2.GroupIdCount

		groupCount, innerErr = s.BlackWhiteItemRepo.GroupCount(ctx, "bwl_id", func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id in (?)", bwIds).Where("status in (?)", []int{modelv2.BlackWhiteItemStatusModified, modelv2.BlackWhiteItemStatusRemovePendingApproval, modelv2.BlackWhiteItemStatusRemoveDraft, modelv2.BlackWhiteItemStatusPass}).Where("start_date <= ?", nowUnix).Where("end_date >= ?", nowUnix)
		})
		if innerErr != nil {
			return innerErr
		}
		for _, v := range groupCount {
			resMap[v.Id].ValidItems = v.Total
		}
		return nil
	})
	eg.Go(func() (innerErr error) {
		// 获取总名单数
		var groupCount []*modelv2.GroupIdCount
		groupCount, innerErr = s.BlackWhiteItemRepo.GroupCount(ctx, "bwl_id", func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id in (?)", bwIds).Where("status in (?)", []int{modelv2.BlackWhiteItemStatusPass, modelv2.BlackWhiteItemStatusRemovePendingApproval, modelv2.BlackWhiteItemStatusRemoveDraft, modelv2.BlackWhiteItemStatusModified})
		})
		if innerErr != nil {
			return innerErr
		}
		for _, v := range groupCount {
			resMap[v.Id].TotalItems = v.Total
		}
		return nil
	})
	eg.Go(func() (innerErr error) {
		// 获取必填字段
		var fields []*modelv2.RmsBlackWhiteField
		fields, innerErr = s.BlackWhiteFieldRepo.GetBlackWhiteFieldsBriefing(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id in (?)", bwIds).Where("required = ?", 1)
		})
		if innerErr != nil {
			return innerErr
		}
		for _, v := range fields {
			resMap[v.BwlID].RequiredFields = append(resMap[v.BwlID].RequiredFields, v.FieldName)
		}
		return nil
	})
	err = eg.Wait()

	return
}
