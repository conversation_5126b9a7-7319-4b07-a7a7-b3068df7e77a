package servicev2

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type EventStoreCfgService struct {
	EventStoreCfgRepo repo.RmsEventStoreCfg
	EventFieldRepo    repo.RmsEventField
}

func (s *EventStoreCfgService) Create(c *gin.Context, cp *modelv2.RmsEventStoreCfg) error {
	cp.SendToIntra = false
	return s.EventStoreCfgRepo.Create(c, nil, cp)
}

func (s *EventStoreCfgService) Update(c *gin.Context, id int64, cp *modelv2.RmsEventStoreCfg) error {
	return s.EventStoreCfgRepo.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	}, map[string]interface{}{
		modelv2.NewRmsEventStoreCfg().CheckPointField(): cp.CheckPoint,
		modelv2.NewRmsEventStoreCfg().PersistentField(): cp.Persistent,
		modelv2.NewRmsEventStoreCfg().StrField1Field():  cp.StrField1,
		modelv2.NewRmsEventStoreCfg().StrField2Field():  cp.StrField2,
		modelv2.NewRmsEventStoreCfg().StrField3Field():  cp.StrField3,
		modelv2.NewRmsEventStoreCfg().StrField4Field():  cp.StrField4,
		modelv2.NewRmsEventStoreCfg().StrField5Field():  cp.StrField5,
		modelv2.NewRmsEventStoreCfg().NumField1Field():  cp.NumField1,
		modelv2.NewRmsEventStoreCfg().NumField2Field():  cp.NumField2,
	})
}

func (s *EventStoreCfgService) Delete(c *gin.Context, id int64) error {
	return s.EventStoreCfgRepo.Delete(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
}

func (s *EventStoreCfgService) List(c *gin.Context, req *request_parameters.EventStoreCfgList) (total int64, li []*response_parameters.EventStoreCfgResp, err error) {
	var models []*modelv2.RmsEventStoreCfg
	total, models, err = s.EventStoreCfgRepo.List(c, filter.NewDefaultFilter(req.TimeRequest.Option, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		return db
	}, true)
	if err != nil {
		return 0, nil, err
	}
	for _, v := range models {
		li = append(li, &response_parameters.EventStoreCfgResp{
			EventStoreCfgUpdate: response_parameters.EventStoreCfgUpdate{
				ID: v.ID,
				EventStoreCfgCreate: response_parameters.EventStoreCfgCreate{
					CheckPoint: v.CheckPoint,
					Persistent: v.Persistent,
					StrField1:  v.StrField1,
					StrField2:  v.StrField2,
					StrField3:  v.StrField3,
					StrField4:  v.StrField4,
					StrField5:  v.StrField5,
					NumField1:  v.NumField1,
					NumField2:  v.NumField2,
				},
			},
		})
	}
	return
}

func (s *EventStoreCfgService) Retrieve(c *gin.Context, id int64) (*modelv2.RmsEventStoreCfg, error) {
	return s.EventStoreCfgRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
}

func (s *EventStoreCfgService) RetrieveByAPCode(c *gin.Context, apCode string) (*modelv2.RmsEventStoreCfg, error) {
	return s.EventStoreCfgRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("check_point=?", apCode)
	})
}

func (s *EventStoreCfgService) getEventStoreCfgFields(ctx context.Context) ([]string, error) {
	var redisKey = consts.RdbKeyEventStoreCfgPrefix

	// Get all field names from Redis hash set
	fields := redisutils.HKeys(redisKey)

	return fields, nil
}
func (s *EventStoreCfgService) UpdateEventFieldCache(ctx context.Context) error {
	var err error

	var cacheEventStoreCfgs = make(map[string]string) // checkPoint => model json
	keys, err := s.getEventStoreCfgFields(ctx)
	if err != nil {
		return err
	}

	var eventStoreCfgModels []*modelv2.RmsEventStoreCfg
	_, eventStoreCfgModels, err = s.EventStoreCfgRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to get rule list").WithErr(err)
	}
	for _, model := range eventStoreCfgModels {
		tmpJson, _ := jsoniter.MarshalToString(model)
		cacheEventStoreCfgs[model.CheckPoint] = tmpJson
	}
	var cmders []redis.Cmder
	cmders, err = redisutils.Pipeline(func(pipe redis.Pipeliner) error {
		for _, checkPointCode := range keys {
			if data, ok := cacheEventStoreCfgs[checkPointCode]; ok {
				delete(cacheEventStoreCfgs, checkPointCode)
				pipe.HSet(ctx, consts.RdbKeyEventStoreCfgPrefix, checkPointCode, data)
			} else {
				pipe.HDel(ctx, consts.RdbKeyEventStoreCfgPrefix, checkPointCode)
			}

		}
		if len(cacheEventStoreCfgs) > 0 {
			for accessPointCode, data := range cacheEventStoreCfgs {
				pipe.HSet(ctx, consts.RdbKeyEventStoreCfgPrefix, accessPointCode, data)
			}
		}
		return nil
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to update event store cfg cache.").WithErr(err)
	}
	for _, cmd := range cmders {
		if err := cmd.Err(); err != nil {
			return er.Internal.WithStack().WithMsg("Failed to event store cfg cache.").WithErr(err)
		}
	}

	return nil
}
