package servicev2

import (
	"context"
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type RuleParameterGroupService struct {
	RuleParameterGroupRepo     repo.RmsRuleParameterGroupRepo
	RuleParameterGroupBindRepo repo.RmsRuleParameterGroupBindRepo
}

func (s *RuleParameterGroupService) Check(c context.Context, cp *modelv2.RmsRuleParameterGroup) error {
	if cp.CheckPoint == "" {
		return er.InvalidArgument.WithStack().WithMsg("Access point cannot be empty")
	}
	if cp.Code == "" {
		return er.InvalidArgument.WithStack().WithMsg("Parameter group code")
	}

	// 一个接入点下只能有一个默认参数组
	if cp.DefaultFlag == 1 {
		lowPG, err := s.RuleParameterGroupRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("check_point = ? ", cp.CheckPoint)
		})
		if err != nil {
			if errors.Is(err, er.NotFound) {
				return nil
			}
			return err
		} else {
			if lowPG.Code != cp.Code {
				return er.Unimplemented.WithStack().WithMsg("An access point has a default parameter group. The current default parameter group code is: [" + lowPG.Code + "]")
			}
		}
	}
	return nil
}

func (s *RuleParameterGroupService) Create(c context.Context, cp *modelv2.RmsRuleParameterGroup) (err error) {
	if err = s.Check(c, cp); err != nil {
		return err
	}
	_, err = s.RuleParameterGroupRepo.Create(c, nil, cp)
	return
}

func (s *RuleParameterGroupService) Update(c context.Context, id int64, cp *modelv2.RmsRuleParameterGroup) (err error) {
	if err = s.Check(c, cp); err != nil {
		return err
	}
	return s.RuleParameterGroupRepo.Update(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	}, cp)
}

func (s *RuleParameterGroupService) Delete(c context.Context, id int64) error {
	_, err := s.RuleParameterGroupBindRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("group_id=?", id)
	})
	if err != nil {
		if errors.Is(err, er.NotFound) {
			return s.RuleParameterGroupRepo.Delete(c, nil, func(db *gorm.DB) *gorm.DB {
				return db.Where("id =? ", id)
			})
		}
		return err
	}
	return er.FailedPrecondition.WithStack().WithMsg("A bound parameter group cannot be deleted.")
}

func (s *RuleParameterGroupService) List(c context.Context, req *request_parameters.RuleParameterGroupList) (total int64, li []*modelv2.RmsRuleParameterGroup, err error) {
	total, li, err = s.RuleParameterGroupRepo.List(c, filter.NewDefaultFilter(nil, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.CheckPoint != "" {
			db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
		}
		if req.Code != "" {
			db = db.Where("code like ?", "%"+req.Code+"%")
		}
		return db
	}, true)
	return
}

func (s *RuleParameterGroupService) Retrieve(c context.Context, id int64) (*modelv2.RmsRuleParameterGroup, error) {
	return s.RuleParameterGroupRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}

func (s *RuleParameterGroupService) All(c *gin.Context) (*response_parameters.RuleParameterGroupAllResp, error) {
	_, li, err := s.RuleParameterGroupRepo.List(c, nil, func(db *gorm.DB) *gorm.DB {
		return db
	})
	if err != nil {
		return nil, err
	}
	resp := &response_parameters.RuleParameterGroupAllResp{}
	for _, v := range li {
		item := &response_parameters.RuleParameterGroupAllItem{
			ID:          v.ID,
			Code:        v.Code,
			Description: v.Description,
		}
		resp.ItemList = append(resp.ItemList, item)
	}
	return resp, nil
}

func (s *RuleParameterGroupService) AllExcludeDefault(c *gin.Context) (*response_parameters.RuleParameterGroupAllResp, error) {
	_, li, err := s.RuleParameterGroupRepo.List(c, nil, func(db *gorm.DB) *gorm.DB {
		return db
	})
	if err != nil {
		return nil, err
	}
	resp := &response_parameters.RuleParameterGroupAllResp{}
	for _, v := range li {
		item := &response_parameters.RuleParameterGroupAllItem{
			ID:          v.ID,
			Code:        v.Code,
			Description: v.Description,
		}

		resp.ItemList = append(resp.ItemList, item)
	}
	return resp, nil
}
