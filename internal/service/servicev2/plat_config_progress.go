package servicev2

import (
	"context"
	"errors"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gorm.io/gorm"
)

type ConfigProgressService struct {
	ConfigProgressRepo repo.ConfigProgressRepo
}

// CreateOrUpdate 创建或更新配置进度
func (s *ConfigProgressService) CreateOrUpdate(ctx context.Context, cp *modelv2.PlatConfigProgress) error {
	// 先查询是否存在
	_, err := s.ConfigProgressRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewPlatConfigProgress().CheckPointCodeField()+" = ?", cp.CheckPointCode)
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 不存在则创建
			err = s.ConfigProgressRepo.Create(ctx, nil, cp)
			if err != nil {
				return err
			}

			return nil
		}
		return err
	}

	// 存在则更新
	return s.ConfigProgressRepo.Updates(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewPlatConfigProgress().CheckPointCodeField()+" = ?", cp.CheckPointCode)
	}, map[string]interface{}{
		"progress": cp.Progress,
	})
}

// RetrieveByCode 根据编码获取配置进度
func (s *ConfigProgressService) RetrieveByCode(ctx context.Context, code string) (*modelv2.PlatConfigProgress, error) {
	item, err := s.ConfigProgressRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewPlatConfigProgress().CheckPointCodeField()+" = ?", code)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist.")
		}
		return nil, err
	}
	return item, nil
}
