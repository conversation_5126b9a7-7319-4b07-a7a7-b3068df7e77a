package servicev2

import (
	"context"
	"errors"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"golang.org/x/sync/errgroup"
	"sort"
	"strings"
	"sync"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type BlackWhiteAuditService struct {
	BlackWhiteListRepo        repo.BlackWhiteListRepo
	BlackWhiteItemRepo        repo.BlackWhiteItemRepo
	BlackWhiteFieldRepo       repo.BlackWhiteFieldRepo
	BlackWhiteAuditRepo       repo.BlackWhiteAuditRepo
	BlackWhiteAuditDetailRepo repo.BlackWhiteAuditDetailRepo
	BlackWhiteListService     *BlackWhiteListService
}
type BwFieldNameAndValues []*BwFieldNameAndValue
type BwFieldNameAndValue struct {
	FieldName  string
	FieldValue interface{}
}

func (s BwFieldNameAndValues) ToLineString() string {
	sort.Slice(s, func(i, j int) bool {
		return s[i].FieldName < s[j].FieldName
	})
	var line strings.Builder
	for _, nv := range s {
		line.WriteString(fmt.Sprintf(" %s=%v,", nv.FieldName, nv.FieldValue))
	}
	return strings.Trim(line.String(), ",")
}

func (s *BlackWhiteAuditService) LastAuditRecord(ctx context.Context, bwlID int64) (*modelv2.RmsBlackWhiteAudit, error) {
	auditModel, err := s.BlackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", bwlID).Order("id desc")
	})
	return auditModel, err
}

func (s *BlackWhiteAuditService) Detail(c context.Context, id int64) (res response_parameters.BlackWhiteAuditDetail, err error) {
	var auditModel *modelv2.RmsBlackWhiteAudit
	// 获取审核详情
	auditModel, err = s.BlackWhiteAuditRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return
	}
	res.BwlID = auditModel.BwlID
	res.State = auditModel.State
	res.SubmissionTime = auditModel.CreatedAt.Unix()
	res.Submitter = auditModel.Submitter
	res.Mode = auditModel.Mode
	//补充名单信息
	var listModel *modelv2.RmsBlackWhiteList
	listModel, err = s.BlackWhiteListRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =?", auditModel.BwlID)
	})
	if err != nil {
		return
	}
	res.ListName = listModel.Name
	res.ListType = listModel.Type
	if auditModel.Mode == modelv2.BlackWhiteAuditModeCreate || auditModel.Mode == modelv2.BlackWhiteAuditModeEdit || auditModel.Mode == modelv2.BlackWhiteAuditModeDelete {
		var ad *modelv2.RmsBlackWhiteAuditDetail
		ad, err = s.BlackWhiteAuditDetailRepo.First(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("audit_id = ?", auditModel.ID)
		})
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
			return
		}
		if err != nil {
			return
		}
		res.Detail = ad.Log
	}
	return
}

func (s *BlackWhiteAuditService) getItemChangeHistory(ctx context.Context, audit *modelv2.RmsBlackWhiteAudit) (string, error) {
	var eg errgroup.Group
	var detail strings.Builder
	var detailMutex sync.Mutex
	eg.Go(func() (innerErr error) {
		// 整理新增记录的信息
		var addBuilder strings.Builder
		addBuilder, innerErr = s.getAuditDetailAboutAdd(ctx, audit)
		if innerErr != nil {
			return
		}
		detailMutex.Lock()
		detail.WriteString(addBuilder.String())
		detailMutex.Unlock()
		return
	})
	eg.Go(func() (innerErr error) {
		// 整理更新记录的信息
		var upBuilder strings.Builder
		upBuilder, innerErr = s.getAuditDetailAboutUpdate(ctx, audit)
		if innerErr != nil {
			return
		}
		detailMutex.Lock()
		detail.WriteString(upBuilder.String())
		detailMutex.Unlock()
		return
	})
	eg.Go(func() (innerErr error) {
		// 整理更新记录的信息
		var rmBuilder strings.Builder
		rmBuilder, innerErr = s.getAuditDetailAboutRemove(ctx, audit)
		if innerErr != nil {
			return
		}
		detailMutex.Lock()
		detail.WriteString(rmBuilder.String())
		detailMutex.Unlock()
		return
	})
	err := eg.Wait()
	return detail.String(), err
}
func (s *BlackWhiteAuditService) getAuditDetailAboutAdd(c context.Context, auditModel *modelv2.RmsBlackWhiteAudit) (addBuilder strings.Builder, innerErr error) {
	var itemModels []*modelv2.RmsBlackWhiteItem
	_, itemModels, innerErr = s.BlackWhiteItemRepo.GetBlackWhiteItems(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("audit_id =?", auditModel.ID).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("parent_id = ?", 0)
	})
	if innerErr != nil {
		return
	}
	if len(itemModels) > 0 {
		addBuilder.WriteString("Created Records:\n")
		for idx, item := range itemModels {
			var fieldNvs BwFieldNameAndValues
			fields, _ := item.Extends.ToFields()

			for fieldName, fieldVal := range fields {
				fieldNvs = append(fieldNvs, &BwFieldNameAndValue{
					FieldName:  fieldName,
					FieldValue: fieldVal,
				})
			}
			if item.StartDate > 0 {
				fieldNvs = append(fieldNvs, &BwFieldNameAndValue{
					FieldName:  "effective start date",
					FieldValue: time.Unix(item.StartDate, 0).Local().Format("2006-01-02"),
				})
			}
			if item.EndDate > 0 {
				var itemEndDate string
				tmpEndDate := time.Unix(item.EndDate, 0).Local()
				itemEndDate = tmpEndDate.Format("2006-01-02")
				if tmpEndDate.Equal(modelv2.ForeverTime) {
					itemEndDate = "永远"
				}
				fieldNvs = append(fieldNvs, &BwFieldNameAndValue{
					FieldName:  "expiration date",
					FieldValue: itemEndDate,
				})
			}
			addBuilder.WriteString(fmt.Sprintf("Record %d:", idx))
			addBuilder.WriteString(fieldNvs.ToLineString())
			addBuilder.WriteString("\n")
		}
		addBuilder.WriteString(fmt.Sprintf("Total Records %d\n", len(itemModels)))

	}
	return
}
func (s *BlackWhiteAuditService) getAuditDetailAboutRemove(c context.Context, auditModel *modelv2.RmsBlackWhiteAudit) (rmBuilder strings.Builder, innerErr error) {
	var itemModels []*modelv2.RmsBlackWhiteItem
	_, itemModels, innerErr = s.BlackWhiteItemRepo.GetBlackWhiteItems(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("audit_id =?", auditModel.ID).Where("status = ?", modelv2.BlackWhiteItemStatusRemovePendingApproval)
	})
	if innerErr != nil {
		return
	}
	if len(itemModels) > 0 {
		rmBuilder.WriteString("Remove Records:\n")
		for idx, item := range itemModels {
			var fieldNvs BwFieldNameAndValues
			fields, _ := item.Extends.ToFields()
			for fieldName, fieldVal := range fields {
				fieldNvs = append(fieldNvs, &BwFieldNameAndValue{
					FieldName:  fieldName,
					FieldValue: fieldVal,
				})
			}

			rmBuilder.WriteString(fmt.Sprintf("Record %d:", idx))
			rmBuilder.WriteString(fieldNvs.ToLineString())
			rmBuilder.WriteString("\n")
		}
		rmBuilder.WriteString(fmt.Sprintf("Total Records %d\n", len(itemModels)))

	}
	return
}
func (s *BlackWhiteAuditService) getAuditDetailAboutUpdate(c context.Context, auditModel *modelv2.RmsBlackWhiteAudit) (upBuilder strings.Builder, innerErr error) {
	var itemModels []*modelv2.RmsBlackWhiteItem
	var parentModels map[int64]*modelv2.RmsBlackWhiteItem
	parentModels, innerErr = s.BlackWhiteItemRepo.GetParentItems(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("audit_id =?", auditModel.ID).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("parent_id > ?", 0)
	})

	if innerErr != nil {
		return
	}
	_, itemModels, innerErr = s.BlackWhiteItemRepo.GetBlackWhiteItems(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("audit_id =?", auditModel.ID).Where("status =?", modelv2.BlackWhiteItemStatusPendingApproval).Where("parent_id >?", 0)
	})
	if innerErr != nil {
		return
	}
	if len(itemModels) > 0 {
		upBuilder.WriteString("Updates Records:\n")
		for idx, item := range itemModels {
			if parent, ok := parentModels[item.ParentID]; ok {
				var tmpDetail strings.Builder
				currentItemExtends, _ := item.Extends.ToFields()
				parentItemExtends, _ := parent.Extends.ToFields()
				// 对比parent
				diff := currentItemExtends.Diff(parentItemExtends)
				tmpDetail.WriteString(fmt.Sprintf(" %d:", idx+1))

				if len(diff) > 0 {
					for _, diffID := range diff {
						tmpDetail.WriteString(fmt.Sprintf(" %s: %v -> %v,", diffID, parentItemExtends[diffID], currentItemExtends[diffID]))
					}
				}
				if parent.StartDate != item.StartDate {
					tmpDetail.WriteString(fmt.Sprintf("effective start date: %v -> %v,",
						time.Unix(parent.StartDate, 0).Local().Format("2006-01-02"),
						time.Unix(item.StartDate, 0).Local().Format("2006-01-02")))
				}
				if parent.EndDate != item.EndDate {
					var parentEndDate, itemEndDate string
					tmpParentEndDate := time.Unix(parent.EndDate, 0).Local()
					parentEndDate = tmpParentEndDate.Format("2006-01-02")
					if tmpParentEndDate.Equal(modelv2.ForeverTime) {
						parentEndDate = "永远"
					}
					tmpEndDate := time.Unix(item.EndDate, 0).Local()
					itemEndDate = tmpEndDate.Format("2006-01-02")
					if tmpEndDate.Equal(modelv2.ForeverTime) {
						itemEndDate = "永远"
					}
					tmpDetail.WriteString(fmt.Sprintf("expiration date: %v -> %v,",
						parentEndDate,
						itemEndDate))
				}
				upBuilder.WriteString(strings.Trim(tmpDetail.String(), ","))
				upBuilder.WriteString("\n")

			}
		}
		upBuilder.WriteString(fmt.Sprintf("Total Records %d\n", len(itemModels)))
	}

	return
}

func (s *BlackWhiteAuditService) Audit(c context.Context, approver string, req *request_parameters.BlackWhiteAudit) (err error) {
	bwlAudit, err := s.BlackWhiteAuditRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.ID)
	})
	if err != nil {
		return
	}
	var state = modelv2.BlackWhiteAuditStatePass
	if !req.Pass {
		state = modelv2.BlackWhiteAuditStateReject
	}
	if bwlAudit.Submitter == approver {
		//审批人不能是提交人，不能自提自审
		err = er.InvalidArgument.WithMsg("The submitter and the approver cannot be the same person.")
		return
	}
	err = s.BlackWhiteAuditRepo.Audit(c, nil, bwlAudit, state, approver, req.RejectReason)
	return
}

func (s *BlackWhiteAuditService) List(c context.Context, req *request_parameters.BlackWhiteAuditRequest) (total int64, respLi []*response_parameters.BlackWhiteAuditList, err error) {
	var li []*modelv2.RmsBlackWhiteAudit
	var listMaps = make(map[int64][]*response_parameters.BlackWhiteAuditList)
	var listIds []int64
	if req.Type == 3 {
		total, li, err = s.BlackWhiteAuditRepo.List(c, nil, func(db *gorm.DB) *gorm.DB {
			db = db.Where("state = ?", modelv2.BlackWhiteAuditStatePending).Where("notified = ?", 0)
			return db.Order("id desc")
		}, true)
	} else {
		total, li, err = s.BlackWhiteAuditRepo.List(c, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
			switch req.Type {
			case 1:
				//未处理
				db = db.Where("state = ?", modelv2.BlackWhiteAuditStatePending)
			case 2:
				//已处理
				db = db.Where("state in (?)", []int{modelv2.BlackWhiteAuditStatePass, modelv2.BlackWhiteAuditStateReject})
			}
			return db.Order("id desc")
		}, true)
	}

	if err != nil {
		return
	}
	for _, v := range li {
		listIds = append(listIds, v.BwlID)
		tmpRes := &response_parameters.BlackWhiteAuditList{
			Id:             v.ID,
			Submitter:      v.Submitter,
			BwlID:          v.BwlID,
			SubmissionTime: v.CreatedAt.Unix(),
			Mode:           v.Mode,
			Auditor:        v.Approver,
			State:          v.State,
		}
		if v.State != modelv2.BlackWhiteAuditStatePending {
			tmpRes.AuditTime = v.UpdatedAt.Unix()
		}
		listMaps[v.BwlID] = append(listMaps[v.BwlID], tmpRes)
		respLi = append(respLi, tmpRes)
	}
	var listModels []*modelv2.RmsBlackWhiteList
	_, listModels, err = s.BlackWhiteListRepo.List(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id in (?)", listIds)
	})
	if err != nil {
		return
	}
	for _, v := range listModels {
		for _, res := range listMaps[v.ID] {
			res.ListName = v.Name
			res.ListType = v.Type
		}
	}
	return
}

// Mark 标记审核提醒为已提醒
func (s *BlackWhiteAuditService) Mark(c context.Context) (err error) {
	err = s.BlackWhiteAuditRepo.Updates(c, nil, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("notified = ?", 0)
	}, map[string]interface{}{
		"notified": 1,
	})
	return
}
