package servicev2

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// DataSourceService 数据源服务
type DataSourceService struct {
	DataSourceRepo      repo.DataSourceRepo
	DataSourceTableRepo repo.DataSourceTableRepo
}

func (s *DataSourceService) Tables(ctx context.Context, id int64) (res []*entity.DataSourceTable, err error) {
	item, err := s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return nil, er.Internal.WithErr(err)
	}
	var tmpTables []*entity.DataSourceTable
	var tables []string
	tables, err = s.DataSourceTableRepo.Tables(ctx, item)
	if err != nil {
		err = er.Internal.WithErr(err)
		return
	}
	for _, table := range tables {
		tmpTables = append(tmpTables, &entity.DataSourceTable{
			DataSourceID: item.ID,
			TableName:    table,
		})
	}
	return tmpTables, nil
}

// Create 创建数据源
func (s *DataSourceService) Create(ctx context.Context, req *request_parameters.DataSourceCreate) (*response_parameters.DataSourceDetail, error) {
	// 检查数据源名称是否已存在
	count, err := s.DataSourceRepo.Count(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("source_name = ?", req.SourceName)
	})
	if err != nil {
		return nil, er.Internal.WithErr(err)
	}
	if count > 0 {
		return nil, er.AlreadyExists.WithMsg("The current data source already exists.")
	}

	// 测试连接
	dataSource := &modelv2.RmsDataSource{
		SourceName:   req.SourceName,
		SourceType:   req.SourceType,
		Address:      req.Address,
		Port:         req.Port,
		Username:     req.Username,
		Password:     req.Password,
		DatabaseName: req.DatabaseName,
		Description:  req.Description,
		ConnStatus:   modelv2.ConnStatusDisconnected,
	}

	connected, err := s.DataSourceRepo.TestConnection(ctx, dataSource)
	if err != nil {
		dataSource.ConnStatus = modelv2.ConnStatusDisconnected
		dataSource.FailReason = err.Error()
	} else if connected {
		dataSource.ConnStatus = modelv2.ConnStatusConnected
		dataSource.LastCheckTime = time.Now().Unix()
	}
	// 创建数据源
	err = s.DataSourceRepo.Create(ctx, nil, dataSource)
	if err != nil {
		return nil, err
	}

	// 返回创建的数据源
	detail := &response_parameters.DataSourceDetail{
		ID:            dataSource.ID,
		SourceName:    dataSource.SourceName,
		SourceType:    dataSource.SourceType,
		Address:       dataSource.Address,
		Port:          dataSource.Port,
		Username:      dataSource.Username,
		DatabaseName:  dataSource.DatabaseName,
		Description:   dataSource.Description,
		ConnStatus:    dataSource.ConnStatus,
		LastCheckTime: time.Unix(dataSource.LastCheckTime, 0).Local().Format("2006-01-02 15:04:05"),
		FailReason:    dataSource.FailReason,
		UpdatedAt:     dataSource.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return detail, err
}

// Update 更新数据源
func (s *DataSourceService) Update(ctx context.Context, req *request_parameters.DataSourceUpdate) (*response_parameters.DataSourceDetail, error) {
	// 检查数据源是否存在
	dataSource, err := s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.ID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist.")
		}
		return nil, er.Internal.WithMsg("Database exception")
	}

	// 检查数据源名称是否已存在（排除自身）
	count, err := s.DataSourceRepo.Count(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("source_name = ? AND id != ?", req.SourceName, req.ID)
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Database exception").WithErr(err)
	}
	if count > 0 {
		return nil, er.Internal.WithMsg("Data source name already exists.")
	}

	// 更新数据源信息
	dataSource.ID = req.ID
	dataSource.SourceName = req.SourceName
	dataSource.SourceType = req.SourceType
	dataSource.Address = req.Address
	dataSource.Port = req.Port
	dataSource.Username = req.Username
	dataSource.Password = req.Password
	dataSource.DatabaseName = req.DatabaseName
	dataSource.Description = req.Description

	// 测试连接
	connected, err := s.DataSourceRepo.TestConnection(ctx, dataSource)
	if err != nil {
		dataSource.FailReason = err.Error()
		dataSource.ConnStatus = modelv2.ConnStatusDisconnected
	} else if connected {
		dataSource.ConnStatus = modelv2.ConnStatusConnected
		dataSource.FailReason = ""
		dataSource.LastCheckTime = time.Now().Unix()
	}
	// 更新数据源
	err = s.DataSourceRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.ID)
	}, dataSource)
	if err != nil {
		return nil, er.Internal.WithMsg("Database exception").WithErr(err)
	}

	// 返回更新后的数据源
	detail := &response_parameters.DataSourceDetail{
		ID:            dataSource.ID,
		SourceName:    dataSource.SourceName,
		SourceType:    dataSource.SourceType,
		Address:       dataSource.Address,
		Port:          dataSource.Port,
		Username:      dataSource.Username,
		DatabaseName:  dataSource.DatabaseName,
		Description:   dataSource.Description,
		ConnStatus:    dataSource.ConnStatus,
		LastCheckTime: time.Unix(dataSource.LastCheckTime, 0).Local().Format("2006-01-02 15:04:05"),
		FailReason:    dataSource.FailReason,
		UpdatedAt:     dataSource.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return detail, nil
}

// Delete 删除数据源
func (s *DataSourceService) Delete(ctx context.Context, id int64) error {

	// 删除数据源
	err := s.DataSourceRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return er.Internal.WithMsg("Database exception").WithErr(err)
	}

	return nil
}

// List 获取数据源列表
func (s *DataSourceService) List(ctx context.Context, req *request_parameters.DataSourceList) (total int64, res []*response_parameters.DataSourceList, err error) {
	// 构建查询条件
	total, items, err := s.DataSourceRepo.List(ctx, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.SourceName != nil && *req.SourceName != "" {
			db = db.Where("source_name LIKE ?", "%"+*req.SourceName+"%")
		}
		if req.SourceType != nil && *req.SourceType != "" {
			db = db.Where("source_type = ?", *req.SourceType)
		}
		if req.Valid == 1 {
			db = db.Where("conn_status = ?", modelv2.ConnStatusConnected)
		}
		if req.Valid == 2 {
			db = db.Where("conn_status = ?", modelv2.ConnStatusDisconnected)
		}
		return db.Order("id desc")
	}, true)
	if err != nil {
		return
	}
	for _, item := range items {
		res = append(res, &response_parameters.DataSourceList{
			ID:         item.ID,
			SourceName: item.SourceName,
			SourceType: item.SourceType,
			Address:    item.Address,
			ConnStatus: item.ConnStatus,
		})
	}
	return
}

// Retrieve 获取数据源详情
func (s *DataSourceService) Retrieve(ctx context.Context, id int64) (*response_parameters.DataSourceDetail, error) {
	// 获取数据源详情
	dataSource, err := s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist.")
		}
		return nil, er.Internal.WithMsg("Database exception")
	}

	// 转换为响应格式
	detail := &response_parameters.DataSourceDetail{
		ID:            dataSource.ID,
		SourceName:    dataSource.SourceName,
		SourceType:    dataSource.SourceType,
		Address:       dataSource.Address,
		Port:          dataSource.Port,
		Username:      dataSource.Username,
		DatabaseName:  dataSource.DatabaseName,
		Description:   dataSource.Description,
		Password:      dataSource.Password,
		ConnStatus:    dataSource.ConnStatus,
		LastCheckTime: time.Unix(dataSource.LastCheckTime, 0).Format("2006-01-02 15:04:05"),
		FailReason:    dataSource.FailReason,
		UpdatedAt:     dataSource.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return detail, nil
}

// TestConnection 测试数据源连接
func (s *DataSourceService) TestConnection(ctx context.Context, req *request_parameters.DataSourceTest) (*response_parameters.DataSourceTestResp, error) {
	// 构建数据源对象
	dataSource := &modelv2.RmsDataSource{
		SourceName:   req.SourceName,
		SourceType:   req.SourceType,
		Address:      req.Address,
		Port:         req.Port,
		Username:     req.Username,
		Password:     req.Password,
		DatabaseName: req.DatabaseName,
	}

	// 测试连接
	connected, err := s.DataSourceRepo.TestConnection(ctx, dataSource)
	resp := &response_parameters.DataSourceTestResp{
		Connected: connected,
	}

	if err != nil {
		resp.Message = fmt.Sprintf("连接失败: %s", err.Error())
		return resp, nil
	}

	if connected {
		resp.Message = "连接成功"
	} else {
		resp.Message = "连接失败"
	}

	return resp, nil
}
