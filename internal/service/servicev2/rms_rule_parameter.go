package servicev2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type RuleParameterService struct {
	RuleParameterRepo repo.RmsRuleParameterRepo
}

func (s *RuleParameterService) Create(c context.Context, cp *modelv2.RmsRuleParameter) error {
	var _, err = s.RuleParameterRepo.Create(c, nil, cp)
	return err
}

func (s *RuleParameterService) Update(c context.Context, id int64, cp *modelv2.RmsRuleParameter) error {
	return s.RuleParameterRepo.Update(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =? ", id)
	}, cp)
}

func (s *RuleParameterService) Delete(c context.Context, id int64) error {
	return s.RuleParameterRepo.Delete(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =? ", id)
	})
}

func (s *RuleParameterService) List(c context.Context, req *request_parameters.RuleParameterList) (total int64, li []*modelv2.RmsRuleParameter, err error) {
	total, li, err = s.RuleParameterRepo.List(c, filter.NewDefaultFilter(nil, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.CheckPoint != "" {
			db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
		}
		if req.ParamType != "" {
			db = db.Where("param_type=?", req.ParamType)
		}
		if req.Code != "" {
			db = db.Where("code like ?", "%"+req.Code+"%")
		}
		if req.Description != "" {
			db = db.Where("description like ?", "%"+req.Description+"%")
		}
		return db
	}, true)
	if err != nil {
		return
	}
	return
}

func (s *RuleParameterService) Retrieve(c context.Context, id int64) (*modelv2.RmsRuleParameter, error) {
	return s.RuleParameterRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =? ", id)
	})
}
