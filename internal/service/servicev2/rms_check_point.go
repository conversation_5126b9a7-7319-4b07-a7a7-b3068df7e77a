package servicev2

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"

	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// CheckPointService 检查点服务
// 负责业务逻辑校验，调用datav2层
// 所有数据库错误通过er.ConvertDBError处理
// 业务错误按er规范封装

type CheckPointService struct {
	CheckPointRepo  repo.RmsCheckPoint
	FilterFieldRepo repo.RmsFilterField
	ConfigProgress  repo.ConfigProgressRepo
}

func NewCheckPointService() *CheckPointService {
	return &CheckPointService{
		CheckPointRepo:  datav2.NewRmsCheckPoint(),
		FilterFieldRepo: datav2.NewRmsFilterField(),
		ConfigProgress:  datav2.NewRmsConfigProgress(),
	}
}
func (s *CheckPointService) ValidateBusinessType(businessType int64) error {
	if businessType != modelv2.BusinessTypeAcquiring &&
		businessType != modelv2.BusinessTypeIssuing &&
		businessType != modelv2.BusinessTypeBanking {
		return er.InvalidArgument.WithMsg("Business type must be acquiring, issuing or banking")
	}
	return nil
}

// Create 创建检查点
func (s *CheckPointService) Create(ctx context.Context, model *modelv2.RmsCheckPoint) (err error) {
	if err = s.ValidateBusinessType(model.BusinessType); err != nil {
		return err
	}
	_, err = s.CheckPointRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsCheckPoint().CodeField(), model.Code)
	})
	if err == nil {
		return er.AlreadyExists.WithMsg("Check point already exists")
	}
	return s.CheckPointRepo.Create(ctx, nil, model)
}

// Update 更新检查点
func (s *CheckPointService) Update(ctx context.Context, req *request_parameters.CheckPointUpdate) (cp *modelv2.RmsCheckPoint, err error) {
	cp, err = s.CheckPointRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.ID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, er.NotFound.WithMsg("Check point not found")
	}
	if err != nil {
		return nil, err
	}
	cp.Code = req.Code
	cp.Label = req.Name
	cp.CheckDuplicate = req.CheckDuplicate
	cp.Memo = req.Remark
	cp.AlwaysRun = req.AlwaysRun

	return cp, s.CheckPointRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsCheckPoint().IDField()+" = ?", req.ID)
	}, cp)
}

// Delete 删除检查点
func (s *CheckPointService) Delete(ctx context.Context, id int64) error {
	return s.CheckPointRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsCheckPoint().IDField()+" = ?", id)
	})
}

// List 检查点列表，支持分页和条件过滤
func (s *CheckPointService) List(ctx context.Context, req *filter.DefaultFilter, code, label string) (total int64, items []*response_parameters.CheckPointResp, err error) {
	fn := func(db *gorm.DB) *gorm.DB {
		if code != "" {
			db = db.Where(modelv2.NewRmsCheckPoint().CodeField()+" LIKE ?", "%"+code+"%")
		}
		if label != "" {
			db = db.Where(modelv2.NewRmsCheckPoint().LabelField()+" LIKE ?", "%"+label+"%")
		}
		return db
	}
	var tmpItems []*modelv2.RmsCheckPoint

	total, tmpItems, err = s.CheckPointRepo.List(ctx, req, fn, true)
	if err != nil {
		return
	}
	for _, item := range tmpItems {
		tmpItem := &response_parameters.CheckPointResp{
			ID:                 item.ID,
			Code:               item.Code,
			Name:               item.Label,
			CheckDuplicate:     item.CheckDuplicate,
			Remark:             item.Memo,
			AlwaysRun:          item.AlwaysRun,
			BusinessType:       item.BusinessType,
			DefaultPkFieldName: item.DefaultPkFieldName,
		}

		var p *modelv2.PlatConfigProgress
		p, err = s.ConfigProgress.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewPlatConfigProgress().CheckPointCodeField()+" = ?", item.Code)
		})
		if err == nil {
			tmpItem.ConfigProgress = p.Progress
		}
		var fli []*modelv2.RmsFilterField
		fli, err = s.FilterFieldRepo.AllByAPCode(ctx, item.Code)
		if err != nil {
			return
		}
		var filterFields = make([]string, 0)
		for _, v := range fli {
			filterFields = append(filterFields, v.FieldName)
		}
		var tmpFilterFieldsJson string
		tmpFilterFieldsJson, err = jsoniter.MarshalToString(filterFields)
		if err != nil {
			return
		}
		tmpItem.FilterFields = tmpFilterFieldsJson
		items = append(items, tmpItem)
	}
	return
}

// RetrieveByID 根据ID获取检查点
func (s *CheckPointService) RetrieveByID(ctx context.Context, id int64) (*modelv2.RmsCheckPoint, error) {
	return s.CheckPointRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsCheckPoint().IDField()+" = ?", id)
	})
}

// RetrieveByCode 根据Code获取检查点
func (s *CheckPointService) RetrieveByCode(ctx context.Context, code string) (response_parameters.CheckPointResp, error) {
	var checkPoint *modelv2.RmsCheckPoint
	var err error
	checkPoint, err = s.CheckPointRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsCheckPoint().CodeField()+" = ?", code)
	})
	if err != nil {
		return response_parameters.CheckPointResp{}, err
	}
	var resp response_parameters.CheckPointResp
	resp.ID = checkPoint.ID
	resp.Code = checkPoint.Code
	resp.Name = checkPoint.Label
	resp.CheckDuplicate = checkPoint.CheckDuplicate
	resp.Remark = checkPoint.Memo
	resp.AlwaysRun = checkPoint.AlwaysRun
	resp.BusinessType = checkPoint.BusinessType
	if checkPoint.FilterFields == nil || *checkPoint.FilterFields == "" {
		resp.FilterFields = "[]"
	} else {
		resp.FilterFields = *checkPoint.FilterFields

	}
	resp.DefaultPkFieldName = checkPoint.DefaultPkFieldName
	var p *modelv2.PlatConfigProgress
	p, err = s.ConfigProgress.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewPlatConfigProgress().CheckPointCodeField()+" = ?", checkPoint.Code)
	})
	if err == nil {
		resp.ConfigProgress = p.Progress
	}
	return resp, nil
}
func (s *CheckPointService) getCheckPointKeys(ctx context.Context) ([]string, error) {
	var (
		cursor uint64
		values []string
		match  = consts.RdbKeyAccessPointPrefix + "*"
	)

	for {
		// 使用 SCAN 遍历匹配的 key
		keys, nextCursor, err := global.Rdb.Scan(ctx, cursor, match, 100).Result()
		if err != nil {
			return nil, fmt.Errorf("failed to scan keys: %w", err)
		}
		cursor = nextCursor
		values = append(values, keys...)
		if cursor == 0 {
			break
		}
	}
	return values, nil
}
func (s *CheckPointService) UpdateCheckPointCache(ctx context.Context) error {
	var err error

	var cacheCheckPoints = make(map[string]string) // access_point => *modelv2.RmsCheckPoint
	keys, err := s.getCheckPointKeys(ctx)
	if err != nil {
		return err
	}

	var checkPointModels []*modelv2.RmsCheckPoint
	_, checkPointModels, err = s.CheckPointRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to get rule list").WithErr(err)
	}
	for _, model := range checkPointModels {
		tmpJson, _ := jsoniter.MarshalToString(model)
		cacheCheckPoints[model.Code] = tmpJson
	}
	var cmders []redis.Cmder
	cmders, err = redisutils.Pipeline(func(pipe redis.Pipeliner) error {
		for _, accessPointCode := range keys {
			cp := strings.ReplaceAll(accessPointCode, consts.RdbKeyAccessPointPrefix, "")
			if data, ok := cacheCheckPoints[cp]; ok {
				delete(cacheCheckPoints, cp)
				pipe.Set(ctx, accessPointCode, data, 0)
			} else {
				pipe.Del(ctx, accessPointCode)
			}

		}
		if len(cacheCheckPoints) > 0 {
			for accessPointCode, data := range cacheCheckPoints {
				pipe.Set(ctx, consts.RdbKeyAccessPointPrefix+accessPointCode, data, 0)
			}
		}
		return nil
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to update check point cache.").WithErr(err)
	}
	for _, cmd := range cmders {
		if err := cmd.Err(); err != nil {
			return er.Internal.WithStack().WithMsg("Failed to check point cache.").WithErr(err)
		}
	}

	return nil
}
