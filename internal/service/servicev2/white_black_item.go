package servicev2

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"go.uber.org/zap"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/excel"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/sha256"
)

type BlackWhiteItemService struct {
	BlackWhiteListRepo  repo.BlackWhiteListRepo
	BlackWhiteItemRepo  repo.BlackWhiteItemRepo
	BlackWhiteFieldRepo repo.BlackWhiteFieldRepo
	BlackWhiteAuditRepo repo.BlackWhiteAuditRepo
}

// validExtends validates the extended fields against the required fields and their data types
func (s *BlackWhiteItemService) validExtends(fields entity.Fields, extends map[string]interface{}) (err error) {

	for _, field := range fields {
		var vv interface{}
		var ok bool
		vv, ok = extends[field.Name]
		if !ok {
			err = er.BlackWhiteInvalidColumn
			return
		}
		if (vv == nil || vv == "") && field.IsRequired {
			err = er.BlackWhiteFileInvalidColumn.WithMsg(fmt.Sprintf("Missing required data in column [%s].", field.Name))
			return
		}
	}
	if len(extends) != len(fields) {
		err = er.BlackWhiteInvalidColumn.WithMsg("Field mismatch")
		return
	}

	return
}

// Update 更新黑白名单记录明细
// 实际逻辑是新增一条记录指向旧记录
func (s *BlackWhiteItemService) Update(c context.Context, submitter string, req *request_parameters.UpdateBlackWhiteItem) (id int64, err error) {
	// 获取黑白名单记录，并判断当前是否处于审核状态
	var bwl *modelv2.RmsBlackWhiteList
	var parentItem *modelv2.RmsBlackWhiteItem
	bwl, err = BlockPendingChanges(c, s.BlackWhiteListRepo, req.BwlID)
	if errors.Is(err, er.NotFound) {
		return
	}
	if err != nil {
		return
	}
	// 判断黑白名单记录明细是否存在
	parentItem, err = s.BlackWhiteItemRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.ID)
	})
	if errors.Is(err, er.NotFound) {
		err = er.BlackWhiteItemInvalid.WithMsg("Detail does not exist.")
		return
	}
	if err != nil {
		return
	}
	if parentItem.Status == modelv2.BlackWhiteItemStatusRemoveDraft {
		//准备删除的记录无法进行编辑
		err = er.BlackWhiteItemInvalid.WithMsg("The current record is about to be deleted and cannot be edited.")
		return
	}
	// 检查请求参数中的字段是否在黑白名单记录中
	fields, err := s.BlackWhiteListRepo.Fields(c, bwl.ID)
	if err != nil {
		return
	}
	err = s.validExtends(fields, req.Extends)
	if err != nil {
		return
	}
	requiredSha256 := GenerateBlackWhiteItemSha256(fields, req.Extends)
	_, err = s.BlackWhiteItemRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("required_sha256 = ?", requiredSha256).
			Where("bwl_id = ?", bwl.ID).Where("id != ?", req.ID).
			Where("status in (?)", modelv2.GetBwItemStatus(modelv2.BwItemDraftStatus, modelv2.BwItemAuditPendingStatus, modelv2.BwItemNormalStatus))
	})
	if !errors.Is(err, er.NotFound) && err != nil {
		return
	}
	if err == nil {
		err = er.BlackWhiteDuplicateRecords
		return
	}
	err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {
		if parentItem.Status == modelv2.BlackWhiteItemStatusDraft {
			//还是草稿的记录直接原记录更新
			parentItem.StartDate = req.StartDate
			parentItem.EndDate = req.EndDate
			parentItem.RemainingDay = parentItem.CalculationRemainingDay()
			parentItem.Extends = modelv2.BlackWhiteItemExtends(req.Extends).ToJson(modelv2.BwSkipMarshalFields)
			parentItem.RequiredSha256 = GenerateBlackWhiteItemSha256(fields, req.Extends)
			parentItem.Status = modelv2.BlackWhiteItemStatusDraft
			innerErr = s.BlackWhiteItemRepo.Updates(c, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", req.ID)
			}, map[string]interface{}{
				"start_date":      req.StartDate,
				"end_date":        req.EndDate,
				"remaining":       (req.EndDate - req.StartDate) / (60 * 60 * 24),
				"extends":         modelv2.BlackWhiteItemExtends(req.Extends).ToJson(modelv2.BwSkipMarshalFields),
				"required_sha256": GenerateBlackWhiteItemSha256(fields, req.Extends),
			})
		} else {
			tmpModel := &modelv2.RmsBlackWhiteItem{
				BwlID:          req.BwlID,
				StartDate:      req.StartDate,
				EndDate:        req.EndDate,
				Extends:        modelv2.BlackWhiteItemExtends(req.Extends).ToJson(modelv2.BwSkipMarshalFields),
				RequiredSha256: GenerateBlackWhiteItemSha256(fields, req.Extends),
				Status:         modelv2.BlackWhiteItemStatusDraft,
				ParentID:       req.ID,
			}
			tmpModel.RemainingDay = tmpModel.CalculationRemainingDay()
			id, innerErr = s.BlackWhiteItemRepo.Create(c, tx, tmpModel)
			if innerErr != nil {
				return
			}
			innerErr = s.BlackWhiteItemRepo.Updates(c, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", req.ID)
			}, map[string]interface{}{
				"status": modelv2.BlackWhiteItemStatusModified,
			})
		}
		if innerErr != nil {
			return
		}
		var updateBwl = map[string]interface{}{"created_by": submitter}

		if bwl.State == modelv2.BlackWhiteListStatePass || bwl.State == modelv2.BlackWhiteListStateReject {
			//如果黑白名单不属于第一次提交数据情况，就更新成修改草稿情况
			updateBwl["state"] = modelv2.BlackWhiteListStateModifyDraft
		}
		innerErr = s.BlackWhiteListRepo.Updates(c, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", req.BwlID)
		}, updateBwl)
		return

	})
	if err != nil {
		err = er.InvalidArgument.WithMsg("Database exception").WithErr(err)
		return
	}
	return
}

// Delete 删除黑白名单记录明细
func (s *BlackWhiteItemService) Delete(c context.Context, session *gorm.DB, submitter string, bwiID int64) (err error) {
	var item *modelv2.RmsBlackWhiteItem

	// 判断黑白名单记录明细是否存在
	item, err = s.BlackWhiteItemRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", bwiID)
	})
	if errors.Is(err, er.NotFound) {
		err = er.BlackWhiteItemInvalid.WithMsg("Detail does not exist.")
		return
	}
	if err != nil {
		return
	}
	// 获取黑白名单记录，并判断当前是否处于审核状态
	var bwl *modelv2.RmsBlackWhiteList
	bwl, err = BlockPendingChanges(c, s.BlackWhiteListRepo, item.BwlID)
	if err != nil {
		return
	}

	err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {
		if item.ParentID > 0 && item.Status != modelv2.BlackWhiteItemStatusRemoved {
			innerErr = s.Delete(c, tx, submitter, item.ParentID)
			if innerErr != nil {
				return
			}
		}
		switch item.Status {
		case modelv2.BlackWhiteItemStatusDraft:
			//还是草稿的记录直接删除
			return s.BlackWhiteItemRepo.Delete(c, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", item.ID)
			})
		case modelv2.BlackWhiteItemStatusPass, modelv2.BlackWhiteItemStatusModified:
			innerErr = s.BlackWhiteItemRepo.Remove(c, tx, item.ID)
			if innerErr != nil {
				return innerErr
			}
			var updateBwl = map[string]interface{}{"created_by": submitter}
			if bwl.State == modelv2.BlackWhiteListStatePass || bwl.State == modelv2.BlackWhiteListStateReject {
				//如果黑白名单不属于第一次提交数据情况，就更新成修改草稿情况
				updateBwl["state"] = modelv2.BlackWhiteListStateModifyDraft
			}
			innerErr = s.BlackWhiteListRepo.Updates(c, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", item.BwlID)
			}, updateBwl)
		}

		return innerErr
	})

	return
}

// Add 创建黑白名单记录明细
func (s *BlackWhiteItemService) Add(c context.Context, submitter string, req *request_parameters.AddBlackWhiteItem) (id int64, err error) {
	// 获取黑白名单记录
	var bwl *modelv2.RmsBlackWhiteList
	bwl, err = BlockPendingChanges(c, s.BlackWhiteListRepo, req.BwlID)
	if errors.Is(err, er.NotFound) {
		return
	}
	if err != nil {
		return
	}

	// 检查请求参数中的字段是否在黑白名单记录中
	fields, err := s.BlackWhiteListRepo.Fields(c, bwl.ID)
	if err != nil {
		return
	}
	err = s.validExtends(fields, req.Extends)
	if err != nil {
		return
	}

	//判断重复
	requiredSha256 := GenerateBlackWhiteItemSha256(fields, req.Extends)
	_, err = s.BlackWhiteItemRepo.First(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("required_sha256 = ?", requiredSha256).
			Where("bwl_id = ?", bwl.ID).
			Where("status in (?)", modelv2.GetBwItemStatus(modelv2.BwItemDraftStatus, modelv2.BwItemAuditPendingStatus, modelv2.BwItemNormalStatus))
	})
	if !errors.Is(err, er.NotFound) && err != nil {
		return
	}
	if err == nil {
		err = er.BlackWhiteDuplicateRecords
		return
	}
	// 新增黑白名单记录
	err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {
		tmpModel := &modelv2.RmsBlackWhiteItem{
			BwlID:          req.BwlID,
			StartDate:      req.StartDate,
			EndDate:        req.EndDate,
			Extends:        modelv2.BlackWhiteItemExtends(req.Extends).ToJson(modelv2.BwSkipMarshalFields),
			RequiredSha256: GenerateBlackWhiteItemSha256(fields, req.Extends),
			Status:         modelv2.BlackWhiteItemStatusDraft,
		}
		tmpModel.RemainingDay = tmpModel.CalculationRemainingDay()
		id, innerErr = s.BlackWhiteItemRepo.Create(c, tx, tmpModel)
		if innerErr != nil {
			return
		}
		var updateBwl = map[string]interface{}{"created_by": submitter}
		if bwl.State == modelv2.BlackWhiteListStatePass || bwl.State == modelv2.BlackWhiteListStateReject {
			//如果黑白名单不属于第一次提交数据情况，就更新成修改草稿情况
			updateBwl["state"] = modelv2.BlackWhiteListStateModifyDraft
		}
		innerErr = s.BlackWhiteListRepo.Updates(c, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", req.BwlID)
		}, updateBwl)
		return innerErr
	})
	if err != nil {
		err = er.InvalidArgument.WithMsg("Database exception").WithErr(err)
		return
	}
	return
}

//var blackWhiteDefaultFields = []string{entity.FieldStartDate, entity.FieldExpirationDate}

// GenerateItemSha256 通过字段定义和用户数据，筛选出来用户的必填数据，然后按照字段名进行降序排序，最后生成sha256
func GenerateBlackWhiteItemSha256(fields entity.Fields, extend modelv2.BlackWhiteItemExtends) []byte {
	var fieldVal []*entity.FieldValue
	for _, v := range fields {
		if v.IsRequired {
			fieldVal = append(fieldVal, &entity.FieldValue{
				Name:  v.Name,
				Value: fmt.Sprintf("%v", extend[v.Name]),
			})
		}
	}
	sort.Slice(fieldVal, func(i, j int) bool {
		return fieldVal[i].Name > fieldVal[j].Name
	})
	var encodeStr strings.Builder
	for _, v := range fieldVal {
		encodeStr.WriteString(v.Value)
	}
	return sha256.Encode(encodeStr.String())
}

// DownloadBlackWhiteListItemTemplate 下载黑白名单明细Excel模板
func (s *BlackWhiteItemService) DownloadBlackWhiteListItemTemplate(ctx context.Context, id int64) (filename string, res *bytes.Buffer, err error) {
	// 获取黑白名单记录
	var bwl *modelv2.RmsBlackWhiteList
	var title []string
	var fields []*modelv2.RmsBlackWhiteField
	bwl, err = s.BlackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if errors.Is(err, er.NotFound) {
		return
	}
	if err != nil {
		return
	}
	filename = strings.ReplaceAll(bwl.Name, " ", "") + "_template.xlsx"

	fields, err = s.BlackWhiteFieldRepo.GetBlackWhiteFieldsBriefing(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", bwl.ID)
	})
	if err != nil {
		return
	}
	for _, field := range fields {
		if field.Required {
			field.FieldName = field.FieldName + "*"
		}
		title = append(title, field.FieldName)
	}
	strCols := []string{excel.ColumnNumberToName(len(title) + 1), excel.ColumnNumberToName(len(title) + 2)}
	title = append(title, modelv2.BusinessFieldStartDate, modelv2.BusinessFieldExpirationDate+"(置空默认值9999年)")
	res, err = excel.New().UseSheet("Sheet1").SetTitle(title...).SetColString(strCols...).WriteBuffer()
	return

}

// UploadBlackWhiteItems handles the business logic of file upload
func (s *BlackWhiteItemService) UploadBlackWhiteItems(ctx context.Context, req *entity.UploadBlackWhiteItemFile) (err error) {
	// 获取黑白名单记录和必填字段
	var (
		bwl         *modelv2.RmsBlackWhiteList
		itemModels  []*modelv2.RmsBlackWhiteItem
		bwItems     []map[string]interface{}
		existsItems map[string]*ExistsBwItem
		fields      entity.Fields
	)

	// 校验黑白名单信息
	bwl, err = BlockPendingChanges(ctx, s.BlackWhiteListRepo, req.BwlID)
	if err != nil {
		return
	}
	// 获取黑白名单明细

	_, itemModels, err = s.BlackWhiteItemRepo.GetBlackWhiteItems(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", req.BwlID).Where("status in (?)", []int{modelv2.BlackWhiteItemStatusPass, modelv2.BlackWhiteItemStatusDraft}).
			Select([]string{"id", "status", "required_sha256"})
	})
	if err != nil {
		return err
	}
	wlog.Info("Retrieve all whitelist and blacklist details", zap.Any("bwl_id", req.BwlID), zap.Any("item_count", len(itemModels)))
	items := make(map[string]modelv2.RmsBlackWhiteItem)
	for _, item := range itemModels {
		items[string(item.RequiredSha256)] = *item
	}
	fields, err = s.BlackWhiteListRepo.Fields(ctx, bwl.ID)
	if err != nil {
		return err
	}
	processor := NewBlackWhiteFileProcessor(fields, items).SetExistsRecover(req.Recover)
	switch req.FileType {
	case "excel":
		bwItems, existsItems, err = processor.ProcessExcelFile(req.File)
	case "csv":
		bwItems, existsItems, err = processor.ProcessCSVFile(req.File)
	}
	if err != nil {
		return err
	}
	var bwItemModels []*modelv2.RmsBlackWhiteItem
	var upItemModels []*modelv2.RmsBlackWhiteItem
	var parentModify []int64

	for _, item := range bwItems {
		var startUnix int64
		var endUnix int64
		var tmpModel = &modelv2.RmsBlackWhiteItem{
			BwlID:   req.BwlID,
			Extends: modelv2.BlackWhiteItemExtends(item).ToJson(modelv2.BwSkipMarshalFields),
		}

		if v, ok := item[modelv2.BusinessFieldStartDate]; ok {
			startUnix = v.(time.Time).Unix()
		}
		if v, ok := item[modelv2.BusinessFieldExpirationDate]; ok {
			endUnix = v.(time.Time).Unix()
		}
		var requiredSha256 = item[modelv2.BusinessFieldRequiredSha256].([]byte)

		tmpModel.StartDate = startUnix
		tmpModel.EndDate = endUnix
		tmpModel.RemainingDay = tmpModel.CalculationRemainingDay()
		tmpModel.RequiredSha256 = requiredSha256
		tmpModel.Status = modelv2.BlackWhiteItemStatusDraft
		parentItem, ok := existsItems[string(requiredSha256)]
		if ok {
			tmpModel.Status = modelv2.BlackWhiteItemStatusDraft
			if parentItem.Status == modelv2.BlackWhiteItemStatusDraft {
				tmpModel.ID = parentItem.ItemID
				upItemModels = append(upItemModels, tmpModel)
				continue
			} else {
				tmpModel.ParentID = parentItem.ItemID
				parentModify = append(parentModify, parentItem.ItemID)
			}
		}
		bwItemModels = append(bwItemModels, tmpModel)
	}

	return db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		if len(upItemModels) > 0 {
			for _, model := range upItemModels {
				innerErr = s.BlackWhiteItemRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
					return db.Where("id = ?", model.ID)
				}, map[string]interface{}{
					"start_date": model.StartDate,
					"end_date":   model.EndDate,
					"remaining":  model.RemainingDay,
					"extends":    model.Extends,
				})

				if innerErr != nil {
					return innerErr
				}
			}
		}
		if len(parentModify) > 0 {
			for _, parentID := range parentModify {
				innerErr = s.BlackWhiteItemRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
					return db.Where("id = ?", parentID)
				}, map[string]interface{}{
					"status": modelv2.BlackWhiteItemStatusModified,
				})
			}
		}
		if len(bwItemModels) > 0 {
			innerErr = s.BlackWhiteItemRepo.BatchCreate(ctx, tx, bwItemModels)
			if innerErr != nil {
				return innerErr
			}
		}

		var updateBwl = map[string]interface{}{"created_by": req.Submitter}
		if bwl.State == modelv2.BlackWhiteListStatePass || bwl.State == modelv2.BlackWhiteListStateReject {
			//如果黑白名单不属于第一次提交数据情况，就更新成修改草稿情况
			updateBwl["state"] = modelv2.BlackWhiteListStateModifyDraft
		}
		innerErr = s.BlackWhiteListRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", bwl.ID)
		}, updateBwl)
		return
	})

}

func (s *BlackWhiteItemService) GetBlackWhiteItems(ctx context.Context, req *request_parameters.GetBlackWhiteItems) (total int64, items []*modelv2.RmsBlackWhiteItem, err error) {

	total, items, err = s.BlackWhiteItemRepo.GetBlackWhiteItems(ctx, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.BwlID != 0 {
			db = db.Where("bwl_id = ?", req.BwlID)
		}
		return db.Where("status in (?)", []int64{
			modelv2.BlackWhiteItemStatusDraft,
			modelv2.BlackWhiteItemStatusPendingApproval,
			modelv2.BlackWhiteItemStatusPass,
		}).Order("id desc")
	}, true)
	return
}

// GetBlackWhiteItemsView 获取黑白名单明细列表,只展示当前生效的明细清单
func (s *BlackWhiteItemService) GetBlackWhiteItemsView(ctx context.Context, req *request_parameters.GetBlackWhiteItems) (total int64, items []*modelv2.RmsBlackWhiteItem, err error) {
	total, items, err = s.BlackWhiteItemRepo.GetBlackWhiteItems(ctx, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.BwlID != 0 {
			db = db.Where("bwl_id = ?", req.BwlID)
		}
		if !req.Now.IsZero() {
			db = db.Where("start_date <= ?", req.Now.Unix()).Where("end_date >= ?", req.Now.Unix())
		}
		return db.Where("status in (?)", []int64{
			modelv2.BlackWhiteItemStatusPass,
			modelv2.BlackWhiteItemStatusRemoveDraft,
			modelv2.BlackWhiteItemStatusRemovePendingApproval,
		})
	}, true)
	return
}
