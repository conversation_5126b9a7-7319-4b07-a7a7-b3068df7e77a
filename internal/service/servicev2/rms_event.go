package servicev2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
	"strings"
)

type EventService struct {
	EventRepo repo.RmsEvent
}

func (s *EventService) Create(ctx context.Context, cp *modelv2.RmsEvent) error {
	return s.EventRepo.Create(ctx, nil, cp)
}

func (s *EventService) Update(ctx context.Context, id int64, cp *modelv2.RmsEvent) error {
	return s.EventRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	}, cp)
}

func (s *EventService) Delete(ctx context.Context, id int64) error {
	return s.EventRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}

func (s *EventService) List(ctx context.Context, req *request_parameters.EventList) (total int64, li []*modelv2.RmsEvent, err error) {
	total, li, err = s.EventRepo.List(ctx, filter.NewDefaultFilter(req.TimeRequest.Option, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		switch req.Field {
		case "STR_FIELD1", "STR_FIELD2", "STR_FIELD3", "STR_FIELD4", "STR_FIELD5", "NUM_FIELD1", "NUM_FIELD2":
			Field := strings.ToLower(req.Field)
			db = db.Where(Field+"=?", req.FieldValue)
		}
		if req.CheckPoint != "" {
			db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
		}
		if req.ResultCode != "" {
			db = db.Where("result_code like ?", "%"+req.ResultCode+"%")
		}
		return db
	}, true)

	return
}

func (s *EventService) Retrieve(ctx context.Context, id int64) (*modelv2.RmsEvent, error) {
	return s.EventRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}
