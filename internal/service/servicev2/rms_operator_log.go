package servicev2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type OperatorLogService struct {
	OperatorLogRepo repo.RmsOperatorLog
}

func (s *OperatorLogService) Create(ctx context.Context, cp *modelv2.RmsOperatorLog) error {
	return s.OperatorLogRepo.Create(ctx, nil, cp)
}

func (s *OperatorLogService) Update(ctx context.Context, id int64, cp *modelv2.RmsOperatorLog) error {
	return s.OperatorLogRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	}, cp)
}

func (s *OperatorLogService) Delete(ctx context.Context, id int64) error {
	return s.OperatorLogRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}

func (s *OperatorLogService) List(ctx context.Context, req *request_parameters.OperatorLogList) (total int64, items []*modelv2.RmsOperatorLog, err error) {
	total, items, err = s.OperatorLogRepo.List(ctx, filter.NewDefaultFilterByRequest(req.TimeRequest.Option, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		return db
	}, true)
	if err != nil {
		return
	}
	return
}

func (s *OperatorLogService) Retrieve(ctx context.Context, id int64) (*modelv2.RmsOperatorLog, error) {
	return s.OperatorLogRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}
