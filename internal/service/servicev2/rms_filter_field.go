package servicev2

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	mapset "github.com/deckarep/golang-set/v2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type FilterFieldService struct {
	FilterFieldRepo repo.RmsFilterField
	PolicyRepo      repo.PolicyRepo
	CheckPointRepo  repo.RmsCheckPoint
}

type FilterFieldFieldEnum struct {
	Code  string
	Label string
}

func (s *FilterFieldService) Check(ctx context.Context, cp *modelv2.RmsFilterField) error {
	if cp.FieldEnum == "" || strings.Contains(cp.FieldEnum, `""`) {
		return er.InvalidArgument.WithMsg("Enumeration fields can not be empty")
	}
	var li []*FilterFieldFieldEnum
	if err := json.Unmarshal([]byte(cp.FieldEnum), &li); err != nil {
		return er.WSEF(err, zap.String("FieldEnum", cp.FieldEnum))
	}
	var exist []*FilterFieldFieldEnum
	for _, v := range li {
		for _, x := range exist {
			if v.Code == x.Code {
				return er.InvalidArgument.WithMsg("Enumeration fields can not be repeated")
			}
		}
		exist = append(exist, v)
	}
	return nil
}

func (s *FilterFieldService) Create(ctx context.Context, cp *modelv2.RmsFilterField) error {
	if err := s.Check(ctx, cp); err != nil {
		return err
	}
	return s.FilterFieldRepo.Create(ctx, nil, cp)
}

func (s *FilterFieldService) Update(ctx context.Context, id int64, mod *modelv2.RmsFilterField) error {
	if err := s.Check(ctx, mod); err != nil {
		return err
	}

	// 获取被更改的枚举值。因为后端无法对枚举值的跟踪，删除在新数据中不存在的枚举值
	enumSet := mapset.NewSet[string]()
	f, err := mod.GetFieldEnum()
	if err != nil {
		return err
	}
	enumSet.Append(f.AllCode()...)

	// 处理规则组的绑定
	_, PolicyLi, err := s.PolicyRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsPolicy().CheckPointField()+" = ?", mod.CheckPoint)
	}, false)
	if err != nil {
		return err
	}
	// 在规则组存在时更改规则组中的信息
	for _, Policy := range PolicyLi {
		filters, err := Policy.GetFilters()
		if err != nil {
			return err
		}

		filterItem := filters[mod.FieldName]
		newfilterItem := []string{}
		for _, v := range filterItem {
			if enumSet.Contains(v) {
				newfilterItem = append(newfilterItem, v)
			}
		}
		if len(newfilterItem) == 0 {
			delete(filters, mod.FieldName)
		} else {
			filters[mod.FieldName] = newfilterItem
		}
		err = Policy.SetFilters(filters)
		if err != nil {
			return err
		}
		err = s.PolicyRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsPolicy().IDField()+" = ?", Policy.ID)
		}, Policy)
		if err != nil {
			return err
		}
	}

	return s.FilterFieldRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsFilterField().IDField()+" = ?", id)
	}, mod)
}

func (s *FilterFieldService) List(ctx context.Context, req *request_parameters.FilterFieldList) (total int64, li []*modelv2.RmsFilterField, err error) {
	// 构建过滤条件
	filterFn := func(db *gorm.DB) *gorm.DB {
		if req.AccessPoint != "" {
			db = db.Where(modelv2.NewRmsFilterField().CheckPointField()+" = ?", req.AccessPoint)
		}
		if req.FieldName != "" {
			db = db.Where(modelv2.NewRmsFilterField().FieldNameField()+" = ?", req.FieldName)
		}
		return db
	}

	// 创建分页过滤器
	defaultFilter := filter.NewDefaultFilter(req.PageRequest.Option)

	total, li, err = s.FilterFieldRepo.List(ctx, defaultFilter, filterFn, true)
	if err != nil {
		return
	}

	return
}

func (s *FilterFieldService) Retrieve(ctx context.Context, id int64) (*modelv2.RmsFilterField, error) {
	return s.FilterFieldRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsFilterField().IDField()+" = ?", id)
	})
}

func (s *FilterFieldService) RetrieveByName(ctx context.Context, req *request_parameters.FilterFieldRetrieve) (*modelv2.RmsFilterField, error) {
	item, err := s.FilterFieldRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsFilterField().CheckPointField()+" = ?", req.AccessPoint).Where(modelv2.NewRmsFilterField().FieldNameField()+" = ?", req.FieldName)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Data does not exist.")
		}
		return nil, err
	}
	return item, nil
}
