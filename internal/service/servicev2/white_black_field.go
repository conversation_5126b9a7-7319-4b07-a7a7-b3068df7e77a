package servicev2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
)

type BlackWhiteFieldService struct {
	BlackWhiteListRepo  repo.BlackWhiteListRepo
	BlackWhiteItemRepo  repo.BlackWhiteItemRepo
	BlackWhiteFieldRepo repo.BlackWhiteFieldRepo
	BlackWhiteAuditRepo repo.BlackWhiteAuditRepo
	RmsEventFieldRepo   repo.RmsEventField
}

// List 黑白名单字段列表
func (s *BlackWhiteFieldService) List(c context.Context, accessPoints []string) (fields []string, err error) {
	return s.RmsEventFieldRepo.GetIntersectionFields(c, accessPoints)
}
