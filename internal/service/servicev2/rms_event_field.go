package servicev2

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type EventFieldService struct {
	EventFieldRepo        repo.RmsEventField
	CheckPoint            repo.RmsCheckPoint
	FilterField           repo.RmsFilterField
	PolicyRepo            repo.PolicyRepo
	EventFieldBindingRepo repo.RmsEventFieldBinding
}

func (s *EventFieldService) Check(c *gin.Context, apCode string, ef *modelv2.RmsEventField) error {
	data, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("check_point=? and field_name=?", apCode, ef.FieldName)
	})
	if err != nil {
		return nil
	}
	if data != nil && data.ID != ef.ID {
		return er.InvalidArgument.WithStack().WithMsg("Field name cannot be repeated")
	}
	return nil
}

// TODO 移动到 RmsCheckPoint model 中
// SetPrimary 设置默认主键
func (s *EventFieldService) SetPrimary(c *gin.Context, id int64, apCode, FieldName string) error {
	// 每个接入点只能有一个默认主键
	// 默认主键以字段名存在接入点中，所以在创建或更新时要用字段名对比，并兼容更新自己的情况。在创建时 id 传 0，更新时需要传实际的 id。
	ap, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("code=?", apCode)
	})
	if err != nil {
		return err
	}
	// 检查默认主键，如果存在
	if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName != "" {
		// 且与当前字段不同
		if *ap.DefaultPkFieldName != FieldName {
			// 查询接入点已有配置是否存在相应字段，防止出问题后在ui中无法更改
			ef, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
				return db.Where("check_point=? and field_name=?", apCode, *ap.DefaultPkFieldName)
			})
			if err != nil {
				// 字段时可以设置
				if !errors.Is(err, er.NotFound) {
					return err
				}
				return s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
					return db.Where("id=?", ap.ID)
				}, map[string]interface{}{"default_pk_field_name": FieldName})
			}
			// 字段是自己时可以设置
			if ef.ID == id {
				return s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
					return db.Where("id=?", ap.ID)
				}, map[string]interface{}{"default_pk_field_name": FieldName})
			}
			// 否则返回已存在主键
			return er.InvalidArgument.WithStack().WithMsg("A default primary key already exists.")
		}
	}
	// 如果不存在，设置主键
	return s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", ap.ID)
	}, map[string]interface{}{"default_pk_field_name": FieldName})
}

// UnSetPrimary 取消设置默认主键
func (s *EventFieldService) UnSetPrimary(c *gin.Context, apCode, FieldName string) error {
	// 每个接入点只能有一个默认主键
	ap, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("code=?", apCode)
	})
	if err != nil {
		return err
	}
	// 检查默认主键，如果存在
	if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName != "" {
		// 且与当前字段不同
		if *ap.DefaultPkFieldName != FieldName {
			// 查询接入点已有配置是否存在相应字段，防止出问题后在ui中无法更改
			_, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
				return db.Where("check_point=? and field_name=?", apCode, *ap.DefaultPkFieldName)
			})
			if err != nil {
				//	字段不存在时可以设置
				if !errors.Is(err, er.NotFound) {
					return err
				}
			} else {
				// 否则抛出错误
				return er.DefaultPkFieldAlreadyExists.WithStack()
			}
		}
	}
	err = s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", ap.ID)
	}, map[string]interface{}{"default_pk_field_name": ""})
	if err != nil {
		return err
	}
	return nil
}

// IsFilterField 检查是否是过滤字段
func (s *EventFieldService) IsFilterField(c *gin.Context, ef *response_parameters.EventFieldResp) (bool, error) {
	// 告知前端是否是过滤字段
	_, err := s.FilterField.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("check_point=? and field_name=?", ef.CheckPoint, ef.FieldName)
	})
	if err != nil {
		if errors.Is(err, er.NotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func (s *EventFieldService) Create(c *gin.Context, req *request_parameters.EventFieldCreate) error {
	var cp = &modelv2.RmsEventField{
		CheckPoint: req.CheckPoint,
		FieldName:  req.FieldName,
		FieldType:  modelv2.EventFieldType(req.FieldType),
		Required:   req.Required,
		Memo:       req.Memo,
	}
	if err := s.Check(c, req.CheckPoint, cp); err != nil {
		return err
	}
	if req.Primary {
		err := s.SetPrimary(c, 0, req.CheckPoint, req.FieldName)
		if err != nil {
			return err
		}
	}
	if req.Filter {
		// 获取接入点，管理接入点中的过滤字段
		cp, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("code=?", req.CheckPoint)
		})
		if err != nil {
			return err
		}
		err = cp.AddFilterField(req.FieldName)
		if err != nil {
			return err
		}
	}
	return s.EventFieldRepo.Create(c, nil, cp)
}

func (s *EventFieldService) Update(c *gin.Context, id int64, req *request_parameters.EventFieldUpdate) error {
	// 处理接入点中的默认主键。
	// 是否是默认主键
	var hasBind bool
	if req.Primary {
		err := s.SetPrimary(c, id, req.CheckPoint, req.FieldName)
		if err != nil {
			return err
		}
	} else {
		// 取消默认主键
		err := s.UnSetPrimary(c, req.CheckPoint, req.FieldName)
		if err != nil {
			if !errors.Is(err, er.DefaultPkFieldAlreadyExists) {
				return err
			}
		}
	}
	oriEventField, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return err
	}
	hasBind, err = s.EventFieldBindingRepo.Bound(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("field_name = ?", oriEventField.FieldName).Where("check_point=?", req.CheckPoint)
	})
	if err != nil {
		return err
	}
	if hasBind && oriEventField.FieldName != req.FieldName {
		return er.InvalidArgument.WithStack().WithMsg("The field name cannot be modified because it is bound to the rule group.")
	}
	lowMod, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return err
	}
	// 获取接入点，管理接入点中的过滤字段
	cp, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("code=?", req.CheckPoint)
	})
	if err != nil {
		return err
	}
	mode := &modelv2.RmsEventField{
		ID:         req.ID,
		CheckPoint: req.CheckPoint,
		FieldName:  req.FieldName,
		FieldType:  modelv2.EventFieldType(req.FieldType),
		Required:   req.Required,
		Memo:       req.Memo,
	}

	if err := s.Check(c, req.CheckPoint, mode); err != nil {
		return err
	}
	if err := s.EventFieldRepo.Update(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id).Select(
			"check_point",
			"field_name",
			"field_type",
			"memo",
			"required",
		)
	}, mode); err != nil {
		return err
	}
	// 如果名字更改，更改规则组中的关联数据
	if lowMod.FieldName != mode.FieldName {
		// 处理规则组的绑定
		_, PolicyLi, err := s.PolicyRepo.List(c, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("check_point=?", lowMod.CheckPoint)
		}, false)
		if err != nil {
			return err
		}
		// 在规则组存在时更改规则组中的信息
		for _, Policy := range PolicyLi {
			filters, err := Policy.GetFilters()
			if err != nil {
				return err
			}
			if len(filters[lowMod.FieldName]) > 0 {
				filters[mode.FieldName] = filters[lowMod.FieldName]
				delete(filters, lowMod.FieldName)

				err = Policy.SetFilters(filters)
				if err != nil {
					return err
				}
				err = s.PolicyRepo.Update(c, nil, func(db *gorm.DB) *gorm.DB {
					return db.Where("id=?", Policy.ID)
				}, Policy)
				if err != nil {
					return err
				}
			}
		}
		// 更新接入点中的业务配置
		//bt, err := cp.GetBusinessTypes()
		//if err != nil {
		//	return err
		//}
		//for k, v := range bt {
		//	if v.SecondFieldName == lowMod.FieldName {
		//		v.SecondFieldName = mode.FieldName
		//	}
		//	bt[k] = v
		//}
		//err = cp.SetBusinessTypes(bt)
		//if err != nil {
		//	return err
		//}
		//err = s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
		//	return db.Where("code=?", cp.Code)
		//}, map[string]any{"business_types": *cp.BusinessTypes})
		//if err != nil {
		//	return err
		//}
	}

	if req.Filter {
		// 更新过滤字段信息
		if oriEventField.FieldName != req.FieldName {
			// 更新字段名
			err = cp.RemoveFilterField(oriEventField.FieldName)
			if err != nil {
				return err
			}
			err = cp.AddFilterField(req.FieldName)
			if err != nil {
				return err
			}
			// 重命名过滤字段
			err := s.FilterField.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
				return db.Where("field_name=?", oriEventField.FieldName)
			}, map[string]interface{}{"field_name": req.FieldName})
			if err != nil {
				return err
			}
		}
	} else {
		// 去掉过滤字段时删除关联值
		err := s.FilterField.Delete(c, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("check_point=? and field_name=?", req.CheckPoint, req.FieldName)
		})
		if err != nil {
			return err
		}
		err = cp.RemoveFilterField(oriEventField.FieldName)
		if err != nil {
			return err
		}
	}
	// 更新过滤字段
	if cp.FilterFields != nil {
		if err := s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("code=?", cp.Code)
		}, map[string]any{"filter_fields": *cp.FilterFields}); err != nil {
			return err
		}
	}
	req.CheckPoint = mode.CheckPoint
	req.FieldName = mode.FieldName
	req.FieldType = string(mode.FieldType)
	req.Memo = mode.Memo
	req.Required = mode.Required
	return err
}

func (s *EventFieldService) Delete(c *gin.Context, id int64) error {
	ef, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return err
	}
	oriEventField, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return err
	}
	var hasBind bool
	hasBind, err = s.EventFieldBindingRepo.Bound(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("field_name = ?", oriEventField.FieldName).Where("check_point=?", oriEventField.CheckPoint)
	})
	if err != nil {
		return err
	}
	if hasBind {
		return er.InvalidArgument.WithStack().WithMsg("The current field has been bound by other business, cannot be deleted.")
	}
	// 获取接入点，管理接入点中的过滤字段
	cp, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("code=?", oriEventField.CheckPoint)
	})
	if err != nil {
		return err
	}
	// 删除接入点中的过滤字段
	err = cp.RemoveFilterField(oriEventField.FieldName)
	if err != nil {
		return err
	}
	// 更新过滤字段
	if cp.FilterFields != nil {
		if err := s.CheckPoint.Updates(c, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("code=?", cp.Code)
		}, map[string]interface{}{"filter_fields": *cp.FilterFields}); err != nil {
			return err
		}
	}
	// 删除过滤字段
	err = s.FilterField.Delete(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("check_point=? and field_name=?", cp.Code, ef.FieldName)
	})
	if err != nil {
		return err
	}
	err = s.UnSetPrimary(c, ef.CheckPoint, ef.FieldName)
	if err != nil {
		if !errors.Is(err, er.DefaultPkFieldAlreadyExists) {
			return err
		}
	}
	return s.EventFieldRepo.Delete(c, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}

func (s *EventFieldService) List(c *gin.Context, req *request_parameters.EventFieldList) (total int64, respLi []*response_parameters.EventFieldResp, err error) {
	var li []*modelv2.RmsEventField
	total, li, err = s.EventFieldRepo.List(c, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.CheckPoint != "" {
			db = db.Where("check_point=?", req.CheckPoint)
		}
		if req.FieldName != "" {
			db = db.Where("field_name=?", req.FieldName)
		}
		return db
	}, true)
	if err != nil {
		return
	}
	for _, field := range li {
		var tmpResp = &response_parameters.EventFieldResp{
			EventFieldUpdate: response_parameters.EventFieldUpdate{
				ID: field.ID,
				EventFieldCreate: response_parameters.EventFieldCreate{
					CheckPoint: field.CheckPoint,
					FieldName:  field.FieldName,
					FieldType:  string(field.FieldType),
					Memo:       field.Memo,
					Required:   field.Required,
				},
			},
		}
		var ap *modelv2.RmsCheckPoint
		ap, err = s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("code=?", field.CheckPoint)
		})
		if err != nil {
			if !errors.Is(err, er.NotFound) {
				return
			}
		} else {
			if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName == field.FieldName {
				tmpResp.Primary = true
			}
		}
		// 告知前端是否是过滤字段
		if tmpResp.Filter, err = s.IsFilterField(c, tmpResp); err != nil {
			return
		}
		respLi = append(respLi, tmpResp)
	}

	return
}

func (s *EventFieldService) Retrieve(c *gin.Context, id int64) (*response_parameters.EventFieldResp, error) {
	data, err := s.EventFieldRepo.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return nil, err
	}

	resp := &response_parameters.EventFieldResp{
		EventFieldUpdate: response_parameters.EventFieldUpdate{
			ID: data.ID,
			EventFieldCreate: response_parameters.EventFieldCreate{
				CheckPoint: data.CheckPoint,
				FieldName:  data.FieldName,
				FieldType:  string(data.FieldType),
				Memo:       data.Memo,
				Required:   data.Required,
			},
		},
	}
	ap, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("code=?", data.CheckPoint)
	})
	if err != nil {
		if !errors.Is(err, er.NotFound) {
			return nil, err
		}
	} else {
		if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName == data.FieldName {
			resp.Primary = true
		}
	}

	return resp, nil
}

// CheckNext 检查是否可以点击下一步
func (s *EventFieldService) CheckNext(c *gin.Context, resp *request_parameters.EventFieldCheckNext) error {
	ap, err := s.CheckPoint.Retrieve(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("code=?", resp.CheckPoint)
	})
	if err != nil {
		return err
	}
	// 接入点勾选数据重复校验时必须设置默认主键
	if ap.CheckDuplicate != nil && *ap.CheckDuplicate == true {
		if ap.DefaultPkFieldName == nil || *ap.DefaultPkFieldName == "" {
			return er.InvalidArgument.WithStack().WithMsg("The access point must set the primary key to check data duplication check.")
		}
	}
	return nil
}

func (s *EventFieldService) getEventFieldKeys(ctx context.Context) ([]string, error) {
	var (
		cursor uint64
		values []string
		match  = consts.RdbKeyEventFieldPrefix + "*"
	)

	for {
		// 使用 SCAN 遍历匹配的 key
		keys, nextCursor, err := global.Rdb.Scan(ctx, cursor, match, 100).Result()
		if err != nil {
			return nil, fmt.Errorf("failed to scan keys: %w", err)
		}
		cursor = nextCursor
		values = append(values, keys...)
		if cursor == 0 {
			break
		}
	}
	return values, nil
}
func (s *EventFieldService) UpdateEventFieldCache(ctx context.Context) error {
	var err error

	var cacheEventFields = make(map[string]map[string]string) // checkPoint => model json
	keys, err := s.getEventFieldKeys(ctx)
	if err != nil {
		return err
	}

	var eventFieldModels []*modelv2.RmsEventField
	_, eventFieldModels, err = s.EventFieldRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to get rule list").WithErr(err)
	}
	for _, model := range eventFieldModels {
		tmpJson, _ := jsoniter.MarshalToString(model)
		if _, ok := cacheEventFields[model.CheckPoint]; !ok {
			cacheEventFields[model.CheckPoint] = make(map[string]string)
		}
		cacheEventFields[model.CheckPoint][model.FieldName] = tmpJson
	}
	var cmders []redis.Cmder
	cmders, err = redisutils.Pipeline(func(pipe redis.Pipeliner) error {
		for _, redisKey := range keys {
			cp := strings.ReplaceAll(redisKey, consts.RdbKeyEventFieldPrefix, "")
			if data, ok := cacheEventFields[cp]; ok {
				delete(cacheEventFields, cp)
				pipe.Del(ctx, redisKey)
				pipe.HMSet(ctx, redisKey, data)
			} else {
				pipe.Del(ctx, redisKey)
			}

		}
		if len(cacheEventFields) > 0 {
			for accessPointCode, data := range cacheEventFields {
				pipe.HMSet(ctx, consts.RdbKeyEventFieldPrefix+accessPointCode, data)
			}
		}
		return nil
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to update event field cache.").WithErr(err)
	}
	for _, cmd := range cmders {
		if err := cmd.Err(); err != nil {
			return er.Internal.WithStack().WithMsg("Failed to event field cache.").WithErr(err)
		}
	}

	return nil
}
