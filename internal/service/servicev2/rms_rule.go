package servicev2

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type RuleService struct {
	RuleRepo repo.RmsRule
	Conf     config.Config
}

// Check 整理数据，处理上下线逻辑
func (s *RuleService) Check(cp *modelv2.RmsRule) error {
	if cp.DeployMethod == "manual" {
		cp.StartTime = nil
		cp.EndTime = nil
	} else {
		if cp.StartTime == nil || cp.StartTime.IsZero() || cp.EndTime == nil || cp.EndTime.IsZero() {
			return er.InvalidArgument.WithStack().WithMsg("The start time and end time cannot be empty.")
		}
		cp.Status = "Offline"
	}

	return nil
}

func (s *RuleService) Create(ctx context.Context, cp *modelv2.RmsRule) error {
	if err := s.Compile(cp.RuleContent, cp.RuleNo, cp.RuleName, cp.Priority); err != nil {
		return err
	}

	if err := s.Check(cp); err != nil {
		return err
	}

	// 规则的预设值，在新引擎中不需要，按照旧引擎的规则填入
	cp.RuleType = "Remote"
	cp.EngineType = "Stateless"
	EngineInstance := "StatefulEngine01"
	cp.EngineInstance = &EngineInstance
	if cp.DeployMethod == "manual" && cp.Status == "Online" {
		nowTime := time.Now()
		cp.SyncedAt = &nowTime
	}

	err := s.RuleRepo.Create(ctx, nil, cp)
	if errors.Is(err, er.DuplicateEntry) {
		return er.DuplicateKeys.WithMsgf("rule_code duplicate: %s", cp.RuleNo)
	}
	return err
}

func (s *RuleService) Update(ctx context.Context, id int64, cp *modelv2.RmsRule) error {
	if err := s.Check(cp); err != nil {
		return err
	}

	if err := s.Compile(cp.RuleContent, cp.RuleNo, cp.RuleName, cp.Priority); err != nil {

		return err
	}
	oriRule, err := s.RuleRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return err
	}
	// 变更规则内容或者更新为上线时更新 syncedAt, 下线时设置为 nil
	// TODO 在引擎侧优化上下线的问题。
	if oriRule.DeployMethod == "manual" && cp.Status == "Online" {
		nowTime := time.Now()
		cp.SyncedAt = &nowTime
	} else {
		// 满足自动状态下规则更新后上下线
		cp.SyncedAt = nil
	}
	err = s.RuleRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	}, cp)
	if errors.Is(err, er.DuplicateEntry) {
		return er.DuplicateKeys.WithMsgf("rule_code duplicate: %s", cp.RuleNo)
	}
	return err
}

func (s *RuleService) Delete(ctx context.Context, id int64) error {
	return s.RuleRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}

func (s *RuleService) List(ctx context.Context, req *request_parameters.RuleList) (total int64, li []*modelv2.RmsRule, err error) {
	total, li, err = s.RuleRepo.List(ctx, filter.NewDefaultFilterByRequest(req.TimeRequest.Option, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.CheckPoint != "" {
			db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
		}
		if req.RuleName != "" {
			db = db.Where("rule_name like ?", "%"+req.RuleName+"%")
		}
		if req.RuleNo != "" {
			db = db.Where(modelv2.NewRmsRule().RuleNoField()+" like ?", "%"+req.RuleNo+"%")
		}
		return db
	}, true)
	if err != nil {
		return
	}

	var resp []*response_parameters.RuleResp
	for _, rule := range li {
		var startTime, endTime *domain.NullableTime
		if rule.StartTime != nil {
			t := domain.NullableTime(*rule.StartTime)
			startTime = &t
		}
		if rule.EndTime != nil {
			t := domain.NullableTime(*rule.EndTime)
			endTime = &t
		}
		resp = append(resp, &response_parameters.RuleResp{
			SyncedAt: rule.SyncedAt,
			RuleUpdate: response_parameters.RuleUpdate{
				ID: rule.ID,
				RuleCreate: response_parameters.RuleCreate{
					RuleNo:       rule.RuleNo,
					RuleName:     rule.RuleName,
					CheckPoint:   rule.CheckPoint,
					ShortCircuit: rule.ShortCircuit,
					Memo:         rule.Memo,
					RuleContent:  rule.RuleContent,
					DeployMethod: rule.DeployMethod,
					StartTime:    startTime,
					EndTime:      endTime,
					Status:       rule.Status,
					Priority:     rule.Priority,
				},
			},
		})
	}
	return
}

func (s *RuleService) Retrieve(ctx context.Context, id int64) (*response_parameters.RuleResp, error) {
	rule, err := s.RuleRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return nil, err
	}
	var startTime, endTime *domain.NullableTime
	if rule.StartTime != nil {
		t := domain.NullableTime(*rule.StartTime)
		startTime = &t
	}
	if rule.EndTime != nil {
		t := domain.NullableTime(*rule.EndTime)
		endTime = &t
	}
	return &response_parameters.RuleResp{
		SyncedAt: rule.SyncedAt,
		RuleUpdate: response_parameters.RuleUpdate{
			ID: rule.ID,
			RuleCreate: response_parameters.RuleCreate{
				RuleNo:       rule.RuleNo,
				RuleName:     rule.RuleName,
				CheckPoint:   rule.CheckPoint,
				ShortCircuit: rule.ShortCircuit,
				Memo:         rule.Memo,
				RuleContent:  rule.RuleContent,
				DeployMethod: rule.DeployMethod,
				StartTime:    startTime,
				EndTime:      endTime,
				Status:       rule.Status,
				Priority:     rule.Priority,
			},
		},
	}, nil
}

func (s *RuleService) SetStatus(ctx context.Context, id int64, status string) error {
	cp, err := s.RuleRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return err
	}
	if cp.DeployMethod == "auto" {
		if status == "Online" {
			return er.Unimplemented.WithStack().WithMsg("Automatically effective rules do not support manual going online.")
		}
		// 在生效时间内下线，清空生效时间和失效时间
		newTime := time.Now()
		if cp.StartTime != nil && cp.StartTime.Before(newTime) && (cp.EndTime == nil || cp.EndTime.After(newTime)) {
			cp.StartTime = nil
			cp.EndTime = nil
			cp.DeployMethod = "manual"
			cp.Status = "Offline"
			return s.RuleRepo.SetStatus(ctx, nil, id, cp)
		}
	}
	cp.Status = status
	if status == "Online" {
		nowTime := time.Now()
		cp.SyncedAt = &nowTime
	}
	return s.RuleRepo.SetStatus(ctx, nil, id, cp)
}

func (s *RuleService) Compile(RuleContent string, ruleNo string, ruleName string, priority int) error {
	httpClient := &http.Client{
		Timeout: 12 * time.Second,
	}
	url := s.Conf.App.GatewayHost + "/api/v1/compile"
	gatewayStruct := struct {
		RuleContent string `json:"ruleContent"`
		RuleNo      string `json:"ruleNo"`
		RuleName    string `json:"ruleName"`
		Priority    int    `json:"priority"`
	}{
		RuleContent: RuleContent,
		RuleNo:      ruleNo,
		RuleName:    ruleName,
		Priority:    priority,
	}
	gatewayReq, err := json.Marshal(&gatewayStruct)
	httpReq, err := http.NewRequest(http.MethodPost, url, strings.NewReader(string(gatewayReq)))
	if err != nil {
		return er.WSEF(err)
	}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return er.WSEF(err).WithMsg("Gateway call failed, please contact technical support.")
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return er.WSEF(err).WithMsg("Gateway call failed, please contact technical support.")
	}
	respBody := new(response_parameters.GatewayRuleCompileResp)
	err = json.Unmarshal(body, respBody)
	if err != nil {
		return er.WSEF(err).WithMsg("Gateway call failed, please contact technical support.")
	}
	if respBody.Body != "success" {
		return er.CompileFail.WSF(zap.ByteString("resp", body)).WithMsg("Compile fail: " + respBody.Body)
	}
	return nil
}

func (s *RuleService) Refresh() error {
	url := s.Conf.App.GatewayHost + "/api/v1/refresh"
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return er.WSEF(err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return er.WSEF(err)
	}
	respBody := new(response_parameters.GatewayRuleCompileResp)
	err = json.Unmarshal(body, respBody)
	if err != nil {
		return er.WSEF(err)
	}
	if respBody.Body != "success" {
		return er.CompileFail.WSF(zap.ByteString("resp", body))
	}
	return nil
}
func (s *RuleService) getRuleKeys(ctx context.Context) ([]string, error) {
	var (
		cursor uint64
		values []string
		match  = consts.RdbKeyRulePrefix + "*"
	)

	for {
		// 使用 SCAN 遍历匹配的 key
		keys, nextCursor, err := global.Rdb.Scan(ctx, cursor, match, 100).Result()
		if err != nil {
			return nil, fmt.Errorf("failed to scan keys: %w", err)
		}
		cursor = nextCursor
		values = append(values, keys...)
		if cursor == 0 {
			break
		}
	}
	return values, nil
}
func (s *RuleService) UpdateRuleCache(ctx context.Context) error {
	var err error
	var cacheRules = make(map[string]time.Time) // rule_no => sync_at
	keys, err := s.getRuleKeys(ctx)
	if err != nil {
		return err
	}

	if len(keys) > 0 {
		var cmders []redis.Cmder
		cmders, err = redisutils.Pipeline(func(pipe redis.Pipeliner) error {
			for _, key := range keys {
				pipe.HMGet(ctx, key, modelv2.NewRmsRule().RuleNoField(), modelv2.NewRmsRule().SyncedAtField())
			}
			return nil
		})
		if err != nil {
			return err
		}

		for _, cmd := range cmders {
			val, err := cmd.(*redis.SliceCmd).Result()
			if err != nil && !errors.Is(err, redis.Nil) {
				return er.Internal.WithMsg("get cache rule failed, please contact technical support.").WithErr(err)
			}
			var tmpUpdateAt time.Time
			tmpUpdateAt, err = time.Parse(time.RFC3339, val[1].(string))
			if err != nil {
				return er.Internal.WithMsg("parse cache rule failed, please contact technical support.").WithErr(err)
			}
			cacheRules[val[0].(string)] = tmpUpdateAt
		}
	}
	var ruleModels []*modelv2.RmsRule
	var upRuleModels, insRuleModels []*modelv2.RmsRule
	_, ruleModels, err = s.RuleRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db
	})
	if err != nil {
		return er.Internal.WithStack().WithMsg("Failed to get rule list").WithErr(err)
	}
	for _, model := range ruleModels {
		if updatedAt, ok := cacheRules[model.RuleNo]; ok {
			//缓存中规则存在则比较更新时间是否需要更新
			if !updatedAt.Equal(model.UpdatedAt) {
				upRuleModels = append(upRuleModels, model)
			}
			delete(cacheRules, model.RuleNo)
		} else {
			//缓存中规则存在则比较更新时间是否需要更新
			insRuleModels = append(insRuleModels, model)
		}
	}
	if len(upRuleModels) > 0 || len(insRuleModels) > 0 || len(cacheRules) > 0 {
		var cmders []redis.Cmder
		cmders, err = redisutils.Pipeline(func(pipe redis.Pipeliner) error {
			for _, model := range upRuleModels {
				pipe.HMSet(ctx, consts.RdbKeyRulePrefix+model.RuleNo, model.ToCacheData())
			}
			for _, model := range insRuleModels {
				pipe.HMSet(ctx, consts.RdbKeyRulePrefix+model.RuleNo, model.ToCacheData())
			}
			for ruleNo, _ := range cacheRules {
				pipe.Del(ctx, consts.RdbKeyRulePrefix+ruleNo)
			}
			return nil
		})
		if err != nil {
			return er.Internal.WithStack().WithMsg("Failed to update rules cache.").WithErr(err)
		}
		for _, cmd := range cmders {
			if err := cmd.Err(); err != nil {
				return er.Internal.WithStack().WithMsg("Failed to update rules cache.").WithErr(err)
			}
		}
	}

	return nil
}
