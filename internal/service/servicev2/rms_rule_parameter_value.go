package servicev2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/datautil"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type RuleParameterValueService struct {
	RuleParameterValueRepo repo.RmsRuleParameterValueRepo
	RuleParameterGroupRepo repo.RmsRuleParameterGroupRepo
	RuleParameterRepo      repo.RmsRuleParameterRepo
}

func (s *RuleParameterValueService) Create(ctx context.Context, cp *request_parameters.RmsRuleParameterValueCreate) error {
	rptModel := &modelv2.RmsRuleParameterValue{
		ParamID:     cp.ParamID,
		GroupID:     cp.GroupID,
		ParamKey:    *cp.ParamKey,
		ParamValue:  cp.ParamValue,
		Description: cp.Description,
	}
	_, err := s.RuleParameterValueRepo.Create(ctx, nil, rptModel)
	return err
}

func (s *RuleParameterValueService) Update(ctx context.Context, id int64, cp *request_parameters.RmsRuleParameterValueUpdate) (resModel *modelv2.RmsRuleParameterValue, err error) {
	resModel = &modelv2.RmsRuleParameterValue{
		ParamID:     cp.ParamID,
		GroupID:     cp.GroupID,
		ParamKey:    *cp.ParamKey,
		ParamValue:  cp.ParamValue,
		Description: cp.Description,
	}
	err = s.RuleParameterValueRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	}, resModel)
	return
}

// UpdateMultiple 虽然名字叫 Multiple，实际上是设置一个参数绑定。视不同类型可能一个绑定会关联一个数组或 map，所以叫 Multiple。
// 对 规则参数关联的增删改查都是这个口，同时还要维护 RuleParameterGroup.ParamIds
func (s *RuleParameterValueService) UpdateMultiple(ctx context.Context, item *request_parameters.RmsRuleParameterValueUpdateMultiple) (*response_parameters.RmsRuleParameterValueUpdateMultipleResp, error) {
	rp, err := s.RuleParameterRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", item.RuleParamID)
	})
	if err != nil {
		return nil, err
	}
	// 检查参数
	switch entity.RuleParameterParamType(rp.ParamType) {
	case entity.RuleParameterParamType_STRING, entity.RuleParameterParamType_NUMBER,
		entity.RuleParameterParamType_LIST,
		entity.RuleParameterParamType_NUMBER_RANGE, entity.RuleParameterParamType_IP_RANGE:
		// 只有 value 值
		seen := map[string]bool{}
		for _, v := range item.RuleParameterValueList {
			if v.ParamValue == "" {
				return nil, er.InvalidArgument.WithStack().WithMsg("Parameter value cannot be empty.")
			}
			if seen[v.ParamValue] {
				return nil, er.InvalidArgument.WithStack().WithMsgf("Parameter value cannot be repeated: %s", v.ParamValue)
			}
			seen[v.ParamValue] = true
		}
	case entity.RuleParameterParamType_MAP, entity.RuleParameterParamType_BANK_CARD_LIST:
		// 校验 key 和 value
		seen := map[string]bool{}
		for _, v := range item.RuleParameterValueList {
			if v.ParamKey == "" {
				return nil, er.InvalidArgument.WithStack().WithMsg("Parameter key cannot be empty.")
			}
			if v.ParamValue == "" {
				return nil, er.InvalidArgument.WithStack().WithMsg("Parameter value cannot be empty.")
			}
			if seen[v.ParamKey] {
				return nil, er.InvalidArgument.WithStack().WithMsgf("Parameter key cannot be repeated: %s", v.ParamValue)
			}
			seen[v.ParamKey] = true
		}
	}
	// 维护 RuleParameterGroup.ParamIds
	rpg, err := s.RuleParameterGroupRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", item.RuleParamGroupID)
	})
	if err != nil {
		return nil, err
	}
	ParamIds := datautil.NewCommaSeparated(rpg.ParamIds)
	// item.RuleParameterValueList 为空是移除，不为空是添加
	if len(item.RuleParameterValueList) > 0 {
		ParamIds.AddInt64(item.RuleParamID)
	} else {
		ParamIds.RemoveInt64(item.RuleParamID)
	}
	if err = s.RuleParameterGroupRepo.UpdateField(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", item.RuleParamGroupID)
	}, "param_ids", ParamIds.Marshal()); err != nil {
		return nil, err

	}
	// 删除已存在的绑定
	err = s.RuleParameterValueRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("param_id = ? AND group_id = ?", item.RuleParamID, item.RuleParamGroupID)
	})
	if err != nil {
		return nil, err
	}

	// 更新或插入
	for _, v := range item.RuleParameterValueList {
		m := &modelv2.RmsRuleParameterValue{
			ParamID:     item.RuleParamID,
			GroupID:     item.RuleParamGroupID,
			ParamKey:    v.ParamKey,
			ParamValue:  v.ParamValue,
			Description: v.Description,
		}
		_, err := s.RuleParameterValueRepo.Create(ctx, nil, m)
		if err != nil {
			return nil, err
		}
	}
	// 刷新，返回前端最新数据
	_, li, err := s.RuleParameterValueRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("param_id = ? AND group_id = ?", item.RuleParamID, item.RuleParamGroupID)
	})
	if err != nil {
		return nil, err
	}
	resp := response_parameters.RmsRuleParameterValueUpdateMultipleResp{}
	for _, v := range li {
		r := &response_parameters.RmsRuleParameterValueUpdateMultipleRespItem{}
		r.ID = v.ID
		r.Description = v.Description
		r.ParamKey = v.ParamKey
		r.ParamValue = v.ParamValue
		resp.ItemList = append(resp.ItemList, r)
	}
	return &resp, nil
}

func (s *RuleParameterValueService) Delete(ctx context.Context, id int64) error {
	return s.RuleParameterValueRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}

func (s *RuleParameterValueService) List(ctx context.Context, req *request_parameters.RuleParameterValueList) (total int64, li []*modelv2.RmsRuleParameterValue, err error) {
	total, li, err = s.RuleParameterValueRepo.List(ctx,
		filter.NewDefaultFilterByRequest(req.TimeRequest.Option, req.PageRequest.Option),
		func(db *gorm.DB) *gorm.DB {
			return db
		}, true)
	if err != nil {
		return 0, nil, err
	}
	return total, li, nil
}

func (s *RuleParameterValueService) Retrieve(ctx context.Context, id int64) (*modelv2.RmsRuleParameterValue, error) {
	return s.RuleParameterValueRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
}
