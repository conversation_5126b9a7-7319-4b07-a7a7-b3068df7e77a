package servicev2

import (
	"context"
	"errors"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// CreateScript 创建Script指标配置
func (s *IndicatorService) CreateScript(ctx context.Context, req *request_parameters.CreateScriptIndicator) (indicatorID, versionID int64, err error) {
	var _ *modelv2.RmsIndicator
	// 判断指标是否存在
	_, err = s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("indicator_name = ?", req.IndicatorName).Where("is_script = ?", true)
	})
	if err == nil {
		err = er.AlreadyExists.WithMsg("Indicator already exists")
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		err = er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
		return
	}

	// 创建事务

	err = db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		// 1. 创建指标配置
		indicator := &modelv2.RmsIndicator{
			IndicatorName: req.IndicatorName,
			AccessPoint:   req.AccessPoint,
			DataSourceID:  req.DataSourceID,
			IsScript:      true,
		}

		// 创建指标
		if innerErr = s.IndicatorRepo.Create(ctx, tx, indicator); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator").WithErr(innerErr).WithStack()
		}
		indicatorID = indicator.ID

		// 2. 创建指标配置版本
		version := &modelv2.RmsIndicatorVersion{
			IndicatorID: indicator.ID,
			Script:      req.Script,
			Remark:      req.Remark,
		}
		// 创建版本
		if innerErr = s.IndicatorVersionRepo.Create(ctx, tx, version); err != nil {
			return er.Internal.WithMsg("Failed to create indicator version").WithErr(innerErr).WithStack()
		}
		versionID = version.ID
		if innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", indicatorID)
		}, map[string]interface{}{"draft_version_id": versionID}); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator[1]").WithErr(innerErr).WithStack()
		}

		return nil
	})

	if err != nil {
		return 0, 0, err
	}

	return indicatorID, versionID, nil
}

// GenerateScriptVersionLog 生成Script指标最新日志
func (s *IndicatorService) GenerateScriptVersionLog(ctx context.Context, user string, indicatorID int64) (err error) {
	var versions []*modelv2.RmsIndicatorVersion
	_, versions, err = s.IndicatorVersionRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("indicator_id = ?", indicatorID).Order("id desc").Limit(2)
	})
	if err != nil {
		return er.Internal.WithMsg("Failed to query indicator versions").WithErr(err).WithStack()
	}
	if len(versions) < 2 {
		return s.IndicatorHistoryRepo.AnalyzeVersionAndSave(ctx, true, user, nil, versions[0])
	} else {
		return s.IndicatorHistoryRepo.AnalyzeVersionAndSave(ctx, true, user, versions[1], versions[0])
	}
}

// UpdateScript 更新Script指标配置（创建新版本或更新未发布版本）
func (s *IndicatorService) UpdateScript(ctx context.Context, req *request_parameters.UpdateScriptIndicator) error {
	// 1. 检查指标是否存在
	var versionID int64
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.ID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return er.NotFound.WithMsg("Indicator does not exist")
		}
		return er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}
	if indicator.DraftVersionID > 0 {
		versionID = indicator.DraftVersionID
	} else if indicator.CurrentVersionID > 0 {
		versionID = indicator.CurrentVersionID
	} else {
		return er.Internal.WithMsg("The indicator data does not exist, cannot be updated.")
	}
	if indicator.Status != modelv2.IndicatorStatusDraft {
		req.IndicatorName = ""
	} else {
		_, err = s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicator().IndicatorNameField(), req.IndicatorName).Where(modelv2.NewRmsIndicator().IDField()+" != ?", indicator.ID).Where(modelv2.NewRmsIndicator().IsScriptField(), true)
		})
		if err == nil {
			return er.AlreadyExists.WithMsg("Indicator name already exists")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return er.Internal.WithMsg("database exception")
		}
		err = nil
	}
	// 2. 获取最近的版本记录
	versionModel, err := s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("indicator_id = ?", req.ID).Where("id = ?", versionID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return er.NotFound.WithMsg("No indicator version found")
	}
	if err != nil {
		return er.Internal.WithMsg("Failed to query latest indicator version").WithErr(err).WithStack()
	}
	if versionModel.Script == req.Script && versionModel.Remark == req.Remark {
		return nil
	}
	// 3. 根据最近版本是否已发布决定更新策略
	if versionModel.Version != "" {
		// 已发布，创建新版本
		return s.createNewScriptVersion(ctx, req)
	} else {
		// 未发布，更新现有版本
		return s.updateExistingScriptVersion(ctx, req, versionModel)
	}
}

// createNewScriptVersion 创建新版本
func (s *IndicatorService) createNewScriptVersion(ctx context.Context, req *request_parameters.UpdateScriptIndicator) error {
	return db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		// 创建新版本
		version := &modelv2.RmsIndicatorVersion{
			IndicatorID: req.ID,
			Script:      req.Script,
			Remark:      req.Remark,
		}

		// 创建版本
		if innerErr = s.IndicatorVersionRepo.Create(ctx, tx, version); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator version").WithErr(innerErr).WithStack()
		}
		versionID := version.ID
		if innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", req.ID)
		}, map[string]interface{}{"draft_version_id": versionID}); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator[1]").WithErr(innerErr).WithStack()
		}

		return nil
	})
}

// updateExistingVersion 更新现有未发布版本
func (s *IndicatorService) updateExistingScriptVersion(ctx context.Context, req *request_parameters.UpdateScriptIndicator, existingVersion *modelv2.RmsIndicatorVersion) error {

	return db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		if req.IndicatorName != "" {
			innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
			}, map[string]interface{}{modelv2.NewRmsIndicator().IndicatorNameField(): req.IndicatorName})
			if innerErr != nil {
				return er.Internal.WithMsg("Failed to update script indicator").WithErr(innerErr).WithStack()
			}
		}
		// 1. 更新版本基本信息
		updateData := map[string]interface{}{
			"script": req.Script,
			"remark": req.Remark,
		}

		innerErr = s.IndicatorVersionRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", existingVersion.ID)
		}, updateData)
		if innerErr != nil {
			return er.Internal.WithMsg("Failed to update indicator version").WithErr(innerErr).WithStack()
		}

		return nil
	})
}

// List 获取指标配置列表
func (s *IndicatorService) ListScript(ctx context.Context, req *request_parameters.GetIndicatorScriptList) (total int64, res []*response_parameters.IndicatorScriptList, err error) {
	defaultFilter := filter.NewDefaultFilter(req.PageRequest.Option)

	// 构建查询条件
	fn := func(db *gorm.DB) *gorm.DB {
		if req.AccessPoint != "" {
			db = db.Where(modelv2.NewRmsIndicator().AccessPointField(), req.AccessPoint)
		}
		if req.IndicatorName != "" {
			db = db.Where("indicator_name LIKE ?", "%"+req.IndicatorName+"%")
		}
		return db.Order(modelv2.NewRmsIndicator().IDField()+" desc").
			Where(modelv2.NewRmsIndicator().IsScriptField(), true)
	}

	// 获取指标配置列表
	total, indicators, err := s.IndicatorRepo.List(ctx, defaultFilter, fn, true)
	if err != nil {
		return 0, nil, er.Internal.WithMsg("Failed to query indicators").WithErr(err).WithStack()
	}

	if len(indicators) == 0 {
		return total, []*response_parameters.IndicatorScriptList{}, nil
	}

	// 收集所有的当前版本ID

	versionIDs := make([]int64, 0, len(indicators))
	for _, indicator := range indicators {

		if indicator.CurrentVersionID > 0 {
			versionIDs = append(versionIDs, indicator.CurrentVersionID)
			continue
		}
		if indicator.DraftVersionID > 0 {
			versionIDs = append(versionIDs, indicator.DraftVersionID)
			continue
		}
	}

	// 获取所有当前版本数据
	_, versions, err := s.IndicatorVersionRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id IN ?", versionIDs)
	})
	if err != nil {
		return 0, nil, er.Internal.WithMsg("Failed to query indicator versions").WithErr(err).WithStack()
	}

	var indicatorID2Version = make(map[int64]*modelv2.RmsIndicatorVersion)
	for _, version := range versions {
		indicatorID2Version[version.IndicatorID] = version
	}

	// 构建响应数据
	res = make([]*response_parameters.IndicatorScriptList, 0, len(indicators))
	for _, indicator := range indicators {

		version, ok := indicatorID2Version[indicator.ID]
		if !ok {
			// 跳过没有版本数据的指标
			continue
		}

		item := &response_parameters.IndicatorScriptList{
			ID:             indicator.ID,
			IndicatorName:  indicator.IndicatorName,
			CurrentVersion: version.Version,
			Script:         version.Script,
			Status:         indicator.Status,
			UpdatedAt:      indicator.UpdatedAt.Format("2006-01-02 15:04:05"),
			HasDraft:       indicator.DraftVersionID > 0,
		}
		res = append(res, item)
	}

	return total, res, nil
}

// Retrieve 获取指标配置详情
func (s *IndicatorService) RetrieveScript(ctx context.Context, id, versionID int64) (*response_parameters.IndicatorScriptDetail, error) {
	// 获取指标配置
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Indicator does not exist")
		}
		return nil, er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}

	// 获取当前版本
	version, err := s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		if versionID > 0 {
			return db.Where("id = ?", versionID)
		}
		if indicator.DraftVersionID > 0 {
			return db.Where("id = ?", indicator.DraftVersionID)
		}
		return db.Where("id = ?", indicator.CurrentVersionID)

	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Indicator version does not exist")
		}
		return nil, er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
	}

	// 构建响应数据
	detail := &response_parameters.IndicatorScriptDetail{
		ID:               indicator.ID,
		CurrentVersionID: indicator.CurrentVersionID,
		CurrentVersion:   version.Version,
		IndicatorName:    indicator.IndicatorName,
		DataSourceID:     indicator.DataSourceID,
		Script:           version.Script,
		Remark:           version.Remark,
	}

	return detail, nil
}

//TestScriptIndicatorSql 测试Script指标
func (s *IndicatorService) TestScriptIndicatorSql(ctx context.Context, indicatorID int64, versionID int64) (res map[string]interface{}, err error) {
	res = make(map[string]interface{})
	var indicator *modelv2.RmsIndicator
	indicator, err = s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", indicatorID)
	})
	if err != nil {
		return
	}
	var version *modelv2.RmsIndicatorVersion
	version, err = s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		if versionID > 0 {
			return db.Where("id = ?", versionID)
		}
		if indicator.DraftVersionID > 0 {
			return db.Where("id = ?", indicator.DraftVersionID)
		}
		return db.Where("id = ?", indicator.CurrentVersionID)
	})
	if err != nil {
		return
	}
	var dataSource *modelv2.RmsDataSource
	dataSource, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", indicator.DataSourceID)
	})
	if err != nil {
		return
	}

	// 创建共享的数据库连接
	conn, err := s.DataSourceRepo.GetConnection(dataSource)
	if err != nil {
		return nil, er.Internal.WithMsg("创建数据库连接失败").WithErr(err).WithStack()
	}

	// 获取原始sql.DB连接以便在任务结束后关闭
	sqlDB, err := conn.DB()
	if err != nil {
		return nil, er.Internal.WithMsg("获取DB实例失败").WithErr(err).WithStack()
	}
	// 确保在整个查询任务结束后关闭连接
	defer sqlDB.Close()
	result := conn.WithContext(ctx).Raw(version.Script).Scan(&res)
	if result.Error != nil {
		return nil, er.Internal.WithMsg("指标SQL执行失败").WithErr(result.Error).WithStack()
	}

	return res, nil
}
