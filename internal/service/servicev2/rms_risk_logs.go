package servicev2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type RiskLogService struct {
	RiskLogRepo repo.RmsRiskLog
}

func (s *RiskLogService) Create(ctx context.Context, cp *modelv2.RmsRiskLog) error {
	return s.RiskLogRepo.Create(ctx, nil, cp)
}

func (s *RiskLogService) Update(ctx context.Context, id int64, cp *modelv2.RmsRiskLog) error {
	return s.RiskLogRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(" id =?", id)
	}, cp)
}

func (s *RiskLogService) Delete(ctx context.Context, id int64) error {
	return s.RiskLogRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(" id =?", id)
	})
}

func (s *RiskLogService) List(ctx context.Context, req *request_parameters.RiskLogList) (total int64, li []*modelv2.RmsRiskLog, err error) {
	total, li, err = s.RiskLogRepo.List(ctx, filter.NewDefaultFilterByRequest(req.TimeRequest.Option, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		return db
	}, true)
	return
}

func (s *RiskLogService) Retrieve(ctx context.Context, id int64) (*modelv2.RmsRiskLog, error) {
	return s.RiskLogRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
}
