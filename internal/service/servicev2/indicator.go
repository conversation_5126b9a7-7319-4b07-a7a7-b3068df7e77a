package servicev2

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"

	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"golang.org/x/sync/semaphore"
	"gorm.io/gorm"
)

// queryResult 查询结果结构体，用于保持查询顺序
type queryResult struct {
	Index  int                      // 原始数据的索引
	Result []map[string]interface{} // 查询结果
	Error  error                    // 查询错误
}

// IndicatorService 指标配置服务
type IndicatorService struct {
	IndicatorRepo        repo.IndicatorRepo
	IndicatorVersionRepo repo.IndicatorVersionRepo
	IndicatorRuleRepo    repo.IndicatorRuleRepo
	IndicatorMeasureRepo repo.IndicatorMeasureRepo
	IndicatorHistoryRepo repo.IndicatorVersionHistoryRepo
	DataSourceTableRepo  repo.DataSourceTableRepo
	DataSourceRepo       repo.DataSourceRepo
}

// NewIndicatorService 创建指标配置服务实例
func NewIndicatorService(
	indicatorRepo repo.IndicatorRepo,
	indicatorVersionRepo repo.IndicatorVersionRepo,
	indicatorRuleRepo repo.IndicatorRuleRepo,
	indicatorMeasureRepo repo.IndicatorMeasureRepo,
) *IndicatorService {
	return &IndicatorService{
		IndicatorRepo:        indicatorRepo,
		IndicatorVersionRepo: indicatorVersionRepo,
		IndicatorRuleRepo:    indicatorRuleRepo,
		IndicatorMeasureRepo: indicatorMeasureRepo,
	}
}

// Create 创建指标配置
func (s *IndicatorService) Create(ctx context.Context, req *request_parameters.CreateIndicator) (indicatorID, versionID int64, err error) {
	var _ *modelv2.RmsIndicator
	var table *modelv2.RmsDataSourceTable
	table, err = s.DataSourceTableRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", req.TableID)
	})
	if err != nil {
		return 0, 0, er.Internal.WithMsg("Failed to query data source table").WithErr(err).WithStack()
	}
	// 判断指标是否存在
	_, err = s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IndicatorNameField(), req.IndicatorName).
			Where(modelv2.NewRmsIndicator().AccessPointField(), req.AccessPoint).
			Where(modelv2.NewRmsIndicator().IsScriptField(), false).
			InnerJoins(fmt.Sprintf(
				"inner join %[1]s on %[1]s.%[3]s=%[2]s.%[4]s and %[1]s.%[5]s=%[6]d",
				s.DataSourceTableRepo.TableName(),
				s.IndicatorRepo.TableName(),
				modelv2.NewRmsDataSourceTable().IDField(),
				modelv2.NewRmsIndicator().TableIDField(),
				modelv2.NewRmsDataSourceTable().BusinessTypeField(),
				table.BusinessType,
			))
	})
	if err == nil {
		err = er.AlreadyExists.WithMsg("Indicator already exists")
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		err = er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
		return
	}
	var dataSource *modelv2.RmsDataSource
	dataSource, err = s.DataSourceTableRepo.GetDataSourceByTableID(ctx, req.TableID)
	if err != nil {
		return 0, 0, er.Internal.WithMsg("Failed to get data source").WithErr(err).WithStack()
	}
	// 创建事务

	err = db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		// 1. 创建指标配置
		indicator := &modelv2.RmsIndicator{
			IndicatorName: req.IndicatorName,
			TableID:       req.TableID,
			DataSourceID:  dataSource.ID,
			AccessPoint:   req.AccessPoint,
			MeasureType:   req.MeasureType,
			IsScript:      false,
		}

		// 创建指标
		if innerErr = s.IndicatorRepo.Create(ctx, tx, indicator); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator").WithErr(innerErr).WithStack()
		}
		indicatorID = indicator.ID

		// 2. 创建指标配置版本
		version := &modelv2.RmsIndicatorVersion{
			IndicatorID:         indicator.ID,
			TimeWindowType:      req.TimeWindow.Type,
			TimeWindowValue:     req.TimeWindow.Value,
			TimeWindowUnit:      req.TimeWindow.Unit,
			TimeWindowColumnID:  req.TimeWindow.ColumnID,
			TimeWindowExcluding: req.TimeWindow.Excluding,
			RulePreview:         req.RulePreview,
			Remark:              req.Remark,
			StartTime:           req.TimeWindow.Start,
			EndTime:             req.TimeWindow.End,
		}
		// 创建版本
		if innerErr = s.IndicatorVersionRepo.Create(ctx, tx, version); err != nil {
			return er.Internal.WithMsg("Failed to create indicator version").WithErr(innerErr).WithStack()
		}
		versionID = version.ID
		if innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicator().IDField(), indicatorID)
		}, map[string]interface{}{modelv2.NewRmsIndicator().DraftVersionIDField(): versionID}); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator[1]").WithErr(innerErr).WithStack()
		}
		// 3. 创建指标配置度量
		measures := make([]*modelv2.RmsIndicatorMeasure, 0, len(req.Measures))
		for _, measure := range req.Measures {
			measures = append(measures, &modelv2.RmsIndicatorMeasure{
				VersionID:     version.ID,
				AggType:       measure.AggType,
				MeasureField:  measure.MeasureField,
				ConditionName: measure.ConditionName,
			})
		}
		if len(measures) > 0 {
			if err := s.IndicatorMeasureRepo.BatchCreate(ctx, tx, measures); err != nil {
				return er.Internal.WithMsg("Failed to create indicator measures").WithErr(err).WithStack()
			}
		}

		// 4. 创建指标配置规则
		rules := make([]*modelv2.RmsIndicatorRule, 0, len(req.Rules))
		for _, rule := range req.Rules {
			rules = append(rules, &modelv2.RmsIndicatorRule{
				VersionID:        version.ID,
				HasLeftBrackets:  rule.HasLeftBrackets,
				HasRightBrackets: rule.HasRightBrackets,
				Connector:        rule.Connector,
				ColumnID:         rule.ColumnID,
				Operator:         rule.Operator,
				Value:            rule.Value,
				ValueType:        rule.ValueType,
			})
		}
		if len(rules) > 0 {
			if err := s.IndicatorRuleRepo.BatchCreate(ctx, tx, rules); err != nil {
				return er.Internal.WithMsg("Failed to create indicator rules").WithErr(err).WithStack()
			}
		}

		return nil
	})

	if err != nil {
		return 0, 0, err
	}

	return indicatorID, versionID, nil
}

// GenerateVersionLog 生成指标最新日志
func (s *IndicatorService) GenerateVersionLog(ctx context.Context, user string, indicatorID int64) (err error) {
	var versions []*modelv2.RmsIndicatorVersion
	_, versions, err = s.IndicatorVersionRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorVersion().IndicatorIDField(), indicatorID).Order(modelv2.NewRmsIndicatorVersion().IDField() + " desc").Limit(2)
	})
	if err != nil {
		return er.Internal.WithMsg("Failed to query indicator versions").WithErr(err).WithStack()
	}
	if len(versions) < 2 {
		return s.IndicatorHistoryRepo.AnalyzeVersionAndSave(ctx, false, user, nil, versions[0])
	} else {
		return s.IndicatorHistoryRepo.AnalyzeVersionAndSave(ctx, false, user, versions[1], versions[0])
	}
}

// Update 更新指标配置（创建新版本或更新未发布版本）
func (s *IndicatorService) Update(ctx context.Context, req *request_parameters.UpdateIndicator) error {
	// 1. 检查指标是否存在
	var versionID int64
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return er.NotFound.WithMsg("Indicator does not exist")
		}
		return er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}
	if indicator.Status != modelv2.IndicatorStatusDraft {
		req.IndicatorName = ""
	} else {
		_, err = s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicator().IndicatorNameField(), req.IndicatorName).Where(modelv2.NewRmsIndicator().IDField()+" != ?", indicator.ID).Where(modelv2.NewRmsIndicator().IsScriptField(), false)
		})
		if err == nil {
			return er.AlreadyExists.WithMsg("Indicator name already exists")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return er.Internal.WithMsg("database exception")
		}
		err = nil
	}
	if indicator.DraftVersionID > 0 {
		versionID = indicator.DraftVersionID
	} else if indicator.CurrentVersionID > 0 {
		versionID = indicator.CurrentVersionID
	} else {
		return er.Internal.WithMsg("The indicator data does not exist, cannot be updated.")
	}

	// 2. 获取最近的版本记录
	versionModel, err := s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorVersion().IndicatorIDField(), req.ID).Where(modelv2.NewRmsIndicatorVersion().IDField(), versionID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return er.NotFound.WithMsg("No indicator version found")
	}
	if err != nil {
		return er.Internal.WithMsg("Failed to query latest indicator version").WithErr(err).WithStack()
	}

	// 3. 根据最近版本是否已发布决定更新策略
	if versionModel.Version != "" {
		// 已发布，创建新版本
		return s.createNewVersion(ctx, req)
	} else {
		// 未发布，更新现有版本
		return s.updateExistingVersion(ctx, req, versionModel)
	}
}

// createNewVersion 创建新版本
func (s *IndicatorService) createNewVersion(ctx context.Context, req *request_parameters.UpdateIndicator) error {
	return db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		// 创建新版本
		version := &modelv2.RmsIndicatorVersion{
			IndicatorID:         req.ID,
			TimeWindowType:      req.TimeWindow.Type,
			TimeWindowValue:     req.TimeWindow.Value,
			TimeWindowUnit:      req.TimeWindow.Unit,
			TimeWindowColumnID:  req.TimeWindow.ColumnID,
			TimeWindowExcluding: req.TimeWindow.Excluding,
			RulePreview:         req.RulePreview,
			Remark:              req.Remark,
			StartTime:           req.TimeWindow.Start,
			EndTime:             req.TimeWindow.End,
		}

		// 创建版本
		if innerErr = s.IndicatorVersionRepo.Create(ctx, tx, version); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator version").WithErr(innerErr).WithStack()
		}
		versionID := version.ID
		if innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
		}, map[string]interface{}{modelv2.NewRmsIndicator().DraftVersionIDField(): versionID}); innerErr != nil {
			return er.Internal.WithMsg("Failed to create indicator[1]").WithErr(innerErr).WithStack()
		}
		// 创建指标配置度量
		measures := make([]*modelv2.RmsIndicatorMeasure, 0, len(req.Measures))
		for _, measure := range req.Measures {
			measures = append(measures, &modelv2.RmsIndicatorMeasure{
				VersionID:     version.ID,
				AggType:       measure.AggType,
				MeasureField:  measure.MeasureField,
				ConditionName: measure.ConditionName,
				ParentID:      measure.ID,
			})
		}
		if len(measures) > 0 {
			if innerErr = s.IndicatorMeasureRepo.BatchCreate(ctx, tx, measures); innerErr != nil {
				return er.Internal.WithMsg("Failed to create indicator measures").WithErr(innerErr).WithStack()
			}
		}

		// 创建指标配置规则
		rules := make([]*modelv2.RmsIndicatorRule, 0, len(req.Rules))
		for _, rule := range req.Rules {
			rules = append(rules, &modelv2.RmsIndicatorRule{
				VersionID:        version.ID,
				HasLeftBrackets:  rule.HasLeftBrackets,
				HasRightBrackets: rule.HasRightBrackets,
				Connector:        rule.Connector,
				ColumnID:         rule.ColumnID,
				Operator:         rule.Operator,
				Value:            rule.Value,
				ValueType:        rule.ValueType,
				ParentID:         rule.ID,
			})
		}
		if len(rules) > 0 {
			if innerErr = s.IndicatorRuleRepo.BatchCreate(ctx, tx, rules); innerErr != nil {
				return er.Internal.WithMsg("Failed to create indicator rules").WithErr(innerErr).WithStack()
			}
		}

		return nil
	})
}

// updateExistingVersion 更新现有未发布版本
func (s *IndicatorService) updateExistingVersion(ctx context.Context, req *request_parameters.UpdateIndicator, existingVersion *modelv2.RmsIndicatorVersion) error {
	measures, err := s.IndicatorMeasureRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorMeasure().VersionIDField(), existingVersion.ID)
	})
	if err != nil {
		return er.Internal.WithMsg("Failed to update indicator version").WithErr(err).WithStack()
	}
	// 计算Measure更新新增的差集数据
	var insMeasures, upMeasures []*modelv2.RmsIndicatorMeasure
	var delMeasureIds []int64
	var currentMeasures []*modelv2.RmsIndicatorMeasure
	for _, measure := range req.Measures {
		currentMeasures = append(currentMeasures, &modelv2.RmsIndicatorMeasure{
			ID:            measure.ID,
			VersionID:     existingVersion.ID,
			AggType:       measure.AggType,
			MeasureField:  measure.MeasureField,
			ConditionName: measure.ConditionName,
		})
	}
	insMeasures, upMeasures, delMeasureIds = modelv2.NewRmsIndicatorMeasure().DiffUp(measures, currentMeasures)
	// 计算Rule更新新增的差集数据
	rules, err := s.IndicatorRuleRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorRule().VersionIDField(), existingVersion.ID)
	})
	if err != nil {
		return er.Internal.WithMsg("Failed to update indicator version").WithErr(err).WithStack()
	}
	var insRules, upRules []*modelv2.RmsIndicatorRule
	var delRuleIds []int64
	var currentRules []*modelv2.RmsIndicatorRule
	for _, rule := range req.Rules {
		currentRules = append(currentRules, &modelv2.RmsIndicatorRule{
			ID:               rule.ID,
			VersionID:        existingVersion.ID,
			HasLeftBrackets:  rule.HasLeftBrackets,
			HasRightBrackets: rule.HasRightBrackets,
			Connector:        rule.Connector,
			ColumnID:         rule.ColumnID,
			Operator:         rule.Operator,
			Value:            rule.Value,
			ValueType:        rule.ValueType,
		})
	}
	insRules, upRules, delRuleIds = modelv2.NewRmsIndicatorRule().DiffUp(rules, currentRules)
	return db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		if req.IndicatorName != "" {
			innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
			}, map[string]interface{}{modelv2.NewRmsIndicator().IndicatorNameField(): req.IndicatorName})
			if innerErr != nil {
				return er.Internal.WithMsg("Failed to update indicator").WithErr(innerErr).WithStack()
			}
		}
		// 1. 更新版本基本信息
		updateData := map[string]interface{}{
			modelv2.NewRmsIndicatorVersion().TimeWindowTypeField():      req.TimeWindow.Type,
			modelv2.NewRmsIndicatorVersion().TimeWindowValueField():     req.TimeWindow.Value,
			modelv2.NewRmsIndicatorVersion().TimeWindowUnitField():      req.TimeWindow.Unit,
			modelv2.NewRmsIndicatorVersion().TimeWindowColumnIDField():  req.TimeWindow.ColumnID,
			modelv2.NewRmsIndicatorVersion().TimeWindowExcludingField(): req.TimeWindow.Excluding,
			modelv2.NewRmsIndicatorVersion().RemarkField():              req.Remark,
			modelv2.NewRmsIndicatorVersion().RulePreviewField():         req.RulePreview,
			modelv2.NewRmsIndicatorVersion().StartTimeField():           req.TimeWindow.Start,
			modelv2.NewRmsIndicatorVersion().EndTimeField():             req.TimeWindow.End,
		}

		innerErr = s.IndicatorVersionRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicatorVersion().IDField(), existingVersion.ID)
		}, updateData)
		if innerErr != nil {
			return er.Internal.WithMsg("Failed to update indicator version").WithErr(innerErr).WithStack()
		}

		// 2. 更新旧的度量记录
		if len(upMeasures) > 0 {
			for _, measure := range upMeasures {
				innerErr = s.IndicatorMeasureRepo.Update(ctx, tx, func(db *gorm.DB) *gorm.DB {
					return db.Where(modelv2.NewRmsIndicatorMeasure().IDField(), measure.ID)
				}, measure)
				if innerErr != nil {
					return er.Internal.WithMsg("Failed to update old indicator measures").WithErr(innerErr).WithStack()
				}

			}
		}

		// 3. 创建新的度量记录
		if len(insMeasures) > 0 {
			innerErr = s.IndicatorMeasureRepo.BatchCreate(ctx, tx, insMeasures)
			if innerErr != nil {
				return er.Internal.WithMsg("Failed to create new indicator measures").WithErr(innerErr).WithStack()
			}
		}
		// 4. 删除的度量记录
		if len(delMeasureIds) > 0 {
			innerErr = s.IndicatorMeasureRepo.Delete(ctx, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where(modelv2.NewRmsIndicatorMeasure().IDField()+" in ?", delMeasureIds)
			})
			if innerErr != nil {
				return er.Internal.WithMsg("Failed to delete old indicator measures").WithErr(innerErr).WithStack()
			}
		}
		// 5. 更新旧的规则记录
		if len(upRules) > 0 {
			for _, rule := range upRules {
				var tmpRule = map[string]interface{}{
					modelv2.NewRmsIndicatorRule().HasLeftBracketsField():  rule.HasLeftBrackets,
					modelv2.NewRmsIndicatorRule().HasRightBracketsField(): rule.HasRightBrackets,
					modelv2.NewRmsIndicatorRule().ConnectorField():        rule.Connector,
					modelv2.NewRmsIndicatorRule().ColumnIDField():         rule.ColumnID,
					modelv2.NewRmsIndicatorRule().OperatorField():         rule.Operator,
					modelv2.NewRmsIndicatorRule().ValueField():            rule.Value,
					modelv2.NewRmsIndicatorRule().ValueTypeField():        rule.ValueType,
				}
				innerErr = s.IndicatorRuleRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
					return db.Where(modelv2.NewRmsIndicatorRule().IDField(), rule.ID)
				}, tmpRule)
				if innerErr != nil {
					return er.Internal.WithMsg("Failed to update old indicator rule").WithErr(innerErr).WithStack()
				}
			}
		}

		// 6. 创建新的规则
		if len(insRules) > 0 {
			innerErr = s.IndicatorRuleRepo.BatchCreate(ctx, tx, insRules)
			if innerErr != nil {
				return er.Internal.WithMsg("Failed to create new indicator rules").WithErr(innerErr).WithStack()
			}
		}

		// 7. 删除的规则记录
		if len(delRuleIds) > 0 {
			innerErr = s.IndicatorRuleRepo.Delete(ctx, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where(modelv2.NewRmsIndicatorRule().IDField()+" in ?", delRuleIds)
			})
			if innerErr != nil {
				return er.Internal.WithMsg("Failed to delete old indicator rules").WithErr(innerErr).WithStack()
			}
		}

		return nil
	})
}

// ReleaseVersion 发布指标版本
func (s *IndicatorService) ReleaseVersion(ctx context.Context, req *request_parameters.ReleaseIndicator) (err error) {
	// 1. 获取指标版本
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = er.NotFound.WithMsg("Indicator version does not exist")
			return
		}
		err = er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
		return
	}

	version, err := s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorVersion().IDField(), indicator.DraftVersionID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = er.NotFound.WithMsg("Indicator version does not exist")
		return
	}
	if err != nil {
		err = er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
		return
	}
	if !indicator.IsScript {
		var preData *entity.Indicator
		// 生成指标SQL
		preData, err = s.GetIndicatorSqlData(ctx, indicator.ID, version.ID)
		if err != nil {
			err = er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
			return
		}
		if err = preData.Validator(); err != nil {
			err = er.Internal.WithMsg(err.Error()).WithErr(err).WithStack()
			return
		}
	}

	versionStr := s.IndicatorVersionRepo.NewVersion(ctx, indicator.ID)
	if versionStr == "" {
		err = er.Internal.WithMsg("Failed to create indicator version")
		return
	}
	err = db.Transaction(ctx, nil, func(tx *gorm.DB) (innerErr error) {
		var upIndicator = map[string]interface{}{
			modelv2.NewRmsIndicator().CurrentVersionIDField(): version.ID,
			modelv2.NewRmsIndicator().CurrentVersionField():   versionStr,
			modelv2.NewRmsIndicator().DraftVersionIDField():   0,
		}
		if indicator.Status != modelv2.IndicatorStatusOffline {
			upIndicator[modelv2.NewRmsIndicator().StatusField()] = modelv2.IndicatorStatusOnline
		}
		innerErr = s.IndicatorRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicator().IDField(), indicator.ID)
		}, upIndicator)
		if innerErr != nil {
			return er.Internal.WithMsg("Failed to update indicator").WithErr(innerErr).WithStack()
		}
		innerErr = s.IndicatorVersionRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicatorVersion().IDField(), version.ID)
		}, map[string]interface{}{
			modelv2.NewRmsIndicatorVersion().VersionField(): versionStr,
		})
		if innerErr != nil {
			return er.Internal.WithMsg("Failed to update indicator version").WithErr(innerErr).WithStack()
		}

		innerErr = s.IndicatorHistoryRepo.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicatorVersionHistory().VersionIDField(), version.ID)
		}, map[string]interface{}{
			modelv2.NewRmsIndicatorVersionHistory().VersionField(): versionStr,
			modelv2.NewRmsIndicatorVersionHistory().IsDraftField(): false,
		})
		if innerErr != nil {
			return er.Internal.WithMsg("Failed to update indicator history").WithErr(innerErr).WithStack()
		}
		return nil
	})

	return
}

// FlushIndicatorCache 刷新指标缓存
func (s *IndicatorService) FlushIndicatorCache(ctx context.Context, indicatorID int64) (err error) {
	// 1. 获取指标版本
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), indicatorID)
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = er.NotFound.WithMsg("Indicator version does not exist")
			return
		}
		err = er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
		return
	}
	if indicator.CurrentVersionID > 0 && indicator.Status == modelv2.IndicatorStatusOnline {
		var version *modelv2.RmsIndicatorVersion
		version, err = s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where(modelv2.NewRmsIndicatorVersion().IDField(), indicator.CurrentVersionID)
		})
		if err != nil {
			err = er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
			return
		}
		switch indicator.IsScript {
		case true:
			jsonData, _ := jsoniter.MarshalToString(map[string]interface{}{
				"indicator_id": indicatorID,
				"code":         version.Script,
				"placeholder":  []string{},
			})
			redisutils.HSet(modelv2.RedisCacheScriptIndicator+":"+indicator.AccessPoint, indicator.IndicatorName, jsonData)

		case false:
			var predata *entity.Indicator
			predata, err = s.GetIndicatorSqlData(ctx, indicator.ID, version.ID)
			if err != nil {
				err = er.Internal.WithMsg("Failed to get indicator sql data").WithErr(err).WithStack()
				return
			}
			if err = predata.Validator(); err != nil {
				err = er.Internal.WithMsg(err.Error()).WithErr(err).WithStack()
				return
			}
			var dataSource *modelv2.RmsDataSource
			dataSource, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where(modelv2.NewRmsDataSource().IDField(), indicator.DataSourceID)
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to get data source").WithErr(err).WithStack()
				return
			}
			var sql string
			var placeholder []string
			switch dataSource.SourceType {
			case "mysql":
				sql, placeholder, err = s.Indicator2MysqlSql(predata)
				if err != nil {
					err = er.Internal.WithMsg("Failed to get indicator mysql sql").WithErr(err).WithStack()
					return
				}
			case "doris":
				sql, placeholder, err = s.Indicator2DorisSql(predata)
				if err != nil {
					err = er.Internal.WithMsg("Failed to get indicator doris sql").WithErr(err).WithStack()
					return
				}
			}
			if sql != "" {
				jsonData, _ := jsoniter.MarshalToString(map[string]interface{}{
					"indicator_id": indicatorID,
					"code":         sql,
					"placeholder":  placeholder,
				})
				redisutils.HSet(modelv2.RedisCacheVisualIndicator+":"+indicator.AccessPoint, indicator.IndicatorName, jsonData)

			}

		}
		return
	}

	return
}

// DeleteIndicatorCache 删除指标缓存
func (s *IndicatorService) DeleteIndicatorCache(ctx context.Context, indicatorID int64) (err error) {
	// 1. 获取指标版本
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), indicatorID)
	})

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = er.NotFound.WithMsg("Indicator version does not exist")
			return
		}
		err = er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
		return
	}
	switch indicator.IsScript {
	case true:
		redisutils.HDel(modelv2.RedisCacheScriptIndicator+":"+indicator.AccessPoint, strconv.FormatInt(indicator.ID, 10))

	case false:
		redisutils.HDel(modelv2.RedisCacheVisualIndicator+":"+indicator.AccessPoint, strconv.FormatInt(indicator.ID, 10))
	}

	return
}

// UpdateStatus 更新指标状态
func (s *IndicatorService) UpdateStatus(ctx context.Context, req *request_parameters.UpdateIndicatorStatus) error {
	// 获取指标信息
	_, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return er.NotFound.WithMsg("Indicator does not exist")
		}
		return er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}

	var status string
	if req.Disable {
		// 设置为禁用状态
		status = modelv2.IndicatorStatusOffline
	} else {
		// 恢复状态，根据current_version_id判断
		status = modelv2.IndicatorStatusOnline
	}

	// 更新指标状态
	err = s.IndicatorRepo.Updates(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), req.ID)
	}, map[string]interface{}{
		modelv2.NewRmsIndicator().StatusField(): status,
	})
	if err != nil {
		return er.Internal.WithMsg("Failed to update indicator status").WithErr(err).WithStack()
	}

	return nil
}

// Delete 删除指标配置
func (s *IndicatorService) Delete(ctx context.Context, id int64) error {
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), id)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return er.NotFound.WithMsg("Indicator does not exist")
	}
	if err != nil {
		return er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}
	if indicator.Status != modelv2.IndicatorStatusDraft {
		return er.Internal.WithMsg("Only draft indicators can be deleted")
	}
	if err := s.IndicatorRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), id)
	}); err != nil {
		return er.Internal.WithMsg("Failed to delete indicator").WithErr(err).WithStack()
	}

	return nil
}

// List 获取指标配置列表
func (s *IndicatorService) List(ctx context.Context, req *request_parameters.GetIndicatorList) (total int64, res []*response_parameters.IndicatorList, err error) {
	defaultFilter := filter.NewDefaultFilter(req.PageRequest.Option)

	// 构建查询条件
	fn := func(db *gorm.DB) *gorm.DB {
		if req.AccessPoint != "" {
			db = db.Where(modelv2.NewRmsIndicator().AccessPointField(), req.AccessPoint)
		}
		if req.Name != "" {
			db = db.Where(modelv2.NewRmsIndicator().IndicatorNameField()+" LIKE ?", "%"+req.Name+"%")
		}
		if req.TableID > 0 {
			db = db.Where(modelv2.NewRmsIndicator().TableIDField(), req.TableID)
		}
		return db.Order(modelv2.NewRmsIndicator().IDField()+" desc").Where(modelv2.NewRmsIndicator().IsScriptField()+" = ?", false)
	}

	// 获取指标配置列表
	total, indicators, err := s.IndicatorRepo.List(ctx, defaultFilter, fn, true)
	if err != nil {
		return 0, nil, er.Internal.WithMsg("Failed to query indicators").WithErr(err).WithStack()
	}

	if len(indicators) == 0 {
		return total, []*response_parameters.IndicatorList{}, nil
	}

	// 收集所有的当前版本ID

	versionIDs := make([]int64, 0, len(indicators))
	for _, indicator := range indicators {
		if indicator.CurrentVersionID > 0 {
			versionIDs = append(versionIDs, indicator.CurrentVersionID)
			continue
		}
		if indicator.DraftVersionID > 0 {
			versionIDs = append(versionIDs, indicator.DraftVersionID)
			continue
		}
	}

	// 获取所有当前版本数据
	_, versions, err := s.IndicatorVersionRepo.List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorVersion().IDField()+" IN ?", versionIDs)
	})
	if err != nil {
		return 0, nil, er.Internal.WithMsg("Failed to query indicator versions").WithErr(err).WithStack()
	}

	var indicatorID2Version = make(map[int64]*modelv2.RmsIndicatorVersion)
	for _, version := range versions {
		indicatorID2Version[version.IndicatorID] = version
	}

	// 获取所有度量数据
	measures, err := s.IndicatorMeasureRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorMeasure().VersionIDField()+" IN ?", versionIDs)
	})
	if err != nil {
		return 0, nil, er.Internal.WithMsg("Failed to query indicator measures").WithErr(err).WithStack()
	}

	// 构建版本ID到度量的映射
	measureMap := make(map[int64][]*modelv2.RmsIndicatorMeasure)
	for _, measure := range measures {
		if _, ok := measureMap[measure.VersionID]; !ok {
			measureMap[measure.VersionID] = make([]*modelv2.RmsIndicatorMeasure, 0)
		}
		measureMap[measure.VersionID] = append(measureMap[measure.VersionID], measure)
	}

	// 构建响应数据
	res = make([]*response_parameters.IndicatorList, 0, len(indicators))
	for _, indicator := range indicators {

		version, ok := indicatorID2Version[indicator.ID]
		if !ok {
			// 跳过没有版本数据的指标
			continue
		}
		var measure string
		measures := measureMap[version.ID]
		for _, m := range measures {
			column := entity.NewDataSourceColumnByColumnID(m.MeasureField)
			if column == nil && m.MeasureField == "*" {
				column = &entity.DataSourceColumn{
					ColumnName: "*",
				}
			}
			if column != nil {

				switch indicator.MeasureType {
				case consts.IndicatorMeasureTypeAgg:
					if measure == "" {
						measure = fmt.Sprintf("%s(%s)", m.AggType, column.ColumnName)
					} else {
						measure += fmt.Sprintf(",%s(%s)", m.AggType, column.ColumnName)
					}
				case consts.IndicatorMeasureTypeSelect:
					if measure == "" {
						measure = column.ColumnName
					} else {
						measure += "," + column.ColumnName
					}
				}

			}

		}
		var timeWindow string
		switch version.TimeWindowType {
		case consts.IndicatorMeasureWindowTypeSliding:
			timeWindow = fmt.Sprintf("%d %s", version.TimeWindowValue, consts.IndicatorVersionSlidingTimeWindowUnits[version.TimeWindowUnit])
		case consts.IndicatorMeasureWindowTypeFixed:
			timeWindow = consts.IndicatorVersionFixedTimeWindowUnits[version.TimeWindowUnit]
		case consts.IndicatorMeasureWindowTypeBefore:
			timeWindow = fmt.Sprintf("Before %s", version.StartTime)
		case consts.IndicatorMeasureWindowTypeAfter:
			timeWindow = fmt.Sprintf("After %s", version.EndTime)
		case consts.IndicatorMeasureWindowTypeBetween:
			timeWindow = fmt.Sprintf("Between %s and %s", version.StartTime, version.EndTime)
		}
		var table *modelv2.RmsDataSourceTable
		table, err = s.DataSourceTableRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", indicator.TableID)
		})
		if err != nil {
			err = er.Internal.WithMsg("Failed to query data source table").WithErr(err).WithStack()
			return
		}
		item := &response_parameters.IndicatorList{
			ID:               indicator.ID,
			IndicatorName:    indicator.IndicatorName,
			CurrentVersionID: indicator.CurrentVersionID,
			CurrentVersion:   indicator.CurrentVersion,
			TableName:        table.Name,
			Measures:         measure,
			TimeWindow:       timeWindow,
			Status:           indicator.Status,
			UpdatedAt:        indicator.UpdatedAt.Format("2006-01-02 15:04:05"),
			HasDraft:         indicator.DraftVersionID > 0,
		}
		res = append(res, item)
	}

	return total, res, nil
}

// Retrieve 获取指标配置详情
func (s *IndicatorService) Retrieve(ctx context.Context, id, versionID int64) (*response_parameters.IndicatorDetail, error) {
	// 获取指标配置
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), id)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Indicator does not exist")
		}
		return nil, er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}

	// 获取当前版本
	version, err := s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		if versionID > 0 {
			return db.Where(modelv2.NewRmsIndicatorVersion().IDField()+" = ?", versionID)
		}
		if indicator.DraftVersionID > 0 {
			return db.Where(modelv2.NewRmsIndicatorVersion().IDField()+" = ?", indicator.DraftVersionID)
		}
		return db.Where(modelv2.NewRmsIndicatorVersion().IDField()+" = ?", indicator.CurrentVersionID)

	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, er.NotFound.WithMsg("Indicator version does not exist")
		}
		return nil, er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
	}
	// 获取TimeWindows
	timeWindowRes := response_parameters.TimeWindow{
		ColumnID:  version.TimeWindowColumnID,
		Type:      version.TimeWindowType,
		Value:     version.TimeWindowValue,
		Start:     version.StartTime,
		End:       version.EndTime,
		Unit:      version.TimeWindowUnit,
		Excluding: version.TimeWindowExcluding,
	}
	// 获取规则
	rules, err := s.IndicatorRuleRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorRule().VersionIDField()+" = ?", version.ID).Order("id asc")
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query indicator rules").WithErr(err).WithStack()
	}
	var measureRes []response_parameters.Measure
	// 获取度量信息
	measures, err := s.IndicatorMeasureRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorMeasure().VersionIDField()+" = ?", version.ID).Order("id asc")
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query indicator measures").WithErr(err).WithStack()
	}
	for _, measure := range measures {
		measureRes = append(measureRes, response_parameters.Measure{
			ID:            measure.ID,
			AggType:       measure.AggType,
			MeasureField:  measure.MeasureField,
			ConditionName: measure.ConditionName,
		})
	}

	// 构建规则列表
	ruleList := make([]entity.IndicatorRule, 0, len(rules))
	for _, rule := range rules {
		ruleList = append(ruleList, entity.IndicatorRule{
			ID:               rule.ID,
			HasLeftBrackets:  rule.HasLeftBrackets,
			HasRightBrackets: rule.HasRightBrackets,
			Connector:        rule.Connector,
			ColumnID:         rule.ColumnID,
			Operator:         rule.Operator,
			Value:            rule.Value,
			ValueType:        rule.ValueType,
		})
	}

	// 构建响应数据
	detail := &response_parameters.IndicatorDetail{
		ID:               indicator.ID,
		IndicatorName:    indicator.IndicatorName,
		MeasureType:      indicator.MeasureType,
		CurrentVersionID: indicator.CurrentVersionID,
		CurrentVersion:   version.Version,
		TableID:          indicator.TableID,
		Measures:         measureRes,
		TimeWindow:       timeWindowRes,
		AccessPoint:      indicator.AccessPoint,
		RulePreview:      version.RulePreview,
		Rules:            ruleList,
		Remark:           version.Remark,
	}

	return detail, nil
}

// ListVersions 获取指标配置版本列表
func (s *IndicatorService) ListVersions(ctx context.Context, req *request_parameters.GetIndicatorVersion) (total int64, res []*response_parameters.IndicatorVersionList, err error) {
	defaultFilter := filter.NewDefaultFilter(req.PageRequest.Option)

	// 检查指标是否存在
	indicator, err := s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField(), req.IndicatorID)
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, nil, er.NotFound.WithMsg("Indicator does not exist")
		}
		return 0, nil, er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}

	// 获取版本历史列表
	total, histories, err := s.IndicatorHistoryRepo.List(ctx, defaultFilter, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorVersionHistory().IndicatorIDField()+" = ?", req.IndicatorID)
	}, true)
	if err != nil {
		return 0, nil, er.Internal.WithMsg("Failed to query indicator versions").WithErr(err).WithStack()
	}

	if len(histories) == 0 {
		return total, []*response_parameters.IndicatorVersionList{}, nil
	}

	for _, history := range histories {
		var version = "V" + history.Version
		if version == "V" {
			version = "Draft"
		}
		var status = "Archived"
		if history.IsDraft {
			status = "Pending changes"
		} else if history.VersionID == indicator.CurrentVersionID {
			status = "Current version"
		}
		item := &response_parameters.IndicatorVersionList{
			IndicatorID:    history.IndicatorID,
			VersionID:      history.VersionID,
			Version:        version,
			Desc:           history.Desc,
			ModifyBy:       history.LastModified,
			Status:         status,
			LastModifiedAt: history.UpdatedAt.Format("2006-01-02 15:04"),
		}
		res = append(res, item)
	}

	return total, res, nil
}

// GetIndicatorSqlData 获取指标SQL生成所需的数据
// 该函数用于收集生成指标SQL语句所需的所有相关数据，包括指标基本信息、时间窗口配置、度量规则等
//
// 参数:
//   - ctx: 上下文信息
//   - indicatorID: 指标ID，用于查询指标基本信息
//   - versionID: 版本ID，如果为0则使用当前版本，用于查询指标的具体配置信息
//
// 返回:
//   - predata: 包含生成SQL所需的所有数据的结构体指针
//   - err: 错误信息，如果发生错误则返回对应的错误
func (s *IndicatorService) GetIndicatorSqlData(ctx context.Context, indicatorID int64, versionID int64) (predata *entity.Indicator, err error) {
	predata = &entity.Indicator{ID: indicatorID}

	// 1. 获取指标基本信息
	var indicator *modelv2.RmsIndicator
	indicator, err = s.IndicatorRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicator().IDField()+" = ?", indicatorID)
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query indicator").WithErr(err).WithStack()
	}

	// 2. 确定使用的版本ID（如果未指定则使用当前版本）
	if versionID == 0 {
		versionID = indicator.DraftVersionID
	}
	if versionID == 0 {
		versionID = indicator.CurrentVersionID
	}
	// 3. 设置指标基本信息
	predata.AccessPoint = indicator.AccessPoint
	predata.IndicatorName = indicator.IndicatorName
	predata.TableID = indicator.TableID
	predata.MeasureType = indicator.MeasureType

	// 4. 获取数据源类型信息
	var dataSource *modelv2.RmsDataSource
	dataSource, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsDataSource().IDField()+" = ?", indicator.DataSourceID)
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query data source").WithErr(err).WithStack()
	}
	predata.DataSourceType = dataSource.SourceType
	predata.DataSourceID = dataSource.ID

	// 5. 获取指标版本配置信息
	var indicatorVersion *modelv2.RmsIndicatorVersion
	indicatorVersion, err = s.IndicatorVersionRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorVersion().IDField()+" = ?", versionID)
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query indicator version").WithErr(err).WithStack()
	}

	// 6. 设置时间窗口配置
	predata.TimeWindow = entity.TimeWindow{
		TimeWindowType:      indicatorVersion.TimeWindowType,
		TimeWindowValue:     indicatorVersion.TimeWindowValue,
		TimeWindowUnit:      indicatorVersion.TimeWindowUnit,
		TimeWindowColumnID:  indicatorVersion.TimeWindowColumnID,
		TimeWindowExcluding: indicatorVersion.TimeWindowExcluding,
		TimeWindowStartTime: indicatorVersion.StartTime,
		TimeWindowEndTime:   indicatorVersion.EndTime,
	}

	// 7. 获取并设置度量规则信息
	var measures []*modelv2.RmsIndicatorMeasure
	measures, err = s.IndicatorMeasureRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorMeasure().VersionIDField()+" = ?", versionID)
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query indicator measures").WithErr(err).WithStack()
	}
	for _, v := range measures {
		predata.Measures = append(predata.Measures, entity.Measure{
			AggType:       v.AggType,
			MeasureField:  v.MeasureField,
			ConditionName: v.ConditionName,
		})
	}

	// 8. 获取并设置规则信息
	var rules []*modelv2.RmsIndicatorRule
	rules, err = s.IndicatorRuleRepo.List(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsIndicatorRule().VersionIDField()+" = ?", versionID)
	})
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to query indicator rules").WithErr(err).WithStack()
	}
	for _, v := range rules {
		predata.Rules = append(predata.Rules, entity.Rule{
			HasLeftBrackets:  v.HasLeftBrackets,
			HasRightBrackets: v.HasRightBrackets,
			Connector:        v.Connector,
			ColumnID:         v.ColumnID,
			Operator:         v.Operator,
			Value:            v.Value,
			ValueType:        v.ValueType,
		})
	}

	return predata, nil
}
func (s *IndicatorService) TestIndicatorSql(ctx context.Context, data []map[string]interface{}, indicatorID int64, versionID int64) (res interface{}, err error) {
	var tmpRes [][]map[string]interface{}
	var preData *entity.Indicator
	// 生成指标SQL
	preData, err = s.GetIndicatorSqlData(ctx, indicatorID, versionID)
	if err != nil {
		err = er.Internal.WithMsg(err.Error())
		return
	}
	if err = preData.Validator(); err != nil {
		err = er.Internal.WithMsg(err.Error()).WithErr(err).WithStack()
		return
	}
	var dataSource *modelv2.RmsDataSource
	dataSource, err = s.DataSourceRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsDataSource().IDField()+" = ?", preData.DataSourceID)
	})
	if err != nil {
		return
	}
	var sql string
	var placeholders []string

	switch preData.DataSourceType {
	case "mysql":
		sql, placeholders, err = s.Indicator2MysqlSql(preData)
		if err != nil {
			return
		}
	case "doris":
		sql, placeholders, err = s.Indicator2DorisSql(preData)
		if err != nil {
			return
		}
	}
	if len(placeholders) > 0 {
		if len(data) == 0 {
			err = er.InvalidParameter.WithMsg("The parameter is incorrect.")
			return
		}
		for _, placeholder := range placeholders {
			for _, datum := range data {
				if _, ok := datum[placeholder]; !ok {
					err = er.InvalidParameter.WithMsg(fmt.Sprintf("The parameter %s is incorrect.", placeholder))
					return
				}
			}
		}
	}

	if len(placeholders) > 0 && len(data) == 0 {
		return
	}
	// 创建共享的数据库连接
	conn, err := s.DataSourceRepo.GetConnection(dataSource)
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to create database connection").WithErr(err).WithStack()
	}

	// 获取原始sql.DB连接以便在任务结束后关闭
	sqlDB, err := conn.DB()
	if err != nil {
		return nil, er.Internal.WithMsg("Failed to obtain DB instance").WithErr(err).WithStack()
	}
	// 确保在整个查询任务结束后关闭连接
	defer sqlDB.Close()
	if len(data) > 0 {
		// 使用信号量控制并发数量，最多10个并发
		sem := semaphore.NewWeighted(10)

		resultChan := make(chan queryResult, len(data))
		var wg sync.WaitGroup
		for i, datum := range data {
			wg.Add(1)
			go func(index int, datum map[string]interface{}) {
				defer wg.Done()
				fmt.Println(3333)

				// 获取信号量
				if err := sem.Acquire(ctx, 1); err != nil {
					resultChan <- queryResult{Index: index, Error: err}
					return
				}
				defer sem.Release(1)
				fmt.Println(1111)
				// 执行单个查询，复用共享连接
				result, queryErr := s.executeSingleQueryWithSharedConn(ctx, conn, sql, placeholders, datum)
				resultChan <- queryResult{
					Index:  index,
					Result: result,
					Error:  queryErr,
				}
			}(i, datum)
		}
		wg.Wait()
		close(resultChan)

		results := make([]queryResult, 0, len(data))
		for result := range resultChan {
			results = append(results, result)
		}

		// 按索引排序以保持原始顺序
		sort.Slice(results, func(i, j int) bool {
			return results[i].Index < results[j].Index
		})

		for _, result := range results {
			if result.Error != nil {
				return nil, result.Error
			}
			tmpRes = append(tmpRes, result.Result)
		}
	} else {
		result, queryErr := s.executeSingleQueryWithSharedConn(ctx, conn, sql, placeholders, nil)
		if queryErr != nil {
			return
		}
		tmpRes = append(tmpRes, result)
	}
	if preData.MeasureType == consts.IndicatorMeasureTypeAgg {
		var tmpAggRes []map[string]interface{}
		for _, re := range tmpRes {
			tmpAggRes = append(tmpAggRes, re[0])
		}
		return tmpAggRes, nil
	}

	return tmpRes, nil
}

// executeSingleQueryWithSharedConn 使用共享连接执行单个查询
func (s *IndicatorService) executeSingleQueryWithSharedConn(ctx context.Context, conn *gorm.DB, sql string, placeholders []string, datum map[string]interface{}) ([]map[string]interface{}, error) {
	// 准备查询参数
	var values []interface{}
	for _, v := range placeholders {
		if vv, ok := datum[v]; ok {
			if strings.Contains(sql, "["+v+"]") {
				sql = strings.Replace(sql, "["+v+"]", fmt.Sprintf("%v", vv), 1)
			} else {
				values = append(values, vv)
			}
		} else {
			return nil, er.InvalidParameter.WithMsg(fmt.Sprintf("参数%s不存在", v)).WithStack()
		}
	}

	// 执行查询，复用共享连接
	var tmpRes []map[string]interface{}
	result := conn.WithContext(ctx).Raw(sql, values...).Scan(&tmpRes)
	if result.Error != nil {
		return nil, er.Internal.WithMsg("指标SQL执行失败").WithErr(result.Error).WithStack()
	}

	return tmpRes, nil
}

func (s *IndicatorService) getColumnMapToType(table *modelv2.RmsDataSourceTable) (res map[string]string, err error) {
	var dataSource *modelv2.RmsDataSource
	res = make(map[string]string)
	dataSource, err = s.DataSourceRepo.First(context.Background(), func(db *gorm.DB) *gorm.DB {
		return db.Where(modelv2.NewRmsDataSource().IDField()+" = ?", table.DataSourceID)
	})
	if err != nil {
		return
	}
	var columns []*entity.Column
	columns, err = s.DataSourceTableRepo.Columns(context.Background(), dataSource, table.Name)
	if err != nil {
		return
	}
	for _, column := range columns {
		res[column.Name] = column.TransType()
	}
	return
}
