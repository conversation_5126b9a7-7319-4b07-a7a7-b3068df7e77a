package servicev2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type HandleLogService struct {
	HandleLogRepo repo.HandleLogRepo
}

//func (s *HandleLogService) Create(c *gin.Context, cp *model.WpHandleLog) error {
//	s.HandleLogRepo.Create()
//	return s.HandleLogRepo.Create(c, cp)
//}

//func (s *HandleLogService) Update(c *gin.Context, id int32, cp *model.WpHandleLog) error {
//	return s.HandleLogRepo.Update(c, int64(id), cp)
//}
//
//func (s *HandleLogService) Delete(c *gin.Context, id int64) error {
//	return s.HandleLogRepo.Delete(c, id)
//}

func (s *HandleLogService) List(ctx context.Context, defaultFilter *filter.DefaultFilter, req *request_parameters.HandleLogList) (total int64, models []*modelv2.WpHandleLog, err error) {
	total, models, err = s.HandleLogRepo.List(ctx, defaultFilter, func(db *gorm.DB) *gorm.DB {
		if req.Puserid != "" {
			db = db.Where("puserid=?", req.Puserid)
		}
		if req.HandleEvents != "" {
			db = db.Where("handle_events=?", req.HandleEvents)
		}
		if req.HandleParams != "" {
			db = db.Where("handle_params=?", req.HandleParams)
		}
		return db
	}, true)

	return
}

func (s *HandleLogService) Retrieve(ctx context.Context, id int64) (*modelv2.WpHandleLog, error) {
	return s.HandleLogRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("hlid = ?", id)
	})
}
