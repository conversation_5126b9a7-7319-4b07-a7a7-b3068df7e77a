package servicev2

import (
	"context"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"go.uber.org/zap"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gorm.io/gorm"
)

type BlackWhiteOperatorLogService struct {
	BlackWhiteItemRepo        repo.BlackWhiteItemRepo
	BlackWhiteListRepo        repo.BlackWhiteListRepo
	BlackWhiteAuditRepo       repo.BlackWhiteAuditRepo
	BlackWhiteOperatorLogRepo repo.BlackWhiteOperatorLogRepo
}

func (b *BlackWhiteOperatorLogService) List(c context.Context, req *request_parameters.BlackWhiteOperatorLogsRequest) (total int64, respLi []*modelv2.RmsBlackWhiteOperatorLog, err error) {
	total, respLi, err = b.BlackWhiteOperatorLogRepo.List(c, filter.NewDefaultFilter(req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", req.BwlID).Order("id desc")
	})
	return
}

type BlackWhiteOperatorLog struct {
	blackWhiteItemRepo        repo.BlackWhiteItemRepo
	blackWhiteListRepo        repo.BlackWhiteListRepo
	blackWhiteAuditRepo       repo.BlackWhiteAuditRepo
	blackWhiteOperatorLogRepo repo.BlackWhiteOperatorLogRepo
	operator                  string
	bwl                       *modelv2.RmsBlackWhiteList
	bwa                       *modelv2.RmsBlackWhiteAudit
	err                       error
}

func (b *BlackWhiteOperatorLogService) NewBlackWhiteOperatorLog(operator string) (res *BlackWhiteOperatorLog) {
	return &BlackWhiteOperatorLog{
		operator:                  operator,
		blackWhiteItemRepo:        b.BlackWhiteItemRepo,
		blackWhiteListRepo:        b.BlackWhiteListRepo,
		blackWhiteAuditRepo:       b.BlackWhiteAuditRepo,
		blackWhiteOperatorLogRepo: b.BlackWhiteOperatorLogRepo,
	}
}

func (b *BlackWhiteOperatorLog) Error() error {
	return b.err
}

type BwLogAction interface {
	Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool)
	Handle(c context.Context, logger *BlackWhiteOperatorLog)
}
type BwLogCreate struct {
	bwlID int64
}

func NewBwLogCreate(bwlID int64) BwLogAction {
	return &BwLogCreate{bwlID: bwlID}
}
func (b *BwLogCreate) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", logger.bwl.ID).Order("id desc")
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa.Mode == modelv2.BlackWhiteAuditModeCreate {
		return true
	}
	return false
}
func (b *BwLogCreate) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	// 统计新创建的记录，当前该黑白名单下的所有记录均为新增
	wlog.Info("BwLogCreate", zap.Any("bwlID", b.bwlID))
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	var (
		total int64
		err   error
	)
	total, err = datav2.NewRmsBlackWhiteItem().Count(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("bwl_id = ?", logger.bwl.ID)
	})
	if err != nil {
		logger.err = err
		return
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("mode = ?", modelv2.BlackWhiteAuditModeCreate).Where("bwl_id = ?", logger.bwl.ID).Order("id desc")
		})
		if logger.err != nil {
			return
		}
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionCreate,
		Log: fmt.Sprintf(`Operation Type: Create List [%s]
Added: %d records
		`, logger.bwl.Name, total),
	}
	wlog.Info("BwLogCreateData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)

}

type BwLogEdit struct {
	bwlID int64
}

func NewBwLogEdit(bwlID int64) BwLogAction {
	return &BwLogEdit{bwlID: bwlID}
}
func (b *BwLogEdit) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(c, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", logger.bwl.ID).Order("id desc")
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa.Mode == modelv2.BlackWhiteAuditModeEdit {
		return true
	}
	return false
}
func (b *BwLogEdit) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	// 统计新创建的记录，当前该黑白名单下的所有记录均为新增
	wlog.Info("BwLogEdit", zap.Any("bwlID", b.bwlID))
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	var (
		updateTotal int64
		addTotal    int64
		delTotal    int64
		err         error
	)
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("mode = ?", modelv2.BlackWhiteAuditModeEdit).Where("bwl_id = ?", logger.bwl.ID).Order("id desc")
		})
		if logger.err != nil {
			return
		}
	}
	updateTotal, err = logger.blackWhiteItemRepo.Count(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("bwl_id = ?", logger.bwl.ID).Where("audit_id = ?", logger.bwa.ID).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("parent_id > ?", 0)
	})
	if err != nil {
		logger.err = err
		return
	}
	addTotal, err = logger.blackWhiteItemRepo.Count(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("bwl_id = ?", logger.bwl.ID).Where("audit_id = ?", logger.bwa.ID).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("parent_id = ?", 0)
	})
	if err != nil {
		logger.err = err
		return
	}
	delTotal, err = logger.blackWhiteItemRepo.Count(ctx, func(tx *gorm.DB) *gorm.DB {
		return tx.Where("bwl_id = ?", logger.bwl.ID).Where("audit_id = ?", logger.bwa.ID).Where("status = ?", modelv2.BlackWhiteItemStatusRemovePendingApproval)
	})
	if err != nil {
		logger.err = err
		return
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionEdit,
		Log:      fmt.Sprintf(`Operation Type: edit [%s]`, logger.bwl.Name),
	}
	if addTotal > 0 {
		data.Log += fmt.Sprintf(`- Added: %d records`, addTotal)
	}
	if updateTotal > 0 {
		data.Log += fmt.Sprintf(`- Modified: %d records`, updateTotal)
	}
	if delTotal > 0 {
		data.Log += fmt.Sprintf(`- Deleted: %d records`, delTotal)
	}
	wlog.Info("BwLogEditData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)
}

type BwLogDelList struct {
	bwlID int64
}

func NewBwLogDelList(bwlID int64) BwLogAction {
	return &BwLogDelList{bwlID: bwlID}
}
func (b *BwLogDelList) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {
	return true
}
func (b *BwLogDelList) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	if logger.err != nil {
		return
	}
	wlog.Info("BwLogDel", zap.Any("bwlID", b.bwlID))
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("mode = ?", modelv2.BlackWhiteAuditModeDelete).Where("bwl_id = ?", logger.bwl.ID).Order("id desc")
		})
		if logger.err != nil {
			return
		}
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionDeleteList,
		Log:      fmt.Sprintf(`Operation Type: delect list  [%s]`, logger.bwl.Name),
	}
	wlog.Info("BwLogDelData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)
}

type BwLogTurnOn struct {
	bwlID   int64
	auditID int64
}

func NewBwLogTurnOn(bwlID, auditID int64) BwLogAction {
	return &BwLogTurnOn{bwlID: bwlID, auditID: auditID}
}
func (b *BwLogTurnOn) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {
	return true
}
func (b *BwLogTurnOn) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	if logger.err != nil {
		return
	}
	wlog.Info("BwLogTurnOn", zap.Any("bwlID", b.bwlID))
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.auditID)
		})
		if logger.err != nil {
			return
		}
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionTurnOn,
		Log:      `Operation Type: turn on`,
	}
	wlog.Info("BwLogTurnOnData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)
}

type BwLogTurnOff struct {
	bwlID   int64
	auditID int64
}

func NewBwLogTurnOff(bwlID, auditID int64) BwLogAction {
	return &BwLogTurnOff{bwlID: bwlID, auditID: auditID}
}
func (b *BwLogTurnOff) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {
	return true
}
func (b *BwLogTurnOff) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	if logger.err != nil {
		return
	}
	wlog.Info("BwLogTurnOff", zap.Any("bwlID", b.bwlID))
	if logger.err != nil {
		return
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.bwlID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.auditID)
		})
		if logger.err != nil {
			return
		}
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionTurnOff,
		Log:      `Operation Type: turn off `,
	}
	wlog.Info("BwLogTurnOffData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)
}

type BwLogApprove struct {
	auditID int64
}

func NewBwLogApprove(auditID int64) BwLogAction {
	return &BwLogApprove{auditID: auditID}
}
func (b *BwLogApprove) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {
	return true
}
func (b *BwLogApprove) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	if logger.err != nil {
		return
	}
	wlog.Info("BwLogApprove", zap.Any("auditID", b.auditID))
	if logger.err != nil {
		return
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.auditID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", logger.bwa.BwlID)
		})
		if logger.err != nil {
			return
		}
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionApproval,
		Log: fmt.Sprintf(`Operation Type: APPROVED  
Approver:%s`, logger.bwa.Approver),
	}
	wlog.Info("BwLogApprovalData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)
}

type BwLogReject struct {
	auditID int64
}

func NewBwLogReject(auditID int64) BwLogAction {
	return &BwLogReject{auditID: auditID}
}
func (b *BwLogReject) Check(c context.Context, logger *BlackWhiteOperatorLog) (res bool) {

	return true
}
func (b *BwLogReject) Handle(ctx context.Context, logger *BlackWhiteOperatorLog) {
	if logger.err != nil {
		return
	}
	wlog.Info("BwLogReject", zap.Any("auditID", b.auditID))
	if logger.err != nil {
		return
	}
	if logger.bwa == nil {
		logger.bwa, logger.err = logger.blackWhiteAuditRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", b.auditID)
		})
		if logger.err != nil {
			return
		}
	}
	if logger.bwl == nil {
		logger.bwl, logger.err = logger.blackWhiteListRepo.First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", logger.bwa.BwlID)
		})
		if logger.err != nil {
			return
		}
	}
	data := &modelv2.RmsBlackWhiteOperatorLog{
		BwlID:    logger.bwl.ID,
		AuditID:  logger.bwa.ID,
		Operator: logger.operator,
		Action:   entity.BwOperatorLogActionReject,
		Log: fmt.Sprintf(`Operation Type: Rejected
Approver:%s
Reason:%s`, logger.bwa.Approver, logger.bwa.Reason),
	}
	wlog.Info("BwLogRejectData", zap.Any("bwa_data", data))
	logger.blackWhiteOperatorLogRepo.Create(ctx, nil, data)
}
