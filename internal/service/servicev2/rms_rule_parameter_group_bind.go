package servicev2

import (
	"context"
	"errors"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
)

type RuleParameterGroupBindService struct {
	RuleParameterGroupBindRepo repo.RmsRuleParameterGroupBindRepo
	RuleParameterGroup         repo.RmsRuleParameterGroupRepo
}

func (s *RuleParameterGroupBindService) Create(ctx context.Context, req *request_parameters.RuleParameterGroupBindCreate) (*modelv2.RmsRuleParameterGroupBind, error) {
	// 前端的组件不支持 bool 值
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	data := &modelv2.RmsRuleParameterGroupBind{
		GroupID:    req.GroupID,
		TargetID:   req.TargetID,
		TargetType: req.TargetType,
		Able:       req.Able,
	}
	pg, err := s.RuleParameterGroup.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =?", req.GroupID)
	})
	if err != nil {
		return nil, err
	}
	data.CheckPoint = &pg.CheckPoint
	err = s.RuleParameterGroupBindRepo.Create(ctx, nil, data)
	return data, err
}

func (s *RuleParameterGroupBindService) CreateList(ctx context.Context, req *request_parameters.RuleParameterGroupBindCreateList) ([]*modelv2.RmsRuleParameterGroupBind, error) {
	var resp []*modelv2.RmsRuleParameterGroupBind
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	for _, gid := range req.GroupIDList {
		data := &modelv2.RmsRuleParameterGroupBind{
			GroupID:    gid,
			TargetID:   req.TargetID,
			TargetType: req.TargetType,
			Able:       req.Able,
		}

		pg, err := s.RuleParameterGroup.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id =?", data.GroupID)
		})
		if err != nil {
			return nil, err
		}
		data.CheckPoint = &pg.CheckPoint
		resp = append(resp, data)
	}

	err := s.RuleParameterGroupBindRepo.Create(ctx, nil, resp...)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *RuleParameterGroupBindService) Update(ctx context.Context, id int64, req *request_parameters.RuleParameterGroupBindUpdate) (*modelv2.RmsRuleParameterGroupBind, error) {
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	data := &modelv2.RmsRuleParameterGroupBind{
		ID:         id,
		GroupID:    req.GroupID,
		TargetID:   req.TargetID,
		TargetType: req.TargetType,
		Able:       req.Able,
	}

	pg, err := s.RuleParameterGroup.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =?", data.GroupID)
	})
	if err != nil {
		return nil, err
	}
	data.CheckPoint = &pg.CheckPoint
	return data, s.RuleParameterGroupBindRepo.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id =?", id)
	}, data)
}

func (s *RuleParameterGroupBindService) Delete(ctx context.Context, id int64) error {
	return s.RuleParameterGroupBindRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
}

func (s *RuleParameterGroupBindService) DeleteList(ctx context.Context, idLi []int64) error {
	return s.RuleParameterGroupBindRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("id in (?)", idLi)
	})
}

func (s *RuleParameterGroupBindService) List(ctx context.Context, req *request_parameters.RuleParameterGroupBindList) (total int64, li []*modelv2.RmsRuleParameterGroupBind, err error) {
	if req.ParamGroupCode != "" && req.ParamGroupId == 0 {
		var pg *modelv2.RmsRuleParameterGroup
		pg, err = s.RuleParameterGroup.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("code = ?", req.ParamGroupCode)
		})
		if err != nil {
			if errors.Is(err, er.NotFound) {
				return
			}
			return
		}
		req.ParamGroupId = pg.ID
	}
	total, li, err = s.RuleParameterGroupBindRepo.List(ctx, filter.NewDefaultFilter(nil, req.PageRequest.Option), func(db *gorm.DB) *gorm.DB {
		if req.ParamGroupId != 0 {
			db = db.Where("group_id=?", req.ParamGroupId)
		}
		if req.TargetType != "" {
			db = db.Where("target_type like ?", "%"+req.TargetType+"%")
		}
		if req.TargetID != "" {
			db = db.Where("target_id like ?", "%"+req.TargetID+"%")
		}
		return db
	})

	return

}

func (s *RuleParameterGroupBindService) Retrieve(ctx context.Context, id int64) (*response_parameters.RuleParameterGroupBindResp, error) {
	data, err := s.RuleParameterGroupBindRepo.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return nil, err
	}
	resp := &response_parameters.RuleParameterGroupBindResp{
		RuleParameterGroupBindUpdate: response_parameters.RuleParameterGroupBindUpdate{
			ID: data.ID,
			RuleParameterGroupBindCreate: response_parameters.RuleParameterGroupBindCreate{
				TargetType: data.TargetType,
				GroupID:    data.GroupID,
				Able:       data.Able,
				TargetID:   data.TargetID,
			},
		},
	}
	rpg, err := s.RuleParameterGroup.Retrieve(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id=?", id)
	})
	if err != nil {
		return nil, err
	}
	resp.ParamGroupCode = rpg.Code
	// 前端的组件不支持 bool 值
	if resp.Able == 1 {
		resp.State = "Yes"
	} else {
		resp.State = "No"
	}
	return resp, nil
}
