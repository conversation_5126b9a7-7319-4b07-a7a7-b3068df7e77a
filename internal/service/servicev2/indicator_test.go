package servicev2

import (
	"fmt"
	"testing"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

func TestGenerateIndicatorSql(t *testing.T) {
	indicator := &entity.Indicator{
		IndicatorName: "测试指标",
		TableID:       entity.DataSourceTableID("13|issuing_transaction"),
		Measures: []entity.Measure{
			{
				MeasureType:   "avg",
				MeasureField:  entity.DataSourceColumnID("13|issuing_transaction|billing_amount"),
				ConditionName: "billing_amount",
			},
		},
		Rules: []entity.Rule{
			{
				HasLeftBrackets:  true,
				HasRightBrackets: false,
				Connector:        "",
				ColumnID:         entity.DataSourceColumnID("13|issuing_transaction|card_id"),
				Operator:         "gt",
				Value:            "6d229af3-94d4-46a8-abb1-bda8f2ceb3a4",
				ValueType:        "fixed",
			},
			{
				HasLeftBrackets:  false,
				HasRightBrackets: true,
				Connector:        "and",
				ColumnID:         entity.DataSourceColumnID("13|issuing_transaction|card_id"),
				Operator:         "eq",
				Value:            "6d229af3-94d4-46a8-abb1-bda8f2ceb3a411",
				ValueType:        "fixed",
			},
		},
		TimeWindow: entity.TimeWindow{
			TimeWindowType:      modelv2.IndicatorVersionTimeWindowSliding,
			TimeWindowValue:     1,
			TimeWindowUnit:      modelv2.IndicatorTimeWindowUnitWeeks,
			TimeWindowColumnID:  entity.DataSourceColumnID("13|issuing_transaction|create_time"),
			TimeWindowExcluding: false,
		},
	}

	service := &IndicatorService{}
	sql, placeholders, err := service.Indicator2MysqlSql(indicator)
	if err != nil {
		t.Fatalf("生成SQL失败: %v", err)
	}
	fmt.Println("生成的SQL语句:\n", sql, placeholders)
	sql, placeholders, err = service.Indicator2DorisSql(indicator)
	if err != nil {
		t.Fatalf("生成SQL失败: %v", err)
	}
	fmt.Println("生成的SQL语句:\n", sql, placeholders)
}
