package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type OperatorLogService struct {
	Logger          *zap.Logger
	OperatorLogRepo IOperatorLogRepo
}

func (s *OperatorLogService) Create(c *gin.Context, cp *model.RmsOperatorLog) error {
	return s.OperatorLogRepo.Create(c, cp)
}

func (s *OperatorLogService) Update(c *gin.Context, id int64, cp *model.RmsOperatorLog) error {
	return s.OperatorLogRepo.Update(c, id, cp)
}

func (s *OperatorLogService) Delete(c *gin.Context, id int64) error {
	return s.OperatorLogRepo.Delete(c, id)
}

func (s *OperatorLogService) List(c *gin.Context, req *domain.OperatorLogList) (*domain.PageData, error) {
	li, err := s.OperatorLogRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.OperatorLogRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *OperatorLogService) Retrieve(c *gin.Context, id int64) (*model.RmsOperatorLog, error) {
	return s.OperatorLogRepo.Retrieve(c, id)
}
