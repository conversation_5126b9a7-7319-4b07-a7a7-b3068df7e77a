package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type EventFieldService struct {
	BaseServer
	Logger                *zap.Logger
	EventFieldRepo        IEventFieldRepo
	CheckPoint            ICheckPointRepo
	FilterField           IFilterFieldRepo
	PolicyRepo            IPolicyRepo
	EventFieldBindingRepo repo.RmsEventFieldBinding
	EventStoreCfgRepo     IEventStoreCfgRepo
}

func (s *EventFieldService) Check(c *gin.Context, apCode string, ef *model.RmsEventField) error {
	data, err := s.EventFieldRepo.RetrieveByName(c, apCode, ef.FieldName)
	if err != nil {
		return nil
	}
	if data != nil && data.ID != ef.ID {
		return er.InvalidArgument.WithStack().WithMsg("Field name cannot be repeated")
	}
	return nil
}

// TODO 移动到 RmsCheckPoint model 中
// SetPrimary 设置默认主键
func (s *EventFieldService) SetPrimary(c *gin.Context, id int64, apCode, FieldName string) error {
	// 每个接入点只能有一个默认主键
	// 默认主键以字段名存在接入点中，所以在创建或更新时要用字段名对比，并兼容更新自己的情况。在创建时 id 传 0，更新时需要传实际的 id。
	ap, err := s.CheckPoint.RetrieveByCode(c, apCode)
	if err != nil {
		return err
	}
	// 检查默认主键，如果存在
	if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName != "" {
		// 且与当前字段不同
		if *ap.DefaultPkFieldName != FieldName {
			// 查询接入点已有配置是否存在相应字段，防止出问题后在ui中无法更改
			ef, err := s.EventFieldRepo.RetrieveByName(c, apCode, *ap.DefaultPkFieldName)
			if err != nil {
				// 字段时可以设置
				if !errors.Is(err, er.NotFound) {
					return err
				}
				return s.CheckPoint.UpdateDefaultPkFieldName(c, ap.ID, FieldName)
			}
			// 字段是自己时可以设置
			if ef.ID == id {
				return s.CheckPoint.UpdateDefaultPkFieldName(c, ap.ID, FieldName)
			}
			// 否则返回已存在主键
			return er.InvalidArgument.WithStack().WithMsg("A default primary key already exists.")
		}
	}
	// 如果不存在，设置主键
	return s.CheckPoint.UpdateDefaultPkFieldName(c, ap.ID, FieldName)
}

// UnSetPrimary 取消设置默认主键
func (s *EventFieldService) UnSetPrimary(c *gin.Context, apCode, FieldName string) error {
	// 每个接入点只能有一个默认主键
	ap, err := s.CheckPoint.RetrieveByCode(c, apCode)
	if err != nil {
		return err
	}
	// 检查默认主键，如果存在
	if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName != "" {
		// 且与当前字段不同
		if *ap.DefaultPkFieldName != FieldName {
			// 查询接入点已有配置是否存在相应字段，防止出问题后在ui中无法更改
			_, err := s.EventFieldRepo.RetrieveByName(c, apCode, *ap.DefaultPkFieldName)
			if err != nil {
				//	字段不存在时可以设置
				if !errors.Is(err, er.NotFound) {
					return err
				}
			} else {
				// 否则抛出错误
				return er.DefaultPkFieldAlreadyExists.WithStack()
			}
		}
	}
	err = s.CheckPoint.UpdateDefaultPkFieldName(c, ap.ID, "")
	if err != nil {
		return err
	}
	return nil
}

// IsFilterField 检查是否是过滤字段
func (s *EventFieldService) IsFilterField(c *gin.Context, ef *domain.EventFieldResp) (bool, error) {
	// 告知前端是否是过滤字段
	_, err := s.FilterField.GetByFieldNameAndAPCode(c, ef.CheckPoint, ef.FieldName)
	if err != nil {
		if errors.Is(err, er.NotFound) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func (s *EventFieldService) Create(c *gin.Context, req *domain.EventFieldCreate) error {
	cp := new(model.RmsEventField)
	if err := s.Copy(req, cp); err != nil {
		return err
	}
	if err := s.Check(c, req.CheckPoint, cp); err != nil {
		return err
	}
	if req.Primary {
		err := s.SetPrimary(c, 0, req.CheckPoint, req.FieldName)
		if err != nil {
			return err
		}
	}
	if req.Filter {
		// 获取接入点，管理接入点中的过滤字段
		cp, err := s.CheckPoint.RetrieveByCode(c, req.CheckPoint)
		if err != nil {
			return err
		}
		err = cp.AddFilterField(req.FieldName)
		if err != nil {
			return err
		}
	}
	return s.EventFieldRepo.Create(c, cp)
}

func (s *EventFieldService) Update(c *gin.Context, id int64, req *domain.EventFieldUpdate) error {
	// 处理接入点中的默认主键。
	// 是否是默认主键
	if req.Primary {
		err := s.SetPrimary(c, id, req.CheckPoint, req.FieldName)
		if err != nil {
			return err
		}
	} else {
		// 取消默认主键
		err := s.UnSetPrimary(c, req.CheckPoint, req.FieldName)
		if err != nil {
			if !errors.Is(err, er.DefaultPkFieldAlreadyExists) {
				return err
			}
		}
	}
	oriEventField, err := s.EventFieldRepo.Retrieve(c, id)
	if err != nil {
		return err
	}
	var hasBind bool
	hasBind, err = s.EventFieldBindingRepo.Bound(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("field_name = ?", oriEventField.FieldName).Where("check_point=?", req.CheckPoint)
	})
	if err != nil {
		return err
	}
	if hasBind && oriEventField.FieldName != req.FieldName {
		return er.InvalidArgument.WithStack().WithMsg("The field name cannot be modified because it is bound to the rule group.")
	}
	lowMod, err := s.EventFieldRepo.Retrieve(c, id)
	if err != nil {
		return err
	}
	// 获取接入点，管理接入点中的过滤字段
	cp, err := s.CheckPoint.RetrieveByCode(c, req.CheckPoint)
	if err != nil {
		return err
	}
	mode := new(model.RmsEventField)
	if err := s.Copy(req, mode); err != nil {
		return err
	}
	if err := s.Check(c, req.CheckPoint, mode); err != nil {
		return err
	}
	if err := s.EventFieldRepo.Update(c, id, mode); err != nil {
		return err
	}
	// 如果名字更改，更改规则组中的关联数据
	if lowMod.FieldName != mode.FieldName {
		// 处理规则组的绑定
		PolicyLi, err := s.PolicyRepo.FindByCPCode(c, lowMod.CheckPoint)
		if err != nil {
			return err
		}
		// 在规则组存在时更改规则组中的信息
		for _, Policy := range PolicyLi {
			filters, err := Policy.GetFilters()
			if err != nil {
				return err
			}
			if len(filters[lowMod.FieldName]) > 0 {
				filters[mode.FieldName] = filters[lowMod.FieldName]
				delete(filters, lowMod.FieldName)

				err = Policy.SetFilters(filters)
				if err != nil {
					return err
				}
				err = s.PolicyRepo.Update(c, Policy.ID, Policy)
				if err != nil {
					return err
				}
			}
		}

	}

	if req.Filter {
		// 更新过滤字段信息
		if oriEventField.FieldName != req.FieldName {
			// 更新字段名
			err = cp.RemoveFilterField(oriEventField.FieldName)
			if err != nil {
				return err
			}
			err = cp.AddFilterField(req.FieldName)
			if err != nil {
				return err
			}
			// 重命名过滤字段
			err := s.FilterField.UpdateFieldName(c, oriEventField.FieldName, req.FieldName)
			if err != nil {
				return err
			}
		}
	} else {
		// 去掉过滤字段时删除关联值
		err := s.FilterField.DeleteByFieldNameAndAPCode(c, req.CheckPoint, req.FieldName)
		if err != nil {
			return err
		}
		err = cp.RemoveFilterField(oriEventField.FieldName)
		if err != nil {
			return err
		}
	}
	// 更新过滤字段
	if cp.FilterFields != nil {
		if err := s.CheckPoint.UpdateFilterFields(c, cp.Code, *cp.FilterFields); err != nil {
			return err
		}
	}
	return s.Copy(mode, req)
}

func (s *EventFieldService) Delete(c *gin.Context, id int64) error {
	ef, err := s.EventFieldRepo.Retrieve(c, id)
	if err != nil {
		return err
	}
	oriEventField, err := s.EventFieldRepo.Retrieve(c, id)
	var hasBind bool
	hasBind, err = s.EventFieldBindingRepo.Bound(c, func(db *gorm.DB) *gorm.DB {
		return db.Where("field_name = ?", oriEventField.FieldName).Where("check_point=?", oriEventField.CheckPoint)
	})
	if err != nil {
		return err
	}
	if hasBind {
		return er.InvalidArgument.WithStack().WithMsg("The current field has been bound by other business, cannot be deleted.")
	}
	if err != nil {
		return err
	}
	// 获取接入点，管理接入点中的过滤字段
	cp, err := s.CheckPoint.RetrieveByCode(c, oriEventField.CheckPoint)
	if err != nil {
		return err
	}
	// 删除接入点中的过滤字段
	err = cp.RemoveFilterField(oriEventField.FieldName)
	if err != nil {
		return err
	}
	// 更新过滤字段
	if cp.FilterFields != nil {
		if err := s.CheckPoint.UpdateFilterFields(c, cp.Code, *cp.FilterFields); err != nil {
			return err
		}
	}
	// 删除过滤字段
	err = s.FilterField.DeleteByFieldNameAndAPCode(c, cp.Code, ef.FieldName)
	if err != nil {
		return err
	}
	err = s.UnSetPrimary(c, ef.CheckPoint, ef.FieldName)
	if err != nil {
		if !errors.Is(err, er.DefaultPkFieldAlreadyExists) {
			return err
		}
	}
	return s.EventFieldRepo.Delete(c, id)
}

func (s *EventFieldService) List(c *gin.Context, req *domain.EventFieldList) (*domain.PageData, error) {
	li, err := s.EventFieldRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.EventFieldRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	var respLi []*domain.EventFieldResp
	err = s.Copy(li, &respLi)
	if err != nil {
		return nil, err
	}
	for _, i := range respLi {
		ap, err := s.CheckPoint.RetrieveByCode(c, i.CheckPoint)
		if err != nil {
			if !errors.Is(err, er.NotFound) {
				return nil, err
			}
		} else {
			if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName == i.FieldName {
				i.Primary = true
			}
		}
		// 告知前端是否是过滤字段
		if i.Filter, err = s.IsFilterField(c, i); err != nil {
			return nil, err
		}
	}
	return domain.FillInPageResponseData(req.PageSearch, count, respLi), nil
}

func (s *EventFieldService) Retrieve(c *gin.Context, id int64) (*domain.EventFieldResp, error) {
	data, err := s.EventFieldRepo.Retrieve(c, id)
	if err != nil {
		return nil, err
	}
	resp := new(domain.EventFieldResp)
	err = s.Copy(data, resp)
	if err != nil {
		return nil, err
	}
	ap, err := s.CheckPoint.RetrieveByCode(c, data.CheckPoint)
	if err != nil {
		if !errors.Is(err, er.NotFound) {
			return nil, err
		}
	} else {
		if ap.DefaultPkFieldName != nil && *ap.DefaultPkFieldName == data.FieldName {
			resp.Primary = true
		}
	}

	return resp, nil
}

// CheckNext 检查是否可以点击下一步
func (s *EventFieldService) CheckNext(c *gin.Context, resp *domain.EventFieldCheckNext) error {
	ap, err := s.CheckPoint.RetrieveByCode(c, resp.CheckPoint)
	if err != nil {
		return err
	}
	// 接入点勾选数据重复校验时必须设置默认主键
	if ap.CheckDuplicate != nil && *ap.CheckDuplicate == true {
		if ap.DefaultPkFieldName == nil || *ap.DefaultPkFieldName == "" {
			return er.InvalidArgument.WithStack().WithMsg("The access point must set the primary key to check data duplication check.")
		}
	}
	return nil
}

func (s *EventFieldService) NotEventStoreCfgList(c *gin.Context, req *domain.EventFieldList) (*domain.NotEventStoreCfgListResp, error) {
	list, err := s.EventFieldRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	EventStoreCfg, err := s.EventStoreCfgRepo.RetrieveByAPCode(c, req.CheckPoint)
	if err != nil {
		return nil, err
	}
	reslist := make([]string, 0)
	for _, eventField := range list {

		if !(eventField.FieldName == *EventStoreCfg.StrField1 || eventField.FieldName == *EventStoreCfg.StrField2 || eventField.FieldName == *EventStoreCfg.StrField3 || eventField.FieldName == *EventStoreCfg.StrField4 || eventField.FieldName == *EventStoreCfg.StrField5 || eventField.FieldName == *EventStoreCfg.NumField1 || eventField.FieldName == *EventStoreCfg.NumField2) {
			reslist = append(reslist, eventField.FieldName)
		}
	}
	return &domain.NotEventStoreCfgListResp{List: reslist}, nil
}
