package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type EventService struct {
	Logger    *zap.Logger
	EventRepo IEventRepo
}

func (s *EventService) Create(c *gin.Context, cp *model.RmsEvent) error {
	return s.EventRepo.Create(c, cp)
}

func (s *EventService) Update(c *gin.Context, id int64, cp *model.RmsEvent) error {
	return s.EventRepo.Update(c, id, cp)
}

func (s *EventService) Delete(c *gin.Context, id int64) error {
	return s.EventRepo.Delete(c, id)
}

func (s *EventService) List(c *gin.Context, req *domain.EventList) (*domain.PageData, error) {
	li, err := s.EventRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.EventRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *EventService) Retrieve(c *gin.Context, id int64) (*model.RmsEvent, error) {
	return s.EventRepo.Retrieve(c, id)
}
