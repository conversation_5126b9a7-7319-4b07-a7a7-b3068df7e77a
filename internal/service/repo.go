package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type IAlarmContactRepo interface {
	Create(c *gin.Context, cp *model.RmsAlarmContact) error
	Update(c *gin.Context, id int64, cp *model.RmsAlarmContact) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.AlarmContactList) ([]*model.RmsAlarmContact, error)
	Count(c *gin.Context, req *domain.AlarmContactList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsAlarmContact, error)
}

type IConfigProgressRepo interface {
	Retrieve(c *gin.Context, code string) (*model.PlatConfigProgress, error)
	Delete(c *gin.Context, id int64) error
	CreateOrUpdate(c *gin.Context, cp *model.PlatConfigProgress) error
}

type IAlarmTimeConfRepo interface {
	Create(c *gin.Context, cp *model.RmsAlarmTimeConf) error
	Update(c *gin.Context, id int64, cp *model.RmsAlarmTimeConf) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.AlarmTimeConfList) ([]*model.RmsAlarmTimeConf, error)
	Count(c *gin.Context, req *domain.AlarmTimeConfList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsAlarmTimeConf, error)
}

type IBlacklistRepo interface {
	Create(c *gin.Context, cp *model.RmsBlacklist) error
	Update(c *gin.Context, id int64, cp *model.RmsBlacklist) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.BlacklistList) ([]*model.RmsBlacklist, error)
	Count(c *gin.Context, req *domain.BlacklistList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsBlacklist, error)
}

type IBusinessTypeRepo interface {
	Create(c *gin.Context, cp *model.RmsBusinessType) error
	Update(c *gin.Context, id int64, cp *model.RmsBusinessType) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.BusinessTypeList) ([]*model.RmsBusinessType, error)
	Count(c *gin.Context, req *domain.BusinessTypeList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsBusinessType, error)
	All(c *gin.Context) ([]*model.RmsBusinessType, error)
	RetrieveByCode(c *gin.Context, code string) (*model.RmsBusinessType, error)
}

type ICheckPointRepo interface {
	Create(c *gin.Context, cp *model.RmsCheckPoint) error
	Update(c *gin.Context, id int64, cp *model.RmsCheckPoint) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.CheckPointList) ([]*model.RmsCheckPoint, error)
	Count(c *gin.Context, req *domain.CheckPointList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsCheckPoint, error)
	AllNameCodeList(c *gin.Context) ([]*model.RmsCheckPoint, error)
	UpdateBusinessConfig(c *gin.Context, code string, businessConfig string) error
	ExistBusinessCode(businessCode string) (bool, error)
	RetrieveByCode(c *gin.Context, code string) (*model.RmsCheckPoint, error)
	UpdateFilterFields(c *gin.Context, code string, FilterFields string) error
	UpdateDefaultPkFieldName(c *gin.Context, id int64, name string) error
}

type IEventRepo interface {
	Create(c *gin.Context, cp *model.RmsEvent) error
	Update(c *gin.Context, id int64, cp *model.RmsEvent) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.EventList) ([]*model.RmsEvent, error)
	Count(c *gin.Context, req *domain.EventList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsEvent, error)
}

type IEventFieldRepo interface {
	Create(c *gin.Context, cp *model.RmsEventField) error
	Update(c *gin.Context, id int64, cp *model.RmsEventField) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.EventFieldList) ([]*model.RmsEventField, error)
	Count(c *gin.Context, req *domain.EventFieldList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsEventField, error)
	RetrieveByName(c *gin.Context, apCode, field_name string) (*model.RmsEventField, error)
	AllByAPCode(c *gin.Context, ap_code string) ([]*model.RmsEventField, error)
}

type IEventStoreCfgRepo interface {
	Create(c *gin.Context, cp *model.RmsEventStoreCfg) error
	Update(c *gin.Context, id int64, cp *model.RmsEventStoreCfg) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.EventStoreCfgList) ([]*model.RmsEventStoreCfg, error)
	Count(c *gin.Context, req *domain.EventStoreCfgList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsEventStoreCfg, error)
	RetrieveByAPCode(c *gin.Context, apCode string) (*model.RmsEventStoreCfg, error)
}

type IFilterFieldRepo interface {
	Create(c *gin.Context, cp *model.RmsFilterField) error
	Update(c *gin.Context, id int64, cp *model.RmsFilterField) error
	DeleteById(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.FilterFieldList) ([]*model.RmsFilterField, error)
	Count(c *gin.Context, req *domain.FilterFieldList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsFilterField, error)
	RetrieveByName(c *gin.Context, apCode, field_name string) (*model.RmsFilterField, error)
	GetByFieldNameAndAPCode(c *gin.Context, apCode, field_name string) (*model.RmsFilterField, error)
	AllByAPCode(c *gin.Context, apCode string) ([]*model.RmsFilterField, error)
	UpdateFieldName(c *gin.Context, oriName, newName string) error
	DeleteByFieldNameAndAPCode(c *gin.Context, apCode, field_name string) error
}

type IOperatorLogRepo interface {
	Create(c *gin.Context, cp *model.RmsOperatorLog) error
	Update(c *gin.Context, id int64, cp *model.RmsOperatorLog) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.OperatorLogList) ([]*model.RmsOperatorLog, error)
	Count(c *gin.Context, req *domain.OperatorLogList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsOperatorLog, error)
}

type IPolicyRepo interface {
	Create(c *gin.Context, cp *model.RmsPolicy) error
	Update(c *gin.Context, id int64, cp *model.RmsPolicy) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.PolicyList) ([]*model.RmsPolicy, error)
	Count(c *gin.Context, req *domain.PolicyList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsPolicy, error)
	RetrieveByCode(c *gin.Context, code string) (*model.RmsPolicy, error)
	SetStatus(c *gin.Context, id int64, status string) error
	FindByCPCode(c *gin.Context, CPCode string) ([]*model.RmsPolicy, error)
}

type IRiskLogRepo interface {
	Create(c *gin.Context, cp *model.RmsRiskLog) error
	Update(c *gin.Context, id int64, cp *model.RmsRiskLog) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.RiskLogList) ([]*model.RmsRiskLog, error)
	Count(c *gin.Context, req *domain.RiskLogList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsRiskLog, error)
}

type IRuleRepo interface {
	Create(c *gin.Context, cp *model.RmsRule) error
	Update(c *gin.Context, id int64, cp *model.RmsRule) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.RuleList) ([]*model.RmsRule, error)
	AllByCPCode(c *gin.Context, cpCode string) ([]*model.RmsRule, error)
	Count(c *gin.Context, req *domain.RuleList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsRule, error)
	SetStatus(c *gin.Context, id int64, cp *model.RmsRule) error
}

type IRuleParameterRepo interface {
	Create(c *gin.Context, cp *model.RmsRuleParameter) error
	Update(c *gin.Context, id int64, cp *model.RmsRuleParameter) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.RuleParameterList) ([]*model.RmsRuleParameter, error)
	Count(c *gin.Context, req *domain.RuleParameterList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameter, error)
	ListByAPCode(c *gin.Context, req *domain.PolicyConfigPageList, apCode string) ([]*model.RmsRuleParameter, error)
	CountByAPCode(c *gin.Context, apCode string) (int64, error)
}

type IRuleParameterGroupRepo interface {
	Create(c *gin.Context, cp *model.RmsRuleParameterGroup) error
	Update(c *gin.Context, id int64, cp *model.RmsRuleParameterGroup) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.RuleParameterGroupList) ([]*model.RmsRuleParameterGroup, error)
	Count(c *gin.Context, req *domain.RuleParameterGroupList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterGroup, error)
	RetrieveByCode(c *gin.Context, code string) (*model.RmsRuleParameterGroup, error)
	FirstByAPCode(c *gin.Context, id int64) (*model.RmsRuleParameterGroup, error)
	All(c *gin.Context) ([]*model.RmsRuleParameterGroup, error)
	SetParamIds(c *gin.Context, RuleParamID int64, ParamIds string) error
	RetrieveByCPCode(c *gin.Context, cp_code string) (*model.RmsRuleParameterGroup, error)
}

type IRuleParameterGroupBindRepo interface {
	Create(c *gin.Context, cp *model.RmsRuleParameterGroupBind) error
	Update(c *gin.Context, id int64, cp *model.RmsRuleParameterGroupBind) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.RuleParameterGroupBindList) ([]*model.RmsRuleParameterGroupBind, error)
	Count(c *gin.Context, req *domain.RuleParameterGroupBindList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterGroupBind, error)
	RetrieveByGroupId(c *gin.Context, GroupID int64) (*model.RmsRuleParameterGroupBind, error)
}

type IRuleParameterValueRepo interface {
	Create(c *gin.Context, cp *model.RmsRuleParameterValue) error
	Update(c *gin.Context, id int64, cp *model.RmsRuleParameterValue) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.RuleParameterValueList) ([]*model.RmsRuleParameterValue, error)
	Count(c *gin.Context, req *domain.RuleParameterValueList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterValue, error)
	AllByGroupID(c *gin.Context, GroupID int64) ([]*model.RmsRuleParameterValue, error)
	AllByParamIDAndGroupID(c *gin.Context, ParamID, GroupID int64) ([]*model.RmsRuleParameterValue, error)
	DeleteByParamIDAndGroupID(c *gin.Context, ParamID, GroupID int64) error
}

type IHandleLogRepo interface {
	Create(c *gin.Context, cp *model.WpHandleLog) error
	Update(c *gin.Context, id int64, cp *model.WpHandleLog) error
	Delete(c *gin.Context, id int64) error
	List(c *gin.Context, req *domain.HandleLogList) ([]*model.WpHandleLog, error)
	Count(c *gin.Context, req *domain.HandleLogList) (count int64, err error)
	Retrieve(c *gin.Context, id int64) (*model.WpHandleLog, error)
}
type ICraFramework interface {
	Update(c *gin.Context, cra_framework_id string, cp *domain.CraFrameworkUpdate) error
	Retrieve(c *gin.Context, cra_framework_id string) (*model.RmsCraFramework, error)
	RiskScoreCount(c *gin.Context, craType *domain.CraFrameworkCount) (string, error)
	List(c *gin.Context) ([]*model.RmsCraFramework, error)
	First(c *gin.Context, cp *model.RmsCraFramework) (*model.RmsCraFramework, error)
}

type ICraParameter interface {
	First(c *gin.Context, cp *model.RmsCraParameter) (*model.RmsCraParameter, error)
	FirstWhereLike(c *gin.Context, parameter map[string]string) (*model.RmsCraParameter, error)
	Create(c *gin.Context, cp *model.RmsCraParameter) error
	Update(c *gin.Context, rms_cra_parameter string, cp *model.RmsCraParameter) error
	Delete(c *gin.Context, rms_cra_parameter string) error
	List(c *gin.Context, data *model.RmsCraParameter) ([]*domain.CraParameter, error)
	Count(c *gin.Context) (count int64, err error)
	Retrieve(c *gin.Context, rms_cra_parameter string) (*model.RmsCraParameter, error)
	BatchUpdate(c *gin.Context, req *domain.CraParameterBatchUpdateReq) error
}

type IcraParameterVal interface {
	First(c *gin.Context, cp *model.RmsCraParameterVal) (*model.RmsCraParameterVal, error)
	Create(c *gin.Context, cp *model.RmsCraParameterVal) error
	Update(c *gin.Context, param_val_id string, cp *model.RmsCraParameterVal) error
	Delete(c *gin.Context, param_val_id string) error
	List(c *gin.Context, data *model.RmsCraParameterVal) ([]*model.RmsCraParameterVal, error)
	Count(c *gin.Context) (count int64, err error)
	Retrieve(c *gin.Context, cra_framework_id string) (*model.RmsCraParameterVal, error)
	GetParameterName(ids []string) ([]*model.RmsCraParameterVal, error)
}
type ICraScoreReference interface {
	Create(c *gin.Context, reference *model.RmsCraScoreReference) error
	Retrieve(c *gin.Context, reference *model.RmsCraScoreReference) (*model.RmsCraScoreReference, error)
	List(c *gin.Context, data *model.RmsCraScoreReference) ([]*model.RmsCraScoreReference, error)
}
type IRiskEventRecord interface {
	List(c *gin.Context, db *gorm.DB, req *domain.AlertQueryListReq) (*domain.AlertQueryListRes, error)
	Count(c *gin.Context, db *gorm.DB) (int64, error)
}

type IRiskFileExtra interface {
	Create(c *gin.Context, cp *model.RmsFileExtra) error
	ReportError(c *gin.Context, file_id string, cp *model.RmsFileExtra) error
	ReportSuccess(c *gin.Context, fileName string, format string, filePath string, region string, uuid string) error
	List(c *gin.Context, req *domain.FilterExtraListReq) ([]*model.RmsFileExtra, error)
	Count(c *gin.Context, req *domain.FilterExtraListReq) (count int64, err error)
	Retrieve(c *gin.Context, file_id string) (*domain.FilterExtraRetrieveRes, error)
}
