package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

type RuleParameterGroupService struct {
	Logger                     *zap.Logger
	RuleParameterGroupRepo     IRuleParameterGroupRepo
	RuleParameterGroupBindRepo IRuleParameterGroupBindRepo
}

func (s *RuleParameterGroupService) Check(c *gin.Context, cp *model.RmsRuleParameterGroup) error {
	if cp.CheckPoint == "" {
		return er.InvalidArgument.WithStack().WithMsg("Access point cannot be empty")
	}
	if cp.Code == "" {
		return er.InvalidArgument.WithStack().WithMsg("Parameter group code")
	}

	// 一个接入点下只能有一个默认参数组
	if cp.DefaultFlag == 1 {
		lowPG, err := s.RuleParameterGroupRepo.RetrieveByCPCode(c, cp.CheckPoint)
		if err != nil {
			if errors.Is(err, er.NotFound) {
				return nil
			}
			return err
		} else {
			if lowPG.Code != cp.Code {
				return er.Unimplemented.WithStack().WithMsg("An access point has a default parameter group. The current default parameter group code is: [" + lowPG.Code + "]")
			}
		}
	}
	return nil
}

func (s *RuleParameterGroupService) Create(c *gin.Context, cp *model.RmsRuleParameterGroup) error {
	if err := s.Check(c, cp); err != nil {
		return err
	}
	return s.RuleParameterGroupRepo.Create(c, cp)
}

func (s *RuleParameterGroupService) Update(c *gin.Context, id int64, cp *model.RmsRuleParameterGroup) error {
	if err := s.Check(c, cp); err != nil {
		return err
	}
	return s.RuleParameterGroupRepo.Update(c, id, cp)
}

func (s *RuleParameterGroupService) Delete(c *gin.Context, id int64) error {
	_, err := s.RuleParameterGroupBindRepo.RetrieveByGroupId(c, id)
	if err != nil {
		if errors.Is(err, er.NotFound) {
			return s.RuleParameterGroupRepo.Delete(c, id)
		}
		return err
	}
	return er.FailedPrecondition.WithStack().WithMsg("A bound parameter group cannot be deleted.")
}

func (s *RuleParameterGroupService) List(c *gin.Context, req *domain.RuleParameterGroupList) (*domain.PageData, error) {
	li, err := s.RuleParameterGroupRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.RuleParameterGroupRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *RuleParameterGroupService) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterGroup, error) {
	return s.RuleParameterGroupRepo.Retrieve(c, id)
}

func (s *RuleParameterGroupService) All(c *gin.Context) (*domain.RuleParameterGroupAllResp, error) {
	li, err := s.RuleParameterGroupRepo.All(c)
	if err != nil {
		return nil, err
	}
	resp := new(domain.RuleParameterGroupAllResp)
	for _, v := range li {
		item := new(domain.RuleParameterGroupAllItem)
		item.ID = v.ID
		item.Code = v.Code
		item.Description = v.Description
		resp.ItemList = append(resp.ItemList, item)
	}
	return resp, nil
}

func (s *RuleParameterGroupService) AllExcludeDefault(c *gin.Context) (*domain.RuleParameterGroupAllResp, error) {
	li, err := s.RuleParameterGroupRepo.All(c)
	if err != nil {
		return nil, err
	}
	resp := new(domain.RuleParameterGroupAllResp)
	for _, v := range li {
		item := new(domain.RuleParameterGroupAllItem)
		item.ID = v.ID
		item.Code = v.Code
		item.Description = v.Description
		resp.ItemList = append(resp.ItemList, item)
	}
	return resp, nil
}
