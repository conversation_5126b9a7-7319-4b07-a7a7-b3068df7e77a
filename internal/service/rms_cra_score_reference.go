package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type CraScoreReferenceService struct {
	Logger             *zap.Logger
	IcraScoreReference ICraScoreReference
}

func (s *CraScoreReferenceService) Retrieve(c *gin.Context, reference *model.RmsCraScoreReference) (*domain.RmsCraScoreReference, error) {
	if reference.CustomerId == "" && (reference.BusinessCode == 0 && reference.EntityId == "") {
		return nil, errors.New("parameter error\n")
	}
	RmsCraScoreReference, err := s.IcraScoreReference.Retrieve(c, reference)
	if err != nil {
		return nil, err
	}
	RmsCraScoreReferenceRes := &domain.RmsCraScoreReference{
		CraScoreReferenceId: RmsCraScoreReference.CraScoreReferenceId, // 唯一ID
		CustomerId:          RmsCraScoreReference.CustomerId,          // 客户ID
		EntityId:            RmsCraScoreReference.EntityId,
		BusinessCode:        RmsCraScoreReference.BusinessCode,
		AssessmentScore:     RmsCraScoreReference.AssessmentScore, // 分数
		AssessmentGrade:     RmsCraScoreReference.AssessmentGrade, // 等级：0=prohibit, 1=high, 2=medium, 3=low
		CreateTime:          RmsCraScoreReference.CreateTime,
		Uid:                 RmsCraScoreReference.Uid,
		Nickname:            RmsCraScoreReference.Nickname,
		Email:               RmsCraScoreReference.Email,
	}
	l := make([]*domain.RmsCraScoreReferenceInfo, 0)
	err = json.Unmarshal([]byte(RmsCraScoreReference.Info), &l)
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}
	infoList := &domain.RmsCraScoreReferenceInfoList{
		AmlList:     make([]*domain.RmsCraScoreReferenceInfo, 0),
		CountryList: make([]*domain.RmsCraScoreReferenceInfo, 0),
		CustomList:  make([]*domain.RmsCraScoreReferenceInfo, 0),
	}
	for _, info := range l {
		switch info.CraParameterType {
		case "AML":

			infoList.AmlList = append(infoList.AmlList, info)

		case "COUNTRY":
			infoList.CountryList = append(infoList.CountryList, info)
		case "CUSTOM":
			infoList.CustomList = append(infoList.CustomList, info)

		}
	}
	RmsCraScoreReferenceRes.Info = infoList
	return RmsCraScoreReferenceRes, nil
}
func (s *CraScoreReferenceService) List(c *gin.Context, data *model.RmsCraScoreReference) ([]*model.RmsCraScoreReference, error) {
	if data.CustomerId == "" && (data.BusinessCode == 0 && data.EntityId == "") {
		return nil, errors.New("parameter error\n")
	}
	return s.IcraScoreReference.List(c, data)
}
