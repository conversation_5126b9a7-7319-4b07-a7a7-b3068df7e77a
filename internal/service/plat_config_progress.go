package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type ConfigProgressService struct {
	Logger         *zap.Logger
	ConfigProgress IConfigProgressRepo
	BaseServer
}

func (s *ConfigProgressService) CreateOrUpdate(c *gin.Context, cp *model.PlatConfigProgress) error {
	err := s.ConfigProgress.CreateOrUpdate(c, cp)
	return err
}

func (s *ConfigProgressService) RetrieveByCode(c *gin.Context, code string) (*model.PlatConfigProgress, error) {
	return s.ConfigProgress.Retrieve(c, code)
}
