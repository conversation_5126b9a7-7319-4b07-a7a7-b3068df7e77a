package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/infra-group/uqpay-common-sdk/pkg/aws"
	"gitv2.uqpaytech.com/infra-group/uqpay-common-sdk/pkg/aws/s3"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"go.uber.org/zap"
	"time"
)

type EventExtartService struct {
	Logger         *zap.Logger
	IRiskFileExtra IRiskFileExtra
}

func (s *EventExtartService) List(c *gin.Context, req *domain.FilterExtraListReq) (*domain.FilterExtraListRes, error) {
	li, err := s.IRiskFileExtra.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.IRiskFileExtra.Count(c, req)
	if err != nil {
		return nil, err
	}

	list := make([]*domain.FilterExtraList, 0)
	for _, v := range li {
		extra := &domain.FilterExtraList{
			FileId:         v.FileId,
			FileName:       v.FileName,       // 文件名
			CreateTime:     v.CreateTime,     // 创建时间
			UpdateTime:     v.UpdateTime,     // 修改时间（文件生成成功时间）
			StartEventTime: v.StartEventTime, // 开始时间
			EndEventTime:   v.EndEventTime,   // 结束时间
			Status:         v.FileStatus,     //0:生成中 -1:失败 1:成功 2:过期
			Field:          v.FileParam,
			ExpireTime:     v.ExpireTime,
			CreatorId:      v.CreatorId,
			CreatorName:    v.CreatorName,
		}

		if v.ExpireTime.Before(time.Now()) {
			extra.Status = 2
		}
		list = append(list, extra)
	}

	res := &domain.FilterExtraListRes{
		ItemList:  list,
		TotalItem: int(count),
	}
	return res, nil
}
func (s *EventExtartService) GetUrl(c *gin.Context, id string) (*domain.FilterExtraUrlRes, error) {
	li, err := s.IRiskFileExtra.Retrieve(c, id)
	if err != nil {
		return nil, err
	}
	client, newClientErr := s3.NewClient(c, config.GetConfig().Aws.S3.Bucket, aws.Credential{
		Region:          config.GetConfig().Aws.S3.Region,
		AccessKeyId:     config.GetConfig().Aws.S3.AccessKeyID,
		SecretAccessKey: config.GetConfig().Aws.S3.SecretAccessKey,
	})
	if newClientErr != nil {
		return nil, newClientErr
	}
	url, err := client.Presign(c, li.FilePath, 5*time.Minute)
	if err != nil {
		return nil, err
	}

	return &domain.FilterExtraUrlRes{FileUrl: url}, nil
}
