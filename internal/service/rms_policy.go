package service

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"time"
)

type PolicyService struct {
	Logger     *zap.Logger
	PolicyRepo IPolicyRepo
	BaseServer
}

func (s *PolicyService) Create(c *gin.Context, cp *model.RmsPolicy) error {
	syncAt := time.Now().Local()
	cp.SyncedAt = &syncAt
	return s.PolicyRepo.Create(c, cp)
}

func (s *PolicyService) Update(c *gin.Context, id int64, cp *model.RmsPolicy) error {
	syncAt := time.Now().Local()
	cp.SyncedAt = &syncAt
	return s.PolicyRepo.Update(c, id, cp)
}

func (s *PolicyService) Delete(c *gin.Context, id int64) error {
	return s.PolicyRepo.Delete(c, id)
}

func (s *PolicyService) List(c *gin.Context, req *domain.PolicyList) (*domain.PolicyListResp, error) {
	li, err := s.PolicyRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.PolicyRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	resp := new(domain.PolicyListResp)
	if err := s.Copy(domain.FillInPageResponseData(req.PageSearch, count, li), resp); err != nil {
		return nil, err
	}
	for _, i := range resp.ItemList {
		var rLi []int64
		if len(i.PolicyRules) > 0 {
			err := json.Unmarshal([]byte(i.PolicyRules), &rLi)
			if err != nil {
				return nil, er.WSEF(err)
			}
			i.RuleNum = len(rLi)
		}
	}
	return resp, nil
}

func (s *PolicyService) Retrieve(c *gin.Context, id int64) (*model.RmsPolicy, error) {
	return s.PolicyRepo.Retrieve(c, id)
}

func (s *PolicyService) SetStatus(c *gin.Context, id int64, status string) error {
	return s.PolicyRepo.SetStatus(c, id, status)
}
