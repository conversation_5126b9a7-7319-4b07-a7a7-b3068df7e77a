package service

import (
	"github.com/google/wire"
	"github.com/jinzhu/copier"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
)

// ProviderSet is service providers.
var ServiceProviderSet = wire.NewSet(
	wire.Struct(new(AlarmContactService), "*"),
	wire.Struct(new(AlarmTimeConfService), "*"),
	wire.Struct(new(BlacklistService), "*"),
	wire.Struct(new(BusinessTypeService), "*"),
	wire.Struct(new(CheckPointService), "*"),
	wire.Struct(new(EventService), "*"),
	wire.Struct(new(EventFieldService), "*"),
	wire.Struct(new(EventStoreCfgService), "*"),
	wire.Struct(new(FilterFieldService), "*"),
	wire.Struct(new(OperatorLogService), "*"),
	wire.Struct(new(PolicyService), "*"),
	wire.Struct(new(RiskLogService), "*"),
	wire.Struct(new(RuleService), "*"),
	wire.Struct(new(RuleParameterService), "*"),
	wire.Struct(new(RuleParameterGroupService), "*"),
	wire.Struct(new(RuleParameterGroupBindService), "*"),
	wire.Struct(new(RuleParameterValueService), "*"),
	wire.Struct(new(HandleLogService), "*"),
	wire.Struct(new(BaseServer), "*"),
	wire.Struct(new(ConfigProgressService), "*"),
	wire.Struct(new(PageDataService), "*"),
)

type BaseServer struct{}

// Copy fromValue to toValue by copier
func (s BaseServer) Copy(fromValue interface{}, toValue interface{}) error {
	err := copier.Copy(toValue, fromValue)
	if err != nil {
		return er.WSEF(err)
	}
	return nil
}
