package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type BlacklistService struct {
	Logger        *zap.Logger
	BlacklistRepo IBlacklistRepo
}

func (s *BlacklistService) Create(c *gin.Context, cp *model.RmsBlacklist) error {
	return s.BlacklistRepo.Create(c, cp)
}

func (s *BlacklistService) Update(c *gin.Context, id int64, cp *model.RmsBlacklist) error {
	return s.BlacklistRepo.Update(c, id, cp)
}

func (s *BlacklistService) Delete(c *gin.Context, id int64) error {
	return s.BlacklistRepo.Delete(c, id)
}

func (s *BlacklistService) List(c *gin.Context, req *domain.BlacklistList) (*domain.PageData, error) {
	li, err := s.BlacklistRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.BlacklistRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *BlacklistService) Retrieve(c *gin.Context, id int64) (*model.RmsBlacklist, error) {
	return s.BlacklistRepo.Retrieve(c, id)
}
