package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

type BusinessTypeService struct {
	Logger           *zap.Logger
	BusinessTypeRepo IBusinessTypeRepo
	CheckPointRepo   ICheckPointRepo
}

func (s *BusinessTypeService) Create(c *gin.Context, cp *model.RmsBusinessType) error {
	return s.BusinessTypeRepo.Create(c, cp)
}

func (s *BusinessTypeService) Update(c *gin.Context, id int64, cp *model.RmsBusinessType) error {
	return s.BusinessTypeRepo.Update(c, id, cp)
}

func (s *BusinessTypeService) Delete(c *gin.Context, id int64) error {
	b, err := s.BusinessTypeRepo.Retrieve(c, id)
	if err != nil {
		return err
	}
	has, err := s.CheckPointRepo.ExistBusinessCode(b.BusinessCode)
	if err != nil {
		return err
	}
	if has {
		return er.FailedPrecondition.WithStack().WithMsgf("BusinessType in use.")
	}
	return s.BusinessTypeRepo.Delete(c, id)
}

func (s *BusinessTypeService) List(c *gin.Context, req *domain.BusinessTypeList) (*domain.PageData, error) {
	li, err := s.BusinessTypeRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.BusinessTypeRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *BusinessTypeService) Retrieve(c *gin.Context, id int64) (*model.RmsBusinessType, error) {
	return s.BusinessTypeRepo.Retrieve(c, id)
}

func (s *BusinessTypeService) All(c *gin.Context) ([]*domain.BusinessTypeAll, error) {
	resp := []*domain.BusinessTypeAll{}
	btLi, err := s.BusinessTypeRepo.All(c)
	if err != nil {
		return nil, err
	}
	for _, i := range btLi {
		resp = append(resp, &domain.BusinessTypeAll{
			BusinessCode: i.BusinessCode,
			BusinessName: i.BusinessName,
		})
	}
	return resp, nil
}
