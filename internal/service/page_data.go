package service

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

type PageDataService struct {
	BaseServer
	Logger             *zap.Logger
	Policy             IPolicyRepo
	Rule               IRuleRepo
	FilterField        IFilterFieldRepo
	CheckPoint         ICheckPointRepo
	EventField         IEventFieldRepo
	RuleParameter      IRuleParameterRepo
	RuleParameterGroup IRuleParameterGroupRepo
	RuleParameterValue IRuleParameterValueRepo
	BusinessTypeRepo   IBusinessTypeRepo
}

func (s *PageDataService) PolicyConfigPage(c *gin.Context, rules_group_code string) (*domain.PolicyConfigPageResp, error) {
	resp := new(domain.PolicyConfigPageResp)
	resp.Policy = new(domain.PolicyConfigPolicyResp)
	policy, err := s.Policy.RetrieveByCode(c, rules_group_code)
	if err != nil {
		if errors.Is(err, er.NotFound) {
			return resp, nil
		}
		return nil, err
	}
	ruleLi, err := s.Rule.AllByCPCode(c, policy.CheckPoint)
	if err != nil {
		return nil, err
	}
	FilterFieldLi, err := s.FilterField.AllByAPCode(c, policy.CheckPoint)
	if err != nil {
		return nil, err
	}
	err = s.Copy(policy, resp.Policy)
	if err != nil {
		return nil, err
	}
	if policy.Filters != nil && *policy.Filters != "" {
		resp.Policy.FilterFieldMap = json.RawMessage(*policy.Filters)
	}
	if policy.PolicyRules != "" {
		resp.Policy.RuleIdList = json.RawMessage(policy.PolicyRules)
	}
	if policy.WhiteListIDs != "" {
		_ = jsoniter.UnmarshalFromString(policy.WhiteListIDs, &resp.Policy.WhiteListIDList)
	}
	err = s.Copy(ruleLi, &resp.RuleLi)
	if err != nil {
		return nil, err
	}
	resp.AllFilterField = []*domain.PolicyConfigPageAllFilterFieldResp{}
	for _, f := range FilterFieldLi {
		a := new(domain.PolicyConfigPageAllFilterFieldResp)
		a.FieldName = f.FieldName
		if f.FieldEnum != "" {
			a.FieldEnum = json.RawMessage(f.FieldEnum)
		}
		resp.AllFilterField = append(resp.AllFilterField, a)
	}
	return resp, err
}

func (s *PageDataService) PolicyConfigPageCreate(c *gin.Context, access_point_code string) (*domain.PolicyConfigPageResp, error) {
	resp := new(domain.PolicyConfigPageResp)
	resp.Policy = new(domain.PolicyConfigPolicyResp)
	ruleLi, err := s.Rule.AllByCPCode(c, access_point_code)
	if err != nil {
		return nil, err
	}
	FilterFieldLi, err := s.FilterField.AllByAPCode(c, access_point_code)
	if err != nil {
		return nil, err
	}
	resp.Policy.CheckPoint = access_point_code
	err = s.Copy(ruleLi, &resp.RuleLi)
	if err != nil {
		return nil, err
	}
	resp.AllFilterField = []*domain.PolicyConfigPageAllFilterFieldResp{}
	for _, f := range FilterFieldLi {
		a := new(domain.PolicyConfigPageAllFilterFieldResp)
		a.FieldName = f.FieldName
		if f.FieldEnum != "" {
			a.FieldEnum = json.RawMessage(f.FieldEnum)
		}
		resp.AllFilterField = append(resp.AllFilterField, a)
	}
	return resp, err
}

func (s *PageDataService) BusinessConfig(c *gin.Context, access_point_code string) (*domain.BusinessConfigResp, error) {
	resp := new(domain.BusinessConfigResp)
	BusinessConfig := []*model.CheckPointBusinessConfig{}

	resp.BusinessTypesLi = BusinessConfig
	EventFieldLi, err := s.EventField.AllByAPCode(c, access_point_code)
	if err != nil {
		return nil, err
	}
	FilterFieldLi, err := s.FilterField.AllByAPCode(c, access_point_code)
	if err != nil {
		return nil, err
	}

	for _, e := range EventFieldLi {
		bs := new(domain.BusinessConfigSecondField)
		isFilterField := false
		// 第二匹配条件 只能选择是过滤字段的 Event Field，显示的枚举是 过滤字段的名称，设置的是编码
		for _, f := range FilterFieldLi {
			if f.FieldName == e.FieldName {
				bs.FieldName = e.FieldName
				bs.FieldEnum = json.RawMessage(f.FieldEnum)
				isFilterField = true
			}
		}
		if isFilterField {
			resp.SecondFieldLi = append(resp.SecondFieldLi, bs)
		}
		// 主键字段可以选择所有的 Event Field 字段
		resp.PrimaryKeyFieldLi = append(resp.PrimaryKeyFieldLi, e.FieldName)
	}
	btLi, err := s.BusinessTypeRepo.All(c)
	if err != nil {
		return nil, err
	}
	businessTypeAll := []*domain.BusinessConfigBusinessTypeAll{}
	for _, i := range btLi {
		businessTypeAll = append(businessTypeAll, &domain.BusinessConfigBusinessTypeAll{
			BusinessName: i.BusinessName,
			BusinessCode: i.BusinessCode,
		})
	}
	resp.BusinessTypeAll = businessTypeAll
	return resp, nil
}

func (s *PageDataService) RmsRuleParameterGroupBind(c *gin.Context, req *domain.PolicyConfigPageList) (*domain.PageData, error) {
	// 获取接入点的参数组
	RuleParameterGroup, err := s.RuleParameterGroup.FirstByAPCode(c, req.RuleParameterGroupId)
	if err != nil {
		return nil, err
	}
	// 获取接入点下所有规则参数
	RuleParameter, err := s.RuleParameter.ListByAPCode(c, req, RuleParameterGroup.CheckPoint)
	if err != nil {
		return nil, err
	}
	count, err := s.RuleParameter.CountByAPCode(c, RuleParameterGroup.CheckPoint)
	if err != nil {
		return nil, err
	}
	// 获取接入点下所有参数值
	RuleParameterValue, err := s.RuleParameterValue.AllByGroupID(c, RuleParameterGroup.ID)
	if err != nil {
		return nil, err
	}
	resp := new(domain.RmsRuleParameterGroupBindResp)
	resp.ItemList = []*domain.RmsRuleParameterGroupBindRuleParameter{}
	for _, r := range RuleParameter {
		rp := new(domain.RmsRuleParameterGroupBindRuleParameter)
		rp.ID = r.ID
		rp.ParamType = r.ParamType
		rp.Code = r.Code
		rp.Description = r.Description
		rp.SetNotBind()
		for _, v := range RuleParameterValue {
			if v.ParamID == r.ID {
				rp.SetIsBind()
				pv := new(domain.RmsRuleParameterGroupBindRuleParameterParameterValue)
				pv.ID = v.ID
				pv.Description = v.Description
				pv.ParamKey = v.ParamKey
				pv.ParamValue = v.ParamValue
				rp.ParameterValue = append(rp.ParameterValue, pv)
			}
		}

		resp.ItemList = append(resp.ItemList, rp)
	}
	return domain.FillInPageResponseData(req.PageSearch, count, resp.ItemList), nil
}
