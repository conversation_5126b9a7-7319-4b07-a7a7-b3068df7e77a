package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type AlarmContactService struct {
	Logger           *zap.Logger
	AlarmContactRepo IAlarmContactRepo
}

func (s *AlarmContactService) Create(c *gin.Context, cp *model.RmsAlarmContact) error {
	return s.AlarmContactRepo.Create(c, cp)
}

func (s *AlarmContactService) Update(c *gin.Context, id int64, cp *model.RmsAlarmContact) error {
	return s.AlarmContactRepo.Update(c, id, cp)
}

func (s *AlarmContactService) Delete(c *gin.Context, id int64) error {
	return s.AlarmContactRepo.Delete(c, id)
}

func (s *AlarmContactService) List(c *gin.Context, req *domain.AlarmContactList) (*domain.PageData, error) {
	li, err := s.AlarmContactRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.AlarmContactRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *AlarmContactService) Retrieve(c *gin.Context, id int64) (*model.RmsAlarmContact, error) {
	return s.AlarmContactRepo.Retrieve(c, id)
}
