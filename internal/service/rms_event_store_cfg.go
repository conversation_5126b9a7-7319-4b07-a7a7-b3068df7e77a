package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type EventStoreCfgService struct {
	Logger            *zap.Logger
	EventStoreCfgRepo IEventStoreCfgRepo
}

func (s *EventStoreCfgService) Create(c *gin.Context, cp *model.RmsEventStoreCfg) error {
	cp.SendToIntra = false
	return s.EventStoreCfgRepo.Create(c, cp)
}

func (s *EventStoreCfgService) Update(c *gin.Context, id int64, cp *model.RmsEventStoreCfg) error {
	return s.EventStoreCfgRepo.Update(c, id, cp)
}

func (s *EventStoreCfgService) Delete(c *gin.Context, id int64) error {
	return s.EventStoreCfgRepo.Delete(c, id)
}

func (s *EventStoreCfgService) List(c *gin.Context, req *domain.EventStoreCfgList) (*domain.PageData, error) {
	li, err := s.EventStoreCfgRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.EventStoreCfgRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *EventStoreCfgService) Retrieve(c *gin.Context, id int64) (*model.RmsEventStoreCfg, error) {
	return s.EventStoreCfgRepo.Retrieve(c, id)
}

func (s *EventStoreCfgService) RetrieveByAPCode(c *gin.Context, apCode string) (*model.RmsEventStoreCfg, error) {
	return s.EventStoreCfgRepo.RetrieveByAPCode(c, apCode)
}
