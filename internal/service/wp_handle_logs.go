package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"go.uber.org/zap"
)

type HandleLogService struct {
	Logger        *zap.Logger
	HandleLogRepo IHandleLogRepo
}

func (s *HandleLogService) Create(c *gin.Context, cp *model.WpHandleLog) error {
	return s.HandleLogRepo.Create(c, cp)
}

func (s *HandleLogService) Update(c *gin.Context, id int32, cp *model.WpHandleLog) error {
	return s.HandleLogRepo.Update(c, int64(id), cp)
}

func (s *HandleLogService) Delete(c *gin.Context, id int64) error {
	return s.HandleLogRepo.Delete(c, id)
}

func (s *HandleLogService) List(c *gin.Context, req *domain.HandleLogList) (*domain.PageData, error) {
	li, err := s.HandleLogRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.HandleLogRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *HandleLogService) Retrieve(c *gin.Context, id int64) (*model.WpHandleLog, error) {
	return s.HandleLogRepo.Retrieve(c, id)
}
