package service

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

type RuleService struct {
	Logger   *zap.Logger
	RuleRepo IRuleRepo
	Conf     config.Config
	BaseServer
}

// Check 整理数据，处理上下线逻辑
func (s *RuleService) Check(c *gin.Context, cp *model.RmsRule) error {
	if cp.DeployMethod == "manual" {
		cp.StartTime = nil
		cp.EndTime = nil
	} else {
		if cp.StartTime == nil || cp.StartTime.IsZero() || cp.EndTime == nil || cp.EndTime.IsZero() {
			return er.InvalidArgument.WithStack().WithMsg("The start time and end time cannot be empty.")
		}
		cp.Status = "Offline"
	}
	//re, err := regexp.Compile(`rule\s*"(\w+)"\s+`)
	//if err != nil {
	//	return er.WSEF(err)
	//}
	//match := re.FindStringSubmatch(cp.RuleContent)
	//ruleCode := ""
	//if len(match) > 1 {
	//	ruleCode = match[1]
	//}
	//if ruleCode != cp.RuleCode {
	//	return er.InvalidArgument.WSF(zap.String("ruleCode", ruleCode), zap.String("RuleCode", cp.RuleCode)).WithMsg(
	//		"The rule code must be the same as the rule name in the rule content.")
	//}
	return nil
}

func (s *RuleService) Create(c *gin.Context, cp *model.RmsRule) error {
	if err := s.Compile(cp.RuleContent, cp.RuleNo, cp.RuleName, cp.Priority); err != nil {
		return err
	}

	if err := s.Check(c, cp); err != nil {
		return err
	}

	// 规则的预设值，在新引擎中不需要，按照旧引擎的规则填入
	cp.RuleType = "Remote"
	cp.EngineType = "Stateless"
	EngineInstance := "StatefulEngine01"
	cp.EngineInstance = &EngineInstance
	if cp.DeployMethod == "manual" && cp.Status == "Online" {
		nowTime := time.Now()
		cp.SyncedAt = &nowTime
	}

	err := s.RuleRepo.Create(c, cp)
	if errors.Is(err, er.DuplicateEntry) {
		return er.DuplicateKeys.WithMsgf("rule_code duplicate: %s", cp.RuleNo)
	}
	return err
}

func (s *RuleService) Update(c *gin.Context, id int64, cp *model.RmsRule) error {
	if err := s.Check(c, cp); err != nil {
		return err
	}

	if err := s.Compile(cp.RuleContent, cp.RuleNo, cp.RuleName, cp.Priority); err != nil {

		return err
	}
	oriRule, err := s.RuleRepo.Retrieve(c, id)
	if err != nil {
		return err
	}
	// 变更规则内容或者更新为上线时更新 syncedAt, 下线时设置为 nil
	// TODO 在引擎侧优化上下线的问题。
	if oriRule.DeployMethod == "manual" && cp.Status == "Online" {
		nowTime := time.Now()
		cp.SyncedAt = &nowTime
	} else {
		// 满足自动状态下规则更新后上下线
		cp.SyncedAt = nil
	}
	err = s.RuleRepo.Update(c, id, cp)
	if errors.Is(err, er.DuplicateEntry) {
		return er.DuplicateKeys.WithMsgf("rule_code duplicate: %s", cp.RuleNo)
	}
	return err
}

func (s *RuleService) Delete(c *gin.Context, id int64) error {
	return s.RuleRepo.Delete(c, id)
}

func (s *RuleService) List(c *gin.Context, req *domain.RuleList) (*domain.PageData, error) {
	li, err := s.RuleRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.RuleRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	resp := []*domain.RuleResp{}
	err = s.Copy(li, &resp)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, resp), nil
}

func (s *RuleService) Retrieve(c *gin.Context, id int64) (*domain.RuleResp, error) {
	r, err := s.RuleRepo.Retrieve(c, id)
	if err != nil {
		return nil, err
	}
	resp := new(domain.RuleResp)
	if err := s.Copy(r, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *RuleService) SetStatus(c *gin.Context, id int64, status string) error {
	cp, err := s.RuleRepo.Retrieve(c, id)
	if err != nil {
		return err
	}
	if cp.DeployMethod == "auto" {
		if status == "Online" {
			return er.Unimplemented.WithStack().WithMsg("Automatically effective rules do not support manual going online.")
		}
		// 在生效时间内下线，清空生效时间和失效时间
		newTime := time.Now()
		if cp.StartTime != nil && cp.StartTime.Before(newTime) && (cp.EndTime == nil || cp.EndTime.After(newTime)) {
			cp.StartTime = nil
			cp.EndTime = nil
			cp.DeployMethod = "manual"
			cp.Status = "Offline"
			return s.RuleRepo.SetStatus(c, id, cp)
		}
	}
	cp.Status = status
	if status == "Online" {
		nowTime := time.Now()
		cp.SyncedAt = &nowTime
	}
	return s.RuleRepo.SetStatus(c, id, cp)
}

func (s *RuleService) Compile(RuleContent string, ruleNo string, ruleName string, priority int) error {
	httpClient := &http.Client{
		Timeout: time.Duration(12 * time.Second),
	}
	url := s.Conf.App.GatewayHost + "/api/v1/compile"
	gatewayStruct := struct {
		RuleContent string `json:"ruleContent"`
		RuleNo      string `json:"ruleNo"`
		RuleName    string `json:"ruleName"`
		Priority    int    `json:"priority"`
	}{
		RuleContent: RuleContent,
		RuleNo:      ruleNo,
		RuleName:    ruleName,
		Priority:    priority,
	}
	gatewayReq, err := json.Marshal(&gatewayStruct)
	httpReq, err := http.NewRequest(http.MethodPost, url, strings.NewReader(string(gatewayReq)))
	if err != nil {
		return er.WSEF(err)
	}
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		return er.WSEF(err).WithMsg("Gateway call failed, please contact technical support.")
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return er.WSEF(err).WithMsg("Gateway call failed, please contact technical support.")
	}
	respBody := new(domain.GatewayRuleCompileResp)
	err = json.Unmarshal(body, respBody)
	if err != nil {
		return er.WSEF(err).WithMsg("Gateway call failed, please contact technical support.")
	}
	if respBody.Body != "success" {
		return er.CompileFail.WSF(zap.ByteString("resp", body)).WithMsg("Compile fail: " + respBody.Body)
	}
	return nil
}

func (s *RuleService) Refresh() error {
	url := s.Conf.App.GatewayHost + "/api/v1/refresh"
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return er.WSEF(err)
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return er.WSEF(err)
	}
	respBody := new(domain.GatewayRuleCompileResp)
	err = json.Unmarshal(body, respBody)
	if err != nil {
		return er.WSEF(err)
	}
	if respBody.Body != "success" {
		return er.CompileFail.WSF(zap.ByteString("resp", body))
	}
	return nil
}
