package service

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"time"
)

type CraParameterValService struct {
	Logger           *zap.Logger
	IcraParameterVal IcraParameterVal
	ICraParameter    ICraParameter
}

func (s *CraParameterValService) Create(c *gin.Context, cp *model.RmsCraParameterVal) (string, *er.BuiltInError) {
	uid := uuid.NewString()
	cp.ParamValId = uid
	cp.CreateTime = time.Now()

	cp1 := &model.RmsCraParameterVal{
		ParamValName:   cp.ParamValName,
		CraFrameworkId: cp.CraFrameworkId,
	}
	if _, err := s.IcraParameterVal.First(c, cp1); err == nil {
		return "", er.CraParameterNameRepeated
	}
	if err := s.IcraParameterVal.Create(c, cp); err != nil {
		return "", er.Unknown
	}
	return uid, nil
}

func (s *CraParameterValService) Update(c *gin.Context, param_val_id string, cp *model.RmsCraParameterVal) *er.BuiltInError {
	cp.UpdateTime = time.Now()

	if cp.Status == -1 {
		if _, err := s.ICraParameter.FirstWhereLike(c, map[string]string{"param_info": fmt.Sprintf("%%%s%%", cp.ParamValId)}); err == nil {
			return er.CraParameterValInactiveError
		}
	}
	if err := s.IcraParameterVal.Update(c, param_val_id, cp); err != nil {
		return er.Unknown
	}
	return nil
}

func (s *CraParameterValService) Delete(c *gin.Context, param_val_id string) error {
	val, err := s.IcraParameterVal.Retrieve(c, param_val_id)
	if err != nil {
		return er.Unknown
	}
	if _, err := s.ICraParameter.FirstWhereLike(c, map[string]string{"param_info": fmt.Sprintf("%%%s%%", val.ParamValId)}); err == nil {
		return er.CraParameterValInactiveError
	}
	return s.IcraParameterVal.Delete(c, param_val_id)
}

func (s *CraParameterValService) List(c *gin.Context, data *model.RmsCraParameterVal) ([]*model.RmsCraParameterVal, error) {

	return s.IcraParameterVal.List(c, data)
}

func (s *CraParameterValService) Retrieve(c *gin.Context, param_val_id string) (*model.RmsCraParameterVal, error) {
	return s.IcraParameterVal.Retrieve(c, param_val_id)
}
