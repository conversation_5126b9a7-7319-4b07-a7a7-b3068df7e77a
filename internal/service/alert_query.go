package service

import (
	"bytes"
	"encoding/csv"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/tealeg/xlsx"
	"github.com/tidwall/gjson"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gorm.io/gorm"
	"net/http"
	"strconv"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/db"
	"go.uber.org/zap"

	"log"
	"time"
)

type AlertAQueryService struct {
	Logger           *zap.Logger
	IRiskEventRecord IRiskEventRecord
	IRiskFileExtra   IRiskFileExtra
}

func (s *AlertAQueryService) List(c *gin.Context, req *domain.AlertQueryListReq) (*domain.AlertQueryListRes, error) {
	date, err := s.GetDateRanges(req.DateType, req.StartTime, req.EndTime)
	if err != nil {
		return nil, errors.New("GetDateRanges error")
	}
	db := db.Doris()
	if req.CheckPoint != "" {
		db = db.Where("check_point = ?", req.CheckPoint)
	}
	if req.Status != nil && len(req.Status) > 0 {
		all := false
		for _, stastus := range req.Status {
			if stastus == "all" {
				all = true
			}
		}
		if !all {
			db = db.Where("result_code in ?", req.Status)
		}
	} else {
		db = db.Where("result_code != ?", "000")

	}
	if req.Str1 != "" {
		db = db.Where("str1 like ?", "%"+req.Str1+"%")
	}
	if req.Str2 != "" {
		db = db.Where("str2 like ?", "%"+req.Str2+"%")
	}
	if req.Str3 != "" {
		db = db.Where("str3 like ?", "%"+req.Str3+"%")
	}
	if req.Str4 != "" {
		db = db.Where("str4 like ?", "%"+req.Str4+"%")
	}
	if req.Str5 != "" {
		db = db.Where("str5 like ?", "%"+req.Str5+"%")
	}
	if req.DateType != "null" {
		db = db.Where("create_time >= ?", date.StartTime)
		db = db.Where("create_time  < ?", date.EndTime)

	} else {
		if req.StartTime != "" {
			db = db.Where("create_time >= ?", req.StartTime+" 00:00:00.000")
		}
		if req.EndTime != "" {
			db = db.Where("create_time <= ? ", req.EndTime+" 23:59:59.999")
		}
	}

	if req.DayStartTime != "" {
		db = db.Where("DATE_FORMAT(create_time, '%H:%i:%s') >= ?", req.DayStartTime)
	}
	if req.DayEndTime != "" {
		db = db.Where("DATE_FORMAT(create_time, '%H:%i:%s') <= ?", req.DayEndTime)
	}

	if req.Num1.Min != "" {
		db = db.Where("num1 >= ?", req.Num1.Min)
	}
	if req.Num1.Max != "" {
		db = db.Where("num1 <= ?", req.Num1.Max)
	}
	if req.Num2.Min != "" {
		db = db.Where("num2 >= ?", req.Num2.Min)
	}
	if req.Num2.Max != "" {
		db = db.Where("num2 <= ?", req.Num2.Max)
	}
	if req.ResultMsg != "" {
		db = db.Where("result_msg like ?", "%"+req.ResultMsg+"%")
	}

	if req.JsonKey != "" && req.JsonValue != "" {
		db = db.Where(fmt.Sprintf(`JSON_EXTRACT(request_parameter,"$.%s") like ? `, req.JsonKey), "%"+req.JsonValue+"%")
	}
	return s.IRiskEventRecord.List(c, db, req)
}
func (s *AlertAQueryService) Statistics(c *gin.Context, req *domain.AlertQueryStatisticsReq) (*domain.AlertQueryStatisticsRes, error) {
	RiskCode := map[string][]string{
		"CP001_Transaction": {"101", "102", "001", "002"},
		"BP002_Payout":      {"002"},
		"BP001_Deposit":     {"002"},
		"CP002_3DS":         {"002", "003"},
		"AP001_Payment":     {"001", "002", "101", "102"},
	}
	list := make([]*domain.AlertQueryStatistics, 0)
	req.StartTime = req.StartTime
	req.EndTime = req.EndTime

	date, err := s.GetDateRanges(req.DateType, req.StartTime, req.EndTime)
	if err != nil {
		return nil, errors.New("GetDateRanges error")
	}
	AllCountdb := db.Doris().Where("check_point = ? and create_time>= ? and create_time<= ? ", req.CheckPoint, date.StartTime, date.EndTime)
	AllCount, err := s.IRiskEventRecord.Count(c, AllCountdb)
	if err != nil {
		return nil, errors.New("AllCount error")
	}
	LastAllCountdb := db.Doris().Where("check_point = ? and create_time>= ? and create_time<= ? ", req.CheckPoint, date.LastStartTime, date.LastEndTime)
	LastAllCount, err := s.IRiskEventRecord.Count(c, LastAllCountdb)
	if err != nil {
		return nil, errors.New("LastAllCount error")
	}
	{
		AllNoPassCountdb := db.Doris().Where("check_point = ? and create_time>= ? and create_time<=? and result_code !=?", req.CheckPoint, date.StartTime, date.EndTime, "000")
		AllNoPassCount, err := s.IRiskEventRecord.Count(c, AllNoPassCountdb)
		if err != nil {
			fmt.Println(err.Error())
			return nil, errors.New("AllNoPassCount error")
		}
		LastAllNoPassCountdb := db.Doris().Where("check_point = ? and create_time>= ? and create_time <= ?and result_code !=?", req.CheckPoint, date.LastStartTime, date.LastEndTime, "000")
		LastAllNoPassCount, err := s.IRiskEventRecord.Count(c, LastAllNoPassCountdb)
		if err != nil {
			return nil, errors.New("LastAllNoPassCount error")
		}

		All := &domain.AlertQueryStatistics{
			Code:             "all",
			NumberOfTriggers: AllNoPassCount, //触发数
		}
		TriggerRate := decimal.NewFromInt(0)
		if AllCount == 0 {
			All.TriggerRate = "null"
			All.TriggerLinkRatio = "null"
		} else {
			TriggerRate = decimal.NewFromInt(AllNoPassCount).Div(decimal.NewFromInt(AllCount))
			All.TriggerRate = TriggerRate.Mul(decimal.NewFromInt(100)).Round(2).String()
		}

		if LastAllCount == 0 {
			All.TriggerLinkRatio = "null"
		} else {
			LastTriggerRate := decimal.NewFromInt(LastAllNoPassCount).Div(decimal.NewFromInt(LastAllCount))

			if LastTriggerRate.Equal(decimal.NewFromInt(0)) {
				All.TriggerLinkRatio = "null"
			} else {
				//(a-b)/b
				All.TriggerLinkRatio = TriggerRate.Sub(LastTriggerRate).Div(LastTriggerRate).Abs().Mul(decimal.NewFromInt(100)).Round(2).String()
				TriggerLinkSymbol := true
				if !TriggerRate.GreaterThanOrEqual(LastTriggerRate) {
					TriggerLinkSymbol = false
				}
				All.TriggerLinkSymbol = TriggerLinkSymbol
			}

		}
		list = append(list, All)

	}

	if codes, ok := RiskCode[req.CheckPoint]; ok {

		for _, code := range codes {
			CodeCountdb := db.Doris().Where("check_point = ? and create_time>= ? and create_time<= ? and result_code =?", req.CheckPoint, date.StartTime, date.EndTime, code)
			CodeCount, err := s.IRiskEventRecord.Count(c, CodeCountdb)
			if err != nil {
				return nil, errors.New("CodeCount error")
			}
			LastCodeCountdb := db.Doris().Where("check_point = ? and create_time>= ? and create_time<= ? and result_code =?", req.CheckPoint, date.LastStartTime, date.LastEndTime, code)
			LastCodeCount, err := s.IRiskEventRecord.Count(c, LastCodeCountdb)
			if err != nil {
				return nil, errors.New("LastCodeCount error")
			}
			Code := &domain.AlertQueryStatistics{
				Code:             code,
				NumberOfTriggers: CodeCount, //触发数
			}
			TriggerRate := decimal.NewFromInt(0)
			if AllCount == 0 {
				Code.TriggerRate = "null"
				Code.TriggerLinkRatio = "null"
			} else {
				TriggerRate = decimal.NewFromInt(CodeCount).Div(decimal.NewFromInt(AllCount))
				Code.TriggerRate = TriggerRate.Mul(decimal.NewFromInt(100)).Round(2).String()
			}

			if LastAllCount == 0 {
				Code.TriggerLinkRatio = "null"
			} else {
				LastTriggerRate := decimal.NewFromInt(LastCodeCount).Div(decimal.NewFromInt(LastAllCount))

				if LastTriggerRate.Equal(decimal.NewFromInt(0)) {
					Code.TriggerLinkRatio = "null"
				} else {
					//(a-b)/b
					Code.TriggerLinkRatio = TriggerRate.Sub(LastTriggerRate).Div(LastTriggerRate).Abs().Mul(decimal.NewFromInt(100)).Round(2).String()
					TriggerLinkSymbol := true
					if !TriggerRate.GreaterThanOrEqual(LastTriggerRate) {
						TriggerLinkSymbol = false
					}
					Code.TriggerLinkSymbol = TriggerLinkSymbol
				}

			}
			list = append(list, Code)

		}

	} else {
		return &domain.AlertQueryStatisticsRes{List: list}, nil
	}

	//查询这一次的
	return &domain.AlertQueryStatisticsRes{List: list}, nil

}
func (s *AlertAQueryService) GetDateRanges(reqDateType, customStartTime, customEndTime string) (*domain.AlertQueryDateRange, error) {
	if reqDateType != "null" {
		return getPredefinedDateRange(reqDateType), nil
	}
	return getCustomDateRange(customStartTime, customEndTime)
}
func getPredefinedDateRange(dateType string) *domain.AlertQueryDateRange {
	nowdaty := time.Now()
	now := time.Date(nowdaty.Year(), nowdaty.Month(), nowdaty.Day(), 0, 0, 0, 0, nowdaty.Location()).AddDate(0, 0, 1)
	var startTime, lastStartTime, lastEndTime string

	switch dateType {
	case "year":
		firstDayOfYear := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
		days := int(now.Sub(firstDayOfYear).Hours() / 24)
		startTime = now.AddDate(0, 0, -days).Format("2006-01-02 15:04:05.000")
		lastStartTime = now.AddDate(0, 0, -2*days).Add(-time.Millisecond).Format("2006-01-02 15:04:05.000")
		lastEndTime = now.AddDate(0, 0, -days).Add(-time.Millisecond).Format("2006-01-02 15:04:05.000")

	case "month":
		firstDayOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
		days := int(now.Sub(firstDayOfMonth).Hours() / 24)
		startTime = now.AddDate(0, 0, -days).Format("2006-01-02 15:04:05.000")
		lastStartTime = now.AddDate(0, 0, -2*days).Format("2006-01-02 15:04:05.000")
		lastEndTime = now.AddDate(0, 0, -days).Add(-time.Millisecond).Format("2006-01-02 15:04:05.000")

	case "week":
		weekday := int(now.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		monday := now.AddDate(0, 0, -weekday+1)
		mondayDate := time.Date(monday.Year(), monday.Month(), monday.Day(), 0, 0, 0, 0, monday.Location())
		todayDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		days := int(todayDate.Sub(mondayDate).Hours() / 24)
		startTime = now.AddDate(0, 0, -days).Format("2006-01-02 15:04:05.000")
		lastStartTime = now.AddDate(0, 0, -(7 + days)).Format("2006-01-02 15:04:05.000")
		lastEndTime = now.AddDate(0, 0, -7).Add(-time.Millisecond).Format("2006-01-02 15:04:05.000")

	case "today":
		startTime = now.AddDate(0, 0, -1).Format("2006-01-02 15:04:05.000")
		lastStartTime = now.AddDate(0, 0, -2).Format("2006-01-02 15:04:05.000")
		lastEndTime = now.AddDate(0, 0, -1).Add(-time.Millisecond).Format("2006-01-02 15:04:05.000")
	}

	return &domain.AlertQueryDateRange{
		StartTime:     startTime,
		EndTime:       now.Add(-time.Millisecond).Format("2006-01-02 15:04:05.000"),
		LastStartTime: lastStartTime,
		LastEndTime:   lastEndTime,
	}
}
func getCustomDateRange(startTimeStr, endTimeStr string) (*domain.AlertQueryDateRange, error) {
	startTime, err := time.Parse("2006-01-02 15:04:05.000", startTimeStr+" 00:00:00.000")
	if err != nil {
		fmt.Println(err.Error())
		return nil, err
	}

	endTime, err := time.Parse("2006-01-02 15:04:05.000", endTimeStr+" 00:00:00.000")
	if err != nil {
		return nil, err
	}
	endTime = endTime.AddDate(0, 0, 1)

	duration := endTime.Sub(startTime)
	days := int(duration.Hours() / 24)

	return &domain.AlertQueryDateRange{
		StartTime:     startTime.Format("2006-01-02 15:04:05.000"),
		EndTime:       endTime.Add(-time.Millisecond).Format("2006-01-02 15:04:05.000"),
		LastStartTime: startTime.AddDate(0, 0, -days).Format("2006-01-02 15:04:05.000"),
		LastEndTime:   endTime.AddDate(0, 0, -days).Add(-time.Millisecond).Format("2006-01-02 15:04:05.000"),
	}, nil
}
func (s *AlertAQueryService) Get(c *gin.Context, req *domain.AlertQueryGetReq) (*domain.AlertQueryGetRes, error) {

	RiskEventRecord := &model.RiskEventRecord{}
	if err := db.Doris().
		Where("check_point = ? and ( str1 = ? or str2 = ? or str3 = ? or str4= ? or str5 = ? )", req.CheckPoint, req.Uuid, req.Uuid, req.Uuid, req.Uuid, req.Uuid).First(RiskEventRecord).
		Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = er.NotFound.WithMsg("risk event not found")
		} else {
			err = er.Internal.WithErr(err).WithMsg("Database query exception")
		}
		return nil, err
	}
	res := &domain.AlertQueryGetRes{
		Uuid:       req.Uuid,
		Id:         RiskEventRecord.ID,                                           //风控系统生成的唯一事件ID
		EventTime:  RiskEventRecord.CreateTime.Format("2006-01-02 15:04:05.000"), //风控事件触发时间（采用国际标准时间，精确到毫秒）
		CheckPoint: RiskEventRecord.CheckPoint,                                   //各业务线接入点编码（如 CP001_Transaction、BP002_Payout）,在开发规则时设计。
		ResultCode: RiskEventRecord.ResultCode,                                   //风控决策结果码（如 000=Pass, 001=Decline, 002=Alert）,在开发规则时设计。
		ResultMsg:  RiskEventRecord.ResultMsg,                                    //风控决策结果信息，rules完整内容包括阈值。命中多条时，返回多条。
		FailedRule: RiskEventRecord.FailedRule,                                   //
	}
	return res, nil
}
func (s *AlertAQueryService) Report(c *gin.Context, req *domain.AlertQueryReportReq) (string, error) {
	//

	//value, exists := c.Get(consts.TokenKey)
	//if !exists {
	//	return "", errors.New("token not found")
	//}
	//
	//claims, ok := value.(*jwt.MyClaims)
	//if !ok {
	//	return "", errors.New("JWT error")
	//}
	//
	//FileParamByte, _ := json.Marshal(req)
	//Uuid := uuid.NewString()
	//ExpireTime := time.Now().AddDate(0, 0, 30)
	FileName := "RE_" + strconv.FormatInt(time.Now().UnixNano(), 10) + "." + req.FileFormat
	date, err := s.GetDateRanges(req.DateType, req.StartTime, req.EndTime)
	if err != nil {
		return "", err
	}
	//
	//StartTime, err := time.ParseInLocation("2006-01-02 15:04:05.000", date.StartTime, time.Local)
	//if err != nil {
	//	return "", err
	//}
	//EndTime, err := time.ParseInLocation("2006-01-02 15:04:05.000", date.EndTime, time.Local)
	//if err != nil {
	//	return "", err
	//}
	//RmsFileExtra := &model.RmsFileExtra{
	//	FileId:         Uuid,                  // uuid
	//	FileName:       FileName,              // 文件名
	//	FileType:       1000,                  // 1000 event
	//	CreatorId:      claims.UserId,         // 创建人id
	//	CreatorName:    claims.Username,       // 创建人名
	//	CreateTime:     time.Now(),            // 创建时间
	//	ExpireTime:     ExpireTime,            // 文件过期时间默认30天
	//	FileParam:      string(FileParamByte), // 文件参数
	//	FileStatus:     0,                     // -1=failed, 0=pending,1=success
	//	StartEventTime: StartTime,             // 开始时间
	//	EndEventTime:   EndTime,               // 结束时间
	//}
	//if err := s.IRiskFileExtra.Create(c, RmsFileExtra); err != nil {
	//	return "", err
	//}
	//临时方案
	{
		db := db.Doris()
		if req.CheckPoint != "" {
			db = db.Where("check_point = ?", req.CheckPoint)
		}
		if req.Status != nil && len(req.Status) > 0 {
			all := false
			for _, stastus := range req.Status {
				if stastus == "all" {
					all = true
				}
			}
			if !all {
				db = db.Where("result_code in ?", req.Status)
			}
		} else {
			db = db.Where("result_code != ?", "000")

		}
		if req.Str1 != "" {
			db = db.Where("str1 like ?", "%"+req.Str1+"%")
		}
		if req.Str2 != "" {
			db = db.Where("str2 like ?", "%"+req.Str2+"%")
		}
		if req.Str3 != "" {
			db = db.Where("str3 like ?", "%"+req.Str3+"%")
		}
		if req.Str4 != "" {
			db = db.Where("str4 like ?", "%"+req.Str4+"%")
		}
		if req.Str5 != "" {
			db = db.Where("str5 like ?", "%"+req.Str5+"%")
		}
		if req.DateType != "null" {
			db = db.Where("create_time >= ?", date.StartTime)
			db = db.Where("create_time  < ?", date.EndTime)

		} else {
			if req.StartTime != "" {
				db = db.Where("create_time >= ?", req.StartTime+" 00:00:00.000")
			}
			if req.EndTime != "" {
				db = db.Where("create_time <= ? ", req.EndTime+" 23:59:59.999")
			}
		}

		if req.DayStartTime != "" {
			db = db.Where("DATE_FORMAT(create_time, '%H:%i:%s') >= ?", req.DayStartTime)
		}
		if req.DayEndTime != "" {
			db = db.Where("DATE_FORMAT(create_time, '%H:%i:%s') <= ?", req.DayEndTime)
		}

		if req.Num1.Min != "" {
			db = db.Where("num1 >= ?", req.Num1.Min)
		}
		if req.Num1.Max != "" {
			db = db.Where("num1 <= ?", req.Num1.Max)
		}
		if req.Num2.Min != "" {
			db = db.Where("num2 >= ?", req.Num2.Min)
		}
		if req.Num2.Max != "" {
			db = db.Where("num2 <= ?", req.Num2.Max)
		}
		if req.ResultMsg != "" {
			db = db.Where("result_msg like ?", "%"+req.ResultMsg+"%")
		}

		if req.JsonKey != "" && req.JsonValue != "" {
			db = db.Where(fmt.Sprintf(`JSON_EXTRACT(request_parameter,"$.%s") like ? `, req.JsonKey), "%"+req.JsonValue+"%")
		}
		li := []*model.RiskEventRecord{}

		if err = db.Order("create_time desc").Limit(50000).Find(&li).Error; err != nil {
			return "", err
		}
		var file *bytes.Reader
		if req.FileFormat == "xlsx" {
			if file, err = s.NewExcel(req, li); err != nil {
				return "", err
			}
		} else {
			if file, err = s.NewCSV(req, li); err != nil {
				return "", err
			}
		}

		// 获取内容长度
		contentLength := int64(file.Len())
		// 获取内容类型（根据实际文件类型设置）
		contentType := "application/octet-stream"
		// 设置额外的响应头
		extraHeaders := map[string]string{
			"Content-Disposition": fmt.Sprintf(`attachment; filename="%s"`, FileName),
			"Filename":            FileName,
		}
		c.Header("Access-Control-Expose-Headers", "Content-Disposition, Filename,filename, Content-Transfer-Encoding")
		// 返回文件
		c.DataFromReader(http.StatusOK, contentLength, contentType, file, extraHeaders)
	}

	//go s.RunWithErrorReporting(c, Uuid, func() error {
	//
	//	db := db.Doris()1
	//	if req.CheckPoint != "" {
	//		db = db.Where("check_point = ?", req.CheckPoint)
	//	}
	//	if req.Status != nil && len(req.Status) > 0 {
	//		all := false
	//		for _, stastus := range req.Status {
	//			if stastus == "all" {
	//				all = true
	//			}
	//		}
	//		if !all {
	//			db = db.Where("result_code in ?", req.Status)
	//		}
	//	} else {
	//		db = db.Where("result_code != ?", "000")
	//
	//	}
	//	if req.Str1 != "" {
	//		db = db.Where("str1 like ?", "%"+req.Str1+"%")
	//	}
	//	if req.Str2 != "" {
	//		db = db.Where("str2 like ?", "%"+req.Str2+"%")
	//	}
	//	if req.Str3 != "" {
	//		db = db.Where("str3 like ?", "%"+req.Str3+"%")
	//	}
	//	if req.Str4 != "" {
	//		db = db.Where("str4 like ?", "%"+req.Str4+"%")
	//	}
	//	if req.Str5 != "" {
	//		db = db.Where("str5 like ?", "%"+req.Str5+"%")
	//	}
	//	if req.DateType != "null" {
	//		db = db.Where("create_time >= ?", date.StartTime)
	//		db = db.Where("create_time  < ?", date.EndTime)
	//
	//	} else {
	//		if req.StartTime != "" {
	//			db = db.Where("create_time >= ?", req.StartTime+" 00:00:00.000")
	//		}
	//		if req.EndTime != "" {
	//			db = db.Where("create_time <= ? ", req.EndTime+" 23:59:59.999")
	//		}
	//	}
	//
	//	if req.DayStartTime != "" {
	//		db = db.Where("DATE_FORMAT(create_time, '%H:%i:%s') >= ?", req.DayStartTime)
	//	}
	//	if req.DayEndTime != "" {
	//		db = db.Where("DATE_FORMAT(create_time, '%H:%i:%s') <= ?", req.DayEndTime)
	//	}
	//
	//	if req.MinAmount != "" {
	//		db = db.Where("num1 >= ?", req.MinAmount)
	//	}
	//	if req.MaxAmount != "" {
	//		db = db.Where("num1 <= ?", req.MaxAmount)
	//	}
	//	if req.ResultMsg != "" {
	//		db = db.Where("result_msg like ?", "%"+req.ResultMsg+"%")
	//	}
	//
	//	if req.JsonKey != "" && req.JsonValue != "" {
	//		db = db.Where(fmt.Sprintf(`JSON_EXTRACT(request_parameter,"$.%s") like ? `, req.JsonKey), "%"+req.JsonValue+"%")
	//	}
	//	li := []*model.RiskEventRecord{}
	//
	//	if err = db.Order("create_time desc").Limit(50000).Find(&li).Error; err != nil {
	//		return err
	//	}
	//	var file io.Reader
	//	if req.FileFormat == "xlsx" {
	//		if file, err = s.NewExcel(req, li); err != nil {
	//			return err
	//		}
	//	} else {
	//		if file, err = s.NewCSV(req, li); err != nil {
	//			return err
	//		}
	//	}
	//	client, newClientErr := s3.NewClient(c, config.GetConfig().Aws.S3.Bucket, aws.Credential{
	//		Region:          config.GetConfig().Aws.S3.Region,
	//		AccessKeyId:     config.GetConfig().Aws.S3.AccessKeyID,
	//		SecretAccessKey: config.GetConfig().Aws.S3.SecretAccessKey,
	//	})
	//
	//	if newClientErr != nil {
	//		return newClientErr
	//	}
	//
	//	typ := ""
	//	if req.FileFormat == "csv" {
	//		typ = "application/csv"
	//	} else {
	//		typ = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
	//	}
	//
	//	f := &s3.File{
	//		Body:        file,
	//		ContentType: typ,
	//		ExpireAt:    &ExpireTime,
	//	}
	//	filePath := fmt.Sprintf("event/%v/%v", time.Now().Format("20060102"), FileName)
	//
	//	_, uploadErr := client.Upload(c, f, filePath)
	//	if uploadErr != nil {
	//		return uploadErr
	//	}
	//	defer f.Close()
	//
	//	if err := s.ReportSuccess(c, FileName, req.FileFormat, filePath, config.GetConfig().Aws.S3.Region, Uuid); err != nil {
	//		return err
	//	}
	//
	//	return nil
	//})
	return "", nil
}
func (s *AlertAQueryService) RunWithErrorReporting(c *gin.Context, Uuid string, fn func() error) {
	defer func() {
		if err := recover(); err != nil {
			log.Printf("file error")
		}
	}()
	if err := fn(); err != nil {
		log.Printf("error: %v", err)
		s.ReportError(c, Uuid)
	}
}
func (s *AlertAQueryService) NewExcel(req *domain.AlertQueryReportReq, li []*model.RiskEventRecord) (*bytes.Reader, error) {
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("Sheet1")
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	bankingMap := map[string]string{
		"account_id":       "str1",
		"transaction_id":   "str2",
		"transaction_time": "str3",
		"amount":           "num1",
		"status":           "status",
		"rules":            "rules",
		"create_time":      "create_time",
	}
	acquiringMap := map[string]string{
		"account_id":         "str1",
		"account_name":       "str2",
		"reference_id":       "str3",
		"transaction_id":     "str4",
		"transaction_amount": "num1",
		"status":             "status",
		"rules":              "rules",
		"create_time":        "create_time",
	}
	issuningMap := map[string]string{
		"account_id":       "str1",
		"card_number":      "str2",
		"transaction_id":   "str3",
		"transaction_time": "str4",
		"amount":           "num1",
		"status":           "status",
		"rules":            "rules",
		"create_time":      "create_time",
	}
	RiskCode := map[string]map[string]string{
		"CP001_Transaction": {
			"000": "Pass",
			"001": "Decline",
			"002": "Alert",
			"101": "Actived decline",
			"102": "Actived alert",
		},
		"BP002_Payout": {
			"000": "Pass",
			"002": "Alert",
		},
		"BP001_Deposit": {
			"000": "Pass",
			"002": "Alert",
		},
		"CP002_3DS": {
			"000": "Pass",
			"002": "Alert",
			"003": "3ds",
		},
		"AP001_Payment": {
			"000": "Pass",
			"001": "Decline",
			"002": "Alert",
			"101": "Actived decline",
			"102": "Actived alert",
		},
	}
	row1 := sheet.AddRow()

	for _, head := range req.HeadList {
		row1.AddCell().Value = head
	}
	for _, v := range li {
		thisRow := sheet.AddRow()
		for _, s2 := range req.HeadList {
			switch req.CheckPoint[:2] {
			case "CP":
				if _, ok := issuningMap[s2]; ok {
					switch s2 {
					case "create_time":
						thisRow.AddCell().Value = v.CreateTime.Format("2006-01-02 15:04:05")
					case "account_id":
						thisRow.AddCell().Value = v.Str1
					case "card_number":
						thisRow.AddCell().Value = v.Str2
					case "transaction_id":
						thisRow.AddCell().Value = v.Str3
					case "transaction_time":
						thisRow.AddCell().Value = v.Str4
					case "amount":
						thisRow.AddCell().Value = v.Num1.String()
					case "status":
						if m, ok := RiskCode[req.CheckPoint]; ok {
							if status, ok1 := m[v.ResultCode]; ok1 {
								thisRow.AddCell().Value = status
							} else {
								thisRow.AddCell().Value = ""
							}
						} else {
							thisRow.AddCell().Value = ""
						}
					case "rules":
						thisRow.AddCell().Value = v.ResultMsg
					}
				} else {
					thisRow.AddCell().Value = gjson.Get(v.RequestParameter, s2).String()
				}
			case "BP":
				if _, ok := bankingMap[s2]; ok {
					switch s2 {
					case "create_time":
						thisRow.AddCell().Value = v.CreateTime.Format("2006-01-02 15:04:05")
					case "account_id":
						thisRow.AddCell().Value = v.Str1
					case "transaction_id":
						thisRow.AddCell().Value = v.Str2
					case "transaction_time":
						thisRow.AddCell().Value = v.Str3
					case "amount":
						thisRow.AddCell().Value = v.Num1.String()
					case "status":
						if m, ok := RiskCode[req.CheckPoint]; ok {
							if status, ok1 := m[v.ResultCode]; ok1 {
								thisRow.AddCell().Value = status
							} else {
								thisRow.AddCell().Value = ""
							}
						} else {
							thisRow.AddCell().Value = ""
						}
					case "rules":
						thisRow.AddCell().Value = v.ResultMsg
					}
				} else {
					thisRow.AddCell().Value = gjson.Get(v.RequestParameter, s2).String()

				}
			case "AP":
				if _, ok := acquiringMap[s2]; ok {
					switch s2 {
					case "create_time":
						thisRow.AddCell().Value = v.CreateTime.Format("2006-01-02 15:04:05")
					case "account_id":
						thisRow.AddCell().Value = v.Str1
					case "account_name":
						thisRow.AddCell().Value = v.Str2
					case "reference_id":
						thisRow.AddCell().Value = v.Str3
					case "transaction_id":
						thisRow.AddCell().Value = v.Str4
					case "transaction_amount":
						thisRow.AddCell().Value = v.Num1.String()
					case "status":
						if m, ok := RiskCode[req.CheckPoint]; ok {
							if status, ok1 := m[v.ResultCode]; ok1 {
								thisRow.AddCell().Value = status
							}
						}
					case "rules":
						thisRow.AddCell().Value = v.ResultMsg
					}
				} else {
					thisRow.AddCell().Value = gjson.Get(v.RequestParameter, s2).String()
				}
			}

		}

	}

	var buf bytes.Buffer
	if err := file.Write(&buf); err != nil {
		return nil, err
	}
	return bytes.NewReader(buf.Bytes()), nil
}
func (s *AlertAQueryService) NewCSV(req *domain.AlertQueryReportReq, li []*model.RiskEventRecord) (*bytes.Reader, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)

	// 定义字段映射（与原来相同）
	bankingMap := map[string]string{
		"account_id":       "str1",
		"transaction_id":   "str2",
		"transaction_time": "str3",
		"amount":           "num1",
		"status":           "status",
		"rules":            "rules",
		"create_time":      "create_time",
	}
	acquiringMap := map[string]string{
		"account_id":         "str1",
		"account_name":       "str2",
		"reference_id":       "str3",
		"transaction_id":     "str4",
		"transaction_amount": "num1",
		"status":             "status",
		"rules":              "rules",
		"create_time":        "create_time",
	}
	issuningMap := map[string]string{
		"account_id":       "str1",
		"card_number":      "str2",
		"transaction_id":   "str3",
		"transaction_time": "str4",
		"amount":           "num1",
		"status":           "status",
		"rules":            "rules",
		"create_time":      "create_time",
	}
	RiskCode := map[string]map[string]string{
		"CP001_Transaction": {
			"000": "Pass",
			"001": "Decline",
			"002": "Alert",
			"101": "Actived decline",
			"102": "Actived alert",
		},
		"BP002_Payout": {
			"000": "Pass",
			"002": "Alert",
		},
		"BP001_Deposit": {
			"000": "Pass",
			"002": "Alert",
		},
		"CP002_3DS": {
			"000": "Pass",
			"002": "Alert",
			"003": "3ds",
		},
		"AP001_Payment": {
			"000": "Pass",
			"001": "Decline",
			"002": "Alert",
			"101": "Actived decline",
			"102": "Actived alert",
		},
	}

	// 写入表头
	if err := writer.Write(req.HeadList); err != nil {
		return nil, err
	}

	// 写入数据行
	for _, v := range li {
		var record []string

		for _, header := range req.HeadList {
			var cellValue string
			switch req.CheckPoint[:2] {
			case "CP":
				if _, ok := issuningMap[header]; ok {
					switch header {
					case "create_time":
						cellValue = v.CreateTime.Format("2006-01-02 15:04:05")
					case "account_id":
						cellValue = v.Str1
					case "card_number":
						cellValue = v.Str2
					case "transaction_id":
						cellValue = v.Str3
					case "transaction_time":
						cellValue = v.Str4
					case "amount":
						cellValue = v.Num1.String()
					case "status":
						if m, ok := RiskCode[req.CheckPoint]; ok {
							if status, ok1 := m[v.ResultCode]; ok1 {
								cellValue = status
							}
						}
					case "rules":
						cellValue = v.ResultMsg
					}
				} else {
					cellValue = gjson.Get(v.RequestParameter, header).String()
				}
			case "BP":
				if _, ok := bankingMap[header]; ok {
					switch header {
					case "create_time":
						cellValue = v.CreateTime.Format("2006-01-02 15:04:05")
					case "account_id":
						cellValue = v.Str1
					case "transaction_id":
						cellValue = v.Str2
					case "transaction_time":
						cellValue = v.Str3
					case "amount":
						cellValue = v.Num1.String()
					case "status":
						if m, ok := RiskCode[req.CheckPoint]; ok {
							if status, ok1 := m[v.ResultCode]; ok1 {
								cellValue = status
							}
						}
					case "rules":
						cellValue = v.ResultMsg
					}
				} else {
					cellValue = gjson.Get(v.RequestParameter, header).String()
				}
			case "AP":
				if _, ok := acquiringMap[header]; ok {
					switch header {
					case "create_time":
						cellValue = v.CreateTime.Format("2006-01-02 15:04:05")
					case "account_id":
						cellValue = v.Str1
					case "account_name":
						cellValue = v.Str2
					case "reference_id":
						cellValue = v.Str3
					case "transaction_id":
						cellValue = v.Str4
					case "transaction_amount":
						cellValue = v.Num1.String()
					case "status":
						if m, ok := RiskCode[req.CheckPoint]; ok {
							if status, ok1 := m[v.ResultCode]; ok1 {
								cellValue = status
							}
						}
					case "rules":
						cellValue = v.ResultMsg
					}
				} else {
					cellValue = gjson.Get(v.RequestParameter, header).String()
				}
			default:
				cellValue = gjson.Get(v.RequestParameter, header).String()

			}

			record = append(record, cellValue)
		}

		if err := writer.Write(record); err != nil {
			return nil, err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, err
	}
	return bytes.NewReader(buf.Bytes()), nil
}
func (s *AlertAQueryService) ReportSuccess(c *gin.Context, fileName string, format string, filePath string, region string, uuid string) error {
	return s.IRiskFileExtra.ReportSuccess(c, fileName, format, filePath, region, uuid)

}
func (s *AlertAQueryService) ReportError(c *gin.Context, uuid string) error {
	return s.IRiskFileExtra.ReportError(c, uuid, &model.RmsFileExtra{UpdateTime: time.Now(), FileStatus: -1})
}
