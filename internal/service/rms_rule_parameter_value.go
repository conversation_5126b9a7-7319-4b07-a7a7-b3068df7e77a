package service

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/datautil"
	"go.uber.org/zap"
)

type RuleParameterValueService struct {
	Logger                 *zap.Logger
	RuleParameterValueRepo IRuleParameterValueRepo
	RuleParameterGroup     IRuleParameterGroupRepo
	RuleParameter          IRuleParameterRepo
}

func (s *RuleParameterValueService) Create(c *gin.Context, cp *model.RmsRuleParameterValue) error {
	return s.RuleParameterValueRepo.Create(c, cp)
}

func (s *RuleParameterValueService) Update(c *gin.Context, id int64, cp *model.RmsRuleParameterValue) error {
	return s.RuleParameterValueRepo.Update(c, id, cp)
}

// UpdateMultiple 虽然名字叫 Multiple，实际上是设置一个参数绑定。视不同类型可能一个绑定会关联一个数组或 map，所以叫 Multiple。
// 对 规则参数关联的增删改查都是这个口，同时还要维护 RuleParameterGroup.ParamIds
func (s *RuleParameterValueService) UpdateMultiple(c *gin.Context, item *domain.RmsRuleParameterValueUpdateMultiple) (*domain.RmsRuleParameterValueUpdateMultipleResp, error) {
	rp, err := s.RuleParameter.Retrieve(c, item.RuleParamID)
	if err != nil {
		return nil, err
	}
	// 检查参数
	switch domain.RuleParameterParamType(rp.ParamType) {
	case domain.RuleParameterParamType_STRING, domain.RuleParameterParamType_NUMBER,
		domain.RuleParameterParamType_LIST,
		domain.RuleParameterParamType_NUMBER_RANGE, domain.RuleParameterParamType_IP_RANGE:
		// 只有 value 值
		seen := map[string]bool{}
		for _, v := range item.RuleParameterValueList {
			if v.ParamValue == "" {
				return nil, er.InvalidArgument.WithStack().WithMsg("Parameter value cannot be empty.")
			}
			if seen[v.ParamValue] {
				return nil, er.InvalidArgument.WithStack().WithMsgf("Parameter value cannot be repeated: %s", v.ParamValue)
			}
			seen[v.ParamValue] = true
		}
	case domain.RuleParameterParamType_MAP, domain.RuleParameterParamType_BANK_CARD_LIST:
		// 校验 key 和 value
		seen := map[string]bool{}
		for _, v := range item.RuleParameterValueList {
			if v.ParamKey == "" {
				return nil, er.InvalidArgument.WithStack().WithMsg("Parameter key cannot be empty.")
			}
			if v.ParamValue == "" {
				return nil, er.InvalidArgument.WithStack().WithMsg("Parameter value cannot be empty.")
			}
			if seen[v.ParamKey] {
				return nil, er.InvalidArgument.WithStack().WithMsgf("Parameter key cannot be repeated: %s", v.ParamValue)
			}
			seen[v.ParamKey] = true
		}
	}
	// 维护 RuleParameterGroup.ParamIds
	rpg, err := s.RuleParameterGroup.Retrieve(c, item.RuleParamGroupID)
	if err != nil {
		return nil, err
	}
	ParamIds := datautil.NewCommaSeparated(rpg.ParamIds)
	// item.RuleParameterValueList 为空是移除，不为空是添加
	if len(item.RuleParameterValueList) > 0 {
		ParamIds.AddInt64(item.RuleParamID)
	} else {
		ParamIds.RemoveInt64(item.RuleParamID)
	}
	if err := s.RuleParameterGroup.SetParamIds(c, item.RuleParamGroupID, ParamIds.Marshal()); err != nil {
		return nil, err
	}

	// 删除已存在的绑定
	err = s.RuleParameterValueRepo.DeleteByParamIDAndGroupID(c, item.RuleParamID, item.RuleParamGroupID)
	if err != nil {
		return nil, err
	}

	// 更新或插入
	for _, v := range item.RuleParameterValueList {
		m := &model.RmsRuleParameterValue{
			ParamID:     item.RuleParamID,
			GroupID:     item.RuleParamGroupID,
			ParamKey:    v.ParamKey,
			ParamValue:  v.ParamValue,
			Description: v.Description,
		}
		err := s.RuleParameterValueRepo.Create(c, m)
		if err != nil {
			return nil, err
		}
	}
	// 刷新，返回前端最新数据
	li, err := s.RuleParameterValueRepo.AllByParamIDAndGroupID(c, item.RuleParamID, item.RuleParamGroupID)
	if err != nil {
		return nil, err
	}
	resp := new(domain.RmsRuleParameterValueUpdateMultipleResp)
	resp.ItemList = []*domain.RmsRuleParameterValueUpdateMultipleRespItem{}
	for _, v := range li {
		r := new(domain.RmsRuleParameterValueUpdateMultipleRespItem)
		r.ID = v.ID
		r.Description = v.Description
		r.ParamKey = v.ParamKey
		r.ParamValue = v.ParamValue
		resp.ItemList = append(resp.ItemList, r)
	}
	return resp, nil
}

func (s *RuleParameterValueService) Delete(c *gin.Context, id int64) error {
	return s.RuleParameterValueRepo.Delete(c, id)
}

func (s *RuleParameterValueService) List(c *gin.Context, req *domain.RuleParameterValueList) (*domain.PageData, error) {
	li, err := s.RuleParameterValueRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.RuleParameterValueRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *RuleParameterValueService) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterValue, error) {
	return s.RuleParameterValueRepo.Retrieve(c, id)
}
