package service

import (
	"encoding/json"
	mapset "github.com/deckarep/golang-set/v2"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"strings"
)

type FilterFieldService struct {
	Logger          *zap.Logger
	FilterFieldRepo IFilterFieldRepo
	PolicyRepo      IPolicyRepo
	CheckPointRepo  ICheckPointRepo
}

type FilterFieldFieldEnum struct {
	Code  string
	Label string
}

func (s *FilterFieldService) Check(c *gin.Context, cp *model.RmsFilterField) error {
	if cp.FieldEnum == "" || strings.Contains(cp.FieldEnum, `""`) {
		return er.InvalidArgument.WithStack().WithMsg("Enumeration fields can not be empty")
	}
	li := []*FilterFieldFieldEnum{}
	if err := json.Unmarshal([]byte(cp.FieldEnum), &li); err != nil {
		return er.WSEF(err, zap.String("FieldEnum", cp.FieldEnum))
	}
	exist := []*FilterFieldFieldEnum{}
	for _, v := range li {
		for _, x := range exist {
			if v.Code == x.Code {
				return er.InvalidArgument.WithStack().WithMsg("Enumeration fields can not be repeated")
			}
		}
		exist = append(exist, v)
	}
	return nil
}

func (s *FilterFieldService) Create(c *gin.Context, cp *model.RmsFilterField) error {
	if err := s.Check(c, cp); err != nil {
		return err
	}
	return s.FilterFieldRepo.Create(c, cp)
}

func (s *FilterFieldService) Update(c *gin.Context, id int64, mod *model.RmsFilterField) error {
	if err := s.Check(c, mod); err != nil {
		return err
	}

	// 获取被更改的枚举值。因为后端无法对枚举值的跟踪，删除在新数据中不存在的枚举值
	enumSet := mapset.NewSet[string]()
	f, err := mod.GetFieldEnum()
	if err != nil {
		return err
	}
	enumSet.Append(f.AllCode()...)

	// 处理规则组的绑定
	PolicyLi, err := s.PolicyRepo.FindByCPCode(c, mod.CheckPoint)
	if err != nil {
		return err
	}
	// 在规则组存在时更改规则组中的信息
	for _, Policy := range PolicyLi {
		filters, err := Policy.GetFilters()
		if err != nil {
			return err
		}

		filterItem := filters[mod.FieldName]
		newfilterItem := []string{}
		for _, v := range filterItem {
			if enumSet.Contains(v) {
				newfilterItem = append(newfilterItem, v)
			}
		}
		if len(newfilterItem) == 0 {
			delete(filters, mod.FieldName)
		} else {
			filters[mod.FieldName] = newfilterItem
		}
		err = Policy.SetFilters(filters)
		if err != nil {
			return err
		}
		err = s.PolicyRepo.Update(c, Policy.ID, Policy)
		if err != nil {
			return err
		}
	}

	return s.FilterFieldRepo.Update(c, id, mod)
}

func (s *FilterFieldService) List(c *gin.Context, req *domain.FilterFieldList) (*domain.PageData, error) {
	li, err := s.FilterFieldRepo.List(c, req)
	if err != nil {
		return nil, err
	}
	count, err := s.FilterFieldRepo.Count(c, req)
	if err != nil {
		return nil, err
	}
	return domain.FillInPageResponseData(req.PageSearch, count, li), nil
}

func (s *FilterFieldService) Retrieve(c *gin.Context, id int64) (*model.RmsFilterField, error) {
	return s.FilterFieldRepo.Retrieve(c, id)
}

func (s *FilterFieldService) RetrieveByName(c *gin.Context, req *domain.FilterFieldRetrieve) (*model.RmsFilterField, error) {
	return s.FilterFieldRepo.RetrieveByName(c, req.CheckPoint, req.FieldName)
}
