package domain

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type AlertQueryListReq struct {
	CheckPoint string             `json:"check_point"binding:"required"` //接入点code
	Status     []string           `json:"status"`
	Str1       string             `json:"str_1"`
	Str2       string             `json:"str_2"`
	Str3       string             `json:"str_3"`
	Str4       string             `json:"str_4"`
	Str5       string             `json:"str_5"`
	Num1       AlertQueryNumRange `json:"num1"`
	Num2       AlertQueryNumRange `json:"num2"`
	StartTime  string             `json:"start_time"`
	EndTime    string             `json:"end_time"`
	DateType   string             `json:"date_type" binding:"oneof=year month week today null"`

	DayStartTime string `json:"day_start_time"` //15:14:00
	DayEndTime   string `json:"day_end_time"`   //15:16:00

	ResultMsg string `json:"result_msg"`
	JsonKey   string `json:"json_key"`
	JsonValue string `json:"json_value"`

	PageSearch
}
type AlertQueryNumRange struct {
	Min string `json:"min"`
	Max string `json:"max"`
}

type AlertQueryReportReq struct {
	CheckPoint string   `json:"check_point"binding:"required"` //接入点code
	Status     []string `json:"status"`
	Str1       string   `json:"str_1"`
	Str2       string   `json:"str_2"`
	Str3       string   `json:"str_3"`
	Str4       string   `json:"str_4"`
	Str5       string   `json:"str_5"`

	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	DateType  string `json:"date_type" binding:"oneof=year month week today null"`

	DayStartTime string `json:"day_start_time"` //15:14:00
	DayEndTime   string `json:"day_end_time"`   //15:16:00

	Num1      AlertQueryNumRange `json:"num1"`
	Num2      AlertQueryNumRange `json:"num2"`
	ResultMsg string             `json:"result_msg"`
	JsonKey   string             `json:"json_key"`
	JsonValue string             `json:"json_value"`

	HeadList   []string `json:"head_list"`                           //列表list
	FileFormat string   `json:"file_format"binding:"oneof=csv xlsx"` //文件类型
}

// data *domain.AlertQueryListReq) ([]*model.RiskEventRecord,
type AlertQueryListRes struct {
	ItemList  []*model.RiskEventRecord `json:"item_list"`
	TotalItem int                      `json:"total_item"`
	PageSearch
}
type AlertQueryStatisticsReq struct {
	CheckPoint string `json:"check_point"binding:"required"` //接入点code
	StartTime  string `json:"start_time"`
	EndTime    string `json:"end_time"`
	DateType   string `json:"date_type" binding:"oneof=year month week today null"`
}
type AlertQueryStatisticsRes struct {
	List []*AlertQueryStatistics `json:"list"`
}
type AlertQueryStatistics struct {
	Code              string `json:"code"`                //codeName
	NumberOfTriggers  int64  `json:"number_of_triggers"`  //触发数
	TriggerRate       string `json:"trigger_rate"`        //触发率
	TriggerLinkRatio  string `json:"trigger_link_ratio"`  //触发环比
	TriggerLinkSymbol bool   `json:"trigger_link_symbol"` //触发环比符号
}
type AlertQueryDateRange struct {
	StartTime     string
	EndTime       string
	LastStartTime string
	LastEndTime   string
}
type AlertQueryGetReq struct {
	CheckPoint string `json:"check_point"binding:"required"` //接入点code
	Uuid       string `json:"uuid"`
}
type AlertQueryGetRes struct {
	Uuid       string `json:"uuid"`        //返回业务线请求的UUID，无需区分类型
	Id         uint64 `json:"id"`          //风控系统生成的唯一事件ID
	EventTime  string `json:"event_time"`  //风控事件触发时间（采用国际标准时间，精确到毫秒）
	CheckPoint string `json:"check_point"` //各业务线接入点编码（如 CP001_Transaction、BP002_Payout）,在开发规则时设计。
	ResultCode string `json:"result_code"` //风控决策结果码（如 000=Pass, 001=Decline, 002=Alert）,在开发规则时设计。
	ResultMsg  string `json:"result_msg"`  //风控决策结果信息，rules完整内容包括阈值。命中多条时，返回多条。
	FailedRule string `json:"failed_rule"` //风控决策命中的rule信息，原样返回，非json数据，业务端需要注意展示问题。result_code+rule_code
}
