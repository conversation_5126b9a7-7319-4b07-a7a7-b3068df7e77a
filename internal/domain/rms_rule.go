package domain

import "time"

type RuleList struct {
	TimeAndPageSearch
	RuleName   string `example:"黑名单" json:"rule_name"` // 规则描述
	RuleNo     string `json:"rule_no"`
	CheckPoint string `example:"CP003" json:"access_point"` // 检查点
}

type RuleCreate struct {
	RuleNo       string        `example:"Cp003BlackListCheck" json:"rule_code" binding:"required"`          // 规则编号
	RuleName     string        `example:"黑名单" json:"rule_name" binding:"required"`                       // 规则名
	CheckPoint   string        `example:"CP003" json:"access_point" binding:"required"`                     //  接入点
	ShortCircuit bool          `example:"false" json:"short_circuit"`                                       // 是否支持短路
	Memo         *string       `example:"黑名单" json:"remark"`                                             // 备注
	RuleContent  string        `example:"end" json:"rule_content" binding:"required"`                       // 规则内容
	DeployMethod string        `example:"manual" json:"deploy_method" binding:"required,oneof=manual auto"` // 上下线方式，manual/auto
	StartTime    *NullableTime `example:"2023-06-07T17:07:20+08:00" json:"effective_time"`                  // 生效时间
	EndTime      *NullableTime `example:"2023-06-07T17:07:20+08:00" json:"expired_time"`                    // 失效时间
	Status       string        `example:"Online" json:"status" binding:"oneof=Online Offline"`              // 状态，Online/Offline
	Priority     int           `json:"priority" binding:"required"`
	//  RuleGroup      *string    `example:"CMF" json:"rule_group"`                            // 规则组，即策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup
	//	RuleType       string     `example:"Remote" json:"rule_type"`                          // 规则类型
	//	Params         *string    `example:"" json:"params"`                                   // 参数，注意与规则参数区分
	//	EngineType     string     `example:"Stateless" json:"engine_type"`                     // 引擎类型，有状态/无状态
	//	EngineInstance *string    `example:"StatefulEngine01" json:"engine_instance"`          // 有状态类型情况下选择的引擎实例
}

type RuleUpdate struct {
	ID int64 `json:"id" example:"1" binding:"required,gte=0"`
	RuleCreate
}

type RuleResp struct {
	SyncedAt *time.Time `json:"synced_at"`
	RuleUpdate
}

//
//// CalOnlineStatus 计算 RuleResp 状态
//func (d *RuleResp) CalExpiredStatus() string {
//	if d.Status == "Offline" {
//		return "Expired"
//	}
//	newTime := time.Now()
//	// 如果没设置 开始时间，或开始时间小于当前时间
//	// 如果没设置 失效时间，或结束时间小于当前时间
//	// 则在生效时间内
//	if (d.StartTime == nil || time.Time(*d.StartTime).Before(newTime)) && (d.EndTime == nil || time.Time(*d.EndTime).After(newTime)) {
//		return "Normal"
//	}
//	return "NotActive"
//}

type RuleListResp struct {
	PageData
	ItemList []*RuleResp `json:"item_list"`
}

// RuleCompile 编译
type RuleCompile struct {
	RuleContent string `json:"rule_content" validate:"required"` // 规则内容
	RuleCode    string `json:"rule_code" validate:"required"`
	RuleName    string `json:"rule_name" validate:"required"`
	Priority    int    `json:"priority" validate:"required"`
}

type GatewayRuleCompileResp struct {
	Success         bool   `json:"success"`
	ResponseCode    string `json:"responseCode"`
	ResponseMessage string `json:"responseMessage"`
	Body            string `json:"body"`
}

type RuleSetStatus struct {
	ID     int64  `example:"1" json:"id" binding:"required"`
	Status string `example:"Online" json:"status" binding:"oneof=Online Offline"`
}
