package domain

import (
	"time"
)

type FilterExtraListReq struct {
	PageSearch
	FileName       string `json:"file_name"` // 字段名
	CreateTime     string `json:"create_time"`
	StartEventTime string `json:"start_event_time"`
	EndEventTime   string `json:"end_event_time"`
}
type FilterExtraRetrieveRes struct {
	FileId         string    `json:"file_id" gorm:"file_id"`                   // uuid
	FileName       string    `json:"file_name" gorm:"file_name"`               // 文件名
	FileType       int64     `json:"file_type" gorm:"file_type"`               // 1000 event
	CreatorId      string    `json:"creator_id" gorm:"creator_id"`             // 创建人id
	CreatorName    string    `json:"creator_name" gorm:"creator_name"`         // 创建人名
	CreateTime     time.Time `json:"create_time" gorm:"create_time"`           // 创建时间
	UpdateTime     time.Time `json:"update_time" gorm:"update_time"`           // 修改时间（文件生成成功时间）
	ExpireTime     time.Time `json:"expire_time" gorm:"expire_time"`           // 文件过期时间默认30天
	FileParam      string    `json:"file_param" gorm:"file_param"`             // 文件参数
	FileStatus     int8      `json:"file_status" gorm:"file_status"`           // -1=failed, 0=pending,1=success
	StartEventTime time.Time `json:"start_event_time" gorm:"start_event_time"` // 开始时间
	EndEventTime   time.Time `json:"end_event_time" gorm:"end_event_time"`     // 结束时间
	FileFormat     string    `json:"file_format" gorm:"file_format"`           // 文件类型 csv excel pdf
	FilePath       string    `json:"file_path" gorm:"file_path"`               // 文件存储路径
	Region         string    `json:"region" gorm:"region"`                     // 文件区域
	ExtraId        string    `json:"extra_id" gorm:"extra_id"`                 // 唯一关联ID
}

type FilterExtraList struct {
	FileName       string    `json:"file_name"`        // 文件名
	CreateTime     time.Time `json:"create_time"`      // 创建时间
	UpdateTime     time.Time `json:"update_time"`      // 修改时间（文件生成成功时间）
	StartEventTime time.Time `json:"start_event_time"` // 开始时间
	EndEventTime   time.Time `json:"end_event_time"`   // 结束时间
	Status         int8      `json:"status"`           //0:生成中 -1:失败 1:成功 2:过期
	Field          string    `json:"field"`
	ExpireTime     time.Time `json:"expire_time"`  //过期时间
	CreatorId      string    `json:"creator_id"`   //创建id
	CreatorName    string    `json:"creator_name"` //创建人姓名
	FileId         string    `json:"file_id"`      // uuid
}
type FilterExtraListRes struct {
	ItemList  []*FilterExtraList `json:"item_list"`
	TotalItem int                `json:"total_item"`
}
type FilterExtraUrlReq struct {
	FileId string `json:"file_id"`
}
type FilterExtraUrlRes struct {
	FileUrl string `json:"file_url"`
}
