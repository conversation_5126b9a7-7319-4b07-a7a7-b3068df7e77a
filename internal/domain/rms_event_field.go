package domain

type EventFieldList struct {
	PageSearch
	CheckPoint string `example:"CP003" json:"access_point"` // 归属检查点（编码）
	FieldName  string `example:"orderSn" json:"field_name"` // 字段名
}
type NotEventStoreCfgList struct {
	CheckPoint string `example:"CP003" json:"access_point"` // 归属检查点（编码）
}

type EventFieldCreate struct {
	CheckPoint string  `example:"CP003" json:"access_point" binding:"required"` // 归属检查点（编码）
	FieldName  string  `example:"orderSn" json:"name_item" binding:"required"`  // 字段名
	FieldType  string  `example:"String" json:"item_type" binding:"required"`   // 字段类型，如String
	Memo       *string `example:"订单号" json:"remark"`                            // 备注
	Required   bool    `example:"true" json:"required"`                         // 是否必填
	Primary    bool    `example:"false" json:"primary_key"`                     // 是否为主键
	Filter     bool    `example:"false" json:"filter"`                          // 是否为过滤字段
	//InnerFieldName    *string `example:"" json:"inner_field_name"`                                           // 内部字段名
	//DefaultValue      *string `example:"" json:"default_value"`                                              // 默认值
	//RequiredCondition string  `example:"{\"fieldName\":\"\",\"fieldValue\":\"\"}" json:"required_condition"` // 必填条件，依赖于其它字段
}

type EventFieldUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	EventFieldCreate
}

type EventFieldCheckNext struct {
	CheckPoint string `example:"CP003" json:"access_point" binding:"required"` // 归属检查点（编码）
}

type EventFieldResp struct {
	Filter bool `example:"false" json:"filter"` // 是否为过滤字段 TODO 需要额外逻辑
	EventFieldUpdate
}

type EventFieldListResp struct {
	PageData
	ItemList []*EventFieldResp `json:"item_list"`
}
type NotEventStoreCfgListResp struct {
	List []string `json:"list"`
}
