package domain

import "time"

type BlacklistList struct {
	RiskKey   string  `example:"TRADE_IP" json:"blacklist_type" binding:"omitempty,oneof=COMPANY_NAME COMPANY_UEN COMPANY_ADDRESS INDIVIDUAL_NAME INDIVIDUAL_NATIONAL_ID INDIVIDUAL_PASSPORT INDIVIDUAL_DRIVERS_LICENSE RESIDENTIAL_ADDRESS MOBILE EMAIL_DOMAIN EMAIL_ADDRESS CUSTOMER_COUNTRY"` // 键，BANK_CARD_NO/TRADE_IP
	RiskValue string  `example:"***********" json:"blacklist_value" binding:"omitempty"`                                                                                                                                                                                                         // 值，如卡号/ip
	Remark    *string `example:"test" json:"remark"`                                                                                                                                                                                                                                             // 备注
	PageSearch
}

//COMPANY_NAME - 公司名称
//COMPANY_UEN
//COMPANY_ADDRESS - 公司地址
//
//INDIVIDUAL_NAME - 全名
//INDIVIDUAL_NATIONAL_ID - 个人身份证ID
//INDIVIDUAL_PASSPORT - 个人护照号
//INDIVIDUAL_DRIVERS_LICENSE
//RESIDENTIAL_ADDRESS - 居住地址
//
//MOBILE - 手机号
//EMAIL_DOMAIN - 电子邮件域名
//EMAIL_ADDRESS - 电子邮件地址
//
//CUSTOMER_COUNTRY----客户国家

type BlacklistCreate struct {
	RiskKey   string  `example:"TRADE_IP" json:"blacklist_type" binding:"oneof=COMPANY_NAME COMPANY_UEN COMPANY_ADDRESS INDIVIDUAL_NAME INDIVIDUAL_NATIONAL_ID INDIVIDUAL_PASSPORT INDIVIDUAL_DRIVERS_LICENSE RESIDENTIAL_ADDRESS MOBILE EMAIL_DOMAIN EMAIL_ADDRESS CUSTOMER_COUNTRY"` // 键，BANK_CARD_NO/TRADE_IP
	RiskValue string  `example:"***********" json:"blacklist_value" binding:"required"`                                                                                                                                                                                                // 值，如卡号/ip
	Remark    *string `example:"test" json:"remark"`                                                                                                                                                                                                                                   // 备注
}

type BlacklistUpdate struct {
	RcID int64 ` json:"rc_id" example:"1" binding:"required,gte=0"`
	BlacklistCreate
}

type BlacklistResp struct {
	BlacklistUpdate
	CreatedAt time.Time `json:"time"`
}

type BlacklistListResp struct {
	PageData
	ItemList []*BlacklistResp `json:"item_list"`
}
