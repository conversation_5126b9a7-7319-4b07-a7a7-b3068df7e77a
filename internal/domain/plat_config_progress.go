package domain

type ConfigProgressList struct {
	PageSearch
	Code  string `example:"CP003" json:"code"`      // 编码
	Label string `example:"CP003-CMF" json:"label"` // 名称
}

type ConfigProgressCreate struct {
	CheckPointCode string `example:"CP003" json:"access_point"`
	Progress       int32  `example:"1" json:"progress"`
}

type ConfigProgressUpdate struct {
	ID int64 ` json:"id" binding:"required"`
	ConfigProgressCreate
}

type ConfigProgressResp struct {
	BusinessType       int64   `example:"1000" json:"business_type"`          // 业务类型配置，用于对接入业务（事件）的细分
	FilterFields       *string `example:"[]" json:"filter_fields"`            // 过滤字段配置，区分更细化的业务数据（policy选取有关）
	DefaultPkFieldName *string `example:"orderSn" json:"default_primary_key"` // 默认主键字段，与filterField相关
	ConfigProgressUpdate
}

type ConfigProgressListResp struct {
	PageData
	ItemList []*ConfigProgressResp `json:"item_list"`
}

type ConfigProgressNameLabel struct {
	Code  string `json:"code"`
	Label string `json:"name"`
}
