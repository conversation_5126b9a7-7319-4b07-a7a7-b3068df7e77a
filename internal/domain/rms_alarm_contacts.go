package domain

type AlarmContactList struct {
	TimeAndPageSearch
}

type AlarmContactCreate struct {
	Name              string  `json:"name" example:"联系人名"`
	Mobile            *string `json:"mobile" example:"13333333333"`
	Email             *string `json:"email" example:"a@a.c"`
	Status            int32   `json:"status" example:"1"`
	AlarmTimeConfigID *int64  `json:"alarm_time_config_id"`
	NoticeTime        string  `json:"notice_time"`
	OpenID            *string `json:"open_id" example:"1"`
}

type AlarmContactUpdate struct {
	ID int64 `json:"id" example:"1"  binding:"required,gte=0"`
	AlarmContactCreate
}
