package domain

type AlarmTimeConfList struct {
	TimeAndPageSearch
}

type AlarmTimeConfCreate struct {
	ResultCode string  `json:"result_code"` // ResultCode
	Status     int32   `json:"status"`      // 状态
	StartTime  *string `json:"start_time"`  // 开始时间
	EndTime    int32   `json:"end_time"`    // 截止时间
	OpenID     *string `json:"open_id"`     // 操作员
}

type AlarmTimeConfUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	AlarmTimeConfCreate
}
