package domain

import (
	"encoding/json"
	"math"
	"time"
)

func FillInPageResponseData(req PageSearch, count int64, data any) *PageData {
	return &PageData{
		TotalNum:  count,
		TotalPage: int64(math.Ceil(float64(count) / float64(req.PageSize))),
		ItemList:  data,
	}
}

type IsZeroer interface {
	IsZero() bool
}

func NullableTime2Time(nt *NullableTime) *time.Time {
	if nt == nil {
		return nil
	}
	t := time.Time(*nt)
	return &t
}
func Time2NullableTime(t *time.Time) *NullableTime {
	if t == nil {
		return nil
	}
	n := NullableTime(*t)
	return &n
}

// NullableTime 前端可以传空字符串的 time.Time
type NullableTime time.Time

// MarshalJSON 自定义 JSON 序列化
func (nt NullableTime) MarshalJSON() ([]byte, error) {
	if time.Time(nt).IsZero() {
		return []byte(`""`), nil
	}
	return json.Marshal(time.Time(nt))
}

// UnmarshalJSON 自定义 JSON 反序列化
func (nt *NullableTime) UnmarshalJSON(data []byte) error {
	if string(data) == `""` {
		*nt = NullableTime(time.Time{})
		return nil
	}

	// 解析时间
	t := time.Time{}
	if err := json.Unmarshal(data, &t); err != nil {
		return err
	}
	*nt = NullableTime(t)
	return nil
}
