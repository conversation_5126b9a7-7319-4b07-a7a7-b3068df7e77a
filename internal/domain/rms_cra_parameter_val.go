package domain

type CraParameterValCreate struct {
	CraFrameworkId string `json:"cra_framework_id" binding:"required"`             // 唯一ID
	ParamValName   string `json:"param_val_name" binding:"required"`               // 参考名
	Description    string `json:"description"`                                     // 描述
	NoInformation  int8   `json:"no_information" binding:"required,oneof=1 2 3 4"` // 无数据结果：0=prohibit, 1=high, 2=medium, 3=low
	HitFactor      string `json:"hit_factor" binding:"required"`                   // 命中结果：JSON参数
	NoHitMatch     int8   `json:"no_hit_match"  binding:"required,oneof=1 2 3 4"`  // 没命中结果：0=prohibit, 1=high, 2=medium, 3=low
	Status         int8   `json:"status"  binding:"required,oneof=1 0 -1""`        // 状态：1=active, 0=默认, -1=inactive
}
type CraParameterValUpdate struct {
	CraFrameworkId string `json:"cra_framework_id" binding:"required"`             // 唯一ID
	ParamValId     string `json:"param_val_id" binding:"required"`                 // 参考id
	Description    string `json:"description"`                                     // 描述
	NoInformation  int8   `json:"no_information" binding:"required,oneof=1 2 3 4"` // 无数据结果：0=prohibit, 1=high, 2=medium, 3=low
	HitFactor      string `json:"hit_factor" binding:"required"`                   // 命中结果：JSON参数
	NoHitMatch     int8   `json:"no_hit_match"  binding:"required,oneof=1 2 3 4"`  // 没命中结果：0=prohibit, 1=high, 2=medium, 3=low
	Status         int8   `json:"status"  binding:"required,oneof=1 0 -1"`         // 状态：1=active, 0=默认, -1=inactive

}
type CraParameterValList struct {
	Status         int8   `json:"status"  binding:"oneof=1 0 -1"`      // 状态：1=active, 0=默认, -1=inactive
	CraFrameworkId string `json:"cra_framework_id" binding:"required"` // 唯一ID
}
