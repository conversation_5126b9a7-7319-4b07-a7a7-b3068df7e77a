package domain

type RuleParameterParamType string

const (
	RuleParameterParamType_STRING         = "STRING"
	RuleParameterParamType_NUMBER         = "NUMBER"
	RuleParameterParamType_LIST           = "LIST"
	RuleParameterParamType_MAP            = "MAP"
	RuleParameterParamType_NUMBER_RANGE   = "NUMBER_RANGE"
	RuleParameterParamType_IP_RANGE       = "IP_RANGE"
	RuleParameterParamType_BANK_CARD_LIST = "BANK_CARD_LIST"
)

// 规则参数
type RuleParameterList struct {
	PageSearch
	CheckPoint  string `example:"CP003" json:"access_point"`                  // 检查点(可多个,逗号分割)
	ParamType   string `example:"STRING" json:"parameter_type"`               // 参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes
	Code        string `example:"CP003_DEFAULT_ACTION" json:"parameter_code"` // 参数编码
	Description string `example:"默认操作" json:"parameter_description"`          // 参数描述
}

type RuleParameterCreate struct {
	CheckPoint    string  `example:"CP003" json:"access_point_list"  binding:"required"`                                                       // 检查点(可多个,逗号分割)
	ParamType     *string `example:"STRING" json:"parameter_type" binding:"oneof=STRING NUMBER LIST MAP NUMBER_RANGE IP_RANGE BANK_CARD_LIST"` // 参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes
	Code          string  `example:"CP003_DEFAULT_ACTION" json:"parameter_code"  binding:"required"`                                           // 参数编码
	Description   string  `example:"默认操作" json:"parameter_description"`                                                                        // 参数描述
	ValueTransfer *string `example:"" json:"value_transfer"`                                                                                   // 值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer
	//AccessFlag    *string `example:"" json:"access_flag"`                                                                                               // 允许接口添加值，默认值apex
}

type RuleParameterUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	RuleParameterCreate
}

type RuleParameterResp struct {
	RuleParameterUpdate
}

type RuleParameterListResp struct {
	PageData
	ItemList []*RuleParameterResp `json:"item_list"`
}
