package domain

import "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"

type CheckPointList struct {
	PageSearch
	Code  string `example:"CP003" json:"code"`      // 编码
	Label string `example:"CP003-CMF" json:"label"` // 名称
}

type CheckPointCreate struct {
	Code           string  `example:"CP003" json:"code" binding:"required,alpha_num_underline"`     // 编码
	Label          string  `example:"CP003-CMF" json:"name" binding:"required,alpha_num_underline"` // 名称
	CheckDuplicate *bool   `example:"true" json:"check_duplicate"`                                  // 数据重复检查
	Memo           *string `example:"test" json:"remark"`                                           // 备注
	AlwaysRun      int     ` json:"always_run"`
	//PreActions    *string `example:"[]" json:"pre_actions"`                                                                                       // 事件预处理配置，如根据ip获取省市
	//VoucherConfigs     *string `example:"" json:"voucher_configs"`                                                                                     // 凭证读取配置，不启用
}

type CheckPointUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	CheckPointCreate
}

type CheckPointResp struct {
	BusinessType       int64   `example:"1000" json:"business_type"`          // 业务类型配置，用于对接入业务（事件）的细分
	FilterFields       string  `example:"[]" json:"filter_fields"`            // 过滤字段配置，区分更细化的业务数据（policy选取有关）
	DefaultPkFieldName *string `example:"orderSn" json:"default_primary_key"` // 默认主键字段，与filterField相关
	ConfigProgress     int32   `json:"config_progress"`                       // 配置进度
	CheckPointUpdate
}

type CheckPointListResp struct {
	PageData
	ItemList []*CheckPointResp `json:"item_list"`
}

type CheckPointNameLabel struct {
	Code  string `json:"code"`
	Label string `json:"name"`
}

type CheckPointBusinessConfigReq struct {
	Code                     string                           `example:"CP003" json:"code" binding:"required"` // 编码
	CheckPointBusinessConfig []model.CheckPointBusinessConfig `json:"check_point_business_config"`
}
