package domain

import (
	"gorm.io/gorm"
)

type IPageSearch interface {
	PageLimit(db *gorm.DB) *gorm.DB
}

// PageSearch 满足 data 的 Param 接口
type PageSearch struct {
	PageNum  int `example:"1" json:"page_num,omitempty" binding:"required" form:"page_num"`
	PageSize int `example:"10" json:"page_size,omitempty" binding:"required" form:"page_size"`
}

func (d *PageSearch) GetPageNum() int {
	return d.PageNum
}

func (d *PageSearch) GetPageSize() int {
	return d.PageSize
}

type TimeParam struct {
	DateType  string `example:"created_at" json:"date_type" form:"date_type"`
	StartTime int64  `example:"1719505150" json:"start_time" form:"start_time"`
	EndTime   int64  `example:"1719965150" json:"end_time" form:"end_time"`
}

func (d *TimeParam) GetDateType() string {
	return d.DateType
}

func (d *TimeParam) GetStartTime() int64 {
	return d.StartTime
}

func (d *TimeParam) GetEndTime() int64 {
	return d.EndTime
}

type TimeAndPageSearch struct {
	PageSearch
	TimeParam
}

type Ids struct {
	Ids []int `json:"ids,omitempty" form:"ids"` // id切片
}
