package request_parameters

type RuleParameterValueList struct {
	PageRequest //
	TimeRequest
}

type RmsRuleParameterValueCreate struct {
	ParamID     int64   `example:"1" json:"param_id" binding:"required"`      // RMS_RULE_PARAMETER外键
	GroupID     int64   `example:"1" json:"group_id" binding:"required"`      // RMS_RULE_PARAMETER_GROUP外键
	ParamKey    *string `example:"" json:"param_key" binding:"required"`      // 健，非键值对类型可空
	ParamValue  string  `example:"001" json:"param_value" binding:"required"` // 值
	Description *string `example:"" json:"description"`                       // 描述
}

type RmsRuleParameterValueUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	RmsRuleParameterValueCreate
}

type RmsRuleParameterValueUpdateMultipleItem struct {
	ID          int64   ` json:"id" example:"1"`
	ParamKey    string  `example:"" json:"param_key" binding:"required"`      // 健，非键值对类型可空
	ParamValue  string  `example:"001" json:"param_value" binding:"required"` // 值
	Description *string `example:"" json:"description"`                       // 描述
}

type RmsRuleParameterValueUpdateMultiple struct {
	RuleParamID            int64                                     `example:"1" json:"rule_param_id" binding:"required"`       // RMS_RULE_PARAMETER外键
	RuleParamGroupID       int64                                     `example:"1" json:"rule_param_group_id" binding:"required"` // RMS_RULE_PARAMETER_GROUP外键
	RuleParameterValueList []RmsRuleParameterValueUpdateMultipleItem `json:"rule_param_value_list" binding:"required"`
}

type RmsRuleParameterValueUpdateMultipleRespItem struct {
	ID          int64   `example:"8" json:"id"`
	ParamKey    string  `gorm:"column:param_key;type:varchar(16);comment:健，非键值对类型可空" json:"param_key"`       // 健，非键值对类型可空
	ParamValue  string  `gorm:"column:param_value;type:varchar(1024);not null;comment:值" json:"param_value"` // 值
	Description *string `gorm:"column:description;type:varchar(256);comment:描述" json:"description"`          // 描述
}

type RmsRuleParameterValueUpdateMultipleResp struct {
	ItemList []*RmsRuleParameterValueUpdateMultipleRespItem `json:"item_list"`
}
