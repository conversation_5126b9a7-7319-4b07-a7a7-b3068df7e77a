package request_parameters

import (
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
)

type CreateBlackWhiteRecord struct {
	Name         string        `json:"name" example:"test" binding:"required"`       // 名单名称，更新时候不处理了字段
	Type         int           `json:"type" example:"1" binding:"required"`          // 名单类型 1.黑名单 2.白名单 ，更新时候不处理了字段
	AccessPoints []string      `json:"access_points" example:"access_point_name" `   // 接入点名称
	Note         string        `json:"note" example:"test note"  validate:"max=500"` // 备注
	Fields       entity.Fields `json:"fields"  `                                     // 字段，更新时候不处理了字段
	// Status int      `json:"status" example:"1" binding:"required"`  // 状态 1.启用 2.禁用
}
type UpdateBlackWhiteRecord struct {
	ID           int64    `json:"id" example:"1"`                               // 名单ID,更新时必填
	AccessPoints []string `json:"access_points" example:"access_point_name" `   // 接入点名称
	Note         string   `json:"note" example:"test note"  validate:"max=500"` // 备注
	// Status int      `json:"status" example:"1" binding:"required"`  // 状态 1.启用 2.禁用
}

// GetBlackWhiteList 获取黑白名单列表
type GetBlackWhiteList struct {
	PageRequest
	Type           int      `json:"type" example:"1" binding:"required"`   // 名单类型 1.黑名单 2.白名单
	Name           string   `json:"name" example:"test"`                   // 名单名称
	AccessPoints   []string `json:"access_points"  `                       // 接入点：access_point_name
	RequiredFields []string `json:"required_fields" example:"account_id" ` // 必填字段筛选，值为字段对应的ID
	Valid          int64    `json:"valid"`                                 //0:忽略 1.有效
}
type GetBlackWhiteRecord struct {
	ID int64 `form:"id" example:"1" binding:"required"` // 黑白名单记录ID
}

type SubmitAudit struct {
	ID int64 `json:"id" example:"1" binding:"required"` // 黑白名单记录ID
}

type UpdateBlackWhite struct {
	ID int64 `json:"id" example:"1" binding:"required"` // 黑白名单记录ID
}

type SetBlackWhiteStatus struct {
	ID     int64 `json:"id" example:"1" binding:"required"` // 黑白名单记录ID
	Status int   `json:"status" example:"0" `               // 状态 0.禁用 1.启用
}

// BwlID int64                     `gorm:"column:black_white_list_id;type:bigint(11);not null;index;comment:黑白名单ID" json:"black_white_list_id"`
//
//	StartDate        int64                     `gorm:"column:start_date;type:bigint(11);not null;comment:开始日期,时间戳" json:"start_date"`
//	EndDate          int64                     `gorm:"column:end_date;type:bigint(11);not null;comment:结束日期,时间戳" json:"end_date"`
//	RemainingDay     int64                     `gorm:"column:remaining;type:bigint(11);not null;comment:剩余时间" json:"remaining"`
//	Extends          BlackWhiteItemExtendsJson `gorm:"column:extends;type:text;comment:扩展字段" json:"extends"`
type AddBlackWhiteItem struct {
	BwlID     int64                  `json:"bwl_id" example:"1" binding:"required"` // 黑白名单ID
	StartDate int64                  `json:"start_date" example:"1" `               // 开始日期，时间戳
	EndDate   int64                  `json:"end_date" example:"1" `                 // 结束日期，时间戳
	Permanent bool                   `json:"permanent" example:"true" `             // 是否永久
	Extends   map[string]interface{} `json:"extends" `                              // 扩展字段,一个map，key为字段ID,int64类型，value类型不定义
}
type UpdateBlackWhiteItem struct {
	AddBlackWhiteItem
	ID int64 `json:"id" example:"1" binding:"required"` // 黑白名单记录明细ID
}
type BlackWhiteItemsRequest struct {
	PageRequest
	BwlID int64 `json:"bwl_id" form:"bwl_id"` // 黑白名单表记录的ID
}

type BlackWhiteAudit struct {
	ID           int64  `json:"id" example:"1" binding:"required"` // 审核记录ID
	Pass         bool   `json:"pass" example:"true" `              // 是否通过 true:通过 false：拒绝
	RejectReason string `json:"reject_reason" example:"拒绝原因"`
}

type BlackWhiteAuditRequest struct {
	PageRequest
	Type int `form:"type" example:"1" ` // 类型 0:所有 1.未处理审核 2.已处理审核 3.待提醒
}

type BlackWhiteOperatorLogsRequest struct {
	PageRequest
	BwlID int `form:"bwl_id" example:"1" binding:"required"` // 黑白名单ID
}

// GetBlackWhiteItems 获取黑白名单明细列表
type GetBlackWhiteItems struct {
	PageRequest
	BwlID int64     `form:"bwl_id" example:"1" binding:"required"` // 黑白名单ID
	Now   time.Time `form:"-"`
}

type ListBlackWhiteField struct {
	AccessPoints []string `json:"access_points" binding:"required" example:"access_point_name" ` // 接入点名称
}
