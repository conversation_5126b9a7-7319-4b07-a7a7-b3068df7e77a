package request_parameters

type ConfigProgressCreate struct {
	CheckPointCode string `example:"CP003" json:"access_point" binding:"required"` // 接入点编码
	Progress       int32  `example:"1" json:"progress" binding:"required"`         // 填写进度
}

type ConfigProgressUpdate struct {
	ID int64 `json:"id" example:"1" binding:"required,gte=0"`
	ConfigProgressCreate
}

type ConfigProgressRetrieve struct {
	Code string `example:"CP003" json:"code" binding:"required"` // 接入点编码
}
