package request_parameters

import (
	"encoding/json"
	"time"
)

type OperatorLogList struct {
	TimeRequest
	PageRequest
}

type ReferenceData struct {
	TableName string `json:"table_name"`
	Key       string `json:"key"`
	Id        string `json:"id"`
}

type SysOperateLog struct {
	Lid           int           `json:"-" gorm:"lid;primary_key;autoIncrement"` // 日志Id
	LogId         string        `json:"log_id" gorm:"log_id"`                   // 操作日志Id
	Type          OperateType   `json:"type" gorm:"type"`                       // 动作类型：INSERT=创建，UPDATE=更新，DELETE=删除
	Uri           string        `json:"uri" gorm:"uri"`                         // 请求路径
	Params        string        `json:"params" gorm:"params"`                   // 请求参数
	Events        string        `json:"events" gorm:"events"`                   // 事件结果
	OriginalData  string        `json:"original_data" gorm:"original_data"`     // 变更数据
	Ip            string        `json:"ip" gorm:"ip"`                           // 请求ip
	UserAgent     string        `json:"user_agent" gorm:"user_agent"`           // 浏览器agent
	ReferenceType ReferenceType `json:"reference_type" gorm:"reference_type"`   // 关联类型
	ReferenceData string        `json:"reference_data" gorm:"reference_data"`   // 关联数据{"table_name": "", "key": "", "id":""}
	CreatorId     string        `json:"creator_id" gorm:"creator_id"`           // 创建人id
	CreatorName   string        `json:"creator_name" gorm:"creator_name"`       // 创建人名
	CreateTime    time.Time     `json:"create_time" gorm:"create_time"`         // 创建时间
	OperateStatus string        `json:"operate_status" gorm:"operate_status"`   // 操作状态：FAILED=失败，COMPLETE=完成
}

func (s *SysOperateLog) TableName() string {
	return "rms_operator_log"
}

type ReferenceType int

const (
	REF_TYPE_LOG      ReferenceType = 0
	REF_TYPE_ACCOUNT  ReferenceType = 1000
	REF_TYPE_CUSTOMER ReferenceType = 1001
	REF_TYPE_FINANCE  ReferenceType = 2000
	REF_TYPE_CONFIG   ReferenceType = 9000
)

// 操作类型
type OperateType string

const (
	OPERATE_TYPE_CREATE OperateType = "INSERT" // 创建
	OPERATE_TYPE_UPDATE OperateType = "UPDATE" // 更新
	OPERATE_TYPE_DELETE OperateType = "DELETE" // 删除
)

func (r ReferenceData) GetReferenceDataStr() string {
	jsonData, _ := json.Marshal(r)
	return string(jsonData)
}

func (s SysOperateLog) UnmarshalReferenceData() (ReferenceData, error) {
	var referenceData ReferenceData
	if err := json.Unmarshal([]byte(s.ReferenceData), &referenceData); err != nil {
		return referenceData, err
	} else {
		return referenceData, nil
	}
}

type ReferenceTable struct {
	OperateType OperateType `json:"operate_type"`
	Entity      any         `json:"entity"`
	RefKey      string      `json:"ref_key"`
	Id          string      `json:"id"`
}

func (s SysOperateLog) GetReferenceTable() (ReferenceTable, error) {
	var referenceTable ReferenceTable
	if err := json.Unmarshal([]byte(s.ReferenceData), &referenceTable); err != nil {
		return referenceTable, err
	} else {
		return referenceTable, nil
	}
}
