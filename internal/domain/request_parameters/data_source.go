package request_parameters

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/encrypt"
)

// DataSourceCreate 创建数据源请求参数
type DataSourceCreate struct {
	SourceName   string            `json:"source_name" binding:"required,max=50" example:"CRM_KYC_Master"`  // 数据源名称
	SourceType   string            `json:"source_type" binding:"required,max=50" example:"Mysql"`           // 数据源类型：mysql,doris
	Address      string            `json:"address" binding:"required,max=255" example:"********"`           // 服务器地址
	Port         int               `json:"port" binding:"required" example:"3306"`                          // 端口
	Username     string            `json:"username" binding:"required,max=50" example:"root"`               // 用户名
	Password     encrypt.Encrypted `json:"password" binding:"required,max=255" example:"password123"`       // 密码
	DatabaseName string            `json:"database_name" binding:"required,max=50" example:"crm_db"`        // 数据库名称
	Description  *string           `json:"description" binding:"omitempty,max=255" example:"CRM系统数据库"` // 描述
}

// DataSourceUpdate 更新数据源请求参数
type DataSourceUpdate struct {
	ID           int64             `json:"id" binding:"required" example:"1"`                               // 数据源ID
	SourceName   string            `json:"source_name" binding:"required,max=50" example:"CRM_KYC_Master"`  // 数据源名称
	SourceType   string            `json:"source_type" binding:"required,max=50" example:"Mysql"`           // 数据源类型：mysql,doris
	Address      string            `json:"address" binding:"required,max=255" example:"********"`           // 服务器地址
	Port         int               `json:"port" binding:"required" example:"3306"`                          // 端口
	Username     string            `json:"username" binding:"required,max=50" example:"root"`               // 用户名
	Password     encrypt.Encrypted `json:"password" binding:"required,max=255" example:"password123"`       // 密码
	DatabaseName string            `json:"database_name" binding:"required,max=50" example:"crm_db"`        // 数据库名称
	Description  *string           `json:"description" binding:"omitempty,max=255" example:"CRM系统数据库"` // 描述
}

// DataSourceList 数据源列表请求参数
type DataSourceList struct {
	PageRequest
	SourceName *string `json:"source_name" form:"source_name"` // 数据源名称
	SourceType *string `json:"source_type" form:"source_type"` // 数据源类型：mysql,doris
	Valid      int64   `json:"valid" form:"valid"`             // 是否有效，0：全部 1:有效 2:无效
}

// DataSourceTest 测试数据源连接请求参数
type DataSourceTest struct {
	SourceName   string            `json:"source_name" binding:"required,max=50" example:"CRM_KYC_Master"` // 数据源名称
	SourceType   string            `json:"source_type" binding:"required,max=50" example:"Mysql"`          // 数据源类型：mysql,doris
	Address      string            `json:"address" binding:"required,max=255" example:"********"`          // 服务器地址
	Port         int               `json:"port" binding:"required" example:"3306"`                         // 端口
	Username     string            `json:"username" binding:"required,max=50" example:"root"`              // 用户名
	Password     encrypt.Encrypted `json:"password" binding:"required,max=255" example:"password123"`      // 密码
	DatabaseName string            `json:"database_name" binding:"required,max=50" example:"crm_db"`       // 数据库名称
}
