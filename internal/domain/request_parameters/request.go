package request_parameters

import "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"

type PageRequest struct {
	PageNum  int64 `example:"1" json:"page_num,omitempty" form:"page_num"`
	PageSize int64 `example:"10" json:"page_size" form:"page_size"`
}

func (c *PageRequest) Option(f *filter.DefaultFilter) *filter.DefaultFilter {
	f.Page = filter.NewPaging(c.Page<PERSON>um, c.PageSize)
	return f
}

type TimeRequest struct {
	DateType  string `example:"created_at" json:"date_type" form:"date_type"`
	StartTime int64  `example:"1719505150" json:"start_time" form:"start_time"`
	EndTime   int64  `example:"1719965150" json:"end_time" form:"end_time"`
}

func (c *TimeRequest) Option(f *filter.DefaultFilter) *filter.DefaultFilter {
	f.Time = filter.NewTime(c.DateType, c.StartTime, c.EndTime)
	return f
}
