package request_parameters

// CreateScriptIndicator 创建Script指标配置请求参数
type CreateScriptIndicator struct {
	IndicatorName string `json:"indicator_name" binding:"required"` // 指标名称
	DataSourceID  int64  `json:"data_source_id" binding:"required"` // 数据源ID
	AccessPoint   string `json:"access_point" binding:"required"`   // 接入点
	Script        string `json:"script" binding:"required"`         // 关联的表名
	Remark        string `json:"remark" binding:"-"`                // 备注
}

// UpdateScriptIndicator 更新Script指标配置请求参数
type UpdateScriptIndicator struct {
	ID            int64  `json:"id" binding:"required"` // 指标ID
	IndicatorName string `json:"indicator_name"`
	Script        string `json:"script" binding:"required"` // 关联的表名
	Remark        string `json:"remark" binding:"-"`        // 备注
}

// GetIndicatorScriptList 获取Script指标配置列表请求参数
type GetIndicatorScriptList struct {
	PageRequest
	IndicatorName string `json:"indicator_name" binding:"-"` // 指标名称
	AccessPoint   string `json:"access_point" binding:"-"`   // 接入点

}

// GetIndicatorScriptDetail 获取指标配置详情请求参数
type GetIndicatorScriptDetail struct {
	ID        int64 `form:"id" binding:"required"`  // 指标ID
	VersionID int64 `form:"version_id" binding:"-"` // 指标版本ID
}
