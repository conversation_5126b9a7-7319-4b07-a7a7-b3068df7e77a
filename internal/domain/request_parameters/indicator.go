package request_parameters

import (
	"errors"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
)

// CreateIndicator 创建指标配置请求参数
type CreateIndicator struct {
	IndicatorName string                 `json:"indicator_name" binding:"required"` // 指标名称
	TableID       int64                  `json:"table_id" binding:"required"`       // 关联的表名
	AccessPoint   string                 `json:"access_point" binding:"required"`   // 接入点
	MeasureType   string                 `json:"measure_type" binding:"required"`   // 度量类型 -agg select
	Measures      []Measure              `json:"measures"`                          // 度量列表
	TimeWindow    TimeWindow             `json:"time_window"`                       // 时间窗口
	RulePreview   string                 `json:"rule_preview"`
	Rules         []entity.IndicatorRule `json:"rules"`              // 规则列表
	Remark        string                 `json:"remark" binding:"-"` // 备注
}

//func (c *CreateIndicator) Validator() (err error) {
//	for _, v := range c.Measures {
//		err = v.Validator()
//		if err != nil {
//			return
//		}
//	}
//	for _, v := range c.Rules {
//		err = v.Validator()
//		if err != nil {
//			return
//		}
//	}
//	err = c.TimeWindow.Validator()
//	if err != nil {
//		return
//	}
//	return nil
//
//}

type Measure struct {
	ID            int64                     `json:"id" `            //更新带上原始ID,新增ID为空
	AggType       string                    `json:"agg_type"`       // 聚合类型 - count, sum, avg, max, min
	MeasureField  entity.DataSourceColumnID `json:"measure_field"`  // 度量字段
	ConditionName string                    `json:"condition_name"` // 条件名称
}

func (m *Measure) Validator() (err error) {
	column := entity.NewDataSourceColumnByColumnID(m.MeasureField)
	if column == nil {
		err = errors.New("measure field incorrect format")
		return
	}
	if !consts.IndicatorMeasureAggTypeMap[m.AggType] {
		err = errors.New("measure type data error")
		return
	}
	return nil
}

type TimeWindow struct {
	ColumnID  entity.DataSourceColumnID `json:"column_id" `
	Type      int                       `json:"type" `      // 时间窗口类型 1.Sliding Window 2.Fixed Window 3.Before 4.After 5.Between
	Value     int                       `json:"value" `     // 时间窗口值 当type为1时，表示时间窗口值，当type=2,3，4,5时不生效
	Start     string                    `json:"start"`      // 当type=3，5生效，数据为类似00:00数据
	End       string                    `json:"end"`        // 当type=4,5生效，数据为类似00:00数据
	Unit      int                       `json:"unit" `      // 时间窗口单位,当type为1时，1:Minutes/2:Hours/3:Days/4:Weeks/5:Months,当type为2时，1:Daily 2:Monthly 3:Annual,当type=3,4,5时不生效
	Excluding bool                      `json:"excluding" ` // 是否排除,当时间单位位天、周、月时生效，勾选不包含当前时间,当type=2,3,4,5时不生效
}

func (t *TimeWindow) Validator() (err error) {
	if t.Type != consts.IndicatorMeasureWindowTypeSliding &&
		t.Type != consts.IndicatorMeasureWindowTypeFixed &&
		t.Type != consts.IndicatorMeasureWindowTypeBefore &&
		t.Type != consts.IndicatorMeasureWindowTypeAfter &&
		t.Type != consts.IndicatorMeasureWindowTypeBetween &&
		t.Type != 0 {
		err = errors.New("time window type data error")
		return
	}
	if t.Type == consts.IndicatorMeasureWindowTypeFixed {
		if t.Value != 1 && t.Value != 2 && t.Value != 3 && t.Value != 0 {
			err = errors.New("time window value data error")
			return
		}
	}
	if t.Type == consts.IndicatorMeasureWindowTypeSliding {
		if t.Unit != 0 && t.Unit != 1 && t.Unit != 2 && t.Unit != 3 && t.Unit != 4 && t.Unit != 5 {
			err = errors.New("time window unit data error")
			return
		}
	}
	if (t.Type == consts.IndicatorMeasureWindowTypeBefore || t.Type == consts.IndicatorMeasureWindowTypeBetween) && t.Start != "" {
		_, err = time.Parse("2006-01-02 15:04:05", t.Start)
		if err != nil {
			err = errors.New("time window start data error")
			return
		}
	}
	if (t.Type == consts.IndicatorMeasureWindowTypeAfter || t.Type == consts.IndicatorMeasureWindowTypeBetween) && t.End != "" {
		_, err = time.Parse("2006-01-02 15:04:05", t.End)
		if err != nil {
			err = errors.New("time window end data error")
			return
		}
	}

	return nil
}

// UpdateIndicator 更新指标配置请求参数
type UpdateIndicator struct {
	ID            int64                  `json:"id" binding:"required"` // 指标ID
	IndicatorName string                 `json:"indicator_name"`        // 指标名称
	Measures      []Measure              `json:"measures"`              // 度量列表
	TimeWindow    TimeWindow             `json:"time_window"`           // 时间窗口
	RulePreview   string                 `json:"rule_preview"`
	Rules         []entity.IndicatorRule `json:"rules"`              // 规则列表
	Remark        string                 `json:"remark" binding:"-"` // 备注
}

func (c *UpdateIndicator) Validator() (err error) {
	for _, v := range c.Measures {
		err = v.Validator()
		if err != nil {
			return
		}
	}
	for _, v := range c.Rules {
		err = v.Validator()
		if err != nil {
			return
		}
	}
	err = c.TimeWindow.Validator()
	if err != nil {
		return
	}
	return nil

}

// GetIndicatorList 获取指标配置列表请求参数
type GetIndicatorList struct {
	PageRequest
	Name        string `json:"indicator_name" binding:"-"` // 指标名称
	TableID     int64  `json:"table_id" binding:"-"`       // 关联的表名
	AccessPoint string `json:"access_point" binding:"-"`   // 接入点

}

// GetIndicatorDetail 获取指标配置详情请求参数
type GetIndicatorDetail struct {
	ID        int64 `form:"id" binding:"required"`  // 指标ID
	VersionID int64 `form:"version_id" binding:"-"` // 指标版本ID
}

// GetIndicatorVersion 获取指标配置版本请求参数
type GetIndicatorVersion struct {
	PageRequest
	IndicatorID int64 `json:"indicator_id" binding:"required"` // 指标ID
}

// RollbackIndicatorVersion 回滚指标配置版本请求参数
type RollbackIndicatorVersion struct {
	IndicatorID int64 `json:"indicator_id" binding:"required"` // 指标ID
	VersionID   int64 `json:"version_id" binding:"required"`   // 版本ID
}

// ReleaseIndicator 发布指标版本请求参数
type ReleaseIndicator struct {
	ID int64 `json:"id" binding:"required"` // 指标ID
}

// UpdateIndicatorStatus 更新指标状态请求参数
type UpdateIndicatorStatus struct {
	ID      int64 `json:"id" binding:"required"` // 指标ID
	Disable bool  `json:"disable"`               // 是否禁用
}

// GenerateIndicatorSql 更新指标状态请求参数
type GenerateIndicatorSql struct {
	IndicatorID int64 `json:"indicator_id" binding:"required"` // 指标ID
	VersionID   int64 `json:"version_id" `                     // 版本ID
}

// TestIndicatorSql 测试指标SQL请求参数
type TestIndicatorSql struct {
	TestData    string `json:"test_data"`                       // 测试数据
	IndicatorID int64  `json:"indicator_id" binding:"required"` // 指标ID
	VersionID   int64  `json:"version_id" `                     // 版本ID

}

// TestScriptIndicatorSql 测试指标SQL请求参数
type TestScriptIndicatorSql struct {
	IndicatorID int64 `json:"indicator_id" binding:"required"` // 指标ID
	VersionID   int64 `json:"version_id" `                     // 版本ID
}
