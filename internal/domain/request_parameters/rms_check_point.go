package request_parameters

type CheckPointCreate struct {
	Code           string  `json:"code" binding:"required,alpha_num_underline"` // 编码
	Name           string  `json:"name" binding:"required,alpha_num_underline"` // 名称
	CheckDuplicate *bool   `json:"check_duplicate"`                             // 数据重复检查
	Remark         *string `json:"remark"`                                      // 备注
	AlwaysRun      int     `json:"always_run"`
	BusinessType   int64   `json:"business_type" binding:"required,oneof=1000 3000 4000"` // 业务类型 1000-banking, 3000-acquiring, 4000-issuing
}

type CheckPointUpdate struct {
	ID             int64   `json:"id" binding:"required,gte=0"`
	Code           string  `json:"code" binding:"required,alpha_num_underline"` // 编码
	Name           string  `json:"name" binding:"required,alpha_num_underline"` // 名称
	CheckDuplicate *bool   `json:"check_duplicate"`                             // 数据重复检查
	Remark         *string `json:"remark"`                                      // 备注
	AlwaysRun      int     `json:"always_run"`
}

type CheckPointList struct {
	PageRequest
	Code  string `json:"code"`  // 编码
	Label string `json:"label"` // 名称
}
