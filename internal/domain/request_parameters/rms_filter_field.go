package request_parameters

type FilterFieldList struct {
	PageRequest
	AccessPoint string `example:"CP004" json:"access_point"` // 检查点
	FieldName   string `example:"bizType" json:"field_name"` // 字段名
}

type FilterFieldRetrieve struct {
	AccessPoint string `example:"CP004" json:"access_point"` // 检查点
	FieldName   string `example:"bizType" json:"field_name"` // 字段名
}

type FilterFieldCreate struct {
	AccessPoint string  `example:"CP004" json:"access_point" binding:"required"`                                                                                       // 检查点
	FieldName   string  `example:"bizType" json:"field_name" binding:"required"`                                                                                       // 字段名
	FieldEnum   string  `example:"[{\"code\":\"DEPOSIT\",\"label\":\"存款\"},{\"code\":\"WITHDRAW\",\"label\":\"提现\"}]" json:"enumeration_value" binding:"required"` // 枚举类型列表的json，各枚举由编码和名称构成
	Memo        *string `example:"业务类型" json:"remark"`                                                                                                             // 备注
}

type FilterFieldUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	FilterFieldCreate
}

type FilterFieldResp struct {
	FilterFieldUpdate
}
