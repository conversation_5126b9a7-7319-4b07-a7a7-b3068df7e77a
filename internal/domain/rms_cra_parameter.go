package domain

import (
	"github.com/shopspring/decimal"
	"time"
)

type CraParameterCreate struct {
	CraFrameworkId     string          `json:"cra_framework_id" binding:"required" :"cra_framework_id"`                              // 唯一ID
	CraParameterName   string          `json:"cra_parameter_name"  binding:"required" :"cra_parameter_name"`                         // 参数名
	CraParameterStatus int8            `json:"cra_parameter_status"  binding:"required,oneof=1 0 -1" :"cra_parameter_status"`        // 状态：1=active, 0=默认, -1=inactive
	CraParameterType   string          `json:"cra_parameter_type" binding:"required,oneof=AML COUNTRY CUSTOM" :"cra_parameter_type"` // AML; COUNTRY; CUSTOM
	Description        string          `json:"description"`                                                                          // 描述
	Weight             decimal.Decimal `json:"weight" binding:"required"`                                                            // 重量，权重
	ParamInfo          string          `json:"param_info" binding:"required" :"param_info"`                                          // 参数参考信息

}
type CraParameterUpdate struct {
	CraParameterId     string          `json:"cra_parameter_id"binding:"required"`                             // 唯一ID
	CraParameterStatus int8            `json:"cra_parameter_status"  binding:"required,oneof=1 0 -1"`          // 状态：1=active, 0=默认, -1=inactive
	CraParameterType   string          `json:"cra_parameter_type" binding:"required,oneof=AML COUNTRY CUSTOM"` // AML; COUNTRY; CUSTOM
	Description        string          `json:"description"`                                                    // 描述
	Weight             decimal.Decimal `json:"weight" binding:"required"`                                      // 重量，权重
	ParamInfo          string          `json:"param_info" binding:"required"`                                  //
	CraFrameworkId     string          `json:"cra_framework_id"binding:"required"`
}
type CraParameterListReq struct {
	CraFrameworkId string `json:"cra_framework_id" binding:"required"` // 唯一ID
}
type CraParameter struct {
	CraParameterId     string          `json:"cra_parameter_id"binding:"required"`                             // 唯一ID
	CraFrameworkId     string          `json:"cra_framework_id" binding:"required"`                            // 唯一ID
	CraParameterName   string          `json:"cra_parameter_name"  binding:"required"`                         // 参数名
	CraParameterStatus int8            `json:"cra_parameter_status"  binding:"required,oneof=1 0 -1"`          // 状态：1=active, 0=默认, -1=inactive
	CraParameterType   string          `json:"cra_parameter_type" binding:"required,oneof=AML COUNTRY CUSTOM"` // AML; COUNTRY; CUSTOM
	Description        string          `json:"description"  binding:"required"`                                // 描述
	Weight             decimal.Decimal `json:"weight" binding:"required"`                                      // 重量，权重
	ParamValIds        []string        `json:"param_val_ids" binding:"required""`                              // 关联参考id
	ParamValNames      []string        `json:"param_val_names" binding:"required""`                            // 关联参考名称
	FormField          []string        `json:"form_field"`                                                     //前端字段名list
	ParamInfo          string          `json:"-"`                                                              // 参数参考信息

}
type RmsCraParameterInesRes struct {
	CraFrameworkId     string          `json:"cra_framework_id" gorm:"cra_framework_id"`         // 唯一ID
	CraParameterId     string          `json:"cra_parameter_id" gorm:"cra_parameter_id"`         // 唯一ID
	CraParameterName   string          `json:"cra_parameter_name" gorm:"cra_parameter_name"`     // 参数名
	CraParameterStatus int8            `json:"cra_parameter_status" gorm:"cra_parameter_status"` // 状态：1=active, 0=默认, -1=inactive
	CraParameterType   string          `json:"cra_parameter_type" gorm:"cra_parameter_type"`     // AML; COUNTRY; CUSTOM
	Description        string          `json:"description" gorm:"description"`                   // 描述
	Weight             decimal.Decimal `json:"weight" gorm:"weight"`                             // 重量，权重
	CreateTime         time.Time       `json:"create_time" gorm:"create_time"`
	UpdateTime         time.Time       `json:"update_time" gorm:"update_time"`
	ParamInfo          string          `json:"param_info" gorm:"param_info"` // 关联字段详细信息
}

type CraParameterListRes struct {
	AmlList     []*CraParameter `json:"aml_list"`
	CountryList []*CraParameter `json:"country_list"`
	CustomList  []*CraParameter `json:"custom_list"`
}
type CraParameterBatchUpdate struct {
	CraParameterId     string          `json:"cra_parameter_id"binding:"required"`                    // 唯一ID
	Weight             decimal.Decimal `json:"weight" binding:"required"`                             // 重量，权重
	CraParameterStatus int8            `json:"cra_parameter_status"  binding:"required,oneof=1 0 -1"` // 状态：1=active, 0=默认, -1=inactive
}
type CraParameterBatchUpdateReq struct {
	CraFrameworkId              string                     `json:"cra_framework_id" gorm:"cra_framework_id"binding:"required"【` // 唯一ID
	CraParameterBatchUpdateList []*CraParameterBatchUpdate `json:"cra_parameter_batch_update_list"`
}
type CraAuditingReq struct {
	Profile      string `json:"profile"binding:"required"`
	EntityType   string `json:"entity_type"binding:"required,oneof=Individual Company"`
	CustomerId   string `json:"customer_id"`
	EntityId     string `json:"entity_id"`
	BusinessCode int16  `json:"business_code"`
}

type ParameterInfoKey struct {
	Type   string   `json:"type"`
	Source string   `json:"source"`
	Key    []string `json:"key"`
}
type ParameterInfo struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// 详细信息结构
type RiskCraInfoRes struct {
	AssessmentGrade  int8            `json:"assessment_grade"`
	AssessmentScore  decimal.Decimal `json:"assessment_score"`
	CraParameterType string          `json:"cra_parameter_type"`
	ParamValName     string          `json:"param_val_name"`
}

// 顶级结构
type RiskCraRes struct {
	AssessmentGrade int8             `json:"assessment_grade"`
	AssessmentScore decimal.Decimal  `json:"assessment_score"`
	Info            []RiskCraInfoRes `json:"info"`
}
