package domain

type EventList struct {
	TimeAndPageSearch
	CheckPoint string `example:"" json:"access_point"`                                                                                                           // 联系人姓名
	Field      string `example:"str_field1" json:"field" binding:"omitempty,oneof=STR_FIELD1 STR_FIELD2 STR_FIELD3 STR_FIELD4 STR_FIELD5 NUM_FIELD1 NUM_FIELD2"` // 字段名
	FieldValue string `example:"111" json:"field_value" `                                                                                                        // 字段值
	ResultCode string `example:"" json:"result_code"`                                                                                                            // 规则结果代码
}
