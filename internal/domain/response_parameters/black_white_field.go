package response_parameters

type BlackWhiteFieldOption struct {
	ID   int64                  `json:"id" example:"1"`      // 字段ID
	Name string                 `json:"name" example:"name"` // 字段名称
	Enum []*BlackWhiteFieldEnum `json:"enum"`                // 枚举值
}

type BlackWhiteFieldEnum struct {
	Key   string `json:"key" example:"1"`      // 枚举展示的值
	Value string `json:"value" example:"name"` // 枚举接口传递的值
}
