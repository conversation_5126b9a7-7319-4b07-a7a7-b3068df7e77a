package response_parameters

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
)

type IndicatorCreateResponse struct {
	IndicatorId int64 `json:"indicator_id"`
	VersionID   int64 `json:"version_id"`
}

// IndicatorListResp 指标配置列表响应
type IndicatorListResp struct {
	Total int64            `json:"total"` // 总数
	Items []*IndicatorList `json:"items"` // 指标配置列表
}

// IndicatorList 指标配置列表项
type IndicatorList struct {
	ID               int64  `json:"id"`                 // 指标ID
	IndicatorName    string `json:"indicator_name"`     // 指标名称
	CurrentVersionID int64  `json:"current_version_id"` // 当前版本ID
	CurrentVersion   string `json:"current_version"`    // 当前版本
	TableName        string `json:"table_name"`         // 关联的表名
	Measures         string `json:"measures"`           // 度量
	TimeWindow       string `json:"time_window"`        // 时间窗口
	HasDraft         bool   `json:"has_draft"`          // 是否存在草稿
	Status           string `json:"status"`             // 状态  online,offline,draft
	UpdatedAt        string `json:"updated_at"`         // 更新时间
}

// IndicatorScriptList Script指标配置列表项
type IndicatorScriptList struct {
	ID             int64  `json:"id"`              // 指标ID
	IndicatorName  string `json:"indicator_name"`  // 指标名称
	CurrentVersion string `json:"current_version"` //当前版本
	Script         string `json:"script"`
	HasDraft       bool   `json:"has_draft"`  // 是否存在草稿
	Status         string `json:"status"`     // 状态 online,offline,draft
	UpdatedAt      string `json:"updated_at"` // 更新时间
}

// IndicatorDetail 指标配置详情
type IndicatorDetail struct {
	ID               int64                  `json:"id"`                 // 指标ID
	CurrentVersionID int64                  `json:"current_version_id"` // 当前版本ID
	CurrentVersion   string                 `json:"current_version"`    // 当前版本
	IndicatorName    string                 `json:"indicator_name" `    // 指标名称
	MeasureType      string                 `json:"measure_type"`       // 度量类型 -agg select
	TableID          int64                  `json:"table_id" `          // 关联的表名
	Measures         []Measure              `json:"measures" `          // 度量列表
	TimeWindow       TimeWindow             `json:"time_window" `       // 时间窗口
	RulePreview      string                 `json:"rule_preview"`
	AccessPoint      string                 `json:"access_point" ` // 访问点列表
	Rules            []entity.IndicatorRule `json:"rules" `        // 规则列表
	Remark           string                 `json:"remark" `       // 备注
}
type IndicatorScriptDetail struct {
	ID               int64  `json:"id"`                 // 指标ID
	CurrentVersionID int64  `json:"current_version_id"` // 当前版本ID
	CurrentVersion   string `json:"current_version"`    // 当前版本
	DataSourceID     int64  `json:"data_source_id" `    // 数据源ID
	IndicatorName    string `json:"indicator_name" `    // 指标名称
	Script           string `json:"script" `            // 规则代码
	Remark           string `json:"remark" `            // 备注
}
type Measure struct {
	ID            int64                     `json:"id" `             // 度量ID
	AggType       string                    `json:"agg_type" `       // 度量类型 - count, sum, avg, max, min
	MeasureField  entity.DataSourceColumnID `json:"measure_field" `  // 度量字段
	ConditionName string                    `json:"condition_name" ` // 条件名称
}
type TimeWindow struct {
	ColumnID  entity.DataSourceColumnID `json:"column_id" `
	Type      int                       `json:"type" `      // 时间窗口类型 1.Sliding Window 2.Fixed Window 3.Before 4.After 5.Between
	Value     int                       `json:"value" `     // 时间窗口值 当type为1时，表示时间窗口值，当type为2时，1:Daily 2:Monthly 3:Annual,当type=3，4,5时不生效
	Start     string                    `json:"start"`      // 当type=3，5生效，数据为类似2025-01-01 00:00:00数据
	End       string                    `json:"end"`        // 当type=4,5生效，数据为类似2025-01-01 00:00:00数据
	Unit      int                       `json:"unit" `      // 时间窗口单位,当type为1时，1:Minutes/2:Hours/3:Days/4:Weeks/5:Months,当type=2,3,4,5时不生效
	Excluding bool                      `json:"excluding" ` // 是否排除,当时间单位位天、周、月时生效，勾选不包含当前时间,当type=2,3,4,5时不生效
}

// IndicatorRule 指标规则
type IndicatorRule struct {
	ID               int64                     `json:"id"`                 // 规则ID
	Preview          string                    `json:"preview"`            // 预览
	HasLeftBrackets  bool                      `json:"has_left_brackets"`  // 是否包含左括号
	HasRightBrackets bool                      `json:"has_right_brackets"` // 是否包含右括号
	Connector        string                    `json:"connector"`          // 连接符(and, or)
	ColumnID         entity.DataSourceColumnID `json:"column_id"`          // 列名
	Operator         string                    `json:"operator"`           // 操作符
	Value            string                    `json:"value"`              // 比较值的字段名或具体值
	ValueType        string                    `json:"value_type"`         // 比较值类型(fixed: 固定值, field: 字段比较)
}

// IndicatorVersionListResp 指标配置版本列表响应
type IndicatorVersionListResp struct {
	Total int64                   `json:"total"` // 总数
	Items []*IndicatorVersionList `json:"items"` // 指标配置版本列表
}

// IndicatorVersionList 指标配置版本列表项
type IndicatorVersionList struct {
	IndicatorID    int64  `json:"indicator_id"`     // 指标ID
	VersionID      int64  `json:"version_id"`       // 版本ID
	Version        string `json:"version"`          // 版本号
	Desc           string `json:"desc"`             // 描述
	ModifyBy       string `json:"modify_by"`        // 修改人
	Status         string `json:"status"`           // 状态  Pending changes,Curerent version,Archived
	LastModifiedAt string `json:"last_modified_at"` // 最后修改时间
}

// ReleaseIndicatorVersion 发布指标版本请求参数
type ReleaseIndicatorVersion struct {
	VersionUUID string `json:"version_uuid" ` // 版本UUID
}

// GenerateIndicatorSqlResponse 生成指标SQL响应
type GenerateIndicatorSqlResponse struct {
	Sql          string   `json:"sql" `          // 指标SQL
	Placeholders []string `json:"placeholders" ` // 占位符
}
