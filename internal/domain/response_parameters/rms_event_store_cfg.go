package response_parameters

type EventStoreCfgCreate struct {
	CheckPoint string  `example:"CP003" json:"access_point" binding:"required"` // 检查点
	Persistent *bool   `example:"true" json:"whether_store" binding:"required"` // 是否存储
	StrField1  *string `example:"orderSn" json:"str_field1" binding:"max=32"`   // 重要字符串类型字段1的字段名（建议主键）
	StrField2  *string `example:"cardNo" json:"str_field2" binding:"max=32"`    // 重要字符串类型字段2的字段名
	StrField3  *string `example:"tradeHash" json:"str_field3" binding:"max=32"` // 重要字符串类型字段3的字段名
	StrField4  *string `example:"ip" json:"str_field4" binding:"max=32"`        // 重要字符串类型字段4的字段名
	StrField5  *string `example:"te" json:"str_field5" binding:"max=32"`        // 重要字符串类型字段5的字段名
	NumField1  *string `example:"amount" json:"num_field1" binding:"max=32"`    // 重要数值类型字段1的字段名
	NumField2  *string `example:"te" json:"num_field2" binding:"max=32"`        // 重要数值类型字段2的字段名
	//SendToIntra *bool   `example:"false" json:"send_to_intra"`  // 是否发送到风控后台
}

type EventStoreCfgUpdate struct {
	ID int64 `json:"id" example:"1" binding:"required,gte=0"`
	EventStoreCfgCreate
}

type EventStoreCfgResp struct {
	EventStoreCfgUpdate
}
