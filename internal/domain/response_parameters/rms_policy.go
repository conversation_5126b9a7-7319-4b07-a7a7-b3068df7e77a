package response_parameters

import (
	"encoding/json"
	"time"
)

// 风控规则策略（即规则组）

type PolicyCreate struct {
	CheckPoint     string          `example:"CP003" json:"access_point" binding:"required"`  // 检查点
	PolicyName     *string         `example:"CMF" json:"rule_group_name" binding:"required"` // 规则组名称
	PolicyNo       string          `example:"CMF" json:"rule_group_code" binding:"required"` // 策略编号
	Memo           *string         `example:"" json:"remark"`                                // 备注
	Status         string          `example:"Online" json:"status" binding:"required"`       // 状态，Online/Offline
	PolicyGroup    *string         `example:"CMF" json:"policy_group"`                       // 策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup
	Filters        *string         `example:"{\"orderNo\":[\"CREDIT\"]}" json:"-"`           // 策略匹配的过滤条件，需事先设置检查点的过滤字段
	PolicyRules    string          `example:"[1,2]" json:"-"`                                // 策略中需要执行的规则id列表json
	FilterFieldMap json.RawMessage `json:"filter_field_map"`                                 // example:"{\"orderNo\":[\"CREDIT\"]}" 策略匹配的过滤条件，需事先设置检查点的过滤字段
	RuleIdList     json.RawMessage `json:"rule_id_list"`                                     // example:"[1,2]" 策略中需要执行的规则id列表json
}

type PolicyUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	PolicyCreate
}

type PolicyResp struct {
	RuleNum  int        `json:"number_of_rules"` // 规则组数量
	SyncedAt *time.Time `json:"synced_at"`       // 同步时间
	PolicyUpdate
}

type PolicySetStatus struct {
	ID     int64  ` json:"id" binding:"required"`
	Status string `json:"status" binding:"oneof=Online Offline"`
}
