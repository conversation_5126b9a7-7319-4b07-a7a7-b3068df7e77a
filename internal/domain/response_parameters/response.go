package response_parameters

import "math"

type ListResponsePointer[T any] struct {
	TotalNum  int64 `json:"total_item"`
	TotalPage int64 `json:"total_page"`
	Items     []*T  `json:"item_list,omitempty"`
}
type ListResponse[T any] struct {
	TotalNum  int64 `json:"total_item"`
	TotalPage int64 `json:"total_page"`
	Items     []T   `json:"item_list,omitempty"`
}

func NewListResponsePointer[T any](total int64, pageNum int64, items []*T) ListResponsePointer[T] {
	return ListResponsePointer[T]{
		TotalNum:  total,
		TotalPage: int64(math.Ceil(float64(total) / float64(pageNum))),
		Items:     items,
	}
}
func NewListResponse[T any](total int64, pageNum int64, items []T) ListResponse[T] {
	return ListResponse[T]{
		TotalNum:  total,
		TotalPage: int64(math.Ceil(float64(total) / float64(pageNum))),
		Items:     items,
	}
}

type NormalCreateResponse struct {
	Id int64 `json:"id"`
}
