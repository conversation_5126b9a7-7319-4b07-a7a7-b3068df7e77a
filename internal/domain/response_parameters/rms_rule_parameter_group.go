package response_parameters

// 规则参数组

type RuleParameterGroupCreate struct {
	CheckPoint  string  `example:"CP003" json:"access_point"  binding:"required"`                 // 检查点
	Code        string  `example:"CP003_DEFAULT" json:"parameter_group_code"  binding:"required"` // 参数组编码
	Able        int32   `example:"1" json:"state" `                                               // 状态，1-有效，0-失效
	Description string  `example:"默认参数组" json:"parameter_group_description"`                      // 参数组描述
	DefaultFlag int32   `example:"1" json:"default_parameter_group"`                              // 默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否
	ParamIds    *string `example:"1,2,3" json:"rule_param_id_list"`                               // 下属规则参数id(可多个,逗号分割)
}

type RuleParameterGroupUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	RuleParameterGroupCreate
}

type RuleParameterGroupResp struct {
	RuleParameterGroupUpdate
}

type RuleParameterGroupAllItem struct {
	ID          int64  `example:"1" json:"id"`                               // 主键
	Code        string `example:"CP003_DEFAULT" json:"parameter_group_code"` // 参数组编码
	Description string `example:"默认参数组" json:"parameter_group_description"`  // 参数组描述
}

type RuleParameterGroupAllResp struct {
	ItemList []*RuleParameterGroupAllItem `json:"item_list"`
}
