package response_parameters

import (
	"time"
)

type AlertQueryListRes struct {
	ItemList  []*RiskEventRecord `json:"item_list"`
	TotalItem int                `json:"total_item"`
	PageNum   int                `example:"1" json:"page_num,omitempty" binding:"required" form:"page_num"`
	PageSize  int                `example:"10" json:"page_size,omitempty" binding:"required" form:"page_size"`
}
type RiskEventRecord struct {
	ID               uint64    `json:"id" `
	CheckPoint       string    `json:"check_point" `
	Str1             string    `json:"str1" `
	Str2             string    `json:"str2" `
	Str3             string    `json:"str3" `
	Str4             string    `json:"str4" `
	Str5             string    `json:"str5" `
	Num1             string    `json:"num1" `
	Num2             string    `json:"num2" `
	ResultCode       string    `json:"result_code" `
	ResultMsg        string    `json:"result_msg" `
	FailedRule       string    `json:"failed_rule" `
	CreateTime       time.Time `json:"create_time" `
	RequestParameter string    `json:"request_parameter" `
}
