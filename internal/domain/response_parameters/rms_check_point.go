package response_parameters

type CheckPointResp struct {
	ID                 int64   `json:"id"`
	Code               string  `json:"code"`
	Name               string  `json:"name"`
	CheckDuplicate     *bool   `json:"check_duplicate"`
	Remark             *string `json:"remark"`
	AlwaysRun          int     `json:"always_run"`
	BusinessType       int64   `json:"business_type"`
	FilterFields       string  `json:"filter_fields"`
	DefaultPkFieldName *string `json:"default_primary_key"`
	ConfigProgress     int32   `json:"config_progress"`
}

type CheckPointListResp struct {
	Total int64             `json:"total"`
	Items []*CheckPointResp `json:"items"`
}

type CheckPointNameLabel struct {
	Code  string `json:"code"`
	Label string `json:"label"`
}
