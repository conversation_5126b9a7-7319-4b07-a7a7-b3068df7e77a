package response_parameters

type BlackWhiteAuditDetail struct {
	ListName       string `json:"list_name" example:"test"`             // 名单名称
	ListType       int    `json:"list_type" example:"1"`                // 名单类型 1.黑名单 2.白名单
	BwlID          int64  `json:"bwl_id" example:"1"`                   // 黑白名单ID
	SubmissionTime int64  `json:"submission_time" example:"1672531200"` // 提交时间,时间戳
	Mode           int    `json:"mode" example:"1"`                     // 操作类型 1.创建操作 2.编辑操作 3.删除名单操作 4.开启状态操作 5.关闭状态操作
	State          int    `json:"state"`                                // 审核状态:1.待审核 2.审核通过 3.审核拒绝
	Submitter      string `json:"submitter" example:"test"`             // 提交人
	Detail         string `json:"detail" example:"test"`                // 提交详情
}

type BlackWhiteAuditList struct {
	Id             int64  `json:"id" example:"1"`                       // 审核记录ID
	ListName       string `json:"list_name" example:"test"`             // 名单名称
	Submitter      string `json:"submitter" example:"test"`             // 提交人
	BwlID          int64  `json:"bwl_id" example:"1"`                   // 黑白名单ID
	ListType       int    `json:"list_type" example:"1"`                // 名单类型 1.黑名单 2.白名单
	SubmissionTime int64  `json:"submission_time" example:"1672531200"` // 提交时间,时间戳
	Mode           int    `json:"mode" example:"1"`                     // 操作类型 1.创建操作 2.编辑操作 3.删除名单操作 4.开启状态操作 5.关闭状态操作
	AuditTime      int64  `json:"audit_time" example:"1672531200"`      // 审核时间,时间戳
	Auditor        string `json:"auditor" example:"test"`               // 审核人
	State          int    `json:"state" example:"1"`                    // 状态 1.待审核 2.审核通过 3.审核拒绝
}
