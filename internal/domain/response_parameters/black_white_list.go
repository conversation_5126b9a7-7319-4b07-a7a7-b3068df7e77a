package response_parameters

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
)

type CreateBlackWhiteRecord struct {
	Id int64 `json:"id" example:"1"` // 黑白名单ID
}
type CreateBlackWhiteItem struct {
	BlackWhiteItemID int64 `json:"bwi_id" example:"1"` // 黑白名单记录ID
}

type GetBlackWhiteRecord struct {
	ID                int64         `json:"id" example:"1" binding:"required"`      // 名单ID
	Name              string        `json:"name" example:"test" binding:"required"` // 名单名称
	Type              int           `json:"type" example:"1" binding:"required"`    // 名单类型 1.黑名单 2.白名单
	AccessPoints      []string      `json:"access_points"`                          // 接入点名称
	Note              string        `json:"note" example:"test note" `              // 备注
	Status            int           `json:"status" example:"1" `                    // 状态开关 0:关闭 1:开启
	State             int           `json:"state" example:"1" `                     // 状态 0.草稿 1.编辑状态草稿 2.待审核 3.审核通过 4.审核拒绝 5.移除
	Submitter         string        `json:"submitter"`                              // 最后一次提交人名字
	LastAuditState    int           `json:"last_audit_state" example:"1" `          // 最后一次审核状态 1.待审核 2.审核通过 3.审核拒绝
	ListEffectiveTime int64         `json:"list_effective_time"`                    // 审批通过的时间，时间戳
	Fields            entity.Fields `json:"fields"  `                               // 字段
}

type BlackWhiteItems struct {
	BlackWhiteItemID int64                         `json:"bwi_id" example:"1"`        // 黑白名单记录ID
	StartDate        int64                         `json:"start_date" `               // 开始日期，时间戳
	ExpirationDate   int64                         `json:"expiration_date" `          // 结束日期，时间戳
	Permanent        bool                          `json:"permanent" example:"true" ` // 是否永久
	RemainingDay     int64                         `json:"remaining_day" example:"1"` // 剩余时间
	Extends          modelv2.BlackWhiteItemExtends `json:"extends"  `                 // 扩展字段
}

// BlackWhiteList 黑白名单列表返回结构
type BlackWhiteList struct {
	ID             int64    `json:"id" example:"1" `                  // 名单ID
	Name           string   `json:"name" example:"test" `             // 名单名称
	Type           int      `json:"type" example:"1" `                // 名单类型 1.黑名单 2.白名单
	AccessPoints   []string `json:"access_points"`                    // 接入点名称
	Status         int      `json:"status" example:"1" `              // 状态开关 0:关闭 1:开启
	State          int      `json:"state" example:"1" `               // 状态 0.草稿 1.编辑状态草稿 2.待审核 3.审核通过 4.审核拒绝 5.移除
	RequiredFields []string `json:"required_fields" example:"1,2" `   // 必填字段，值为字段对应的名称
	CreatedBy      string   `json:"created_by" example:"test" `       // 最后编辑人
	CreatedAt      int64    `json:"created_at" example:"1717728000" ` // 创建时间,秒级时间戳
	ValidItems     int64    `json:"valid_items" example:"1" `         // 名单当前生效数
	TotalItems     int64    `json:"total_items" example:"1" `         // 名单当前总数
}
