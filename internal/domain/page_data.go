package domain

import (
	"encoding/json"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type PolicyConfigPageList struct {
	PageSearch
	RuleParameterGroupId int64 `example:"79" json:"rule_parameter_group_id"` // 规则参数组id
}

type PolicyConfigPolicyResp struct {
	FilterFieldMap json.RawMessage `json:"filter_field_map"` // example:"{\"orderNo\":[\"CREDIT\"]}" 策略匹配的过滤条件，需事先设置检查点的过滤字段
	RuleIdList     json.RawMessage `json:"rule_id_list"`     // example:"[1,2]" 策略中需要执行的规则id列表json
	PolicyResp
}

type PolicyConfigPageAllFilterFieldResp struct {
	FieldName string          `json:"field_name"` // 字段名
	FieldEnum json.RawMessage `json:"field_enum"` // 枚举类型列表的json，各枚举由编码和名称构成
}

type PolicyConfigPageResp struct {
	Policy         *PolicyConfigPolicyResp
	RuleLi         []*RuleResp
	AllFilterField []*PolicyConfigPageAllFilterFieldResp
}

// BusinessConfigSecondField 第二过滤字段
type BusinessConfigSecondField struct {
	FieldName string          `json:"field_name"` // 字段名，对应 EventField 的字段名
	FieldEnum json.RawMessage `json:"field_enum"` // 字段枚举，对应 FilterField.FieldEnum
}

// BusinessConfigBusinessTypeAll 所有业务配置
type BusinessConfigBusinessTypeAll struct {
	BusinessCode string `example:"DEPOSIT" json:"business_code"` // 业务编码
	BusinessName string `example:"充值" json:"business_name"`      // 业务名称
}

type BusinessConfigResp struct {
	BusinessTypesLi   []*model.CheckPointBusinessConfig `json:"business_types_list"`    // 业务类型
	SecondFieldLi     []*BusinessConfigSecondField      `json:"second_field_list"`      // 第二匹配条件
	BusinessTypeAll   []*BusinessConfigBusinessTypeAll  `json:"business_type_all"`      // 所有业务配置
	PrimaryKeyFieldLi []string                          `json:"primary_key_field_list"` // 主键字段
}

type RmsRuleParameterGroupBindRuleParameterParameterValue struct {
	ID          int64   `json:"id"`
	ParamKey    string  `gorm:"column:param_key;type:varchar(16);comment:健，非键值对类型可空" json:"param_key"`       // 健，非键值对类型可空
	ParamValue  string  `gorm:"column:param_value;type:varchar(1024);not null;comment:值" json:"param_value"` // 值
	Description *string `gorm:"column:description;type:varchar(256);comment:描述" json:"description"`          // 描述
}

type RmsRuleParameterGroupBindRuleParameter struct {
	ID             int64                                                   `json:"id"`
	ParamType      string                                                  `example:"STRING" json:"parameter_type" `              // 参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes
	Code           string                                                  `example:"CP003_DEFAULT_ACTION" json:"parameter_code"` // 参数编码
	Description    string                                                  `example:"默认操作" json:"parameter_description"`          // 参数描述
	Bind           string                                                  `example:"YES" json:"bind"`                            // 是否绑定，根据 rms_rule_parameter_value 是否有值调用 SetIsBind 或 SetNotBind
	ParameterValue []*RmsRuleParameterGroupBindRuleParameterParameterValue `json:"parameter_value"`                               // 参数值
}

func (d *RmsRuleParameterGroupBindRuleParameter) SetIsBind() {
	d.Bind = "YES"
}

func (d *RmsRuleParameterGroupBindRuleParameter) SetNotBind() {
	d.Bind = "NO"
}

type RmsRuleParameterGroupBindResp struct {
	PageData
	ItemList []*RmsRuleParameterGroupBindRuleParameter `json:"item_list"` // 规则参数
}
