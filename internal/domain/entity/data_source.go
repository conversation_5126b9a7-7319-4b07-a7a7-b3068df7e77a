package entity

import (
	"fmt"
	"strconv"
	"strings"
)

type DataSourceColumnID string

func NewDataSourceColumnID(tableID int64, columnName string) DataSourceColumnID {
	return DataSourceColumnID(fmt.Sprintf("%d|%s", tableID, columnName))
}
func NewDataSourceColumnByColumnID(columnID DataSourceColumnID) *DataSourceColumn {
	ex := strings.Split(string(columnID), "|")
	if len(ex) != 2 {
		return nil
	}
	tableID, err := strconv.ParseInt(ex[0], 10, 64)
	if err != nil {
		return nil
	}
	return &DataSourceColumn{
		ColumnName: ex[1],
		ColumnID:   columnID,
		TableID:    tableID,
	}
}

type DataSourceTable struct {
	TableName    string `json:"table_name"`
	DataSourceID int64  `json:"-"`
}

type DataSourceColumn struct {
	TableID    int64              `json:"table_id"`
	ColumnName string             `json:"column_name"`
	ColumnID   DataSourceColumnID `json:"column_id"`
	FieldType  string             `json:"field_type"`
	DataType   string             `json:"data_type"`
	ColumnType string             `json:"column_type"`
	Comment    string             `json:"comment"`
}
