package entity

type Black<PERSON>hiteField struct {
	ID         int64  `gorm:"column:id;type:bigint(11);not null;primaryKey;autoIncrement:true;comment:主键" json:"id"`
	Name       string `gorm:"column:name;type:varchar(100);not null" json:"name"`
	Field      string `gorm:"column:field;type:varchar(100);not null;comment:字段" json:"field"`
	FieldType  int    `gorm:"column:field_type;type:int(11);not null;comment:字段类型1-字符串 2-数字 3-日期 4-日期时间" json:"field_type"`
	Expected   string `gorm:"column:expected;type:varchar(100);not null;comment:预期值" json:"expected"`
	Rule       string `gorm:"column:rule;type:text;comment:规则" json:"rule"`
	General    int    `gorm:"column:general;type:tinyint(1);not null;default:0;comment:通用字段 1.通用字段 0:特殊字段" json:"general"`
	Enum       string `gorm:"column:enum;type:text;comment:枚举值" json:"enum"`
	IsRequired bool   `json:"is_required"`
}
