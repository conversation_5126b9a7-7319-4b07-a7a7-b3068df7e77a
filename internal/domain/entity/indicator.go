package entity

import (
	"errors"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"
)

// IndicatorRuleRequest 指标规则请求参数
type IndicatorRule struct {
	ID               int64              `json:"id" `                            // 规则ID,创建时为空
	HasLeftBrackets  bool               `json:"has_left_brackets" binding:"-"`  // 是否包含左括号
	HasRightBrackets bool               `json:"has_right_brackets" binding:"-"` // 是否包含右括号
	Connector        string             `json:"connector"`                      // 连接符(and, or)
	ColumnID         DataSourceColumnID `json:"column_id" `                     // 列名
	Operator         string             `json:"operator" `                      // 操作符 ne(!=),gt(>),lt(<),like(Contains),eq(=),lte(<=),gte(>=),ne(!=),in,notin,is_true,is_false
	// 右侧比较值信息
	Value     string `json:"value" `      // 比较值的字段名或具体值
	ValueType string `json:"value_type" ` // 比较值类型(fixed: 固定值, field: 字段比较)

}

func (r *IndicatorRule) Validator() (err error) {
	if r.Connector != "" && r.Connector != "and" && r.Connector != "or" {
		err = errors.New("connector is incorrect format")
		return
	}
	if r.Operator != "" && !consts.IndicatorRuleOperatorMap[r.Operator] {
		err = errors.New("operator is incorrect format")
		return
	}
	if r.ValueType != "" && !consts.IndicatorRuleValueTypeMap[r.ValueType] {
		err = errors.New("value type is incorrect format")
		return
	}
	return nil
}

// 以下结构体用来生成指标数据
type Indicator struct {
	ID             int64
	DataSourceType string
	DataSourceID   int64
	AccessPoint    string
	IndicatorName  string
	TableID        int64
	MeasureType    string
	Measures       []Measure
	Rules          []Rule
	TimeWindow     TimeWindow
}
type Measure struct {
	AggType       string
	MeasureField  DataSourceColumnID
	ConditionName string
}
type Rule struct {
	HasLeftBrackets  bool
	HasRightBrackets bool
	Connector        string
	ColumnID         DataSourceColumnID
	Operator         string
	Value            string
	ValueType        string
}

type TimeWindow struct {
	TimeWindowType      int
	TimeWindowValue     int
	TimeWindowUnit      int
	TimeWindowStartTime string
	TimeWindowEndTime   string
	TimeWindowColumnID  DataSourceColumnID
	TimeWindowExcluding bool
}

func (c *Indicator) Validator() (err error) {
	if c.IndicatorName == "" {
		err = errors.New("indicator name is required")
		return
	}
	if c.DataSourceType == "" {
		err = errors.New("data source type is required")
		return
	}
	if c.AccessPoint == "" {
		err = errors.New("access point is required")
		return
	}
	if c.TableID == 0 {
		err = errors.New("table id is required")
		return
	}
	if c.TimeWindow.TimeWindowStartTime != "" ||
		c.TimeWindow.TimeWindowEndTime != "" ||
		c.TimeWindow.TimeWindowType > 0 ||
		c.TimeWindow.TimeWindowValue > 0 ||
		c.TimeWindow.TimeWindowUnit > 0 ||
		c.TimeWindow.TimeWindowColumnID != "" ||
		c.TimeWindow.TimeWindowExcluding {
		if c.TimeWindow.TimeWindowType == 0 {
			err = errors.New("time window type is required")
			return
		}
		if c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeSliding &&
			c.TimeWindow.TimeWindowValue == 0 {
			err = errors.New("time window value is required")
			return
		}
		if (c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeBefore ||
			c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeBetween) && c.TimeWindow.TimeWindowStartTime == "" {
			err = errors.New("time window start time is required")
			return
		}
		if (c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeAfter ||
			c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeBetween) && c.TimeWindow.TimeWindowStartTime == "" {
			err = errors.New("time window end time is required")
			return
		}
		if (c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeSliding || c.TimeWindow.TimeWindowType == consts.IndicatorMeasureWindowTypeFixed) && c.TimeWindow.TimeWindowUnit == 0 {
			err = errors.New("time window unit is required")
			return
		}
		if c.TimeWindow.TimeWindowColumnID == "" {
			err = errors.New("time window column id is required")
			return
		}
		if c := NewDataSourceColumnByColumnID(c.TimeWindow.TimeWindowColumnID); c == nil {
			err = errors.New("time window column id format error")
		}
	}

	if len(c.Measures) == 0 {
		err = errors.New("measures is not empty")
		return
	}
	for _, v := range c.Measures {
		if v.MeasureField == "" {
			err = errors.New("measure field is required")
			return
		}
		if c := NewDataSourceColumnByColumnID(v.MeasureField); c == nil && v.MeasureField != "*" {
			err = errors.New("measure field format error")
		}
		if c.MeasureType == consts.IndicatorMeasureTypeAgg {
			if v.AggType == "" {
				err = errors.New("measure aggregate type is required")
				return
			}
			if !consts.IndicatorMeasureAggTypeMap[v.AggType] {
				err = errors.New("measure aggregate type is incorrect format")
				return
			}
		}

		if v.ConditionName == "" {
			err = errors.New("condition name is required")
			return
		}

	}
	for idx, v := range c.Rules {
		if v.Connector == "" && idx > 0 {
			err = errors.New("rule connector is required")
			return
		}
		if v.ColumnID == "" {
			err = errors.New("rule column id is required")
			return
		}
		if v.Operator == "" {
			err = errors.New("operator is required")
			return
		}
		if v.ValueType != consts.IndicatorRuleValueTypeFixed && v.ValueType != consts.IndicatorRuleValueTypeField {
			err = errors.New("the value type must be field or fixed")
			return
		}
		if v.Operator != "is_true" && v.Operator != "is_false" {
			if v.Value == "" {
				err = errors.New("value is required")
				return
			}
			if v.ValueType == "" {
				err = errors.New("value type is required")
				return
			}
		}

	}
	return nil

}
