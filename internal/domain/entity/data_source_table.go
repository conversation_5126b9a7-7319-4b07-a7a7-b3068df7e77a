package entity

import "strings"

type Column struct {
	Name       string `gorm:"column:COLUMN_NAME"`
	DataType   string `gorm:"column:DATA_TYPE"`
	ColumnType string `gorm:"column:COLUMN_TYPE"`
	Comment    string `gorm:"column:COLUMN_COMMENT"`
}

func (c *Column) TransType() string {
	//判断数据库字段类型，然后返回 String，Numeric，DateTime

	switch strings.ToUpper(c.DataType) {
	case "VARCHAR", "CHAR", "TEXT", "LONGTEXT", "MEDIUMTEXT", "TINYTEXT":
		return "String"
	case "INT", "BIGINT", "MEDIUMINT", "SMALLINT", "TINYINT", "DECIMAL":
		return "Numeric"
	case "DATE", "DATETIME", "TIMESTAMP":
		return "DateTime"
	}
	return ""
}
