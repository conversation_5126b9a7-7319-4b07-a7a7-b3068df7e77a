package entity

import (
	jsoniter "github.com/json-iterator/go"
)

type FieldsJson string
type Fields []*Field
type Field struct {
	Name       string `json:"name"` // field name
	IsRequired bool   `json:"is_required"`
}
type FieldValue struct {
	Name  string `json:"name"` // field name
	Value string `json:"value"`
}

func (f Fields) ToJson() FieldsJson {
	json, _ := jsoniter.Marshal(f)
	return FieldsJson(json)
}
func (f FieldsJson) ToFields() Fields {
	var fields Fields
	jsoniter.Unmarshal([]byte(f), &fields)
	return fields
}

type BlackWhiteListDetail struct {
	ID             int      `json:"id" example:"1" binding:"required"`                             // 名单ID
	Name           string   `json:"name" example:"test" binding:"required"`                        // 名单名称
	Type           int      `json:"type" example:"1" binding:"required"`                           // 名单类型 1.黑名单 2.白名单
	AccessPoints   []string `json:"access_point" example:"access_point_name" `                     // 接入点名称
	Note           string   `json:"note" example:"test note" `                                     // 备注
	Status         int      `json:"status" example:"1" `                                           // 状态开关 0:关闭 1:开启
	State          int      `json:"state" example:"1" `                                            // 状态 1.草稿 2.待审核 2.审核通过 3.审核拒绝 4.移除
	Fields         []*Field `json:"fields" example:"[{\"field\":\"name\",\"is_required\":true}]" ` // 字段
	LastAuditState int      `json:"last_audit_state" example:"1" `                                 // 最后一次审核状态 1.待审核 2.审核通过 3.审核拒绝
	CreatedBy      string   //创建人
}

type SaveBlackWhiteRecord struct {
	ID           int64    `json:"id" example:"1"`                               // 名单ID,更新时必填
	Name         string   `json:"name" example:"test" binding:"required"`       // 名单名称，更新时候不处理了字段
	Type         int      `json:"type" example:"1" binding:"required"`          // 名单类型 1.黑名单 2.白名单 ，更新时候不处理了字段
	AccessPoints []string `json:"access_points" example:"access_point_name" `   // 接入点名称
	Note         string   `json:"note" example:"test note"  validate:"max=500"` // 备注
	Fields       Fields   `json:"fields"  `                                     // 字段，更新时候不处理了字段
	Submitter    string
	// Status int      `json:"status" example:"1" binding:"required"`  // 状态 1.启用 2.禁用
}

type Progress struct {
	Name      string `json:"name"`       //进度名称
	Completed bool   `json:"completed"`  //已完成
	Executor  string `json:"executor"`   //执行人
	ExecuteAt int64  `json:"execute_at"` //执行时间,时间戳
}

var RequiredFiledSha256 = "required_field_sha256"

// init 实时打印黑白名单内存数据，便于调试
//func init() {
//	go func() {
//		for {
//			tt := BlackRulesCache.GetAccessPointRules()
//			dd, e := tt.MarshalJSON()
//			fmt.Println(string(dd))
//			fmt.Println(e)
//			time.Sleep(5 * time.Second)
//		}
//	}()
//
//}
