package domain

import (
	"github.com/shopspring/decimal"
	"time"
)

type CraFrameworkUpdate struct {
	CraFrameworkId           string          `json:"cra_framework_id" binding:"required"`                  // 唯一ID
	CraType                  string          `json:"cra_type" binding:"required,oneof=Company Individual"` // cra业务：KYC，KYB
	LowHighestPercentage     decimal.Decimal `json:"low_highest_percentage" binding:"required"`            // low最高分数（百分比）
	LowLowestPercentage      decimal.Decimal `json:"low_lowest_percentage" binding:"required"`             // low最低分数（百分比）
	LowGradeScore            int64           `json:"low_grade_score" binding:"required"`                   // low分数
	MediumHighestPercentage  decimal.Decimal `json:"medium_highest_percentage" binding:"required"`         // medium最高分数（百分比）
	MediumLowestPercentage   decimal.Decimal `json:"medium_lowest_percentage" binding:"required"`          // medium最低分数（百分比）
	MediumGradeScore         int64           `json:"medium_grade_score" gorm:"medium_grade_score"`         // medium分数
	HighHighestPercentage    decimal.Decimal `json:"high_highest_percentage" binding:"required"`           // high最高分数（百分比）
	HighLowestPercentage     decimal.Decimal `json:"high_lowest_percentage" binding:"required"`            // high最低分数（百分比）
	HighGradeScore           int64           `json:"high_grade_score"binding:"required"`                   // high分数
	ProhibitLowestPercentage decimal.Decimal `json:"prohibit_lowest_percentage" binding:"required"`        // prohibit最低分数（百分比）
	Status                   int8            `json:"status" binding:"required,oneof=1 0 -1"`               // 状态：1=active, 0=默认, -1=inactiv
	UpdateTime               time.Time       `json:"update_time" gorm:"update_time"`
}
type CraFrameworkCount struct {
	CraType        string `json:"cra_type" binding:"required,oneof=Company Individual"` // cra业务：KYC，KYB
	HighGradeScore int64  `json:"high_grade_score"`                                     // high分数
}
type RmsCraFrameworkInfo struct {
	CraFrameworkId           string          `json:"cra_framework_id" gorm:"cra_framework_id"`                     // 唯一ID
	CraType                  string          `json:"cra_type" gorm:"cra_type"`                                     // cra业务：KYC，KYB
	LowHighestPercentage     decimal.Decimal `json:"low_highest_percentage" gorm:"low_highest_percentage"`         // low最高分数（百分比）
	LowLowestPercentage      decimal.Decimal `json:"low_lowest_percentage" gorm:"low_lowest_percentage"`           // low最低分数（百分比）
	LowGradeScore            int64           `json:"low_grade_score" gorm:"low_grade_score"`                       // low分数
	MediumHighestPercentage  decimal.Decimal `json:"medium_highest_percentage" gorm:"medium_highest_percentage"`   // medium最高分数（百分比）
	MediumLowestPercentage   decimal.Decimal `json:"medium_lowest_percentage" gorm:"medium_lowest_percentage"`     // medium最低分数（百分比）
	MediumGradeScore         int64           `json:"medium_grade_score" gorm:"medium_grade_score"`                 // medium分数
	HighHighestPercentage    decimal.Decimal `json:"high_highest_percentage" gorm:"high_highest_percentage"`       // high最高分数（百分比）
	HighLowestPercentage     decimal.Decimal `json:"high_lowest_percentage" gorm:"high_lowest_percentage"`         // high最低分数（百分比）
	HighGradeScore           int64           `json:"high_grade_score" gorm:"high_grade_score"`                     // high分数
	ProhibitLowestPercentage decimal.Decimal `json:"prohibit_lowest_percentage" gorm:"prohibit_lowest_percentage"` // prohibit最低分数（百分比）
	Status                   int8            `json:"status" gorm:"status"`                                         // 状态：1=active, 0=默认, -1=inactive
	CreateTime               time.Time       `json:"create_time" gorm:"create_time"`
	UpdateTime               time.Time       `json:"update_time" gorm:"update_time"`
}
