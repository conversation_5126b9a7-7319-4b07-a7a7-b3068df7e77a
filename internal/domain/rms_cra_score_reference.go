package domain

import (
	"github.com/shopspring/decimal"
	"time"
)

type RmsCraScoreReferenceInfo struct {
	ParamValName     string          `json:"param_val_name" gorm:"param_val_name"`         // 参考名
	CraParameterType string          `json:"cra_parameter_type" gorm:"cra_parameter_type"` // AML; COUNTRY; CUSTOM
	AssessmentScore  decimal.Decimal `json:"assessment_score" gorm:"assessment_score"`     // 分数
	AssessmentGrade  int8            `json:"assessment_grade" gorm:"assessment_grade"`     // 等级：0=prohibit, 1=high, 2=medium, 3=low
}
type RmsCraScoreReference struct {
	CraScoreReferenceId string                        `json:"cra_score_reference_id" gorm:"cra_score_reference_id"` // 唯一ID
	CustomerId          string                        `json:"customer_id" gorm:"customer_id"`                       // 客户ID
	EntityId            string                        `json:"entity_id"`
	BusinessCode        int16                         `json:"business_code"`
	Info                *RmsCraScoreReferenceInfoList `json:"info" gorm:"info"`                         // Cra字段详情
	AssessmentScore     decimal.Decimal               `json:"assessment_score" gorm:"assessment_score"` // 分数
	AssessmentGrade     int8                          `json:"assessment_grade" gorm:"assessment_grade"` // 等级：0=prohibit, 1=high, 2=medium, 3=low
	CreateTime          time.Time                     `json:"create_time" gorm:"create_time"`
	Uid                 string                        `json:"uid" gorm:"uid"`
	Nickname            string                        `json:"nickname" gorm:"nickname"`
	Email               string                        `json:"email" gorm:"email"`
}
type RmsCraScoreReferenceInfoList struct {
	AmlList     []*RmsCraScoreReferenceInfo `json:"aml_list"`
	CountryList []*RmsCraScoreReferenceInfo `json:"country_list"`
	CustomList  []*RmsCraScoreReferenceInfo `json:"custom_list"`
}
type RmsCraScoreReferenceRetrieveReq struct {
	CraScoreReferenceId string `json:"cra_score_reference_id"`        // 唯一ID
	CustomerId          string `json:"customer_id"binding:"required"` // 客户ID
	EntityId            string `json:"entity_id"`
	BusinessCode        int16  `json:"business_code"`
}
type RmsCraScoreReferenceListReq struct {
	CustomerId   string `json:"customer_id"binding:"required"` // 客户ID
	EntityId     string `json:"entity_id"`
	BusinessCode int16  `json:"business_code"`
}
