package domain

type RuleParameterGroupBindList struct {
	PageSearch
	ParamGroupId   int64  `json:"parameter_group_id"`                                                                               // 参数组id
	ParamGroupCode string `example:"te" json:"parameter_group_code"`                                                                // 参数组编码
	TargetType     string `example:"MERCHANT" json:"target_type" binding:"omitempty,oneof=MERCHANT CHANNEL CARD_NUMBER RISK_LEVEL"` // 绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级
	TargetID       string `example:"" json:"target_value"`                                                                          // 绑定目标id
}

type RuleParameterGroupBindCreate struct {
	TargetType *string `example:"MERCHANT" json:"target_type" binding:"required,oneof=MERCHANT CHANNEL CARD_NUMBER RISK_LEVEL"` // 绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级
	GroupID    int64   `example:"1" json:"parameter_group_id"  binding:"required"`                                              // 参数组id
	Able       int32   `json:"-" `                                                                                              // 状态，1-有效，0-失效
	State      string  `example:"Yes" json:"state" binding:"oneof=Yes No"`                                                      // 状态，Yes-1-有效，No-0-失效
	TargetID   string  `example:"" json:"target_value"`                                                                         // 绑定目标id
}

type RuleParameterGroupBindCreateList struct {
	GroupIDList []int64 `example:"1,2" json:"parameter_group_id_list" binding:"required"`                                        // 参数组id 数组
	TargetType  *string `example:"MERCHANT" json:"target_type" binding:"required,oneof=MERCHANT CHANNEL CARD_NUMBER RISK_LEVEL"` // 绑定目标类型，MERCHANT-商户，CHANNEL-渠道 CARD_NUMBER-卡号 RISK_LEVEL-风控等级
	Able        int32   `json:"-"`                                                                                               // 状态，1-有效，0-失效
	State       string  `example:"Yes" json:"state" binding:"required,oneof=Yes No"`                                             // 状态，Yes-1-有效，No-0-失效
	TargetID    string  `example:"" json:"target_value" binding:"required"`                                                      // 绑定目标id
}

type RuleParameterGroupBindUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	RuleParameterGroupBindCreate
}

type RuleParameterGroupBindDeleteList struct {
	Value []int64 `json:"value" example:"[1,2,3]" binding:"required"`
}

type RuleParameterGroupBindResp struct {
	ParamGroupCode string `example:"te" json:"parameter_group_code"` // 参数组编码
	RuleParameterGroupBindUpdate
}

type RuleParameterGroupBindListItem struct {
	RuleParameterGroupBindCreate
	ParamGroupCode string `example:"te" json:"parameter_group_code"`                             // 参数组编码
	ID             int64  ` json:"parameter_group_bind_id" example:"1" binding:"required,gte=0"` // 在前端对应界面， id 是关键字会导致报错。配合前端修改成 parameters-group-id
}

type RuleParameterGroupBindListResp struct {
	PageData
	ItemList []*RuleParameterGroupBindListItem `json:"item_list"`
}
