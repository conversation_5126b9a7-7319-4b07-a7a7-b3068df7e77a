package domain

type BusinessTypeList struct {
	BusinessName string `json:"business_name"`
	TimeAndPageSearch
}
type BusinessTypeCreate struct {
	BusinessCode string `example:"DEPOSIT" json:"business_code" binding:"required"` // 业务编码
	BusinessName string `example:"充值" json:"business_name" binding:"required"`      // 业务名称
}

type BusinessTypeUpdate struct {
	ID int64 ` json:"id" example:"1" binding:"required,gte=0"`
	BusinessTypeCreate
}

type BusinessTypeAll struct {
	BusinessCode string `example:"DEPOSIT" json:"business_code"` // 业务编码
	BusinessName string `example:"充值" json:"business_name"`      // 业务名称
}
