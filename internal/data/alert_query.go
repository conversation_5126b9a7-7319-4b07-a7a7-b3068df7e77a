package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type AlertQuery struct {
	*Base
}

func (p *AlertQuery) List(c *gin.Context, db *gorm.DB, req *domain.AlertQueryListReq) (*domain.AlertQueryListRes, error) {
	li := []*model.RiskEventRecord{}
	total, err := p.Base.ListCount(c, db, nil, &li)
	if err != nil {
		return nil, err
	}
	if err := p.Base.ListPage(c, db.Order("create_time DESC"), req, &li); err != nil {
		return nil, err
	}

	res := &domain.AlertQueryListRes{
		ItemList:  li,
		TotalItem: int(total),
	}
	return res, err
}
func (p *AlertQuery) Count(c *gin.Context, db *gorm.DB) (int64, error) {

	return p.Base.Count(c, db, new(model.RiskEventRecord))
}
