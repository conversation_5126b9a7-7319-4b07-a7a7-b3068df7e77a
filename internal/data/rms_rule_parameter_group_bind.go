package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type RuleParameterGroupBind struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *RuleParameterGroupBind) ListLimit(db *gorm.DB, req *domain.RuleParameterGroupBindList) *gorm.DB {
	if req.ParamGroupId != 0 {
		db = db.Where("group_id=?", req.ParamGroupId)
	}
	if req.TargetType != "" {
		db = db.Where("target_type like ?", "%"+req.TargetType+"%")
	}
	if req.TargetID != "" {
		db = db.Where("target_id like ?", "%"+req.TargetID+"%")
	}
	return db
}

func (p *RuleParameterGroupBind) Create(c *gin.Context, cp *model.RmsRuleParameterGroupBind) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *RuleParameterGroupBind) Update(c *gin.Context, id int64, cp *model.RmsRuleParameterGroupBind) error {
	return p.Base.Updates(c, p.db.Where("id=?", id).Select("check_point", "able", "group_id"), cp)
}
func (p *RuleParameterGroupBind) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsRuleParameterGroupBind))
}
func (p *RuleParameterGroupBind) List(c *gin.Context, req *domain.RuleParameterGroupBindList) ([]*model.RmsRuleParameterGroupBind, error) {
	li := []*model.RmsRuleParameterGroupBind{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *RuleParameterGroupBind) Count(c *gin.Context, req *domain.RuleParameterGroupBindList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsRuleParameterGroupBind))
}
func (p *RuleParameterGroupBind) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterGroupBind, error) {
	cp := new(model.RmsRuleParameterGroupBind)
	return cp, p.Base.Retrieve(c, id, cp)
}
func (p *RuleParameterGroupBind) RetrieveByGroupId(c *gin.Context, GroupID int64) (*model.RmsRuleParameterGroupBind, error) {
	cp := new(model.RmsRuleParameterGroupBind)
	return cp, p.Base.First(c, p.db.Where("group_id=?", GroupID), cp)
}
