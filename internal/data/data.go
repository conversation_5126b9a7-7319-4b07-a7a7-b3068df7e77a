package data

import (
	"github.com/google/wire"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

// DataProviderSet is biz providers.
var DataProviderSet = wire.NewSet(
	NewBase,
	wire.Struct(new(AlarmContact), "*"),
	wire.Struct(new(AlarmTimeConf), "*"),
	wire.Struct(new(Blacklist), "*"),
	wire.Struct(new(BusinessType), "*"),
	wire.Struct(new(CheckPoint), "*"),
	wire.Struct(new(Event), "*"),
	wire.Struct(new(EventField), "*"),
	wire.Struct(new(EventStoreCfg), "*"),
	wire.Struct(new(FilterField), "*"),
	wire.Struct(new(OperatorLog), "*"),
	wire.Struct(new(Policy), "*"),
	wire.Struct(new(RiskLog), "*"),
	wire.Struct(new(Rule), "*"),
	wire.Struct(new(RuleParameter), "*"),
	wire.Struct(new(RuleParameterGroup), "*"),
	wire.Struct(new(RuleParameterGroupBind), "*"),
	wire.Struct(new(RuleParameterValue), "*"),
	wire.Struct(new(HandleLog), "*"),
	wire.Struct(new(ConfigProgress), "*"),
	wire.Bind(new(service.IAlarmContactRepo), new(*AlarmContact)),
	wire.Bind(new(service.IBlacklistRepo), new(*Blacklist)),
	wire.Bind(new(service.IAlarmTimeConfRepo), new(*AlarmTimeConf)),
	wire.Bind(new(service.IBusinessTypeRepo), new(*BusinessType)),
	wire.Bind(new(service.ICheckPointRepo), new(*CheckPoint)),
	wire.Bind(new(service.IEventRepo), new(*Event)),
	wire.Bind(new(service.IEventFieldRepo), new(*EventField)),
	wire.Bind(new(service.IEventStoreCfgRepo), new(*EventStoreCfg)),
	wire.Bind(new(service.IFilterFieldRepo), new(*FilterField)),
	wire.Bind(new(service.IOperatorLogRepo), new(*OperatorLog)),
	wire.Bind(new(service.IPolicyRepo), new(*Policy)),
	wire.Bind(new(service.IRiskLogRepo), new(*RiskLog)),
	wire.Bind(new(service.IRuleRepo), new(*Rule)),
	wire.Bind(new(service.IRuleParameterRepo), new(*RuleParameter)),
	wire.Bind(new(service.IRuleParameterGroupRepo), new(*RuleParameterGroup)),
	wire.Bind(new(service.IRuleParameterGroupBindRepo), new(*RuleParameterGroupBind)),
	wire.Bind(new(service.IRuleParameterValueRepo), new(*RuleParameterValue)),
	wire.Bind(new(service.IHandleLogRepo), new(*HandleLog)),
	wire.Bind(new(service.IConfigProgressRepo), new(*ConfigProgress)),
)
