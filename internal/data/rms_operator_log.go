package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type OperatorLog struct {
	*Base
}

func (p *OperatorLog) Create(c *gin.Context, cp *model.RmsOperatorLog) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *OperatorLog) Update(c *gin.Context, id int64, cp *model.RmsOperatorLog) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *OperatorLog) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsOperatorLog))
}
func (p *OperatorLog) List(c *gin.Context, req *domain.OperatorLogList) ([]*model.RmsOperatorLog, error) {
	li := []*model.RmsOperatorLog{}
	return li, p.Base.ListPage(c, p.db.Order("id DESC"), req, &li)
}
func (p *OperatorLog) Count(c *gin.Context, req *domain.OperatorLogList) (count int64, err error) {
	return p.Base.ListCount(c, p.db, req, new(model.RmsOperatorLog))
}
func (p *OperatorLog) Retrieve(c *gin.Context, id int64) (*model.RmsOperatorLog, error) {
	cp := new(model.RmsOperatorLog)
	return cp, p.Base.Retrieve(c, id, cp)
}
