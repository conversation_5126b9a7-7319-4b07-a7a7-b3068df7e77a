package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type AlarmContact struct {
	*Base
}

func (p *AlarmContact) Create(c *gin.Context, cp *model.RmsAlarmContact) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *AlarmContact) Update(c *gin.Context, id int64, cp *model.RmsAlarmContact) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *AlarmContact) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsAlarmContact))
}
func (p *AlarmContact) List(c *gin.Context, req *domain.AlarmContactList) ([]*model.RmsAlarmContact, error) {
	li := []*model.RmsAlarmContact{}
	return li, p.Base.ListPage(c, p.db.Order("id DESC"), req, &li)
}
func (p *AlarmContact) Count(c *gin.Context, req *domain.AlarmContactList) (count int64, err error) {
	return p.Base.ListCount(c, p.db, req, new(model.RmsAlarmContact))
}
func (p *AlarmContact) Retrieve(c *gin.Context, id int64) (*model.RmsAlarmContact, error) {
	cp := new(model.RmsAlarmContact)
	return cp, p.Base.Retrieve(c, id, cp)
}
