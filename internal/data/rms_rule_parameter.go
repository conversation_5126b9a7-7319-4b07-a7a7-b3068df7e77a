package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type RuleParameter struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *RuleParameter) ListLimit(db *gorm.DB, req *domain.RuleParameterList) *gorm.DB {
	if req.CheckPoint != "" {
		db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
	}
	if req.ParamType != "" {
		db = db.Where("param_type=?", req.ParamType)
	}
	if req.Code != "" {
		db = db.Where("code like ?", "%"+req.Code+"%")
	}
	if req.Description != "" {
		db = db.Where("description like ?", "%"+req.Description+"%")
	}
	return db
}

func (p *RuleParameter) Create(c *gin.Context, cp *model.RmsRuleParameter) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *RuleParameter) Update(c *gin.Context, id int64, cp *model.RmsRuleParameter) error {
	return p.Base.Updates(c, p.db.Omit("access_flag").Where("id=?", id), cp)
}
func (p *RuleParameter) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsRuleParameter))
}
func (p *RuleParameter) List(c *gin.Context, req *domain.RuleParameterList) ([]*model.RmsRuleParameter, error) {
	li := []*model.RmsRuleParameter{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *RuleParameter) Count(c *gin.Context, req *domain.RuleParameterList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsRuleParameter))
}
func (p *RuleParameter) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameter, error) {
	cp := new(model.RmsRuleParameter)
	return cp, p.Base.Retrieve(c, id, cp)
}

func (p *RuleParameter) ListByAPCode(c *gin.Context, req *domain.PolicyConfigPageList, apCode string) ([]*model.RmsRuleParameter, error) {
	li := []*model.RmsRuleParameter{}
	// check_point 是逗号分割的 check_point.code
	return li, p.Base.ListPage(c, p.db.Where("check_point like ?", "%"+apCode+"%"), req, &li)
}

func (p *RuleParameter) CountByAPCode(c *gin.Context, apCode string) (int64, error) {
	return p.Base.Count(c, p.db.Where("check_point like ?", "%"+apCode+"%"), new(model.RmsRuleParameter))
}
