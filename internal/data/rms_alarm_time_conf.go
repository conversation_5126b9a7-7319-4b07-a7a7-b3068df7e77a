package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type AlarmTimeConf struct {
	*Base
}

func (p *AlarmTimeConf) Create(c *gin.Context, cp *model.RmsAlarmTimeConf) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *AlarmTimeConf) Update(c *gin.Context, id int64, cp *model.RmsAlarmTimeConf) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *AlarmTimeConf) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsAlarmTimeConf))
}
func (p *AlarmTimeConf) List(c *gin.Context, req *domain.AlarmTimeConfList) ([]*model.RmsAlarmTimeConf, error) {
	li := []*model.RmsAlarmTimeConf{}
	return li, p.Base.ListPage(c, p.db.Order("id DESC"), req, &li)
}
func (p *AlarmTimeConf) Count(c *gin.Context, req *domain.AlarmTimeConfList) (count int64, err error) {
	return p.Base.ListCount(c, p.db, req, new(model.RmsAlarmTimeConf))
}
func (p *AlarmTimeConf) Retrieve(c *gin.Context, id int64) (*model.RmsAlarmTimeConf, error) {
	cp := new(model.RmsAlarmTimeConf)
	return cp, p.Base.Retrieve(c, id, cp)
}
