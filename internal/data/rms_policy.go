package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
	"time"
)

type Policy struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *Policy) ListLimit(c *gin.Context, db *gorm.DB, req *domain.PolicyList) *gorm.DB {
	if req.CheckPoint != "" {
		db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
	}
	// PolicyGroup 是规则组名，对应 policy_name
	if req.PolicyGroup != "" {
		db = db.Where("policy_name like ?", "%"+req.PolicyGroup+"%")
	}
	if req.PolicyNo != "" {
		db = db.Where("policy_no like ?", "%"+req.PolicyNo+"%")
	}
	return db
}

func (p *Policy) Create(c *gin.Context, cp *model.RmsPolicy) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *Policy) Update(c *gin.Context, id int64, cp *model.RmsPolicy) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *Policy) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsPolicy))
}
func (p *Policy) List(c *gin.Context, req *domain.PolicyList) ([]*model.RmsPolicy, error) {
	li := []*model.RmsPolicy{}
	return li, p.Base.ListPage(c, p.ListLimit(c, p.db, req).Order("id DESC"), req, &li)
}
func (p *Policy) Count(c *gin.Context, req *domain.PolicyList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(c, p.db, req), req, new(model.RmsPolicy))
}
func (p *Policy) Retrieve(c *gin.Context, id int64) (*model.RmsPolicy, error) {
	cp := new(model.RmsPolicy)
	return cp, p.Base.Retrieve(c, id, cp)
}

func (p *Policy) FindByCPCode(c *gin.Context, CPCode string) ([]*model.RmsPolicy, error) {
	models := []*model.RmsPolicy{}
	return models, p.Base.All(c, p.db.Where("check_point=?", CPCode), &models)
}

func (p *Policy) RetrieveByCode(c *gin.Context, code string) (*model.RmsPolicy, error) {
	cp := new(model.RmsPolicy)
	return cp, p.Base.First(c, p.db.Where("policy_no=?", code), cp)
}

func (p *Policy) SetStatus(c *gin.Context, id int64, status string) error {
	syncAt := time.Now().Local()
	cp := &model.RmsPolicy{Status: status, SyncedAt: &syncAt, ID: id}
	return p.Base.Updates(c, p.db.Select("status"), cp)
}
