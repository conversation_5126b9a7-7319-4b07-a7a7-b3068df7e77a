package data

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
)

type RmsCraParameterVal struct {
	*Base
}

func (p *RmsCraParameterVal) Create(c *gin.Context, cp *model.RmsCraParameterVal) error {
	err := p.Base.CreateOmit(c, cp, "update_time")
	if err != nil {
		return err
	}
	return p.UpdateRdbForParameterVal(c, cp.CraFrameworkId)

}
func (p *RmsCraParameterVal) Update(c *gin.Context, param_val_id string, cp *model.RmsCraParameterVal) error {
	RmsCraParameterValInfo := &model.RmsCraParameterVal{}
	err := p.db.Model(model.RmsCraParameterVal{}).Where("param_val_id=?", param_val_id).First(RmsCraParameterValInfo).Error
	if err != nil {
		return err
	}
	err = p.db.Select("*").Omit("id,create_time,param_val_name,cra_framework_id").Where("param_val_id=?", param_val_id).Updates(cp).Error
	if err != nil {
		return err
	}
	return p.UpdateRdbForParameterVal(c, RmsCraParameterValInfo.CraFrameworkId)
}
func (p *RmsCraParameterVal) Delete(c *gin.Context, param_val_id string) error {
	RmsCraParameterValInfo := &model.RmsCraParameterVal{}
	err := p.db.Model(model.RmsCraParameterVal{}).Where("param_val_id=?", param_val_id).First(RmsCraParameterValInfo).Error
	if err != nil {
		return err
	}
	err = p.db.Where("param_val_id=?", param_val_id).Delete(new(model.RmsCraParameterVal)).Error
	if err != nil {
		return err
	}
	return p.UpdateRdbForParameterVal(c, RmsCraParameterValInfo.CraFrameworkId)
}
func (p *RmsCraParameterVal) List(c *gin.Context, data *model.RmsCraParameterVal) ([]*model.RmsCraParameterVal, error) {
	li := []*model.RmsCraParameterVal{}
	err := p.db.Where(data).Find(&li).Error
	return li, err
}
func (p *RmsCraParameterVal) Count(c *gin.Context) (count int64, err error) {
	err = p.db.Model(model.RmsCraParameterVal{}).Count(&count).Error
	return
}

func (p *RmsCraParameterVal) Retrieve(c *gin.Context, param_val_id string) (*model.RmsCraParameterVal, error) {
	f := new(model.RmsCraParameterVal)
	err := p.db.Where("param_val_id=?", param_val_id).First(f).Error
	return f, err
}
func (p *RmsCraParameterVal) First(c *gin.Context, cp *model.RmsCraParameterVal) (val *model.RmsCraParameterVal, err error) {
	err = p.db.Where(cp).First(&val).Error
	return

}
func (p *RmsCraParameterVal) GetParameterName(ids []string) (list []*model.RmsCraParameterVal, err error) {
	err = p.db.Model(model.RmsCraParameterVal{}).Where("param_val_id in ?", ids).Find(&list).Error
	return
}
func (p *RmsCraParameterVal) UpdateRdbForParameterVal(c *gin.Context, cra_framework_id string) error {
	list := make([]*model.RmsCraParameterVal, 0)
	err := p.db.Model(model.RmsCraParameterVal{}).Where("cra_framework_id = ?", cra_framework_id).Find(&list).Error
	if err != nil {
		return err
	}

	for _, val := range list {
		userJSON, _ := json.Marshal(val)

		// 存储 JSON 字符串
		err = global.Rdb.Set(c, fmt.Sprintf("risk:cra:rms_cra_parameterval:%s", val.ParamValId), userJSON, 0).Err()
		if err != nil {
			return err
		}
	}

	return nil
}
