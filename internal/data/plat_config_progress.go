package data

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
)

type ConfigProgress struct {
	*Base
}

func (p *ConfigProgress) CreateOrUpdate(c *gin.Context, cp *model.PlatConfigProgress) error {
	pt := new(model.PlatConfigProgress)
	err := p.First(c, p.db.Where("check_point_code=?", cp.CheckPointCode), pt)
	if errors.Is(err, er.NotFound) {
		return p.Base.Create(c, p.db, cp)
	}
	return p.Base.Updates(c, p.db.Where("check_point_code=?", cp.CheckPointCode), cp)
}
func (p *ConfigProgress) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.PlatConfigProgress))
}
func (p *ConfigProgress) Retrieve(c *gin.Context, code string) (*model.PlatConfigProgress, error) {
	cp := new(model.PlatConfigProgress)
	return cp, p.Base.First(c, p.db.Where("check_point_code = ?", code), cp)
}
