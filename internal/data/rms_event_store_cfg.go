package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type EventStoreCfg struct {
	*Base
}

func (p *EventStoreCfg) Create(c *gin.Context, cp *model.RmsEventStoreCfg) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *EventStoreCfg) Update(c *gin.Context, id int64, cp *model.RmsEventStoreCfg) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *EventStoreCfg) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsEventStoreCfg))
}
func (p *EventStoreCfg) List(c *gin.Context, req *domain.EventStoreCfgList) ([]*model.RmsEventStoreCfg, error) {
	li := []*model.RmsEventStoreCfg{}
	return li, p.Base.ListPage(c, p.db, req, &li)
}
func (p *EventStoreCfg) Count(c *gin.Context, req *domain.EventStoreCfgList) (count int64, err error) {
	return p.Base.ListCount(c, p.db, req, new(model.RmsEventStoreCfg))
}
func (p *EventStoreCfg) Retrieve(c *gin.Context, id int64) (*model.RmsEventStoreCfg, error) {
	cp := new(model.RmsEventStoreCfg)
	return cp, p.Base.Retrieve(c, id, cp)
}

func (p *EventStoreCfg) RetrieveByAPCode(c *gin.Context, apCode string) (*model.RmsEventStoreCfg, error) {
	cp := new(model.RmsEventStoreCfg)
	return cp, p.Base.First(c, p.db.Where("check_point=?", apCode), cp)
}
