package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type FilterField struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *FilterField) ListLimit(db *gorm.DB, req *domain.FilterFieldList) *gorm.DB {
	if req.CheckPoint != "" {
		db = db.Where("check_point=?", req.CheckPoint)
	}
	if req.FieldName != "" {
		db = db.Where("field_name=?", req.FieldName)
	}
	return db
}

func (p *FilterField) Create(c *gin.Context, cp *model.RmsFilterField) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *FilterField) Update(c *gin.Context, id int64, cp *model.RmsFilterField) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *FilterField) UpdateFieldName(c *gin.Context, oriName, newName string) error {
	return p.Base.Update(c, p.db.Model(new(model.RmsFilterField)).Where("field_name=?", oriName), "field_name", newName)
}
func (p *FilterField) DeleteById(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsFilterField))
}
func (p *FilterField) DeleteByFieldNameAndAPCode(c *gin.Context, apCode, field_name string) error {
	return p.Base.Delete(c, p.db.Where("check_point=? and field_name=?", apCode, field_name), new(model.RmsFilterField))
}
func (p *FilterField) List(c *gin.Context, req *domain.FilterFieldList) ([]*model.RmsFilterField, error) {
	li := []*model.RmsFilterField{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *FilterField) AllByAPCode(c *gin.Context, apCode string) ([]*model.RmsFilterField, error) {
	li := []*model.RmsFilterField{}
	return li, p.Base.AllByCPCode(c, p.db, &li, apCode)
}
func (p *FilterField) Count(c *gin.Context, req *domain.FilterFieldList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsFilterField))
}
func (p *FilterField) Retrieve(c *gin.Context, id int64) (*model.RmsFilterField, error) {
	cp := new(model.RmsFilterField)
	return cp, p.Base.Retrieve(c, id, cp)
}
func (p *FilterField) RetrieveByName(c *gin.Context, apCode, field_name string) (*model.RmsFilterField, error) {
	cp := new(model.RmsFilterField)
	return cp, p.Base.First(c, p.db.Where("check_point=? and field_name=?", apCode, field_name), cp)
}
func (p *FilterField) GetByFieldNameAndAPCode(c *gin.Context, apCode, field_name string) (*model.RmsFilterField, error) {
	cp := new(model.RmsFilterField)
	return cp, p.Base.Take(c, p.db.Where("check_point=? and field_name=?", apCode, field_name), cp)
}
