package data

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gorm.io/gorm"
)

type CheckPoint struct {
	*Base
}

func (p *CheckPoint) Listlimit(db *gorm.DB, req *domain.CheckPointList) *gorm.DB {
	if req.Code != "" {
		db = db.Where("code like ?", "%"+req.Code+"%")
	}
	if req.Label != "" {
		db = db.Where("label like ?", "%"+req.Label+"%")
	}
	return db
}

func (p *CheckPoint) Create(c *gin.Context, cp *model.RmsCheckPoint) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *CheckPoint) Update(c *gin.Context, id int64, cp *model.RmsCheckPoint) error {
	return p.Base.Updates(c, p.db.Select(
		"code",
		"label",
		"check_duplicate",
		"memo",
	).Where("id=?", id), cp)
}
func (p *CheckPoint) UpdateDefaultPkFieldName(c *gin.Context, id int64, name string) error {
	return p.Base.Update(c, p.db.Where("id=?", id).Model(new(model.RmsCheckPoint)), "default_pk_field_name", name)
}
func (p *CheckPoint) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsCheckPoint))
}
func (p *CheckPoint) List(c *gin.Context, req *domain.CheckPointList) ([]*model.RmsCheckPoint, error) {
	li := []*model.RmsCheckPoint{}
	return li, p.Base.ListPage(c, p.Listlimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *CheckPoint) Count(c *gin.Context, req *domain.CheckPointList) (count int64, err error) {
	return p.Base.ListCount(c, p.Listlimit(p.db, req), req, new(model.RmsCheckPoint))
}
func (p *CheckPoint) Retrieve(c *gin.Context, id int64) (*model.RmsCheckPoint, error) {
	cp := new(model.RmsCheckPoint)
	return cp, p.Base.Retrieve(c, id, cp)
}

func (p *CheckPoint) RetrieveByCode(c *gin.Context, code string) (*model.RmsCheckPoint, error) {
	cp := new(model.RmsCheckPoint)
	return cp, p.Base.First(c, p.db.Where("code=?", code), cp)
}

func (p *CheckPoint) AllNameCodeList(c *gin.Context) ([]*model.RmsCheckPoint, error) {
	data := []*model.RmsCheckPoint{}
	err := p.db.Select("code", "label").Find(&data).Error
	return data, er.ConvertDBError(err)
}
func (p *CheckPoint) UpdateBusinessConfig(c *gin.Context, code string, businessConfig string) error {
	db := p.db.Model(new(model.RmsCheckPoint)).Where("code=?", code)
	return p.Base.Updates(c, db, map[string]any{"business_types": businessConfig})
}
func (p *CheckPoint) UpdateFilterFields(c *gin.Context, code string, FilterFields string) error {
	db := p.db.Model(new(model.RmsCheckPoint)).Where("code=?", code)
	return p.Base.Updates(c, db, map[string]any{"filter_fields": FilterFields})
}
func (p *CheckPoint) ExistBusinessCode(businessCode string) (bool, error) {
	cp := new(model.RmsCheckPoint)
	err := er.ConvertDBError(p.db.Where("business_types like ?", `%"businessCode":"`+businessCode+`"%`).First(cp).Error)
	if err != nil {
		if errors.Is(err, er.NotFound) {
			return false, nil
		}
		return false, err
	}
	return true, err
}
