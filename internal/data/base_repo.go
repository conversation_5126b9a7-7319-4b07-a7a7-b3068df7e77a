package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gorm.io/gorm"
	"time"
)

func NewBase(db *gorm.DB) *Base {
	return &Base{
		db: db,
	}
}

type Base struct {
	db *gorm.DB
}

type IPageParam interface {
	GetPageNum() int
	GetPageSize() int
}

type ITimeParam interface {
	GetDateType() string
	GetStartTime() int64
	GetEndTime() int64
}

// IListParam ListPage 的通用参数处理，支持时间和分页筛选
type IListFilter interface {
	IPageParam
	ITimeParam
}

// ListFilter 为 ListPage 处理通用的分页和时间筛选参数，返回增加筛选的 db。
func (p *Base) ListFilter(db *gorm.DB, req any) *gorm.DB {
	if pageReq, ok := req.(IPageParam); ok {
		db = p.PageFilter(db, pageReq)
	}
	if timeReq, ok := req.(ITimeParam); ok {
		db = p.TimeFilter(db, timeReq)
	}
	return db
}

// CountFilter 为 ListCount 处理通用的筛选参数，返回增加筛选的 db
func (p *Base) CountFilter(db *gorm.DB, req any) *gorm.DB {
	if timeReq, ok := req.(ITimeParam); ok {
		db = p.TimeFilter(db, timeReq)
	}
	return db
}

func (p *Base) PageFilter(db *gorm.DB, req IPageParam) *gorm.DB {
	pageNum := 1
	pageSize := 10
	if req.GetPageNum() != 0 {
		pageNum = req.GetPageNum()
	}
	if req.GetPageSize() != 0 || req.GetPageSize() < 1000 {
		pageSize = req.GetPageSize()
	}
	offset := (pageNum - 1) * pageSize
	return db.Offset(offset).Limit(pageSize)
}

func (t *Base) TimeFilter(db *gorm.DB, req ITimeParam) *gorm.DB {
	field := "created_at"
	switch req.GetDateType() {
	case "created_at", "updated_at", "synced_at":
		field = req.GetDateType()
	}

	if req.GetStartTime() > 0 {
		db.Where(field+" >= ?", time.Unix(req.GetStartTime(), 0))
	}
	if req.GetEndTime() > 0 {
		db.Where(field+" <= ?", time.Unix(req.GetEndTime(), 0))
	}
	return db
}

func (p *Base) Create(c *gin.Context, db *gorm.DB, ptr any) error {
	err := db.Create(ptr).Error
	return er.ConvertDBError(err)
}

// Updates 更新数据，注意 db 带更新范围限制
func (p *Base) Update(c *gin.Context, db *gorm.DB, key string, value any) error {
	err := db.Update(key, value).Error
	return er.ConvertDBError(err)
}

// Updates 更新数据，注意 db 带更新范围限制
func (p *Base) Updates(c *gin.Context, db *gorm.DB, ptr any) error {
	err := db.Updates(ptr).Omit("created_at").Error
	return er.ConvertDBError(err)
}
func (p *Base) DeleteById(c *gin.Context, id int64, ptr any) error {
	return er.ConvertDBError(p.db.Where("id=?", id).Delete(ptr).Error)
}
func (p *Base) Delete(c *gin.Context, db *gorm.DB, ptr any) error {
	return er.ConvertDBError(db.Delete(ptr).Error)
}
func (p *Base) ListPage(c *gin.Context, db *gorm.DB, req any, ptr any) error {
	db = p.ListFilter(db, req)
	return er.ConvertDBError(db.Find(ptr).Error)
}

func (p *Base) ListCount(c *gin.Context, db *gorm.DB, req any, ptr any) (count int64, err error) {
	db = p.CountFilter(db, req)
	return count, er.ConvertDBError(db.Model(ptr).Count(&count).Error)
}

func (p *Base) Count(c *gin.Context, db *gorm.DB, ptr any) (count int64, err error) {
	return count, er.ConvertDBError(db.Model(ptr).Count(&count).Error)
}

func (p *Base) Retrieve(c *gin.Context, id int64, ptr any) error {
	return er.ConvertDBError(p.db.Where("id=?", id).First(ptr).Error)
}

func (p *Base) First(c *gin.Context, db *gorm.DB, ptr any) error {
	return er.ConvertDBError(db.First(ptr).Error)
}

// Take 与 First 类似，但不要求排序，返回第一条记录
func (p *Base) Take(c *gin.Context, db *gorm.DB, ptr any) error {
	return er.ConvertDBError(db.Take(ptr).Error)
}

func (p *Base) CreateOmit(c *gin.Context, ptr any, Omit ...string) error {
	err := p.db.Omit(Omit...).Create(ptr).Error
	return er.ConvertDBError(err)
}

func (p *Base) All(c *gin.Context, db *gorm.DB, ptr any) error {
	return er.ConvertDBError(db.Find(ptr).Error)
}

func (p *Base) AllByCPCode(c *gin.Context, db *gorm.DB, ptr any, cpCode string) error {
	return er.ConvertDBError(db.Where("check_point=?", cpCode).Find(ptr).Error)
}

func (p *Base) RetrieveOmit(c *gin.Context, id int64, ptr any, Omit ...string) error {
	return er.ConvertDBError(p.db.Omit(Omit...).Where("id=?", id).First(ptr).Error)
}
