package data

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
)

type RmsCraFramework struct {
	*Base
}

func (p *RmsCraFramework) Update(c *gin.Context, cra_framework_id string, cp *domain.CraFrameworkUpdate) error {

	info := model.RmsCraFramework{
		CraFrameworkId:           cp.CraFrameworkId,                                                         // 唯一ID
		CraType:                  cp.CraType,                                                                // cra业务：KYC，KYB
		LowHighestPercentage:     cp.LowHighestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(),     // low最高分数（百分比）
		LowLowestPercentage:      cp.LowLowestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(),      // low最低分数（百分比）
		LowGradeScore:            cp.LowGradeScore,                                                          // low分数
		MediumHighestPercentage:  cp.MediumHighestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(),  // medium最高分数（百分比）
		MediumLowestPercentage:   cp.MediumLowestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(),   // medium最低分数（百分比）
		MediumGradeScore:         cp.MediumGradeScore,                                                       // medium分数
		HighHighestPercentage:    cp.HighHighestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(),    // high最高分数（百分比）
		HighLowestPercentage:     cp.HighLowestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(),     // high最低分数（百分比）
		HighGradeScore:           cp.HighGradeScore,                                                         // high分数
		ProhibitLowestPercentage: cp.ProhibitLowestPercentage.Div(decimal.NewFromInt(100)).InexactFloat64(), // prohibit最低分数（百分比）
		Status:                   cp.Status,                                                                 // 状态：1=active, 0=默认, -1=inactiv
		UpdateTime:               cp.UpdateTime,
	}
	err := p.db.Select("*").Omit("create_time,id ").Where("cra_framework_id=?", cra_framework_id).Updates(info).Error
	if err != nil {
		return err
	}

	//存储redis
	list := make([]*model.RmsCraFramework, 0)
	err = p.db.Model(model.RmsCraFramework{}).Where("1 = 1").Find(&list).Error
	if err != nil {
		return err
	}

	for _, val := range list {
		userJSON, _ := json.Marshal(val)

		// 存储 JSON 字符串
		err = global.Rdb.Set(c, fmt.Sprintf("risk:cra:rms_cra_framework:%s", val.CraFrameworkId), userJSON, 0).Err()
		if err != nil {
			return err
		}
	}

	return err
}

func (p *RmsCraFramework) Retrieve(c *gin.Context, cra_framework_id string) (*model.RmsCraFramework, error) {
	f := new(model.RmsCraFramework)
	err := p.db.Where("cra_framework_id=?", cra_framework_id).First(f).Error
	return f, err
}
func (p *RmsCraFramework) RiskScoreCount(c *gin.Context, req *domain.CraFrameworkCount) (string, error) {
	//查询 高等级 分数
	framework := &model.RmsCraFramework{}
	err := p.db.Where("cra_type=?", req.CraType).First(framework).Error
	if err != nil {
		return "", err
	}

	list := make([]*model.RmsCraParameter, 0)
	err = p.db.Where("cra_framework_id= ? and cra_parameter_status = ?", framework.CraFrameworkId, 1).Find(&list).Error
	if err != nil {
		return "", err
	}
	count := decimal.NewFromInt(0)
	if req.HighGradeScore != 0 {
		framework.HighGradeScore = req.HighGradeScore
	}
	for _, parameter := range list {
		count = count.Add(decimal.NewFromFloat(parameter.Weight).Mul(decimal.NewFromInt(framework.HighGradeScore)))
	}

	return count.String(), nil
}
func (p *RmsCraFramework) List(c *gin.Context) ([]*model.RmsCraFramework, error) {
	li := []*model.RmsCraFramework{}
	err := p.db.Find(&li).Error
	return li, err
}
func (p *RmsCraFramework) First(c *gin.Context, cp *model.RmsCraFramework) (*model.RmsCraFramework, error) {
	//查询 高等级 分数
	framework := &model.RmsCraFramework{}
	err := p.db.Where(cp).First(framework).Error
	if err != nil {
		return framework, err
	}
	return framework, err
}
