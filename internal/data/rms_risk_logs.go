package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type RiskLog struct {
	*Base
}

func (p *RiskLog) Create(c *gin.Context, cp *model.RmsRiskLog) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *RiskLog) Update(c *gin.Context, id int64, cp *model.RmsRiskLog) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *RiskLog) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsRiskLog))
}
func (p *RiskLog) List(c *gin.Context, req *domain.RiskLogList) ([]*model.RmsRiskLog, error) {
	li := []*model.RmsRiskLog{}
	return li, p.Base.ListPage(c, p.db.Order("id DESC"), req, &li)
}
func (p *RiskLog) Count(c *gin.Context, req *domain.RiskLogList) (count int64, err error) {
	return p.Base.ListCount(c, p.db, req, new(model.RmsRiskLog))
}
func (p *RiskLog) Retrieve(c *gin.Context, id int64) (*model.RmsRiskLog, error) {
	cp := new(model.RmsRiskLog)
	return cp, p.Base.Retrieve(c, id, cp)
}
