package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
	"strings"
)

type Event struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *Event) ListLimit(c *gin.Context, db *gorm.DB, req *domain.EventList) (*gorm.DB, error) {
	switch req.Field {
	case "STR_FIELD1", "STR_FIELD2", "STR_FIELD3", "STR_FIELD4", "STR_FIELD5", "NUM_FIELD1", "NUM_FIELD2":
		Field := strings.ToLower(req.Field)
		db = db.Where(Field+"=?", req.FieldValue)
	}
	if req.CheckPoint != "" {
		db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
	}
	if req.ResultCode != "" {
		db = db.Where("result_code like ?", "%"+req.ResultCode+"%")
	}
	return db, nil
}

func (p *Event) Create(c *gin.Context, cp *model.RmsEvent) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *Event) Update(c *gin.Context, id int64, cp *model.RmsEvent) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *Event) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsEvent))
}
func (p *Event) List(c *gin.Context, req *domain.EventList) ([]*model.RmsEvent, error) {
	li := []*model.RmsEvent{}
	db, err := p.ListLimit(c, p.db, req)
	if err != nil {
		return nil, err
	}
	return li, p.Base.ListPage(c, db.Order("id DESC"), req, &li)
}
func (p *Event) Count(c *gin.Context, req *domain.EventList) (count int64, err error) {
	db, err := p.ListLimit(c, p.db, req)
	if err != nil {
		return 0, err
	}
	return p.Base.ListCount(c, db, req, new(model.RmsEvent))
}
func (p *Event) Retrieve(c *gin.Context, id int64) (*model.RmsEvent, error) {
	cp := new(model.RmsEvent)
	return cp, p.Base.Retrieve(c, id, cp)
}
