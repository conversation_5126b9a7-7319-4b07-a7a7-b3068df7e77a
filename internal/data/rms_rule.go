package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type Rule struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *Rule) ListLimit(c *gin.Context, db *gorm.DB, req *domain.RuleList) *gorm.DB {
	if req.CheckPoint != "" {
		db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
	}
	if req.RuleName != "" {
		db = db.Where("rule_name like ?", "%"+req.RuleName+"%")
	}
	if req.RuleNo != "" {
		db = db.Where("rule_no like ?", "%"+req.RuleNo+"%")
	}
	return db
}

func (p *Rule) Create(c *gin.Context, cp *model.RmsRule) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *Rule) Update(c *gin.Context, id int64, cp *model.RmsRule) error {
	return p.Base.Updates(c, p.db.Where("id=?", id).Select(
		"check_point",
		"rule_no",
		"rule_name",
		"start_time",
		"end_time",
		"agenda_name",
		"rule_content",
		"memo",
		"status",
		"short_circuit",
		"deploy_method",
		"synced_at",
		"updated_at",
		"priority",
	), cp)
}
func (p *Rule) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsRule))
}
func (p *Rule) List(c *gin.Context, req *domain.RuleList) ([]*model.RmsRule, error) {
	li := []*model.RmsRule{}
	return li, p.Base.ListPage(c, p.ListLimit(c, p.db.Order("id DESC"), req), req, &li)
	//return li, p.Base.ListPage(c, p.ListLimit(c, p.db, req).Omit("rule_content"), req, &li)
}
func (p *Rule) AllByCPCode(c *gin.Context, cpCode string) ([]*model.RmsRule, error) {
	li := []*model.RmsRule{}
	return li, p.Base.AllByCPCode(c, p.db.Order("id DESC"), &li, cpCode)
	//return li, p.Base.AllByCPCode(c, p.db.Omit("rule_content"), &li, cpCode)
}
func (p *Rule) Count(c *gin.Context, req *domain.RuleList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(c, p.db, req), req, new(model.RmsRule))
}
func (p *Rule) Retrieve(c *gin.Context, id int64) (*model.RmsRule, error) {
	cp := new(model.RmsRule)
	return cp, p.Base.Retrieve(c, id, cp)
}

func (p *Rule) SetStatus(c *gin.Context, id int64, cp *model.RmsRule) error {
	return p.Base.Updates(c, p.db.Select("start_time", "end_time", "deploy_method", "status", "synced_at"), cp)
}
