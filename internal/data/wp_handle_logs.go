package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type HandleLog struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *HandleLog) ListLimit(c *gin.Context, db *gorm.DB, req *domain.HandleLogList) *gorm.DB {
	if req.Puserid != "" {
		db = db.Where("puserid=?", req.Puserid)
	}
	if req.HandleEvents != "" {
		db = db.Where("handle_events=?", req.HandleEvents)
	}
	if req.HandleParams != "" {
		db = db.Where("handle_params=?", req.HandleParams)
	}
	return db
}

func (p *HandleLog) Create(c *gin.Context, cp *model.WpHandleLog) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *HandleLog) Update(c *gin.Context, id int64, cp *model.WpHandleLog) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *HandleLog) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.WpHandleLog))
}
func (p *HandleLog) List(c *gin.Context, req *domain.HandleLogList) ([]*model.WpHandleLog, error) {
	li := []*model.WpHandleLog{}
	return li, p.Base.ListPage(c, p.ListLimit(c, p.db, req), req, &li)
}
func (p *HandleLog) Count(c *gin.Context, req *domain.HandleLogList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(c, p.db, req), req, new(model.WpHandleLog))
}
func (p *HandleLog) Retrieve(c *gin.Context, id int64) (*model.WpHandleLog, error) {
	cp := new(model.WpHandleLog)
	return cp, p.Base.Retrieve(c, id, cp)
}
