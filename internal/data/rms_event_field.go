package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type EventField struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *EventField) ListLimit(db *gorm.DB, req *domain.EventFieldList) *gorm.DB {
	if req.CheckPoint != "" {
		db = db.Where("check_point=?", req.CheckPoint)
	}
	if req.FieldName != "" {
		db = db.Where("field_name=?", req.FieldName)
	}
	return db
}

func (p *EventField) Create(c *gin.Context, cp *model.RmsEventField) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *EventField) Update(c *gin.Context, id int64, cp *model.RmsEventField) error {
	return p.Base.Updates(c, p.db.Where("id=?", id).Select(
		"check_point",
		"field_name",
		"field_type",
		"memo",
		"required",
	), cp)
}
func (p *EventField) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsEventField))
}
func (p *EventField) List(c *gin.Context, req *domain.EventFieldList) ([]*model.RmsEventField, error) {
	li := []*model.RmsEventField{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *EventField) Count(c *gin.Context, req *domain.EventFieldList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsEventField))
}
func (p *EventField) Retrieve(c *gin.Context, id int64) (*model.RmsEventField, error) {
	cp := new(model.RmsEventField)
	return cp, p.Base.Retrieve(c, id, cp)
}
func (p *EventField) RetrieveByName(c *gin.Context, apCode, field_name string) (*model.RmsEventField, error) {
	cp := new(model.RmsEventField)
	return cp, p.Base.First(c, p.db.Where("check_point=? and field_name=?", apCode, field_name), cp)
}
func (p *EventField) AllByAPCode(c *gin.Context, ap_code string) ([]*model.RmsEventField, error) {
	li := []*model.RmsEventField{}
	db := p.db.Where("check_point=?", ap_code)
	return li, p.Base.All(c, db, &li)
}
