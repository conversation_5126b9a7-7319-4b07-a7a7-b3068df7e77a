package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsRule 风控规则
type RmsRule struct {
	Empty
}

// TableName RmsRule's table name
func (*RmsRule) TableName() string {
	return modelv2.TableNameRmsRule
}

func NewRmsRule() repo.RmsRule {
	return &RmsRule{}
}

func (r *RmsRule) Create(ctx context.Context, session *gorm.DB, models ...*modelv2.RmsRule) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsRule{}).Create(models)
		if resTx.Error != nil {
			return er.ConvertDBError(resTx.Error)
		}
		return nil
	})
	return
}

func (r *RmsRule) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRule) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsRule{})).Select(
			"check_point",
			"rule_no",
			"rule_name",
			"start_time",
			"end_time",
			"agenda_name",
			"rule_content",
			"memo",
			"status",
			"short_circuit",
			"deploy_method",
			"synced_at",
			"updated_at",
			"priority",
		).Updates(model).Error)
	})
}

func (r *RmsRule) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsRule{})).Delete(&modelv2.RmsRule{}).Error)
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsRule) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRule, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsRule{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsRule) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsRule{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsRule) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRule, error) {
	var item modelv2.RmsRule
	var err error
	err = fn(global.DB.Model(&modelv2.RmsRule{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsRule) GetByRuleNo(ctx context.Context, ruleNo string) (*modelv2.RmsRule, error) {
	var item modelv2.RmsRule
	err := global.DB.Model(&modelv2.RmsRule{}).Where("rule_no = ?", ruleNo).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsRule) ListByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsRule, error) {
	var items []*modelv2.RmsRule
	err := global.DB.Model(&modelv2.RmsRule{}).Where("check_point = ?", checkPoint).Order("id DESC").WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsRule) ListByStatus(ctx context.Context, status string) ([]*modelv2.RmsRule, error) {
	var items []*modelv2.RmsRule
	err := global.DB.Model(&modelv2.RmsRule{}).Where("status = ?", status).Order("id DESC").WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsRule) ListByRuleGroup(ctx context.Context, ruleGroup string) ([]*modelv2.RmsRule, error) {
	var items []*modelv2.RmsRule
	err := global.DB.Model(&modelv2.RmsRule{}).Where("rule_group = ?", ruleGroup).Order("id DESC").WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
func (r *RmsRule) SetStatus(ctx context.Context, session *gorm.DB, id int64, cp *modelv2.RmsRule) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return tx.Select("start_time", "end_time", "deploy_method", "status", "synced_at").Where("id =?", id).Updates(cp).Error
	})

}
