package datav2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsFilterField 事件过滤字段配置（亦即枚举字段配置）
type RmsFilterField struct {
	Empty
}

// TableName RmsFilterField's table name
func (*RmsFilterField) TableName() string {
	return modelv2.TableNameRmsFilterField
}

func NewRmsFilterField() repo.RmsFilterField {
	return &RmsFilterField{}
}

func (r *RmsFilterField) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsFilterField) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsFilterField{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

func (r *RmsFilterField) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsFilterField) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsFilterField{})).Updates(model).Error)
	})
}

func (r *RmsFilterField) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsFilterField{})).Updates(data).Error)
	})
}

func (r *RmsFilterField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsFilterField{})).Delete(&modelv2.RmsFilterField{}).Error)
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsFilterField) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsFilterField, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsFilterField{})).WithContext(ctx))
	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsFilterField) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsFilterField{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsFilterField) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsFilterField, error) {
	var item modelv2.RmsFilterField
	var err error
	err = fn(global.DB.Model(&modelv2.RmsFilterField{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsFilterField) FindByFieldName(ctx context.Context, fieldName string) ([]*modelv2.RmsFilterField, error) {
	var items []*modelv2.RmsFilterField
	err := global.DB.Model(&modelv2.RmsFilterField{}).Where("field_name = ?", fieldName).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsFilterField) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsFilterField, error) {
	var items []*modelv2.RmsFilterField
	err := global.DB.Model(&modelv2.RmsFilterField{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsFilterField) FindByFieldNameAndCheckPoint(ctx context.Context, fieldName, checkPoint string) (*modelv2.RmsFilterField, error) {
	var item modelv2.RmsFilterField
	err := global.DB.Model(&modelv2.RmsFilterField{}).Where("field_name = ? AND check_point = ?", fieldName, checkPoint).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsFilterField) AllByAPCode(ctx context.Context, apCode string) ([]*modelv2.RmsFilterField, error) {
	var items []*modelv2.RmsFilterField
	err := global.DB.Model(&modelv2.RmsFilterField{}).Where("check_point = ?", apCode).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
