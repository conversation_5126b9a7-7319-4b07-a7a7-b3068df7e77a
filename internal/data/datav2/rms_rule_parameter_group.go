package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsRuleParameterGroup 规则参数组
type RmsRuleParameterGroup struct {
	Empty
}

func NewRmsRuleParameterGroup() repo.RmsRuleParameterGroupRepo {
	return &RmsRuleParameterGroup{}
}

// TableName RmsRuleParameterGroup's table name
func (*RmsRuleParameterGroup) TableName() string {
	return modelv2.TableNameRmsRuleParameterGroup
}

func (r *RmsRuleParameterGroup) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsRuleParameterGroup) (id int64, err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsRuleParameterGroup{}).Create(model)
		if resTx.Error != nil {
			return er.ConvertDBError(resTx.Error)
		}
		id = model.ID
		return nil
	})
	return
}

func (r *RmsRuleParameterGroup) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRuleParameterGroup) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameterGroup{})).Updates(model).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

func (r *RmsRuleParameterGroup) UpdateField(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, field string, value interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameterGroup{})).Update(field, value).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

func (r *RmsRuleParameterGroup) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameterGroup{})).Delete(&modelv2.RmsRuleParameterGroup{}).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsRuleParameterGroup) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRuleParameterGroup, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsRuleParameterGroup{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsRuleParameterGroup) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsRuleParameterGroup{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsRuleParameterGroup) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRuleParameterGroup, error) {
	var item modelv2.RmsRuleParameterGroup
	var err error
	err = fn(global.DB.Model(&modelv2.RmsRuleParameterGroup{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsRuleParameterGroup) FindByCode(ctx context.Context, code string) (*modelv2.RmsRuleParameterGroup, error) {
	var item modelv2.RmsRuleParameterGroup
	err := global.DB.Model(&modelv2.RmsRuleParameterGroup{}).Where("code = ?", code).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsRuleParameterGroup) FindByCheckPoint(ctx context.Context, checkPoint string) (*modelv2.RmsRuleParameterGroup, error) {
	var item modelv2.RmsRuleParameterGroup
	err := global.DB.Model(&modelv2.RmsRuleParameterGroup{}).Where("check_point = ?", checkPoint).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsRuleParameterGroup) ListAll(ctx context.Context) ([]*modelv2.RmsRuleParameterGroup, error) {
	var items []*modelv2.RmsRuleParameterGroup
	err := global.DB.Model(&modelv2.RmsRuleParameterGroup{}).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsRuleParameterGroup) ListExcludeDefault(ctx context.Context) ([]*modelv2.RmsRuleParameterGroup, error) {
	var items []*modelv2.RmsRuleParameterGroup
	err := global.DB.Model(&modelv2.RmsRuleParameterGroup{}).Where("default_flag = ?", 0).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsRuleParameterGroup) UpdateParamIds(ctx context.Context, session *gorm.DB, id int64, paramIds string) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return r.UpdateField(ctx, global.DB, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", id)
		}, "param_ids", paramIds)
	})
}
