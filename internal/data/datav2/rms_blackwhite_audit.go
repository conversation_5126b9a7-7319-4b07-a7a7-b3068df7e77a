package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/json"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

var _ repo.BlackWhiteAuditRepo = &RmsBlackWhiteAudit{}

// RmsBlackWhiteAudit 黑名单白名单审核
type RmsBlackWhiteAudit struct {
	Empty
}

func NewRmsBlackWhiteAudit() repo.BlackWhiteAuditRepo {
	return &RmsBlackWhiteAudit{}
}

// TableName RmsBlackWhiteAudit's table name
func (r *RmsBlackWhiteAudit) TableName() string {
	return modelv2.TableNameRmsBlackWhiteAudit
}

// 创建审核任务
func (r *RmsBlackWhiteAudit) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsBlackWhiteAudit) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = er.ConvertDBError(tx.WithContext(ctx).Create(model).Error)
		if innerErr != nil {
			return innerErr
		}
		innerErr = er.ConvertDBError(NewRmsBlackWhiteList().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", model.BwlID)
		}, map[string]interface{}{"state": modelv2.BlackWhiteListStatePendingApproval}))
		if innerErr != nil {
			return innerErr
		}
		innerErr = er.ConvertDBError(NewRmsBlackWhiteItem().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", model.BwlID).Where("status = ?", modelv2.BlackWhiteItemStatusDraft)
		}, map[string]interface{}{"status": modelv2.BlackWhiteItemStatusPendingApproval, "audit_id": model.ID}))
		if innerErr != nil {
			return innerErr
		}
		innerErr = er.ConvertDBError(NewRmsBlackWhiteItem().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", model.BwlID).Where("status = ?", modelv2.BlackWhiteItemStatusRemoveDraft)
		}, map[string]interface{}{"status": modelv2.BlackWhiteItemStatusRemovePendingApproval, "audit_id": model.ID}))
		if innerErr != nil {
			return innerErr
		}
		return
	})
}
func (r *RmsBlackWhiteAudit) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteAudit, error) {
	var item modelv2.RmsBlackWhiteAudit
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteAudit{})).WithContext(ctx).First(&item).Error)
	return &item, err
}
func (r *RmsBlackWhiteAudit) Updates(ctx context.Context, session *gorm.DB, fn func(tx *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteAudit{})).Updates(data).Error)
	})
}
func (r *RmsBlackWhiteAudit) Audit(ctx context.Context, session *gorm.DB, auditModel *modelv2.RmsBlackWhiteAudit, state int, approver string, reason string) error {
	bwl, err := NewRmsBlackWhiteList().First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", auditModel.BwlID)
	})
	if err != nil {
		return err
	}
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = r.Updates(ctx, tx, func(tx *gorm.DB) *gorm.DB {
			return tx.Where("id = ?", auditModel.ID)
		}, map[string]interface{}{
			"state":    state,
			"reason":   reason,
			"approver": approver,
		})
		if innerErr != nil {
			return innerErr
		}
		switch auditModel.Mode {
		case modelv2.BlackWhiteAuditModeDelete:
			innerErr = r.auditRemoveList(ctx, tx, bwl, auditModel, state)
			if innerErr != nil {
				return
			}
			if state == modelv2.BlackWhiteAuditStatePass {
				innerErr = NewRmsBlackWhiteListCache(bwl.Type == modelv2.BlackWhiteListTypeBlack).RemoveBlackWhite(bwl.ID)
			}
			return
		case modelv2.BlackWhiteAuditModeTurnOn:

			innerErr = r.auditChangeStatus(ctx, tx, auditModel, modelv2.BlackWhiteListStatusTurnOn, state)
			if innerErr != nil {
				return
			}
			if state == modelv2.BlackWhiteAuditStatePass {
				innerErr = NewRmsBlackWhiteListCache(bwl.Type == modelv2.BlackWhiteListTypeBlack).ChangeStatus(bwl.ID, true)
			}
			return
		case modelv2.BlackWhiteAuditModeTurnOff:
			innerErr = r.auditChangeStatus(ctx, tx, auditModel, modelv2.BlackWhiteListStatusTurnOff, state)
			if innerErr != nil {
				return
			}
			if state == modelv2.BlackWhiteAuditStatePass {
				innerErr = NewRmsBlackWhiteListCache(bwl.Type == modelv2.BlackWhiteListTypeBlack).ChangeStatus(bwl.ID, false)
			}
			return
		case modelv2.BlackWhiteAuditModeCreate, modelv2.BlackWhiteAuditModeEdit:
			innerErr = r.auditSave(ctx, tx, auditModel, state)
			if innerErr != nil {
				return
			}
			if state == modelv2.BlackWhiteAuditStatePass {
				innerErr = NewRmsBlackWhiteListCache(bwl.Type == modelv2.BlackWhiteListTypeBlack).IncUpdate(ctx, bwl.ID)
			}
			return
		}
		return nil
	})

}
func (r *RmsBlackWhiteAudit) auditRemoveList(ctx context.Context, session *gorm.DB, bwl *modelv2.RmsBlackWhiteList, auditModel *modelv2.RmsBlackWhiteAudit, state int) error {
	switch state {
	case modelv2.BlackWhiteAuditStatePass:
		return NewRmsBlackWhiteList().Remove(ctx, session, auditModel.BwlID, auditModel.Submitter)
	case modelv2.BlackWhiteAuditStateReject:
		var tmpBwl modelv2.RmsBlackWhiteList
		if bwl.ChangeData != "" {
			tmpBwl = json.JsonUnmarshal[modelv2.RmsBlackWhiteList](bwl.ChangeData)

		}
		return NewRmsBlackWhiteList().Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", auditModel.BwlID)
		}, map[string]interface{}{
			"state":       tmpBwl.State,
			"change_data": "",
		})
	}
	return nil

}
func (r *RmsBlackWhiteAudit) auditChangeStatus(ctx context.Context, session *gorm.DB, auditModel *modelv2.RmsBlackWhiteAudit, status int, state int) error {

	switch state {
	case modelv2.BlackWhiteAuditStatePass:
		//删除现行规则
		var listState = modelv2.BlackWhiteListStatePass
		if status == modelv2.BlackWhiteListStatusTurnOff {
			listState = modelv2.BlackWhiteListStateDisable
		}
		return NewRmsBlackWhiteList().Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", auditModel.BwlID)
		}, map[string]interface{}{
			"state":       listState,
			"status":      status,
			"change_data": "",
		})
	case modelv2.BlackWhiteAuditStateReject:
		var tmpModel modelv2.RmsBlackWhiteList
		listModel, err := NewRmsBlackWhiteList().First(ctx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", auditModel.BwlID)
		})
		if err != nil {
			return err
		}
		if listModel.ChangeData != "" {
			tmpModel = json.JsonUnmarshal[modelv2.RmsBlackWhiteList](listModel.ChangeData)
		}

		return NewRmsBlackWhiteList().Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", auditModel.BwlID)
		}, map[string]interface{}{
			"state":       tmpModel.State,
			"change_data": "",
		})
	}
	return nil
}

func (r *RmsBlackWhiteAudit) auditSave(ctx context.Context, session *gorm.DB, auditModel *modelv2.RmsBlackWhiteAudit, state int) (err error) {
	switch state {
	case modelv2.BlackWhiteAuditStatePass:
		err = NewRmsBlackWhiteList().AuditPass(ctx, session, auditModel.BwlID)
		if err != nil {
			return err
		}
		return NewRmsBlackWhiteItem().AuditPass(ctx, session, auditModel.BwlID)
	case modelv2.BlackWhiteAuditStateReject:
		err = NewRmsBlackWhiteList().AuditReject(ctx, session, auditModel.BwlID)
		if err != nil {
			return err
		}
		return NewRmsBlackWhiteItem().AuditReject(ctx, session, auditModel.BwlID)
	}
	return nil
}

func (r *RmsBlackWhiteAudit) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteAudit, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteAudit{})).WithContext(ctx))
	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}
