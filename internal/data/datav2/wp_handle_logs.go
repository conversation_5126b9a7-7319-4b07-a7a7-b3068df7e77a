package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// WpHandleLog 用户操作日志
type WpHandleLog struct {
	Empty
}

func NewWpHandleLog() repo.HandleLogRepo {
	return &WpHandleLog{}
}

// TableName WpHandleLog's table name
func (*WpHandleLog) TableName() string {
	return modelv2.TableNameWpHandleLog
}

func (r *WpHandleLog) Create(ctx context.Context, session *gorm.DB, model *modelv2.WpHandleLog) (id int32, err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.WpHandleLog{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		id = model.Hlid
		return nil
	})
	return
}
func (r *WpHandleLog) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.WpHandleLog) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.WpHandleLog{})).Updates(model).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}
func (r *WpHandleLog) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.WpHandleLog{})).Delete(&modelv2.WpHandleLog{}).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据

func (r *WpHandleLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.WpHandleLog, err error) {
	tx := fn(global.DB.Model(&modelv2.WpHandleLog{})).WithContext(ctx)

	// 获取列表数据
	err = r.DoDefaultFilter(defaultFilter, tx).Order("hlid desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数

	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *WpHandleLog) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.WpHandleLog{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}
func (r *WpHandleLog) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.WpHandleLog, error) {
	var item modelv2.WpHandleLog
	var err error
	err = fn(global.DB.Model(&RmsBlackWhiteList{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}
