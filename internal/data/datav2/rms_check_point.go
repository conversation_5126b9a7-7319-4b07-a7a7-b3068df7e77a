package datav2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsCheckPoint 检查点
type RmsCheckPoint struct {
	Empty
}

// TableName RmsCheckPoint's table name
func (*RmsCheckPoint) TableName() string {
	return modelv2.TableNameRmsCheckPoint
}

func NewRmsCheckPoint() repo.RmsCheckPoint {
	return &RmsCheckPoint{}
}
func (m *RmsCheckPoint) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsCheckPoint) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsCheckPoint{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

func (m *RmsCheckPoint) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsCheckPoint) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsCheckPoint{})).Updates(model).Error)
	})
}

func (m *RmsCheckPoint) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsCheckPoint{})).Updates(data).Error)
	})
}

func (m *RmsCheckPoint) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsCheckPoint{})).Delete(&modelv2.RmsCheckPoint{}).Error)
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (m *RmsCheckPoint) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsCheckPoint, err error) {
	tx := m.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsCheckPoint{})).WithContext(ctx))

	// 获取列表数据
	err = m.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (m *RmsCheckPoint) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsCheckPoint{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (m *RmsCheckPoint) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsCheckPoint, error) {
	var item modelv2.RmsCheckPoint
	err := fn(global.DB.Model(&modelv2.RmsCheckPoint{})).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}
func (m *RmsCheckPoint) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsCheckPoint, error) {
	var item modelv2.RmsCheckPoint
	var err error
	err = fn(global.DB.Model(&modelv2.RmsCheckPoint{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (m *RmsCheckPoint) FindByCode(ctx context.Context, code string) (*modelv2.RmsCheckPoint, error) {
	var item modelv2.RmsCheckPoint
	err := global.DB.Model(&modelv2.RmsCheckPoint{}).Where("code = ?", code).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (m *RmsCheckPoint) FindByAlwaysRun(ctx context.Context, alwaysRun int) ([]*modelv2.RmsCheckPoint, error) {
	var items []*modelv2.RmsCheckPoint
	err := global.DB.Model(&modelv2.RmsCheckPoint{}).Where("always_run = ?", alwaysRun).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
