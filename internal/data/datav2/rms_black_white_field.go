package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

var _ repo.BlackWhiteFieldRepo = &RmsBlackWhiteField{}

// RmsBlackWhiteField 黑名单白名单字段列表
type RmsBlackWhiteField struct {
	Empty
}

func NewRmsBlackWhiteField() repo.BlackWhiteFieldRepo {
	return &RmsBlackWhiteField{}
}

// TODO 后期再说
// TableName RmsBlackWhiteField's table name
func (*RmsBlackWhiteField) TableName() string {
	return modelv2.TableNameRmsBlackWhiteField
}
func (r *RmsBlackWhiteField) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteField, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteField{})).WithContext(ctx))
	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

func (r *RmsBlackWhiteField) BatchCreate(ctx context.Context, session *gorm.DB, m []*modelv2.RmsBlackWhiteField) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		if innerErr := tx.Model(&modelv2.RmsBlackWhiteField{}).Create(m).Error; innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

// Delete
func (r *RmsBlackWhiteField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx).Delete(&modelv2.RmsBlackWhiteField{}).Error)
	})
}
func (r *RmsBlackWhiteField) GetBlackWhiteFieldsBriefing(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (res []*modelv2.RmsBlackWhiteField, err error) {
	err = er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteField{}).WithContext(ctx)).Find(&res).Error)
	return
}
