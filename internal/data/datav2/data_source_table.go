package datav2

import (
	"context"
	"fmt"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsDataSourceTable 数据源表信息表数据访问层
type RmsDataSourceTable struct {
	Empty
}

// NewRmsDataSourceTable 创建数据源表信息表数据访问层实例
func NewRmsDataSourceTable() repo.DataSourceTableRepo {
	return &RmsDataSourceTable{}
}

// TableName 返回表名
func (r *RmsDataSourceTable) TableName() string {
	return modelv2.TableNameRmsDataSourceTable
}

// List 获取列表数据
func (r *RmsDataSourceTable) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsDataSourceTable, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsDataSourceTable{}))).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

// Count 获取记录总数
func (r *RmsDataSourceTable) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (int64, error) {
	var total int64
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsDataSourceTable{}).WithContext(ctx)).Count(&total).Error)
	return total, err
}

// First 获取单条记录
func (r *RmsDataSourceTable) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsDataSourceTable, error) {
	var item modelv2.RmsDataSourceTable
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsDataSourceTable{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// GetDataSourceByTableID 获取单条记录
func (r *RmsDataSourceTable) GetDataSourceByTableID(ctx context.Context, tableID int64) (*modelv2.RmsDataSource, error) {
	var item modelv2.RmsDataSource
	err := er.ConvertDBError(global.DB.Model(&modelv2.RmsDataSource{}).
		Joins("INNER JOIN rms_data_source_table ON rms_data_source.id = rms_data_source_table.data_source_id").
		Where("rms_data_source_table.id = ?", tableID).Select("rms_data_source.*").
		First(&item).Error)
	return &item, err
}

// Create 创建记录
func (r *RmsDataSourceTable) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsDataSourceTable) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Create(data).Error)
	})
}

// Update 更新记录
func (r *RmsDataSourceTable) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsDataSourceTable) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsDataSourceTable{})).Updates(data).Error)
	})
}

// Updates 批量更新记录
func (r *RmsDataSourceTable) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsDataSourceTable{})).Updates(data).Error)
	})
}

// Delete 删除记录
func (r *RmsDataSourceTable) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsDataSourceTable{})).Delete(&modelv2.RmsDataSourceTable{}).Error)
	})
}

// BatchCreate 批量创建记录
func (r *RmsDataSourceTable) BatchCreate(ctx context.Context, session *gorm.DB, items []*modelv2.RmsDataSourceTable) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Create(&items).Error)
	})
}

func (r *RmsDataSourceTable) Tables(ctx context.Context, dataSource *modelv2.RmsDataSource) ([]string, error) {
	// 添加context超时控制
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	db, err := NewRmsDataSource().GetConnection(dataSource)
	if err != nil {
		return nil, err
	}
	// 获取原始sql.DB连接以便关闭
	sqlDB, err := db.DB()
	if err != nil {
		return nil, er.Internal.WithMsg("获取DB实例失败").WithErr(err).WithStack()
	}
	defer sqlDB.Close()

	var tables []string
	// 使用参数化查询防止SQL注入
	result := db.WithContext(ctx).Raw(fmt.Sprintf("SHOW TABLES FROM %s", dataSource.DatabaseName)).Scan(&tables)
	if result.Error != nil {
		return nil, er.Internal.WithMsg("获取表名失败").WithErr(result.Error).WithStack()
	}
	return tables, nil
}

func (r *RmsDataSourceTable) Columns(ctx context.Context, dataSource *modelv2.RmsDataSource, tableName string) ([]*entity.Column, error) {
	// 添加context超时控制
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	db, err := NewRmsDataSource().GetConnection(dataSource)
	if err != nil {
		return nil, err
	}
	// 获取原始sql.DB连接以便关闭
	sqlDB, err := db.DB()
	if err != nil {
		return nil, er.Internal.WithMsg("获取DB实例失败").WithErr(err).WithStack()
	}
	defer sqlDB.Close()

	var columns []*entity.Column
	// 使用参数化查询防止SQL注入
	result := db.WithContext(ctx).Raw(
		"SELECT COLUMN_NAME,DATA_TYPE,COLUMN_TYPE,COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?",
		dataSource.DatabaseName,
		tableName,
	).Scan(&columns)
	if result.Error != nil {
		return nil, er.Internal.WithMsg("获取字段名失败").WithErr(result.Error).WithStack()
	}
	return columns, nil
}
