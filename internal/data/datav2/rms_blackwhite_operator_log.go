package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsBlackWhiteOperatorLog 黑名单白名单操作日志
type RmsBlackWhiteOperatorLog struct {
	Empty
}

func NewRmsBlackWhiteOperatorLog() repo.BlackWhiteOperatorLogRepo {
	return &RmsBlackWhiteOperatorLog{}
}

// TableName modelv2.RmsBlackWhiteOperatorLog's table name
func (r *RmsBlackWhiteOperatorLog) TableName() string {
	return modelv2.TableNameRmsOperatorLog
}

// Create
func (r *RmsBlackWhiteOperatorLog) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsBlackWhiteOperatorLog) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(tx.Model(&modelv2.RmsBlackWhiteOperatorLog{}).Create(model).Error)
	})
	return
}

// List
func (r *RmsBlackWhiteOperatorLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteOperatorLog, err error) {
	tx := fn(global.DB.Model(&modelv2.RmsBlackWhiteOperatorLog{})).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}
