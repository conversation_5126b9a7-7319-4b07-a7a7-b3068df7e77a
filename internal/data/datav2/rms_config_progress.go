package datav2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

type RmsConfigProgress struct{}

func NewRmsConfigProgress() repo.ConfigProgressRepo {
	return &RmsConfigProgress{}
}

func (r *RmsConfigProgress) TableName() string {
	return "rms_config_progress"
}

func (r *RmsConfigProgress) Create(ctx context.Context, session *gorm.DB, model *modelv2.PlatConfigProgress) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.PlatConfigProgress{}).Create(model).Error)
	})
}

func (r *RmsConfigProgress) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.PlatConfigProgress) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.PlatConfigProgress{})).Updates(model).Error)
	})
}

func (r *RmsConfigProgress) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.PlatConfigProgress{})).Updates(data).Error)
	})
}

func (r *RmsConfigProgress) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.PlatConfigProgress{})).Delete(&modelv2.PlatConfigProgress{}).Error)
	})
}

func (r *RmsConfigProgress) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.PlatConfigProgress, err error) {
	tx := fn(global.DB.Model(&modelv2.PlatConfigProgress{})).WithContext(ctx)
	err = tx.Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return
		}
	}
	return
}

func (r *RmsConfigProgress) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.PlatConfigProgress{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsConfigProgress) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.PlatConfigProgress, error) {
	var item modelv2.PlatConfigProgress
	err := fn(global.DB.Model(&modelv2.PlatConfigProgress{})).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}
