package datav2

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"

	"gorm.io/gorm"
)

type Empty struct{}

func (b *Empty) DoDefaultFilter(filter *filter.DefaultFilter, tx *gorm.DB) *gorm.DB {
	if filter == nil {
		return tx
	}
	if filter.Page != nil {
		tx = filter.Page.Filter(tx)
	}
	if filter.Time != nil {
		tx = filter.Time.Filter(tx)
	}
	return tx
}
func (b *Empty) FilterPage(filter *filter.DefaultFilter, tx *gorm.DB) *gorm.DB {
	if filter == nil {
		return tx
	}
	if filter.Page != nil {
		tx = filter.Page.Filter(tx)
	}
	return tx
}
func (b *Empty) FilterTime(filter *filter.DefaultFilter, tx *gorm.DB) *gorm.DB {
	if filter == nil {
		return tx
	}
	if filter.Time != nil {
		tx = filter.Time.Filter(tx)
	}
	return tx
}
