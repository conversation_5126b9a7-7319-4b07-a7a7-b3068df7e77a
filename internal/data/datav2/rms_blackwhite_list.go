package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/json"
	"gorm.io/gorm"
)

// RmsBlackWhiteList 黑名单白名单
type RmsBlackWhiteList struct {
	Empty
}

func NewRmsBlackWhiteList() repo.BlackWhiteListRepo {
	return &RmsBlackWhiteList{}
}

// TableName modelv2.RmsBlackWhiteList's table name
func (r *RmsBlackWhiteList) TableName() string {
	return modelv2.TableNameRmsBlackWhiteList
}

// List 获取名单列表
func (r *RmsBlackWhiteList) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteList{}))).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

// GetBlackList 获取黑名单列表
func (r *RmsBlackWhiteList) GetBlackList(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteList{})).Where("type = ?", modelv2.BlackWhiteListTypeBlack)).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

// GetWhiteList 获取白名单列表
func (r *RmsBlackWhiteList) GetWhiteList(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteList{})).Where("type = ?", modelv2.BlackWhiteListTypeWhite)).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

// First 获取单条名单记录
func (r *RmsBlackWhiteList) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteList, error) {
	var item modelv2.RmsBlackWhiteList
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteList{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// Update 更新名单记录
func (r *RmsBlackWhiteList) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsBlackWhiteList) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteList{})).Updates(data).Error)
	})
}
func (r *RmsBlackWhiteList) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteList{})).Updates(data).Error)
	})
}
func (r *RmsBlackWhiteList) UpdateChangeData(ctx context.Context, session *gorm.DB, id int64, submitter string, changeData string) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsBlackWhiteList{}).Where("id = ?", id).Updates(map[string]interface{}{
			"change_data": changeData,
			"state":       modelv2.BlackWhiteListStateModifyDraft,
			"created_by":  submitter,
		}).Error)
	})
}
func (r *RmsBlackWhiteList) Remove(ctx context.Context, session *gorm.DB, id int64, submitter string) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", id)
		}, map[string]interface{}{
			"state":       modelv2.BlackWhiteListStateRemoved,
			"change_data": "",
			"created_by":  submitter,
		})
		if innerErr != nil {
			return
		}
		innerErr = NewRmsBlackWhiteItem().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", id)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusRemoved,
		})
		if innerErr != nil {
			return
		}
		innerErr = NewRmsEventFieldBinding().Remove(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bind_table_name = ?", r.TableName()).Where("bind_table_id = ?", id)
		})
		if innerErr != nil {
			return
		}

		return
	})
}

func (r *RmsBlackWhiteList) AuditPass(ctx context.Context, session *gorm.DB, id int64) (err error) {
	var bwl *modelv2.RmsBlackWhiteList
	var updateData = map[string]interface{}{
		"state":       modelv2.BlackWhiteListStatePass,
		"change_data": "",
		"status":      modelv2.BlackWhiteListStatusTurnOn,
	}
	bwl, err = NewRmsBlackWhiteList().First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", id)
	})
	if err != nil {
		return err
	}
	if len(bwl.ChangeData) > 0 {
		tmpBwl := json.JsonUnmarshal[modelv2.RmsBlackWhiteList](bwl.ChangeData)
		updateData["note"] = tmpBwl.Note
		updateData["created_by"] = tmpBwl.CreatedBy
		updateData["access_points"] = tmpBwl.AccessPoints
	}
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", id)
		}, updateData)
		return
	})
}
func (r *RmsBlackWhiteList) AuditReject(ctx context.Context, session *gorm.DB, id int64) (err error) {
	var updateData = map[string]interface{}{
		"state":       modelv2.BlackWhiteListStateReject,
		"change_data": "",
	}

	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", id)
		}, updateData)
		return
	})
}

// Create 创建名单记录
func (r *RmsBlackWhiteList) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsBlackWhiteList) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsBlackWhiteList{}).Create(model)
		if resTx.Error != nil {
			return er.ConvertDBError(resTx.Error)
		}
		return nil
	})
	return
}

func (r *RmsBlackWhiteList) Fields(ctx context.Context, bwlID int64) (fields entity.Fields, err error) {
	var fieldsModels []*modelv2.RmsBlackWhiteField
	fieldsModels, err = NewRmsBlackWhiteField().GetBlackWhiteFieldsBriefing(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", bwlID)
	})
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	for _, field := range fieldsModels {
		fields = append(fields, &entity.Field{
			Name:       field.FieldName,
			IsRequired: field.Required,
		})
	}
	return
}
