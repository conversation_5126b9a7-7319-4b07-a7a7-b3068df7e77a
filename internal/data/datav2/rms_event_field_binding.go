package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsEventFieldBinding 风险字段绑定情况
type RmsEventFieldBinding struct {
	Empty
}

// TableName RmsEventField's table name
func (r *RmsEventFieldBinding) TableName() string {
	return modelv2.TableNameRmsEventFieldBinding
}
func NewRmsEventFieldBinding() repo.RmsEventFieldBinding {
	return &RmsEventFieldBinding{}
}
func (r *RmsEventFieldBinding) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEventFieldBinding, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsEventFieldBinding{}))).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

func (r *RmsEventFieldBinding) Remove(ctx context.Context, session *gorm.DB, fn func(db2 *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsEventFieldBinding{})).WithContext(ctx).Delete(&modelv2.RmsEventFieldBinding{}).Error
		if innerErr != nil {
			return
		}
		return
	})
}

// Create 创建记录
func (r *RmsEventFieldBinding) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEventFieldBinding) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsEventFieldBinding{}).Create(model)
		if resTx.Error != nil {
			return er.ConvertDBError(resTx.Error)
		}
		return nil
	})
	return
}

// BatchCreate 批量创建记录
func (r *RmsEventFieldBinding) BatchCreate(ctx context.Context, session *gorm.DB, models []*modelv2.RmsEventFieldBinding) (err error) {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsEventFieldBinding{}).Create(models).Error)
	})
}

func (r *RmsEventFieldBinding) Bound(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (bound bool, err error) {
	var total int64
	err = fn(global.DB.Model(&modelv2.RmsEventFieldBinding{})).WithContext(ctx).Count(&total).Error
	return total > 0, er.ConvertDBError(err)
}
