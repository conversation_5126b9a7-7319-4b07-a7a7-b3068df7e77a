package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsRuleParameterValue 规则参数值
type RmsRuleParameterValue struct {
	Empty
}

func NewRmsRuleParameterValue() repo.RmsRuleParameterValueRepo {
	return &RmsRuleParameterValue{}
}

// TableName RmsRuleParameterValue's table name
func (*RmsRuleParameterValue) TableName() string {
	return modelv2.TableNameRmsRuleParameterValue
}

func (r *RmsRuleParameterValue) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsRuleParameterValue) (id int64, err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsRuleParameterValue{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		id = model.ID
		return nil
	})
	return
}

func (r *RmsRuleParameterValue) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRuleParameterValue) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameterValue{})).Updates(model).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

func (r *RmsRuleParameterValue) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameterValue{})).Delete(&modelv2.RmsRuleParameterValue{}).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsRuleParameterValue) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRuleParameterValue, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsRuleParameterValue{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsRuleParameterValue) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsRuleParameterValue{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsRuleParameterValue) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRuleParameterValue, error) {
	var item modelv2.RmsRuleParameterValue
	var err error
	err = fn(global.DB.Model(&modelv2.RmsRuleParameterValue{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsRuleParameterValue) AllByGroupID(ctx context.Context, groupID int64) ([]*modelv2.RmsRuleParameterValue, error) {
	var items []*modelv2.RmsRuleParameterValue
	err := global.DB.Model(&modelv2.RmsRuleParameterValue{}).Where("group_id = ?", groupID).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
