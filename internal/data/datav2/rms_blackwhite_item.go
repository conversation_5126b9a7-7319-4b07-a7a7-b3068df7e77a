package datav2

import (
	"context"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

var _ repo.BlackWhiteItemRepo = &RmsBlackWhiteItem{}

// RmsBlackWhiteList 黑名单白名单
type RmsBlackWhiteItem struct {
	Empty
}

func NewRmsBlackWhiteItem() repo.BlackWhiteItemRepo {
	return &RmsBlackWhiteItem{}
}

// TableName RmsBlackWhiteItem's table name
func (*RmsBlackWhiteItem) TableName() string {
	return modelv2.TableNameRmsBlackWhiteItem
}
func (r *RmsBlackWhiteItem) BatchCreate(ctx context.Context, session *gorm.DB, m []*modelv2.RmsBlackWhiteItem) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		if innerErr := tx.Model(&modelv2.RmsBlackWhiteItem{}).Create(m).Error; innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}
func (r *RmsBlackWhiteItem) GetParentItems(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (res map[int64]*modelv2.RmsBlackWhiteItem, err error) {
	res = make(map[int64]*modelv2.RmsBlackWhiteItem)
	var parentIDs []int64
	var items []*modelv2.RmsBlackWhiteItem
	err = er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteItem{})).WithContext(ctx).Pluck("parent_id", &parentIDs).Error)
	if err != nil {
		return
	}
	err = er.ConvertDBError(global.DB.Model(&RmsBlackWhiteItem{}).WithContext(ctx).Where("id in?", parentIDs).Find(&items).Error)
	if err != nil {
		return
	}
	for _, v := range items {
		res[v.ID] = v
	}
	return
}

// Create 创建名单记录
func (r *RmsBlackWhiteItem) Create(ctx context.Context, session *gorm.DB, m *modelv2.RmsBlackWhiteItem) (id int64, err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		resTx := tx.Model(&modelv2.RmsBlackWhiteItem{}).Create(m)
		if resTx.Error != nil {
			innerErr = er.ConvertDBError(resTx.Error)
			return innerErr
		}
		id = m.ID
		return nil
	})
	return
}

// Delete 删除名单记录
func (r *RmsBlackWhiteItem) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteItem{})).Delete(&modelv2.RmsBlackWhiteItem{}).Error)
	})
}

// Update 更新名单记录
func (r *RmsBlackWhiteItem) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteItem{})).Updates(data).Error)
	})
}
func (r *RmsBlackWhiteItem) Remove(ctx context.Context, session *gorm.DB, id int64) (err error) {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = r.Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", id)
		}, map[string]interface{}{"status": modelv2.BlackWhiteItemStatusRemoveDraft})
		if innerErr != nil {
			return innerErr
		}
		return nil
	})
}

// GetBlackWhiteItems 获取名单列表
func (r *RmsBlackWhiteItem) GetBlackWhiteItems(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteItem, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteItem{}))).WithContext(ctx)
	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
	}
	return total, items, err
}

// First 获取单条名单记录
func (r *RmsBlackWhiteItem) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteItem, error) {
	var item modelv2.RmsBlackWhiteItem
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteItem{})).WithContext(ctx).First(&item).Error)
	return &item, err
}

// 审核通过
func (r *RmsBlackWhiteItem) AuditPass(ctx context.Context, session *gorm.DB, bwlID int64) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		//旧数据，需要被替换更新的数据更新成Remove
		var parentIds []int64
		innerErr = global.DB.Table(r.TableName()+" as t").Select("t.parent_id").Where("t.bwl_id = ?", bwlID).Where("t.status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("t.parent_id > ?", 0).Pluck("parent_id", &parentIds).Error
		if innerErr != nil {
			return innerErr
		}
		if len(parentIds) > 0 {
			innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
				return db.Where("bwl_id = ?", bwlID).Where("id in (?)", parentIds)
			}, map[string]interface{}{
				"status": modelv2.BlackWhiteItemStatusRemoved,
			})
			if innerErr != nil {
				return innerErr
			}
		}
		//新数据，需要被替换更新的数据更新成Pass
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusPass,
		})
		if innerErr != nil {
			return innerErr
		}
		//删除旧数据
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("status = ?", modelv2.BlackWhiteItemStatusRemovePendingApproval)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusRemoved,
		})
		if innerErr != nil {
			return innerErr
		}

		return nil
	})
}

// 审核拒绝
func (r *RmsBlackWhiteItem) AuditReject(ctx context.Context, session *gorm.DB, bwlID int64) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		//拒绝新增待审核数据
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("parent_id = ?", 0).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusReject,
		})
		if innerErr != nil {
			return innerErr
		}
		//拒绝修改待审核数据
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("parent_id > ?", 0).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusRemoved,
		})
		if innerErr != nil {
			return innerErr
		}
		//拒绝移除待审核数据
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("status = ?", modelv2.BlackWhiteItemStatusRemovePendingApproval)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusPass,
		})
		if innerErr != nil {
			return innerErr
		}
		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("status = ?", modelv2.BlackWhiteItemStatusModified)
		}, map[string]interface{}{
			"status": modelv2.BlackWhiteItemStatusPass,
		})
		return innerErr
	})
}

func (r *RmsBlackWhiteItem) GroupCount(ctx context.Context, idColumn string, fn func(tx *gorm.DB) *gorm.DB) (res []*modelv2.GroupIdCount, err error) {
	err = fn(global.DB.Model(&modelv2.RmsBlackWhiteItem{})).WithContext(ctx).Select(fmt.Sprintf("%s as id, count(*) as total", idColumn)).Group(idColumn).Find(&res).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return
}

func (r *RmsBlackWhiteItem) Count(ctx context.Context, fn func(tx *gorm.DB) *gorm.DB) (total int64, err error) {
	err = er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteItem{})).WithContext(ctx).Count(&total).Error)
	return
}
