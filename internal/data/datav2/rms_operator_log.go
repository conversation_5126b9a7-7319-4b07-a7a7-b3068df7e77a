package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsOperatorLog 风控操作日志
type RmsOperatorLog struct {
	Empty
}

// TableName RmsOperatorLog's table name
func (*RmsOperatorLog) TableName() string {
	return modelv2.TableNameRmsOperatorLog
}

func NewRmsOperatorLog() repo.RmsOperatorLog {
	return &RmsOperatorLog{}
}
func (r *RmsOperatorLog) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsOperatorLog) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsOperatorLog{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

func (r *RmsOperatorLog) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsOperatorLog) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsOperatorLog{})).Updates(model).Error)
	})
}

func (r *RmsOperatorLog) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsOperatorLog{})).Updates(data).Error)
	})
}

func (r *RmsOperatorLog) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsOperatorLog{})).Delete(&modelv2.RmsOperatorLog{}).Error)
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsOperatorLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsOperatorLog, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsOperatorLog{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsOperatorLog) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsOperatorLog{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsOperatorLog) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsOperatorLog, error) {
	var item modelv2.RmsOperatorLog
	var err error
	err = fn(global.DB.Model(&modelv2.RmsOperatorLog{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsOperatorLog) FindByUserID(ctx context.Context, userID string) ([]*modelv2.RmsOperatorLog, error) {
	var items []*modelv2.RmsOperatorLog
	err := global.DB.Model(&modelv2.RmsOperatorLog{}).Where("user_id = ?", userID).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsOperatorLog) FindByMethod(ctx context.Context, method string) ([]*modelv2.RmsOperatorLog, error) {
	var items []*modelv2.RmsOperatorLog
	err := global.DB.Model(&modelv2.RmsOperatorLog{}).Where("method = ?", method).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
