package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsEvent 风险事件
type RmsEvent struct {
	Empty
}

// TableName RmsEvent's table name
func (*RmsEvent) TableName() string {
	return modelv2.TableNameRmsEvent
}

func NewRmsEvent() repo.RmsEvent {
	return &RmsEvent{}
}

func (r *RmsEvent) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEvent) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsEvent{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

func (r *RmsEvent) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsEvent) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEvent{})).Updates(model).Error)
	})
}

func (r *RmsEvent) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEvent{})).Updates(data).Error)
	})
}

func (r *RmsEvent) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEvent{})).Delete(&modelv2.RmsEvent{}).Error)
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsEvent) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEvent, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsEvent{})).WithContext(ctx))
	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsEvent) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsEvent{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsEvent) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsEvent, error) {
	var item modelv2.RmsEvent
	var err error
	err = fn(global.DB.Model(&modelv2.RmsEvent{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsEvent) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsEvent, error) {
	var items []*modelv2.RmsEvent
	err := global.DB.Model(&modelv2.RmsEvent{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsEvent) FindByResultCode(ctx context.Context, resultCode string) ([]*modelv2.RmsEvent, error) {
	var items []*modelv2.RmsEvent
	err := global.DB.Model(&modelv2.RmsEvent{}).Where("result_code = ?", resultCode).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsEvent) FindByCheckPointAndResultCode(ctx context.Context, checkPoint, resultCode string) ([]*modelv2.RmsEvent, error) {
	var items []*modelv2.RmsEvent
	err := global.DB.Model(&modelv2.RmsEvent{}).
		Where("check_point = ? AND result_code = ?", checkPoint, resultCode).
		WithContext(ctx).
		Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
