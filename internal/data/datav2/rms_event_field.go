package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"slices"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsEventField 风险事件字段配置
type RmsEventField struct {
	Empty
}

// TableName RmsEventField's table name
func (*RmsEventField) TableName() string {
	return modelv2.TableNameRmsEventField
}

func NewRmsEventField() repo.RmsEventField {
	return &RmsEventField{}
}
func (r *RmsEventField) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEventField) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsEventField{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

func (r *RmsEventField) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsEventField) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEventField{})).Updates(model).Error)
	})
}

func (r *RmsEventField) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEventField{})).Updates(data).Error)
	})
}

func (r *RmsEventField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEventField{})).Delete(&modelv2.RmsEventField{}).Error)
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsEventField) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEventField, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsEventField{})).WithContext(ctx))
	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsEventField) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsEventField{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsEventField) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsEventField, error) {
	var item modelv2.RmsEventField
	var err error
	err = fn(global.DB.Model(&modelv2.RmsEventField{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsEventField) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsEventField, error) {
	var items []*modelv2.RmsEventField
	err := global.DB.Model(&modelv2.RmsEventField{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsEventField) FindByFieldName(ctx context.Context, fieldName string) ([]*modelv2.RmsEventField, error) {
	var items []*modelv2.RmsEventField
	err := global.DB.Model(&modelv2.RmsEventField{}).Where("field_name = ?", fieldName).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

// GetIntersectionFields 获取多个检查点中都存在的字段
func (r *RmsEventField) GetIntersectionFields(ctx context.Context, checkPoints []string) (fields []string, err error) {
	err = global.DB.Model(&modelv2.RmsEventField{}).WithContext(ctx).Where("check_point IN (?)", checkPoints).
		Group("field_name").Having("COUNT(DISTINCT check_point) = ?", len(checkPoints)).Pluck("field_name", &fields).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return
}
func (r *RmsEventField) FindByCheckPointAndFieldName(ctx context.Context, checkPoint, fieldName string) (*modelv2.RmsEventField, error) {
	var item modelv2.RmsEventField
	err := global.DB.Model(&modelv2.RmsEventField{}).
		Where("check_point = ? AND field_name = ?", checkPoint, fieldName).
		WithContext(ctx).
		First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsEventField) CheckFiledExists(c context.Context, checkPoints []string, fields []string) (exists bool, field string, err error) {
	existsFields, err := r.GetIntersectionFields(c, checkPoints)
	if err != nil {
		return
	}
	for _, field := range fields {
		if !slices.Contains(existsFields, field) {
			return false, field, nil
		}
	}
	return true, "", nil
}
