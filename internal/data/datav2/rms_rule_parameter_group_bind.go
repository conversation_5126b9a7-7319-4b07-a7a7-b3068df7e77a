package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsRuleParameterGroupBind 规则参数组绑定关系
type RmsRuleParameterGroupBind struct {
	Empty
}

func NewRmsRuleParameterGroupBind() repo.RmsRuleParameterGroupBindRepo {
	return &RmsRuleParameterGroupBind{}
}

// TableName RmsRuleParameterGroupBind's table name
func (*RmsRuleParameterGroupBind) TableName() string {
	return modelv2.TableNameRmsRuleParameterGroupBind
}

func (r *RmsRuleParameterGroupBind) Create(ctx context.Context, session *gorm.DB, models ...*modelv2.RmsRuleParameterGroupBind) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsRuleParameterGroupBind{}).Create(models)
		if resTx.Error != nil {
			return er.ConvertDBError(resTx.Error)
		}
		return nil
	})
	return
}

func (r *RmsRuleParameterGroupBind) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRuleParameterGroupBind) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsRuleParameterGroupBind{})).Updates(model).Error)
	})
}

func (r *RmsRuleParameterGroupBind) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {

		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsRuleParameterGroupBind{})).Delete(&modelv2.RmsRuleParameterGroupBind{}).Error)

	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsRuleParameterGroupBind) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRuleParameterGroupBind, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsRuleParameterGroupBind{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsRuleParameterGroupBind) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsRuleParameterGroupBind{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsRuleParameterGroupBind) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRuleParameterGroupBind, error) {
	var item modelv2.RmsRuleParameterGroupBind
	var err error
	err = fn(global.DB.Model(&modelv2.RmsRuleParameterGroupBind{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsRuleParameterGroupBind) GetByGroupID(ctx context.Context, groupID int64) (*modelv2.RmsRuleParameterGroupBind, error) {
	var item modelv2.RmsRuleParameterGroupBind
	err := global.DB.Model(&modelv2.RmsRuleParameterGroupBind{}).Where("group_id = ?", groupID).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsRuleParameterGroupBind) ListByTargetType(ctx context.Context, targetType string) ([]*modelv2.RmsRuleParameterGroupBind, error) {
	var items []*modelv2.RmsRuleParameterGroupBind
	err := global.DB.Model(&modelv2.RmsRuleParameterGroupBind{}).Where("target_type = ?", targetType).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsRuleParameterGroupBind) ListByTargetID(ctx context.Context, targetID string) ([]*modelv2.RmsRuleParameterGroupBind, error) {
	var items []*modelv2.RmsRuleParameterGroupBind
	err := global.DB.Model(&modelv2.RmsRuleParameterGroupBind{}).Where("target_id = ?", targetID).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
