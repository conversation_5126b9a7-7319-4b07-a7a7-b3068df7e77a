package datav2

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/consts"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/compare"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsIndicator 指标配置
type RmsIndicator struct {
	Empty
}

// TableName 表名
func (r *RmsIndicator) TableName() string {
	return modelv2.TableNameRmsIndicator
}

// NewRmsIndicator 创建指标配置实例
func NewRmsIndicator() repo.IndicatorRepo {
	return &RmsIndicator{}
}

// Create 创建指标配置
func (r *RmsIndicator) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicator) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicator{}).WithContext(ctx).Create(data).Error)
	})
}

// Update 更新指标配置
func (r *RmsIndicator) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicator) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicator{})).Updates(data).Error)
	})
}

// Updates 更新指标配置版本
func (r *RmsIndicator) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicator{})).Updates(data).Error)
	})
}

// First 获取单个指标配置
func (r *RmsIndicator) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicator, error) {
	var item modelv2.RmsIndicator
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicator{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// List 获取指标配置列表
func (r *RmsIndicator) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicator, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsIndicator{}))).WithContext(ctx)

	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return
	}
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return
	}
	return
}

// Delete 删除指标配置
func (r *RmsIndicator) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicator{})).Delete(&modelv2.RmsIndicator{}).Error)
	})
}

// RmsIndicatorVersion 指标配置版本
type RmsIndicatorVersion struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorVersion) TableName() string {
	return modelv2.TableNameRmsIndicatorVersion
}

// NewRmsIndicatorVersion 创建指标配置版本实例
func NewRmsIndicatorVersion() repo.IndicatorVersionRepo {
	return &RmsIndicatorVersion{}
}

// Create 创建指标配置版本
func (r *RmsIndicatorVersion) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicatorVersion) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx).Create(data).Error)
	})
}

// Updates 更新指标配置版本
func (r *RmsIndicatorVersion) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersion{})).Updates(data).Error)
	})
}

// NewVersion 创建新的版本号
func (r *RmsIndicatorVersion) NewVersion(ctx context.Context, indicatorID int64) string {
	count, err := r.Count(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("indicator_id = ?", indicatorID).Where("version > ?", "0")
	})
	if err != nil {
		return ""
	}

	return modelv2.GetIndicatorVersion(1, count+1)
}

// Delete 删除指标配置版本
func (r *RmsIndicatorVersion) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersion{})).Delete(&modelv2.RmsIndicatorVersion{}).Error)
	})
}

// First 获取单个指标配置版本
func (r *RmsIndicatorVersion) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicatorVersion, error) {
	var item modelv2.RmsIndicatorVersion
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// List 获取指标配置版本列表
func (r *RmsIndicatorVersion) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicatorVersion, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsIndicatorVersion{}))).WithContext(ctx)

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return total, items, err
	}

	return 0, items, nil
}

// Count 获取指标配置版本数量
func (r *RmsIndicatorVersion) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (int64, error) {
	var total int64
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorVersion{}).WithContext(ctx)).Count(&total).Error)
	return total, err
}

// RmsIndicatorRule 指标配置规则
type RmsIndicatorRule struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorRule) TableName() string {
	return modelv2.TableNameRmsIndicatorRule
}

// NewRmsIndicatorRule 创建指标配置规则实例
func NewRmsIndicatorRule() repo.IndicatorRuleRepo {
	return &RmsIndicatorRule{}
}

// BatchCreate 批量创建指标配置规则
func (r *RmsIndicatorRule) BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorRule) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorRule{}).WithContext(ctx).Create(data).Error)
	})
}

// List 获取指标配置规则列表
func (r *RmsIndicatorRule) List(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) ([]*modelv2.RmsIndicatorRule, error) {
	var items []*modelv2.RmsIndicatorRule
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorRule{}).WithContext(ctx)).Find(&items).Error)
	return items, err
}

// Update 更新指标配置
func (r *RmsIndicatorRule) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorRule) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorRule{})).Updates(data).Error)
	})
}
func (r *RmsIndicatorRule) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorRule{})).Updates(data).Error)
	})
}

// Delete 删除指标配置规则
func (r *RmsIndicatorRule) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorRule{})).Delete(&modelv2.RmsIndicatorRule{}).Error)
	})
}

// RmsIndicatorMeasure 指标配置度量
type RmsIndicatorMeasure struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorMeasure) TableName() string {
	return modelv2.TableNameRmsIndicatorMeasure
}

// NewRmsIndicatorMeasure 创建指标配置度量实例
func NewRmsIndicatorMeasure() repo.IndicatorMeasureRepo {
	return &RmsIndicatorMeasure{}
}

// BatchCreate 批量创建指标配置度量
func (r *RmsIndicatorMeasure) BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorMeasure) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorMeasure{}).WithContext(ctx).Create(data).Error)
	})
}

// Update 更新指标配置
func (r *RmsIndicatorMeasure) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorMeasure) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorMeasure{})).Updates(data).Error)
	})
}

// List 获取指标配置度量列表
func (r *RmsIndicatorMeasure) List(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) ([]*modelv2.RmsIndicatorMeasure, error) {
	var items []*modelv2.RmsIndicatorMeasure
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorMeasure{}).WithContext(ctx)).Find(&items).Error)
	return items, err
}

// Delete 删除指标配置度量
func (r *RmsIndicatorMeasure) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorMeasure{})).Delete(&modelv2.RmsIndicatorMeasure{}).Error)
	})
}

// RmsIndicatorVersionHistory 指标配置版本历史
type RmsIndicatorVersionHistory struct {
	Empty
}

// TableName 表名
func (r *RmsIndicatorVersionHistory) TableName() string {
	return modelv2.TableNameRmsIndicatorVersionHistory
}

type IndicatorCompare struct {
	BaseInfo *modelv2.RmsIndicatorVersion
	Measure  []*modelv2.RmsIndicatorMeasure
	Rules    []*modelv2.RmsIndicatorRule
}

// NewRmsIndicatorVersionHistory 创建指标配置版本历史实例
func NewRmsIndicatorVersionHistory() repo.IndicatorVersionHistoryRepo {
	return &RmsIndicatorVersionHistory{}
}

// Create 创建指标配置版本历史
func (r *RmsIndicatorVersionHistory) Create(ctx context.Context, session *gorm.DB, data *modelv2.RmsIndicatorVersionHistory) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx).Create(data).Error)
	})
}

// BatchCreate 批量创建指标配置版本历史
func (r *RmsIndicatorVersionHistory) BatchCreate(ctx context.Context, session *gorm.DB, data []*modelv2.RmsIndicatorVersionHistory) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(tx.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx).Create(data).Error)
	})
}

// Update 更新指标配置版本历史
func (r *RmsIndicatorVersionHistory) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsIndicatorVersionHistory) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersionHistory{})).Updates(data).Error)
	})
}

// Updates 更新指标配置版本历史
func (r *RmsIndicatorVersionHistory) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersionHistory{})).Updates(data).Error)
	})
}

// First 获取单个指标配置版本历史
func (r *RmsIndicatorVersionHistory) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsIndicatorVersionHistory, error) {
	var item modelv2.RmsIndicatorVersionHistory
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsIndicatorVersionHistory{}).WithContext(ctx)).First(&item).Error)
	return &item, err
}

// List 获取指标配置版本历史列表
func (r *RmsIndicatorVersionHistory) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsIndicatorVersionHistory, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsIndicatorVersionHistory{}))).WithContext(ctx)

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = er.ConvertDBError(tx.Count(&total).Error)
		return total, items, err
	}

	return 0, items, nil
}

// Delete 删除指标配置版本历史
func (r *RmsIndicatorVersionHistory) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsIndicatorVersionHistory{})).Delete(&modelv2.RmsIndicatorVersionHistory{}).Error)
	})
}

func (r *RmsIndicatorVersionHistory) AnalyzeVersionAndSave(ctx context.Context, isScript bool, modifyUser string, oldVersion *modelv2.RmsIndicatorVersion, newVersion *modelv2.RmsIndicatorVersion) (err error) {
	var history *modelv2.RmsIndicatorVersionHistory
	var isDraft bool
	var up, ins, del string
	if newVersion.Version == "" {
		isDraft = true
	}
	history, err = r.First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("version_id = ?", newVersion.ID).Where("indicator_id = ?", newVersion.IndicatorID)
	})
	if errors.Is(err, gorm.ErrRecordNotFound) {
		history = &modelv2.RmsIndicatorVersionHistory{
			IndicatorID: newVersion.IndicatorID,
			VersionID:   newVersion.ID,
			Version:     newVersion.Version,
			IsDraft:     isDraft,
		}
	}
	history.LastModified = modifyUser
	if oldVersion == nil {
		history.Desc = "Initial version created"
		if history.ID > 0 {
			err = r.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
				return db.Where("id = ?", history.ID)
			}, history)
		} else {
			err = r.Create(ctx, nil, history)
		}
		return
	}
	if isScript {
		var desc string
		if oldVersion.Script != newVersion.Script {
			desc += "Script was changed\n"
		}
		if oldVersion.Remark != newVersion.Remark {
			desc += "Remark was changed"
		}
		history.Desc = desc
	} else {
		var oldIndicatorCompare, newIndicatorCompare IndicatorCompare
		oldIndicatorCompare.BaseInfo = oldVersion
		newIndicatorCompare.BaseInfo = newVersion
		{
			oldIndicatorCompare.Measure, err = NewRmsIndicatorMeasure().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", oldVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
			newIndicatorCompare.Measure, err = NewRmsIndicatorMeasure().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", newVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
			oldIndicatorCompare.Rules, err = NewRmsIndicatorRule().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", oldVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
			newIndicatorCompare.Rules, err = NewRmsIndicatorRule().List(ctx, func(db *gorm.DB) *gorm.DB {
				return db.Where("version_id = ?", newVersion.ID).Order("id asc")
			})
			if err != nil {
				err = er.Internal.WithMsg("Failed to process historical changes")
				return
			}
		}
		var tmpDesc []string
		if oldIndicatorCompare.BaseInfo.TimeWindowType != newIndicatorCompare.BaseInfo.TimeWindowType {
			tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_type: %s -> %s", consts.IndicatorVersionTimeWindowTypes[oldIndicatorCompare.BaseInfo.TimeWindowType], consts.IndicatorVersionTimeWindowTypes[newIndicatorCompare.BaseInfo.TimeWindowType]))
		}
		if oldIndicatorCompare.BaseInfo.TimeWindowUnit != newIndicatorCompare.BaseInfo.TimeWindowUnit {
			var new, old string
			var tmpUnit = []*string{&old, &new}
			for idx, tmpVersion := range []*modelv2.RmsIndicatorVersion{oldIndicatorCompare.BaseInfo, newIndicatorCompare.BaseInfo} {
				switch tmpVersion.TimeWindowType {
				case consts.IndicatorMeasureWindowTypeSliding:
					*tmpUnit[idx] = consts.IndicatorVersionSlidingTimeWindowUnits[tmpVersion.TimeWindowUnit]
				case consts.IndicatorMeasureWindowTypeFixed:
					*tmpUnit[idx] = consts.IndicatorVersionFixedTimeWindowUnits[tmpVersion.TimeWindowUnit]
				}
			}
			tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_unit: %s -> %s", *tmpUnit[0], *tmpUnit[1]))
		}
		if oldIndicatorCompare.BaseInfo.TimeWindowValue != newIndicatorCompare.BaseInfo.TimeWindowValue {
			tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_value: %d -> %d", oldIndicatorCompare.BaseInfo.TimeWindowValue, newIndicatorCompare.BaseInfo.TimeWindowValue))
		}
		if oldIndicatorCompare.BaseInfo.TimeWindowExcluding != newIndicatorCompare.BaseInfo.TimeWindowExcluding {
			tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_excluding: %v -> %v", oldIndicatorCompare.BaseInfo.TimeWindowExcluding, newIndicatorCompare.BaseInfo.TimeWindowExcluding))
		}
		if oldIndicatorCompare.BaseInfo.StartTime != newIndicatorCompare.BaseInfo.StartTime {
			tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_start: %s -> %s", oldIndicatorCompare.BaseInfo.StartTime, newIndicatorCompare.BaseInfo.StartTime))
		}
		if oldIndicatorCompare.BaseInfo.EndTime != newIndicatorCompare.BaseInfo.EndTime {
			tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_start: %s -> %s", oldIndicatorCompare.BaseInfo.StartTime, newIndicatorCompare.BaseInfo.StartTime))
		}
		if string(oldIndicatorCompare.BaseInfo.TimeWindowColumnID) != string(newIndicatorCompare.BaseInfo.TimeWindowColumnID) {
			oldColumn := entity.NewDataSourceColumnByColumnID(oldIndicatorCompare.BaseInfo.TimeWindowColumnID)
			newColumn := entity.NewDataSourceColumnByColumnID(newIndicatorCompare.BaseInfo.TimeWindowColumnID)
			if oldColumn != nil && newColumn != nil {
				tmpDesc = append(tmpDesc, fmt.Sprintf("time_window_column: %s -> %s", oldColumn.ColumnName, newColumn.ColumnName))
			}
		}
		if len(tmpDesc) > 0 {
			up += "- " + strings.Join(tmpDesc, ",") + "\n"
		}
		//history.Desc = strings.Join(tmpDesc, "\n") + "\n"
		// 处理 Measures
		tmpDesc = []string{}

		var oldMeasureMap = make(map[int64]*modelv2.RmsIndicatorMeasure)
		for _, oldMeasure := range oldIndicatorCompare.Measure {
			oldMeasureMap[oldMeasure.ID] = oldMeasure
		}
		var insMeasure, _, delMeasure = compare.NewCompare[int64, *modelv2.RmsIndicatorMeasure](func(data *modelv2.RmsIndicatorMeasure) int64 {
			return data.ParentID
		}, func(new *modelv2.RmsIndicatorMeasure, old *modelv2.RmsIndicatorMeasure) bool {
			var isUpdated bool
			if new.ConditionName != old.ConditionName {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("condition_name: %s -> %s", old.ConditionName, new.ConditionName))
			}
			if new.AggType != old.AggType {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("measure_aggregate_type: %s -> %s", old.AggType, new.AggType))
			}
			if new.MeasureField != old.MeasureField {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("measure_field: %s -> %s", old.MeasureField, new.MeasureField))
			}
			if len(tmpDesc) > 0 {
				up += "- Measure Update:" + strings.Join(tmpDesc, ",") + "\n"

			}
			return isUpdated
		}, oldMeasureMap).Compare(newIndicatorCompare.Measure)

		if len(insMeasure) > 0 {
			ins += fmt.Sprintf("- Added %d new measures\n", len(insMeasure))
		}
		if len(delMeasure) > 0 {
			del += fmt.Sprintf("- Delete %d measures\n", len(delMeasure))
		}

		//处理规则
		tmpDesc = []string{}

		var oldRuleMap = make(map[int64]*modelv2.RmsIndicatorRule)
		for _, oldRule := range oldIndicatorCompare.Rules {
			oldRuleMap[oldRule.ID] = oldRule
		}
		var insRule, _, delRule = compare.NewCompare[int64, *modelv2.RmsIndicatorRule](func(data *modelv2.RmsIndicatorRule) int64 {
			return data.ParentID
		}, func(new *modelv2.RmsIndicatorRule, old *modelv2.RmsIndicatorRule) bool {
			var isUpdated bool
			oldColumn := entity.NewDataSourceColumnByColumnID(old.ColumnID)
			newColumn := entity.NewDataSourceColumnByColumnID(new.ColumnID)
			if oldColumn == nil || newColumn == nil {
				return false
			}
			if new.ColumnID != old.ColumnID {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("rule_column_name: %s -> %s", oldColumn.ColumnName, newColumn.ColumnName))
			}
			if new.Operator != old.Operator {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("rule_operator: %s -> %s", old.Operator, new.Operator))
			}
			if new.HasLeftBrackets != old.HasLeftBrackets {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("left_brackets: %v -> %v", old.HasLeftBrackets, new.HasLeftBrackets))
			}
			if new.HasRightBrackets != old.HasRightBrackets {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("right_brackets: %v -> %v", old.HasRightBrackets, new.HasRightBrackets))
			}
			if new.Connector != old.Connector {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("connector: %v -> %v", old.Connector, new.Connector))
			}
			if new.Value != old.Value {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("value: %v -> %v", old.Value, new.Value))
			}
			if new.ValueType != old.ValueType {
				isUpdated = true
				tmpDesc = append(tmpDesc, fmt.Sprintf("value_type: %v -> %v", old.ValueType, new.ValueType))
			}
			if len(tmpDesc) > 0 {
				up += "- RuleUpdate: " + strings.Join(tmpDesc, ",") + "\n"

			}

			return isUpdated
		}, oldRuleMap).Compare(newIndicatorCompare.Rules)
		if len(insRule) > 0 {
			ins += fmt.Sprintf("- Added %d new rules\n", len(insRule))
		}
		if len(delRule) > 0 {
			del += fmt.Sprintf("- Delete %d rules\n", len(delRule))
		}
		history.Desc = ""
		if len(up) > 0 {
			history.Desc += "Updates Record:\n" + up
		}
		if len(ins) > 0 {
			history.Desc += "Insert Record:\n" + ins
		}
		if len(del) > 0 {
			history.Desc += "Delete Record:\n" + del
		}
	}

	if history.ID > 0 {
		err = r.Update(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("id = ?", history.ID)
		}, history)
	} else {
		err = r.Create(ctx, nil, history)
	}
	return
}
