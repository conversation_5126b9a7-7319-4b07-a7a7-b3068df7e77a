package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsRuleParameter 规则参数
type RmsRuleParameter struct {
	Empty
}

func NewRmsRuleParameter() repo.RmsRuleParameterRepo {
	return &RmsRuleParameter{}
}

// TableName RmsRuleParameter's table name
func (*RmsRuleParameter) TableName() string {
	return modelv2.TableNameRmsRuleParameter
}

func (r *RmsRuleParameter) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsRuleParameter) (id int64, err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsRuleParameter{}).Create(model)
		if resTx.Error != nil {
			return er.ConvertDBError(resTx.Error)
		}
		id = model.ID
		return nil
	})
	return
}

func (r *RmsRuleParameter) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsRuleParameter) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameter{})).Omit("access_flag").Updates(model).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

func (r *RmsRuleParameter) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsRuleParameter{})).Delete(&modelv2.RmsRuleParameter{}).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsRuleParameter) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsRuleParameter, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsRuleParameter{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsRuleParameter) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsRuleParameter{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsRuleParameter) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsRuleParameter, error) {
	var item modelv2.RmsRuleParameter
	var err error
	err = fn(global.DB.Model(&modelv2.RmsRuleParameter{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsRuleParameter) FindByCode(ctx context.Context, code string) (*modelv2.RmsRuleParameter, error) {
	var item modelv2.RmsRuleParameter
	err := global.DB.Model(&modelv2.RmsRuleParameter{}).Where("code = ?", code).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}

func (r *RmsRuleParameter) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsRuleParameter, error) {
	var items []*modelv2.RmsRuleParameter
	err := global.DB.Model(&modelv2.RmsRuleParameter{}).Where("check_point LIKE ?", "%"+checkPoint+"%").WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
