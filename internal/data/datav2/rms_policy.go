package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsPolicy 风控规则策略（即规则组）
type RmsPolicy struct {
	Empty
}

// TableName RmsPolicy's table name
func (*RmsPolicy) TableName() string {
	return modelv2.TableNameRmsPolicy
}

func NewRmsPolicy() repo.PolicyRepo {
	return &RmsPolicy{}
}
func (r *RmsPolicy) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsPolicy) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsPolicy{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

func (r *RmsPolicy) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsPolicy) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsPolicy{})).Updates(model).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}
func (r *RmsPolicy) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsPolicy{})).Updates(data).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}
func (r *RmsPolicy) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		innerErr = fn(tx.Model(&modelv2.RmsPolicy{})).Delete(&modelv2.RmsPolicy{}).Error
		if innerErr != nil {
			return er.ConvertDBError(innerErr)
		}
		return nil
	})
}

// List 获取列表数据，可选择是否返回总数
// withTotal: true 返回总数，false 只返回列表数据
func (r *RmsPolicy) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsPolicy, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsPolicy{})).WithContext(ctx))

	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

func (r *RmsPolicy) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsPolicy{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

func (r *RmsPolicy) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsPolicy, error) {
	var item modelv2.RmsPolicy
	var err error
	err = fn(global.DB.Model(&modelv2.RmsPolicy{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

func (r *RmsPolicy) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsPolicy, error) {
	var items []*modelv2.RmsPolicy
	err := global.DB.Model(&modelv2.RmsPolicy{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}

func (r *RmsPolicy) FindByPolicyNo(ctx context.Context, policyNo string) (*modelv2.RmsPolicy, error) {
	var item modelv2.RmsPolicy
	err := global.DB.Model(&modelv2.RmsPolicy{}).Where("policy_no = ?", policyNo).WithContext(ctx).First(&item).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return &item, nil
}
