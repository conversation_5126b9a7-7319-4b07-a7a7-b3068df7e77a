package datav2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gorm.io/gorm"
)

// RmsEventStoreCfg 事件存储配置
type RmsEventStoreCfg struct {
	Empty
}

// TableName RmsEventStoreCfg's table name
func (*RmsEventStoreCfg) TableName() string {
	return modelv2.TableNameRmsEventStoreCfg
}

// NewRmsEventStoreCfg creates a new RmsEventStoreCfg instance
func NewRmsEventStoreCfg() repo.RmsEventStoreCfg {
	return &RmsEventStoreCfg{}
}

// Create creates a new RmsEventStoreCfg record
func (r *RmsEventStoreCfg) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsEventStoreCfg) (err error) {
	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
		resTx := tx.Model(&modelv2.RmsEventStoreCfg{}).Create(model)
		if resTx.Error != nil {
			return resTx.Error
		}
		return nil
	})
	return
}

// Update updates an existing modelv2.RmsEventStoreCfg record
func (r *RmsEventStoreCfg) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *modelv2.RmsEventStoreCfg) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEventStoreCfg{})).Updates(model).Error)
	})
}

// Updates updates specific fields in an existing modelv2.RmsEventStoreCfg record
func (r *RmsEventStoreCfg) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEventStoreCfg{})).Updates(data).Error)
	})
}

// Delete deletes a modelv2.RmsEventStoreCfg record
func (r *RmsEventStoreCfg) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(fn(tx.Model(&modelv2.RmsEventStoreCfg{})).Delete(&modelv2.RmsEventStoreCfg{}).Error)
	})
}

// List retrieves a list of modelv2.RmsEventStoreCfg records with optional total count
// withTotal: true returns total count, false returns only list data
func (r *RmsEventStoreCfg) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsEventStoreCfg, err error) {
	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsEventStoreCfg{})).WithContext(ctx))
	// 获取列表数据
	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
	if err != nil {
		err = er.ConvertDBError(err)
		return 0, nil, err
	}

	// 是否需要获取总数
	if len(withTotal) > 0 && withTotal[0] {
		err = tx.Count(&total).Error
		if err != nil {
			err = er.ConvertDBError(err)
			return 0, items, err
		}
		return total, items, nil
	}

	return 0, items, nil
}

// Count counts modelv2.RmsEventStoreCfg records based on the condition
func (r *RmsEventStoreCfg) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
	err = fn(global.DB.Model(&modelv2.RmsEventStoreCfg{})).WithContext(ctx).Count(&total).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return
}

// Retrieve retrieves a single modelv2.RmsEventStoreCfg record
func (r *RmsEventStoreCfg) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsEventStoreCfg, error) {
	var item modelv2.RmsEventStoreCfg
	var err error
	err = fn(global.DB.Model(&modelv2.RmsEventStoreCfg{})).WithContext(ctx).First(&item).Error
	if err != nil {
		err = er.ConvertDBError(err)
	}
	return &item, err
}

// FindByCheckPoint retrieves modelv2.RmsEventStoreCfg records by check point
func (r *RmsEventStoreCfg) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*modelv2.RmsEventStoreCfg, error) {
	var items []*modelv2.RmsEventStoreCfg
	err := global.DB.Model(&modelv2.RmsEventStoreCfg{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
	if err != nil {
		return nil, er.ConvertDBError(err)
	}
	return items, nil
}
