package datav2

import (
	"context"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/db"
	"gorm.io/gorm"
)

var _ repo.BlackWhiteAuditDetailRepo = &RmsBlackWhiteAuditDetail{}

// RmsBlackWhiteAuditDetail 黑名单白名单审核
type RmsBlackWhiteAuditDetail struct {
	Empty
}

func NewRmsBlackWhiteAuditDetail() repo.BlackWhiteAuditDetailRepo {
	return &RmsBlackWhiteAuditDetail{}
}

// TableName RmsBlackWhiteAudit's table name
func (r *RmsBlackWhiteAuditDetail) TableName() string {
	return modelv2.TableNameRmsBlackWhiteAuditDetail
}

// 创建审核任务
func (r *RmsBlackWhiteAuditDetail) Create(ctx context.Context, session *gorm.DB, model *modelv2.RmsBlackWhiteAuditDetail) error {
	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
		return er.ConvertDBError(tx.WithContext(ctx).Create(model).Error)
	})
}
func (r *RmsBlackWhiteAuditDetail) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteAuditDetail, error) {
	var item modelv2.RmsBlackWhiteAuditDetail
	err := er.ConvertDBError(fn(global.DB.Model(&modelv2.RmsBlackWhiteAuditDetail{})).WithContext(ctx).First(&item).Error)
	return &item, err
}
