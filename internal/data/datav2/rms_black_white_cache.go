package datav2

import (
	"context"
	"errors"
	"github.com/go-redis/redis/v8"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/compare"
	"slices"
	"strconv"
	"strings"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/repo"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/json"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/sha256"
	"gorm.io/gorm"
)

type RmsBlackWhiteCache struct {
	isBlack bool
}

func NewRmsBlackWhiteListCache(isBlack bool) repo.BlackWhiteCacheRepo {
	return &RmsBlackWhiteCache{
		isBlack: isBlack,
	}
}
func NewRmsBlackListCache() repo.BlackWhiteCacheRepo {
	return &RmsBlackWhiteCache{
		isBlack: true,
	}
}
func NewRmsWhiteListCache() repo.BlackWhiteCacheRepo {
	return &RmsBlackWhiteCache{
		isBlack: false,
	}
}
func (s *RmsBlackWhiteCache) IsBlack() bool {
	return s.isBlack
}
func (s *RmsBlackWhiteCache) IncUpdate(ctx context.Context, bwlID int64) (err error) {
	var nowUnix = time.Now().Local().Unix()
	var bwl *modelv2.RmsBlackWhiteList
	cacheKeyPrefix := NewCacheKey(s.isBlack)
	bwl, err = NewRmsBlackWhiteList().First(ctx, func(db *gorm.DB) *gorm.DB {
		return db.Where("id = ?", bwlID)
	})
	if err != nil {
		return err
	}
	var accessPoints string
	accessPoints, err = redisutils.HGet(cacheKeyPrefix.GetListKey(bwlID), "access_points")
	if errors.Is(err, redis.Nil) {
		accessPoints = "[]"
	}
	var fields []string
	var requiredFields []string
	var fieldModels entity.Fields

	fieldModels, err = NewRmsBlackWhiteList().Fields(ctx, bwl.ID)
	if err != nil {
		return
	}

	for _, field := range fieldModels {
		fields = append(fields, field.Name)
		if field.IsRequired {
			requiredFields = append(requiredFields, field.Name)
		}
	}
	var oldAps = json.JsonUnmarshal[[]string](accessPoints)
	var oldApMap = make(map[string]string)
	if len(bwl.ChangeData) > 0 || accessPoints == "[]" {
		tmpBwl := json.JsonUnmarshal[modelv2.RmsBlackWhiteList](bwl.ChangeData)
		if tmpBwl.AccessPoints != "" {
			bwl.AccessPoints = tmpBwl.AccessPoints
		}

		var newAps = json.JsonUnmarshal[[]string](bwl.AccessPoints)

		for _, ap := range oldAps {
			oldApMap[ap] = ap
		}
		ins, _, del := compare.NewCompare[string, string](func(s string) string {
			return s
		}, func(s2 string, s string) bool {
			return false
		}, oldApMap).Compare(newAps)
		for _, ap := range ins {
			err = s.addAccessPointBw(cacheKeyPrefix.GetAccessPointKey(), ap, bwlID)
			if err != nil {
				return
			}
		}
		for _, ap := range del {
			err = s.removeAccessPointBw(cacheKeyPrefix.GetAccessPointKey(), ap, bwlID)
			if err != nil {
				return
			}
		}
		suc := redisutils.HSet(cacheKeyPrefix.GetListKey(bwlID), "access_points", tmpBwl.AccessPoints)
		if !suc {
			err = er.Internal.WithMsg("failed to flush cache[1]").WithStack()
			return
		}
	}

	suc := redisutils.HMSet(cacheKeyPrefix.GetListKey(bwlID), map[string]interface{}{
		"bwl_id":          strconv.FormatInt(bwl.ID, 10),
		"fields":          json.JsonMarshal(fields),
		"access_points":   bwl.AccessPoints,
		"required_fields": json.JsonMarshal(requiredFields),
		"name":            bwl.Name,
		"status":          "true",
	})
	if !suc {
		err = er.Internal.WithMsg("failed to flush cache[1]").WithStack()
		return
	}

	//internal/data/datav2/rms_blackwhite_item.go:119
	var parentIds []int64
	var delKeys []string
	err = global.DB.Table(modelv2.NewRmsBlackWhiteItem().TableName()+" as t").Select("t.parent_id").Where("t.bwl_id = ?", bwlID).Where("t.status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("t.parent_id > ?", 0).Pluck("parent_id", &parentIds).Error
	if err != nil {
		return
	}

	if len(parentIds) > 0 {
		var requiredSha256 [][]byte
		err = global.DB.Model(modelv2.NewRmsBlackWhiteItem()).Where("bwl_id = ?", bwlID).Where("id in (?)", parentIds).Pluck("required_sha256", &requiredSha256).Error
		if err != nil {
			return err
		}
		for _, sha := range requiredSha256 {
			delKeys = append(delKeys, sha256.BytesToBase64String(sha)[:20])
		}

	}
	{
		var requiredSha256 [][]byte
		err = global.DB.Model(modelv2.NewRmsBlackWhiteItem()).Where("bwl_id = ?", bwlID).Where("status = ?", modelv2.BlackWhiteItemStatusRemovePendingApproval).Pluck("required_sha256", &requiredSha256).Error
		if err != nil {
			return
		}
		for _, sha := range requiredSha256 {
			delKeys = append(delKeys, sha256.BytesToBase64String(sha)[:20])
		}
		if len(delKeys) > 0 {
			redisutils.HDel(cacheKeyPrefix.GetItemKey(bwlID), delKeys...)
		}
	}
	if len(delKeys) > 0 {
		redisutils.HDel(cacheKeyPrefix.GetItemKey(bwlID), delKeys...)
	}

	{
		var items []*modelv2.RmsBlackWhiteItem
		_, items, err = NewRmsBlackWhiteItem().GetBlackWhiteItems(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("bwl_id = ?", bwlID).Where("status = ?", modelv2.BlackWhiteItemStatusPendingApproval).Where("start_date <= ?", nowUnix).Where("end_date >= ?", nowUnix)
		})
		if err != nil {
			return
		}
		for _, item := range items {

			var fields modelv2.BlackWhiteItemExtends
			var rule = make(map[string]interface{})
			fields, err = item.Extends.ToFields()
			if err != nil {
				return
			}
			for fieldName, fieldValue := range fields {
				rule[fieldName] = fieldValue
			}

			//redisutils.HSet(cacheKeyPrefix.GetItemKey(bwlID), sha256.BytesToBase64String(item.RequiredSha256)[:20], json.JsonMarshal(rule)) //TODO 复原完整数据
			redisutils.HSet(cacheKeyPrefix.GetItemKey(bwlID), sha256.BytesToBase64String(item.RequiredSha256)[:20], "1")

		}

	}

	return nil
}
func (c *RmsBlackWhiteCache) ChangeStatus(bwlID int64, status bool) (err error) {
	cacheKeyPrefix := NewCacheKey(c.isBlack)
	var exists int64
	exists, err = redisutils.Exists(cacheKeyPrefix.GetListKey(bwlID))
	if err != nil {
		return
	}
	if exists > 0 {
		suc := redisutils.HSet(cacheKeyPrefix.GetListKey(bwlID), "status", strconv.FormatBool(status))
		if !suc {
			err = er.Internal.WithMsg("failed to change black white cache status").WithStack()
		}
	}

	return nil
}
func (c *RmsBlackWhiteCache) RemoveBlackWhite(bwlID int64) (err error) {
	cacheKeyPrefix := NewCacheKey(c.isBlack)
	accessPointJson, err := redisutils.HGet(cacheKeyPrefix.GetListKey(bwlID), "access_points")
	if errors.Is(err, redis.Nil) {
		err = nil
		return
	}
	if err != nil {
		return
	}
	if len(accessPointJson) > 0 {
		accessPoint := json.JsonUnmarshal[[]string](accessPointJson)
		for _, ap := range accessPoint {
			err = c.removeAccessPointBw(cacheKeyPrefix.GetAccessPointKey(), ap, bwlID)
			if err != nil {
				return
			}
		}
		suc := redisutils.Del(cacheKeyPrefix.GetListKey(bwlID))
		if !suc {
			err = er.Internal.WithMsgf("Failed to remove black white cache,bwlID:%d,key:%s", bwlID, cacheKeyPrefix.GetListKey(bwlID)).WithStack()
			return
		}
		suc = redisutils.Del(cacheKeyPrefix.GetItemKey(bwlID))
		if !suc {
			err = er.Internal.WithMsgf("Failed to remove black white cache,bwlID:%d,key:%s", bwlID, cacheKeyPrefix.GetItemKey(bwlID)).WithStack()
			return
		}
	}

	return nil
}

func (c *RmsBlackWhiteCache) removeAccessPointBw(key string, accessPoint string, bwlID int64) (err error) {
	res, err := redisutils.HGet(key, accessPoint)
	if errors.Is(err, redis.Nil) {
		return nil
	}
	if err != nil {
		return
	}
	var bwIDs = strings.Split(res, ",")
	for idx, id := range bwIDs {
		if id == strconv.FormatInt(bwlID, 10) {
			bwIDs = slices.Delete(bwIDs, idx, idx+1)
		}
	}
	suc := redisutils.HSet(key, accessPoint, strings.Join(bwIDs, ","))
	if !suc {
		err = er.Internal.WithMsgf("Failed to update access point's BwlIDs,bwlID:%d,key:%s", bwlID, key).WithStack()
	}
	return
}
func (c *RmsBlackWhiteCache) addAccessPointBw(key string, accessPoint string, bwlID int64) (err error) {
	res, err := redisutils.HGet(key, accessPoint)
	if errors.Is(err, redis.Nil) {
		err = nil
	}
	if err != nil {
		return
	}
	var bwIDs []string

	if res != "" {
		bwIDs = strings.Split(res, ",")
	}

	suc := redisutils.HSet(key, accessPoint, strings.Join(append(bwIDs, strconv.FormatInt(bwlID, 10)), ","))
	if !suc {
		err = er.Internal.WithMsgf("Failed to update access point's BwlIDs,bwlID:%d,key:%s", bwlID, key).WithStack()
	}
	return
}
func (s *RmsBlackWhiteCache) GetBlackWhiteCache(ctx context.Context, day string) (res *modelv2.BlackWhiteCache, err error) {
	var maxAuditID = make(map[int64]int64) // bwl_id => audit_id
	var dayTime time.Time
	var listType = modelv2.BlackWhiteListTypeWhite
	res = modelv2.NewBwCacheMap(s.isBlack)
	if s.isBlack {
		listType = modelv2.BlackWhiteListTypeBlack
	}
	dayTime, err = time.ParseInLocation("2006-01-02", day, time.Local)
	if err != nil {
		err = er.InvalidArgument.WithMsg("day format error, for example:[2006-01-02].")
		return
	}
	var auditPass []*modelv2.RmsBlackWhiteAudit
	_, auditPass, err = NewRmsBlackWhiteAudit().List(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("state = ?", modelv2.BlackWhiteAuditStatePass).Where("mode != ?", modelv2.BlackWhiteAuditModeDelete).Group("bwl_id").Select("max(id) as id,bwl_id")
	})
	if err != nil {
		return
	}
	if len(auditPass) > 0 {
		var bwlIDs []int64
		for _, pass := range auditPass {
			maxAuditID[pass.BwlID] = pass.ID
			bwlIDs = append(bwlIDs, pass.BwlID)
		}
		var bwlModels []*modelv2.RmsBlackWhiteList
		_, bwlModels, err = NewRmsBlackWhiteList().List(ctx, nil, func(db *gorm.DB) *gorm.DB {
			return db.Where("type = ?", listType).Where("id in ?", bwlIDs)
		})
		if err != nil {
			return
		}

		for _, bwl := range bwlModels {
			var bwlCache *modelv2.BlackWhiteCacheMetadata
			bwlCache, err = s.GenerateBlackWhiteCache(ctx, maxAuditID[bwl.ID], dayTime, bwl)
			if err != nil {
				return
			}
			err = res.Set(bwlCache)
			if err != nil {
				return
			}
		}
	}

	return
}

func (s *RmsBlackWhiteCache) GenerateBlackWhiteCache(ctx context.Context, maxAuditID int64, dayTime time.Time, bwl *modelv2.RmsBlackWhiteList) (bwlCache *modelv2.BlackWhiteCacheMetadata, err error) {
	var fields []string
	var requiredFields []string
	var fieldModels entity.Fields
	//生成黑白名单基本信息
	bwlCache = &modelv2.BlackWhiteCacheMetadata{
		BwlID:                bwl.ID,
		Name:                 bwl.Name,
		AccessPoints:         json.JsonUnmarshal[[]string](bwl.AccessPoints),
		Status:               bwl.Status == 1,
		Rules:                []map[string]interface{}{},
		RequiredFieldsSha256: make(map[string]int),
	}
	fieldModels, err = NewRmsBlackWhiteList().Fields(ctx, bwl.ID)
	if err != nil {
		return
	}

	for _, field := range fieldModels {
		fields = append(fields, field.Name)
		if field.IsRequired {
			requiredFields = append(requiredFields, field.Name)
		}
	}
	bwlCache.Fields = fields
	bwlCache.RequiredFields = requiredFields
	//生成黑白名单规则
	var items []*modelv2.RmsBlackWhiteItem
	_, items, err = NewRmsBlackWhiteItem().GetBlackWhiteItems(ctx, nil, func(db *gorm.DB) *gorm.DB {
		return db.Where("bwl_id = ?", bwlCache.BwlID).
			Where("audit_id > 0").
			Where("audit_id <= ?", maxAuditID).
			Where("status  in ?", []int64{modelv2.BlackWhiteItemStatusPass, modelv2.BlackWhiteItemStatusPass, modelv2.BlackWhiteItemStatusModified}).
			Where("start_date <= ?", dayTime.Unix()).
			Where("end_date >= ?", dayTime.Unix())
	})
	if err != nil {
		return
	}
	var rules []map[string]interface{}
	for _, item := range items {
		var fields modelv2.BlackWhiteItemExtends
		var rule = make(map[string]interface{})
		fields, err = item.Extends.ToFields()
		if err != nil {
			return
		}
		for fieldName, fieldValue := range fields {
			rule[fieldName] = fieldValue
		}
		rules = append(rules, rule)

		bwlCache.RequiredFieldsSha256[sha256.BytesToBase64String(item.RequiredSha256)[:20]] = len(rules) - 1
	}
	bwlCache.Rules = rules
	return bwlCache, nil
}

var RdbKeyBlackItemPrefix = "risk:{black_list}:items:"                  // risk:black_list:items:%d bwl_ids => bwi json array
var RdbKeyWhiteItemPrefix = "risk:{white_list}:items:"                  // risk:white_list:items:%d bwl_ids => bwi json array
var RdbKeyBlackListAccessPointPrefix = "risk:{black_list}:access_point" // risk:black_list:access_point  access_point => bwl_ids json
var RdbKeyWhiteListAccessPointPrefix = "risk:{white_list}:access_point" // risk:white_list:access_point  access_point => bwl_ids json
var RdbKeyBlackListPrefix = "risk:{black_list}:record:"                 // risk:black_list:record:%d bwl_id => bwl json
var RdbKeyWhiteListPrefix = "risk:{white_list}:record:"                 // risk:white_list:record:%d  bwl_id => bwl json
type CacheKey struct {
	ItemPrefix string
	ListPrefix string
	AccessKey  string
	UpdateKey  string
}

func NewCacheKey(isBlack bool) *CacheKey {
	if isBlack {
		return &CacheKey{
			ItemPrefix: RdbKeyBlackItemPrefix,
			ListPrefix: RdbKeyBlackListPrefix,
			AccessKey:  RdbKeyBlackListAccessPointPrefix,
			UpdateKey:  "risk:{black_list}:update",
		}
	} else {
		return &CacheKey{
			ItemPrefix: RdbKeyWhiteItemPrefix,
			ListPrefix: RdbKeyWhiteListPrefix,
			AccessKey:  RdbKeyWhiteListAccessPointPrefix,
			UpdateKey:  "risk:{white_list}:update",
		}
	}
}
func (c *CacheKey) GetItemKey(bwlID int64) string {
	return c.ItemPrefix + strconv.FormatInt(bwlID, 10)
}
func (c *CacheKey) GetNewItemKey(bwlID int64) string {
	return c.ItemPrefix + "new" + strconv.FormatInt(bwlID, 10)
}
func (c *CacheKey) GetNewItemPrefixKey() string {
	return c.ItemPrefix + "new"
}
func (c *CacheKey) GetListKey(bwlID int64) string {
	return c.ListPrefix + strconv.FormatInt(bwlID, 10)
}
func (c *CacheKey) GetAccessPointKey() string {
	return c.AccessKey
}

type KeysSlice []string
type KeysMap map[string]interface{}

func (s KeysSlice) ToMap() map[string]interface{} {
	var res = make(map[string]interface{}, len(s))
	for _, val := range []string(s) {
		res[val] = nil
	}
	return res
}

// getPrefixKeysMap 获取指定前缀的所有键并转换为map
func getPrefixKeysMap(prefix string) (map[string]interface{}, error) {
	keys, err := redisutils.GetPrefixKeys(prefix)
	if err != nil {
		return nil, err
	}
	return KeysSlice(keys).ToMap(), nil
}

// getPrefixHashKeysMap 获取指定前缀的所有键并转换为map
func getPrefixHashKeysMap(key string) (map[string]interface{}, error) {
	keys := redisutils.HKeys(key)
	return KeysSlice(keys).ToMap(), nil
}
func (s *RmsBlackWhiteCache) Flush(model *modelv2.BlackWhiteCache) (err error) {
	var ctx = context.Background()
	if model == nil {
		return nil
	}
	var (
		delListKeyMap = make(map[string]interface{})
		delItemMap    = make(map[string]interface{})
		delAccessMap  = make(map[string]interface{})
	)
	cacheKeyPrefix := NewCacheKey(model.IsBlack)
	delListKeyMap, err = getPrefixKeysMap(cacheKeyPrefix.ListPrefix)
	if err != nil {
		return
	}
	delItemMap, err = getPrefixKeysMap(cacheKeyPrefix.ItemPrefix)
	if err != nil {
		return
	}
	delAccessMap, err = getPrefixHashKeysMap(cacheKeyPrefix.GetAccessPointKey())
	if err != nil {
		return
	}
	_, err = redisutils.Pipeline(func(rdb redis.Pipeliner) (innerErr error) {

		for bwlID, value := range model.BwlIDs {
			//删除当前数据库还存在的数据，避免后期删除
			delete(delListKeyMap, cacheKeyPrefix.GetListKey(bwlID))
			delete(delItemMap, cacheKeyPrefix.GetItemKey(bwlID))

			innerErr = rdb.HMSet(ctx, cacheKeyPrefix.GetListKey(bwlID), map[string]interface{}{
				"bwl_id":          strconv.FormatInt(value.BwlID, 10),
				"fields":          json.JsonMarshal(value.Fields),
				"access_points":   json.JsonMarshal(value.AccessPoints),
				"required_fields": json.JsonMarshal(value.RequiredFields),
				"name":            value.Name,
				"status":          strconv.FormatBool(value.Status),
			}).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush cache[1]").WithErr(innerErr).WithStack()
				return
			}
			var newItemKey = cacheKeyPrefix.GetNewItemKey(bwlID)
			if len(value.RequiredFieldsSha256) > 0 {
				for sha, _ := range value.RequiredFieldsSha256 {
					//innerErr = rdb.HSet(ctx, newItemKey, sha, json.JsonMarshal(value.Rules[itemIdx])).Err()
					innerErr = rdb.HSet(ctx, newItemKey, sha, "1").Err() //TODO 以后需要详细字段数据时候写进去
					if innerErr != nil {
						rdb.Del(ctx, newItemKey)
						innerErr = er.Internal.WithMsg("failed to flush cache[2]").WithErr(innerErr).WithStack()
						return
					}
				}
				innerErr = rdb.Rename(ctx, newItemKey, cacheKeyPrefix.GetItemKey(bwlID)).Err()
				if err != nil {
					innerErr = er.Internal.WithMsg("failed to flush cache[3]").WithStack().WithErr(innerErr)
					return
				}
			}

		}
		for accessPoint, bwlIDs := range model.AccessPointRules {
			var ids []string
			for _, id := range bwlIDs {
				ids = append(ids, strconv.FormatInt(id, 10))
			}
			delete(delAccessMap, accessPoint)
			innerErr = rdb.HSet(ctx, cacheKeyPrefix.GetAccessPointKey(), accessPoint, strings.Join(ids, ",")).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush cache[4]").WithErr(innerErr).WithStack()
				return
			}
		}
		//清除当前数据库已经不存在的数据
		for _, list := range []map[string]interface{}{delListKeyMap, delItemMap} {
			for key, _ := range list {
				innerErr = rdb.Del(ctx, key).Err()
				if innerErr != nil {
					innerErr = er.Internal.WithMsg("failed to flush cache[5]").WithErr(innerErr).WithStack()
					return
				}
			}
		}
		for key, _ := range delAccessMap {
			innerErr = rdb.HDel(ctx, cacheKeyPrefix.GetAccessPointKey(), key).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush cache[6]").WithErr(innerErr).WithStack()
				return
			}
		}
		rdb.Set(ctx, cacheKeyPrefix.UpdateKey, time.Now().Local().Format("2006-01-02 15:04:05"), 0)
		return nil
	})
	if err != nil {
		return
	}
	var newKeys []string
	newKeys, err = redisutils.GetPrefixKeys(cacheKeyPrefix.GetNewItemPrefixKey())
	if err != nil {
		return
	}
	for _, newKey := range newKeys {
		oldKey := strings.Replace(newKey, "new", "", 1)
		_, err = redisutils.Rename(newKey, oldKey)
		if err != nil {
			return
		}
	}
	return
}
