package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type RmsCraScoreReference struct {
	*Base
}

func (p *RmsCraScoreReference) Create(c *gin.Context, reference *model.RmsCraScoreReference) error {
	return p.db.Create(reference).Error

}
func (p *RmsCraScoreReference) Retrieve(c *gin.Context, reference *model.RmsCraScoreReference) (RmsCraScoreReference *model.RmsCraScoreReference, err error) {
	err = p.db.Where(reference).Order("create_time desc").First(&RmsCraScoreReference).Error
	return

}
func (p *RmsCraScoreReference) List(c *gin.Context, data *model.RmsCraScoreReference) (RmsCraScoreReferencelist []*model.RmsCraScoreReference, error error) {

	err := p.db.Where(data).Order("create_time desc").Find(&RmsCraScoreReferencelist).Error
	if err != nil {
		return nil, err
	}
	return

}
