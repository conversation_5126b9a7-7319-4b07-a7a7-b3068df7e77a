package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
)

type RuleParameterValue struct {
	*Base
}

func (p *RuleParameterValue) Create(c *gin.Context, cp *model.RmsRuleParameterValue) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *RuleParameterValue) Update(c *gin.Context, id int64, cp *model.RmsRuleParameterValue) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *RuleParameterValue) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsRuleParameterValue))
}
func (p *RuleParameterValue) List(c *gin.Context, req *domain.RuleParameterValueList) ([]*model.RmsRuleParameterValue, error) {
	li := []*model.RmsRuleParameterValue{}
	return li, p.Base.ListPage(c, p.db.Order("id DESC"), req, &li)
}
func (p *RuleParameterValue) Count(c *gin.Context, req *domain.RuleParameterValueList) (count int64, err error) {
	return p.Base.ListCount(c, p.db, req, new(model.RmsRuleParameterValue))
}
func (p *RuleParameterValue) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterValue, error) {
	cp := new(model.RmsRuleParameterValue)
	return cp, p.Base.Retrieve(c, id, cp)
}

func (p *RuleParameterValue) AllByGroupID(c *gin.Context, GroupID int64) ([]*model.RmsRuleParameterValue, error) {
	li := []*model.RmsRuleParameterValue{}
	return li, p.Base.All(c, p.db.Where("group_id=?", GroupID), &li)
}

func (p *RuleParameterValue) AllByParamIDAndGroupID(c *gin.Context, ParamID, GroupID int64) ([]*model.RmsRuleParameterValue, error) {
	li := []*model.RmsRuleParameterValue{}
	return li, p.Base.All(c, p.db.Where("param_id=? and group_id=?", ParamID, GroupID), &li)
}

func (p *RuleParameterValue) DeleteByParamIDAndGroupID(c *gin.Context, ParamID, GroupID int64) error {
	return p.Base.Delete(c, p.db.Where("param_id=? and group_id=?", ParamID, GroupID), new(model.RmsRuleParameterValue))
}
