package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type Blacklist struct {
	*Base
}

func (p *Blacklist) ListLimit(db *gorm.DB, req *domain.BlacklistList) *gorm.DB {
	if len(req.<PERSON>) > 0 {
		db = db.Where("risk_key=?", req.RiskKey)
	}
	if len(req.RiskValue) > 0 {
		db = db.Where("risk_value like ?", "%"+req.RiskValue+"%")
	}
	if req.Remark != nil && len(*req.Remark) > 0 {
		db = db.Where("remark like ?", "%"+*req.Remark+"%")
	}
	return db
}

func (p *Blacklist) Create(c *gin.Context, cp *model.RmsBlacklist) error {
	return p.Base.CreateOmit(c, cp, "updated_at")
}
func (p *Blacklist) Update(c *gin.Context, id int64, cp *model.RmsBlacklist) error {
	return p.Base.Updates(c, p.db.Where("rc_id=?", id), cp)
}
func (p *Blacklist) Delete(c *gin.Context, id int64) error {
	return p.Base.Delete(c, p.db.Where("rc_id=?", id), new(model.RmsBlacklist))
}
func (p *Blacklist) List(c *gin.Context, req *domain.BlacklistList) ([]*model.RmsBlacklist, error) {
	li := []*model.RmsBlacklist{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("rc_id DESC"), req, &li)
}
func (p *Blacklist) Count(c *gin.Context, req *domain.BlacklistList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsBlacklist))
}
func (p *Blacklist) Retrieve(c *gin.Context, id int64) (*model.RmsBlacklist, error) {
	cp := new(model.RmsBlacklist)
	return cp, p.Base.Take(c, p.db.Where("rc_id=?", id), cp)
}
