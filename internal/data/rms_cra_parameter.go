package data

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"
)

type RmsCraParameter struct {
	*Base
}

func (p *RmsCraParameter) Create(c *gin.Context, cp *model.RmsCraParameter) error {
	//修改状态
	if cp.CraParameterStatus == 1 {
		parameterVal := make([]string, 0)
		var result []*domain.ParameterInfo

		// 将 JSON 数据解码到 map 中
		err := json.Unmarshal([]byte(cp.ParamInfo), &result)
		if err != nil {
			return err
		}
		for _, v := range result {
			parameterVal = append(parameterVal, v.Value)
		}
		if err := p.db.Model(&model.RmsCraParameterVal{}).Where("param_val_id in ?", parameterVal).Update("status", 1).Error; err != nil {
			return err
		}
	}
	err := p.Base.CreateOmit(c, cp, "update_time")
	if err != nil {
		return err
	}
	return p.UpdateRdbForParameter(c, cp.CraFrameworkId)
}
func (p *RmsCraParameter) Update(c *gin.Context, rms_cra_parameter string, cp *model.RmsCraParameter) error {
	//修改状态
	if cp.CraParameterStatus == 1 {
		parameterVal := make([]string, 0)
		var result []*domain.ParameterInfo

		// 将 JSON 数据解码到 map 中
		err := json.Unmarshal([]byte(cp.ParamInfo), &result)
		if err != nil {
			return err
		}
		for _, v := range result {
			parameterVal = append(parameterVal, v.Value)
		}
		if err := p.db.Model(&model.RmsCraParameterVal{}).Where("param_val_id in ?", parameterVal).Update("status", 1).Error; err != nil {
			return err
		}
	}
	err := p.db.Select("*").Omit("id,create_time,cra_parameter_name,cra_framework_id").Where("cra_parameter_id=?", rms_cra_parameter).Updates(cp).Error
	if err != nil {
		return err
	}
	return p.UpdateRdbForParameter(c, cp.CraFrameworkId)
}
func (p *RmsCraParameter) Delete(c *gin.Context, rms_cra_parameter string) error {
	RmsCraParameterInfo := &model.RmsCraParameter{}
	err := p.db.Model(model.RmsCraParameter{}).Where("cra_parameter_id=?", rms_cra_parameter).First(RmsCraParameterInfo).Error
	if err != nil {
		return err
	}
	err = p.db.Where("cra_parameter_id=?", rms_cra_parameter).Delete(new(model.RmsCraParameter)).Error
	if err != nil {
		return err
	}
	return p.UpdateRdbForParameter(c, RmsCraParameterInfo.CraFrameworkId)
}
func (p *RmsCraParameter) List(c *gin.Context, data *model.RmsCraParameter) ([]*domain.CraParameter, error) {
	li := []*domain.CraParameter{}
	err := p.db.Table("rms_cra_parameter").Where(data).Find(&li).Error
	return li, err
}
func (p *RmsCraParameter) Count(c *gin.Context) (count int64, err error) {
	err = p.db.Model(RmsCraParameter{}).Count(&count).Error
	return
}

func (p *RmsCraParameter) Retrieve(c *gin.Context, rms_cra_parameter string) (*model.RmsCraParameter, error) {
	f := new(model.RmsCraParameter)
	err := p.db.Where("cra_parameter_id=?", rms_cra_parameter).First(f).Error
	return f, err
}
func (p *RmsCraParameter) First(c *gin.Context, cp *model.RmsCraParameter) (val *model.RmsCraParameter, err error) {
	err = p.db.Where(cp).First(&val).Error
	return
}
func (p *RmsCraParameter) FirstWhereLike(c *gin.Context, parameter map[string]string) (val *model.RmsCraParameter, err error) {
	db := p.db
	for k, v := range parameter {
		db = db.Where(fmt.Sprintf("%s like ?", k), v)
	}
	err = db.First(&val).Error
	return
}

func (p *RmsCraParameter) BatchUpdate(c *gin.Context, req *domain.CraParameterBatchUpdateReq) error {
	ids := make([]string, 0)
	Weightsql := " CASE "
	weightParam := make([]interface{}, 0) // 使用 []interface{} 类型
	CraParameterStatusSql := " CASE "
	CraParameterStatusParam := make([]interface{}, 0) // 使用 []interface{} 类型

	// 动态生成 SQL 和参数
	for _, i := range req.CraParameterBatchUpdateList {
		ids = append(ids, i.CraParameterId)
		Weightsql += " WHEN cra_parameter_id = ? THEN ? "
		CraParameterStatusSql += " WHEN cra_parameter_id = ? THEN ? "
		weightParam = append(weightParam, i.CraParameterId, i.Weight) // 直接追加 interface{}
		CraParameterStatusParam = append(CraParameterStatusParam, i.CraParameterId, i.CraParameterStatus)
	}
	Weightsql += " END"
	CraParameterStatusSql += " END"

	// 拼接完整的 SQL 语句
	sql := fmt.Sprintf("UPDATE rms_cra_parameter SET weight = %s, cra_parameter_status = %s WHERE cra_parameter_id IN (?) and cra_framework_id = ?;", Weightsql, CraParameterStatusSql)

	// 合并所有参数
	allParams := append(weightParam, CraParameterStatusParam...)
	allParams = append(allParams, ids)                // 将 ids 作为最后一个参数
	allParams = append(allParams, req.CraFrameworkId) // 将 ids 作为最后一个参数

	// 执行 SQL
	err := p.db.Exec(sql, allParams...).Error
	if err != nil {
		return err
	}

	return p.UpdateRdbForParameter(c, req.CraFrameworkId)
}
func (p *RmsCraParameter) UpdateRdbForParameter(c *gin.Context, cra_framework_id string) error {
	list := make([]*model.RmsCraParameter, 0)
	err := p.db.Model(model.RmsCraParameter{}).Where("cra_framework_id = ?", cra_framework_id).Find(&list).Error
	if err != nil {
		return err
	}

	for _, val := range list {
		userJSON, _ := json.Marshal(val)

		// 存储 JSON 字符串
		err = global.Rdb.Set(c, fmt.Sprintf("risk:cra:rms_cra_parameter:%s", val.CraParameterId), userJSON, 0).Err()
		if err != nil {
			return err
		}
	}
	return nil
}
