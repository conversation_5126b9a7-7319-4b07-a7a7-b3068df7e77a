package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type RuleParameterGroup struct {
	*Base
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *RuleParameterGroup) ListLimit(db *gorm.DB, req *domain.RuleParameterGroupList) *gorm.DB {
	if req.CheckPoint != "" {
		db = db.Where("check_point like ?", "%"+req.CheckPoint+"%")
	}
	if req.Code != "" {
		db = db.Where("code like ?", "%"+req.Code+"%")
	}
	return db
}

func (p *RuleParameterGroup) Create(c *gin.Context, cp *model.RmsRuleParameterGroup) error {
	return p.Base.Create(c, p.db, cp)
}
func (p *RuleParameterGroup) Update(c *gin.Context, id int64, cp *model.RmsRuleParameterGroup) error {
	return p.Base.Updates(c, p.db.Where("id=?", id).Select(
		"code",
		"check_point",
		"description",
		"default_flag",
		"able",
	), cp)
}
func (p *RuleParameterGroup) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsRuleParameterGroup))
}
func (p *RuleParameterGroup) List(c *gin.Context, req *domain.RuleParameterGroupList) ([]*model.RmsRuleParameterGroup, error) {
	li := []*model.RmsRuleParameterGroup{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *RuleParameterGroup) Count(c *gin.Context, req *domain.RuleParameterGroupList) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsRuleParameterGroup))
}
func (p *RuleParameterGroup) RetrieveByCPCode(c *gin.Context, cp_code string) (*model.RmsRuleParameterGroup, error) {
	cp := new(model.RmsRuleParameterGroup)
	return cp, p.Base.First(c, p.db.Where("check_point=?", cp_code), cp)
}
func (p *RuleParameterGroup) Retrieve(c *gin.Context, id int64) (*model.RmsRuleParameterGroup, error) {
	cp := new(model.RmsRuleParameterGroup)
	return cp, p.Base.Retrieve(c, id, cp)
}
func (p *RuleParameterGroup) RetrieveByCode(c *gin.Context, code string) (*model.RmsRuleParameterGroup, error) {
	cp := new(model.RmsRuleParameterGroup)
	return cp, p.Base.First(c, p.db.Where("code=?", code), cp)
}
func (p *RuleParameterGroup) FirstByAPCode(c *gin.Context, id int64) (*model.RmsRuleParameterGroup, error) {
	cp := new(model.RmsRuleParameterGroup)
	return cp, p.Base.First(c, p.db.Where("id=?", id), cp)
}
func (p *RuleParameterGroup) All(c *gin.Context) ([]*model.RmsRuleParameterGroup, error) {
	li := []*model.RmsRuleParameterGroup{}
	return li, p.Base.All(c, p.db, &li)
}

func (p *RuleParameterGroup) AllExcludeDefault(c *gin.Context) ([]*model.RmsRuleParameterGroup, error) {
	li := []*model.RmsRuleParameterGroup{}
	return li, p.Base.All(c, p.db.Where("default_flag=?", 0), &li)
}

func (p *RuleParameterGroup) SetParamIds(c *gin.Context, RuleParamID int64, ParamIds string) error {
	return p.Base.Update(c, p.db.Model(new(model.RmsRuleParameterGroup)).Where("id=?", RuleParamID), "param_ids", ParamIds)
}
