package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
	"time"
)

type FilterExtra struct {
	*Base
}

func (p *FilterExtra) Create(c *gin.Context, cp *model.RmsFileExtra) error {
	return p.Base.CreateOmit(c, cp, "update_time")
}
func (p *FilterExtra) ReportError(c *gin.Context, file_id string, cp *model.RmsFileExtra) error {
	return p.Base.Updates(c, p.db.Select("update_time", "file_status").Where("file_id=?", file_id), cp)
}
func (p *FilterExtra) ReportSuccess(c *gin.Context, fileName string, format string, filePath string, region string, uuid string) error {
	if err := p.Base.db.Transaction(func(tx *gorm.DB) error {

		if err := tx.Select("update_time", "file_status").Where("file_id = ?", uuid).Updates(model.RmsFileExtra{UpdateTime: time.Now(), FileStatus: 1}).Error; err != nil {
			return err
		}

		RmsFileS3 := &model.RmsFileS3{
			CreateTime: time.Now(), // 创建时间
			FileName:   fileName,
			FileFormat: format,   // 文件类型 csv excel pdf
			FilePath:   filePath, // 文件存储路径
			Region:     region,   // 文件区域
			ExtraId:    uuid,     // 唯一关联ID
		}
		if err := tx.Create(RmsFileS3).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}

// ListLimit 返回包含 req 筛选后的 db 指针
func (p *FilterExtra) ListLimit(db *gorm.DB, req *domain.FilterExtraListReq) *gorm.DB {
	if req.FileName != "" {
		db = db.Where("file_name like ?", "%"+req.FileName+"%")
	}
	if req.CreateTime != "" {
		db = db.Where("DATE(create_time)=?", req.CreateTime)
	}
	if req.StartEventTime != "" && req.EndEventTime != "" {
		db = db.Where("start_event_time<= ?", req.EndEventTime)
		db = db.Where("end_event_time>= ?", req.StartEventTime)
	}
	return db
}

func (p *FilterExtra) List(c *gin.Context, req *domain.FilterExtraListReq) ([]*model.RmsFileExtra, error) {
	li := []*model.RmsFileExtra{}
	return li, p.Base.ListPage(c, p.ListLimit(p.db, req).Order("id DESC"), req, &li)
}

func (p *FilterExtra) Count(c *gin.Context, req *domain.FilterExtraListReq) (count int64, err error) {
	return p.Base.ListCount(c, p.ListLimit(p.db, req), req, new(model.RmsFileExtra))
}
func (p *FilterExtra) Retrieve(c *gin.Context, file_id string) (*domain.FilterExtraRetrieveRes, error) {
	cp := new(domain.FilterExtraRetrieveRes)

	err := p.Base.db.Select("s3.file_path as file_path").Table("rms_file_extra as extra").
		Where("file_id = ?", file_id).
		Joins("left join rms_file_s3  as s3 on s3.extra_id=extra.file_id").First(cp).Error
	return cp, err
}
