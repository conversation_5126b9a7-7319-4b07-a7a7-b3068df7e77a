package data

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gorm.io/gorm"
)

type BusinessType struct {
	*Base
}

func (p *BusinessType) Listlimit(db *gorm.DB, req *domain.BusinessTypeList) *gorm.DB {
	if req.BusinessName != "" {
		db = db.Where("business_name like ?", "%"+req.BusinessName+"%")
	}
	return db
}

func (p *BusinessType) Create(c *gin.Context, cp *model.RmsBusinessType) error {
	return p.Base.Create(c, p.db, cp)
}
func (p *BusinessType) Update(c *gin.Context, id int64, cp *model.RmsBusinessType) error {
	return p.Base.Updates(c, p.db.Where("id=?", id), cp)
}
func (p *BusinessType) Delete(c *gin.Context, id int64) error {
	return p.Base.DeleteById(c, id, new(model.RmsBusinessType))
}
func (p *BusinessType) List(c *gin.Context, req *domain.BusinessTypeList) ([]*model.RmsBusinessType, error) {
	li := []*model.RmsBusinessType{}
	return li, p.Base.ListPage(c, p.Listlimit(p.db, req).Order("id DESC"), req, &li)
}
func (p *BusinessType) Count(c *gin.Context, req *domain.BusinessTypeList) (count int64, err error) {
	return p.Base.ListCount(c, p.Listlimit(p.db, req), req, new(model.RmsBusinessType))
}
func (p *BusinessType) Retrieve(c *gin.Context, id int64) (*model.RmsBusinessType, error) {
	cp := new(model.RmsBusinessType)
	return cp, p.Base.Retrieve(c, id, cp)
}
func (p *BusinessType) RetrieveByCode(c *gin.Context, code string) (*model.RmsBusinessType, error) {
	cp := new(model.RmsBusinessType)
	return cp, p.Base.Take(c, p.db.Where("business_code=?", code), cp)
}
func (p *BusinessType) All(c *gin.Context) ([]*model.RmsBusinessType, error) {
	li := []*model.RmsBusinessType{}
	return li, p.Base.All(c, p.db, &li)
}
