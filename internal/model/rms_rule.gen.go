package model

import (
	"time"
)

const TableNameRmsRule = "rms_rule"

// RmsRule 风控规则
type RmsRule struct {
	ID             int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                                                        // 主键
	RuleGroup      *string    `gorm:"column:rule_group;type:varchar(32);not null;index:idx_rg,priority:1;default:CMF;comment:规则组，即策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup" json:"rule_group"` // 规则组，即策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup
	RuleNo         string     `gorm:"column:rule_no;type:varchar(32);not null;uniqueIndex:uidx_rno,priority:1;comment:规则编号" json:"rule_no"`                                                            // 规则编号
	RuleName       string     `gorm:"column:rule_name;type:varchar(64);not null;comment:规则描述" json:"rule_name"`                                                                                        // 规则描述
	CheckPoint     string     `gorm:"column:check_point;type:varchar(256);not null;comment:检查点" json:"check_point"`                                                                                    // 检查点
	RuleType       string     `gorm:"column:rule_type;type:varchar(16);not null;comment:规则类型" json:"rule_type"`                                                                                        // 规则类型
	RuleContent    string     `gorm:"column:rule_content;type:text;not null;comment:规则内容" json:"rule_content"`                                                                                         // 规则内容
	ShortCircuit   bool       `gorm:"column:short_circuit;type:tinyint(1);not null;comment:是否支持短路" json:"short_circuit"`                                                                               // 是否支持短路
	Params         *string    `gorm:"column:params;type:varchar(1024);comment:参数，注意与规则参数区分" json:"params"`                                                                                             // 参数，注意与规则参数区分
	AgendaName     string     `gorm:"column:agenda_name;type:varchar(128);not null;comment:Agenda，和规则内容中的package一致" json:"agenda_name"`                                                                // Agenda，和规则内容中的package一致
	EngineType     string     `gorm:"column:engine_type;type:varchar(16);not null;comment:引擎类型，有状态/无状态" json:"engine_type"`                                                                            // 引擎类型，有状态/无状态
	DeployMethod   string     `gorm:"column:deploy_method;type:varchar(8);not null;comment:上下线方式，manual/auto" json:"deploy_method"`                                                                    // 上下线方式，manual/auto
	EngineInstance *string    `gorm:"column:engine_instance;type:varchar(128);comment:有状态类型情况下选择的引擎实例" json:"engine_instance"`                                                                         // 有状态类型情况下选择的引擎实例
	StartTime      *time.Time `gorm:"column:start_time;type:datetime;index:idx_start_end,priority:1;comment:生效时间" json:"start_time"`                                                                   // 生效时间
	EndTime        *time.Time `gorm:"column:end_time;type:datetime;index:idx_start_end,priority:2;comment:失效时间" json:"end_time"`                                                                       // 失效时间
	Status         string     `gorm:"column:status;type:varchar(16);not null;comment:状态，Online/Offline" json:"status"`                                                                                 // 状态，Online/Offline
	Memo           *string    `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                                                                             // 备注
	SyncedAt       *time.Time `gorm:"column:synced_at;type:datetime;comment:同步时间" json:"synced_at"`
	Priority       int        `gorm:"column:priority;type:int"`
	// 同步时间
	CreatedAt *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName RmsRule's table name
func (*RmsRule) TableName() string {
	return TableNameRmsRule
}
