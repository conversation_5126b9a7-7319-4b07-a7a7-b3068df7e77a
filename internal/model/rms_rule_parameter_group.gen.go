package model

import (
	"time"
)

const TableNameRmsRuleParameterGroup = "rms_rule_parameter_group"

// RmsRuleParameterGroup 规则参数组
type RmsRuleParameterGroup struct {
	ID          int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                         // 主键
	Code        string    `gorm:"column:code;type:varchar(64);not null;uniqueIndex:uidx_code,priority:1;comment:参数组编码" json:"code"` // 参数组编码
	ParamIds    string    `gorm:"column:param_ids;type:varchar(256);comment:下属规则参数id(可多个,逗号分割)" json:"param_ids"`                   // 下属规则参数id(可多个,逗号分割)
	CheckPoint  string    `gorm:"column:check_point;type:varchar(256);not null;comment:检查点" json:"check_point"`                     // 检查点
	Description string    `gorm:"column:description;type:varchar(256);not null;comment:参数组描述" json:"description"`                   // 参数组描述
	DefaultFlag int32     `gorm:"column:default_flag;type:int;not null;comment:默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否" json:"default_flag"` // 默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否
	Able        int32     `gorm:"column:able;type:int;not null;comment:状态，1-有效，0-失效" json:"able"`                                   // 状态，1-有效，0-失效。
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`                          // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`                          // 更新时间
}

// TableName RmsRuleParameterGroup's table name
func (*RmsRuleParameterGroup) TableName() string {
	return TableNameRmsRuleParameterGroup
}
