package model

import (
	"time"
)

const TableNameRmsEvent = "rms_event"

// RmsEvent 风险事件
type RmsEvent struct {
	ID             int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                     // 主键
	CheckPoint     string    `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp_rcode,priority:1;comment:联系人姓名" json:"check_point"` // 联系人姓名
	StrField1      *string   `gorm:"column:str_field1;type:varchar(64);comment:重要字符串类型字段1（建议主键），字段名见对应存储配置" json:"str_field1"`                     // 重要字符串类型字段1（建议主键），字段名见对应存储配置
	StrField2      *string   `gorm:"column:str_field2;type:varchar(64);comment:重要字符串类型字段2，字段名见对应存储配置" json:"str_field2"`                           // 重要字符串类型字段2，字段名见对应存储配置
	StrField3      *string   `gorm:"column:str_field3;type:varchar(64);comment:重要字符串类型字段3，字段名见对应存储配置" json:"str_field3"`                           // 重要字符串类型字段3，字段名见对应存储配置
	StrField4      *string   `gorm:"column:str_field4;type:varchar(64);comment:重要字符串类型字段4，字段名见对应存储配置" json:"str_field4"`                           // 重要字符串类型字段4，字段名见对应存储配置
	StrField5      *string   `gorm:"column:str_field5;type:varchar(64);comment:重要字符串类型字段5，字段名见对应存储配置" json:"str_field5"`                           // 重要字符串类型字段5，字段名见对应存储配置
	NumField1      *float64  `gorm:"column:num_field1;type:decimal(16,2);comment:重要数值类型字段1(值=原始值*1000)，字段名见对应存储配置" json:"num_field1"`              // 重要数值类型字段1(值=原始值*1000)，字段名见对应存储配置
	NumField2      *float64  `gorm:"column:num_field2;type:decimal(16,2);comment:重要数值类型字段2(值=原始值*1000)，字段名见对应存储配置" json:"num_field2"`              // 重要数值类型字段2(值=原始值*1000)，字段名见对应存储配置
	Event          string    `gorm:"column:event;type:text;not null;comment:json显示存储的事件完整内容" json:"event"`                                         // json显示存储的事件完整内容
	ResultCode     *string   `gorm:"column:result_code;type:varchar(16);index:idx_ckp_rcode,priority:2;comment:规则结果代码" json:"result_code"`         // 规则结果代码
	ResultMessage  string    `gorm:"column:result_message;type:varchar(64);not null;comment:规则结果内容" json:"result_message"`                         // 规则结果内容
	RuleNo         *string   `gorm:"column:rule_no;type:varchar(32);comment:规则编号" json:"rule_no"`                                                  // 规则编号
	RulesNotPassed *string   `gorm:"column:rules_not_passed;type:varchar(255);comment:未通过规则" json:"rules_not_passed"`                              // 未通过规则
	CreatedAt      time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`                                      // 创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`                                      // 更新时间
}

// TableName RmsEvent's table name
func (*RmsEvent) TableName() string {
	return TableNameRmsEvent
}
