package modelv2

import (
	jsoniter "github.com/json-iterator/go"
)

// RmsBlackWhiteItem
const (
	BlackWhiteItemStatusDraft                 = 1 //草稿
	BlackWhiteItemStatusRemoveDraft           = 2 // 移除草稿
	BlackWhiteItemStatusPendingApproval       = 3 // 新增/修改待审核
	BlackWhiteItemStatusRemovePendingApproval = 4 // 移除待审核
	BlackWhiteItemStatusPass                  = 5 // 审核通过
	BlackWhiteItemStatusReject                = 6 // 审核拒绝
	BlackWhiteItemStatusRemoved               = 7 // 移除

	BlackWhiteItemStatusModified = 8 //已经审核通过的记录被修改，状态变成当前状态，审核通过->移除，审核拒绝->审核通过
)

var BwItemNormalStatus = []int{
	BlackWhiteItemStatusPass,
}
var BwItemDraftStatus = []int{
	BlackWhiteItemStatusDraft,
	BlackWhiteItemStatusRemoveDraft,
}
var BwItemAuditPendingStatus = []int{
	BlackWhiteItemStatusPendingApproval,
	BlackWhiteItemStatusRemovePendingApproval,
}

const TableNameRmsBlackWhiteItem = "rms_black_white_item"

// RmsBlackWhiteList 黑名单白名单
type RmsBlackWhiteItem struct {
	ID             int64                     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	BwlID          int64                     `gorm:"column:bwl_id;type:bigint(11);not null;index;comment:黑白名单ID" json:"bwl_id"`
	StartDate      int64                     `gorm:"column:start_date;type:bigint(11);not null;comment:开始日期,时间戳" json:"start_date"`
	EndDate        int64                     `gorm:"column:end_date;type:bigint(11);not null;comment:结束日期,时间戳" json:"end_date"`
	RemainingDay   int64                     `gorm:"column:remaining;type:bigint(11);not null;comment:剩余时间" json:"remaining"`
	Extends        BlackWhiteItemExtendsJson `gorm:"column:extends;type:text;comment:扩展字段" json:"extends"`
	ParentID       int64                     `gorm:"column:parent_id;type:bigint(11);not null;comment:父ID,如果数据来源于数据更新，就将ID指向旧数据" json:"parent_id"`
	Status         int                       `gorm:"column:status;type:tinyint(1);not null;default:1;comment:状态:1.草稿 2.移除草稿 3.新增/修改待审核 4.移除待审核 5.审核通过 6.审核拒绝 7.移除" json:"status"`
	AuditID        int64                     `gorm:"column:audit_id;type:bigint;not null;comment:审核ID" json:"audit_id"`
	RequiredSha256 []byte                    `gorm:"column:required_sha256;type:BINARY(32);not null;comment:必填字段sha256" json:"required_sha256"`
	BaseModel
}

func NewRmsBlackWhiteItem() *RmsBlackWhiteItem {
	return &RmsBlackWhiteItem{}
}
func GetBwItemStatus(status ...[]int) (res []int) {
	for _, v := range status {
		res = append(res, v...)
	}
	return
}

// TableName RmsBlackWhiteItem's table name
func (*RmsBlackWhiteItem) TableName() string {
	return TableNameRmsBlackWhiteItem
}
func (s *RmsBlackWhiteItem) CalculationRemainingDay() int64 {
	return (s.EndDate-s.StartDate)/(60*60*24) + 1
}

type BlackWhiteItemExtendsJson string
type BlackWhiteItemExtends map[string]interface{} //field name => value

func (f BlackWhiteItemExtends) ToJson(excludeFields map[string]bool) BlackWhiteItemExtendsJson {
	// 创建一个不包含需要排除字段的临时映射
	filteredMap := make(map[string]interface{})
	// 排除字段列表

	// 只复制不在排除列表中的字段
	for key, value := range f {
		if !excludeFields[key] {
			filteredMap[key] = value
		}
	}

	json, _ := jsoniter.MarshalToString(filteredMap)
	return BlackWhiteItemExtendsJson(json)
}
func (f BlackWhiteItemExtends) Diff(o BlackWhiteItemExtends) (diff []string) {
	// 遍历当前map
	for key, value := range f {
		// 检查另一个map是否存在相同的key
		if otherValue, exists := o[key]; exists {
			// 直接比较value是否相等，因为都是基础类型
			if value != otherValue {
				diff = append(diff, key)
			}
		}
	}
	return diff
}

func (f BlackWhiteItemExtendsJson) ToFields() (BlackWhiteItemExtends, error) {
	var fields BlackWhiteItemExtends
	err := jsoniter.Unmarshal([]byte(f), &fields)
	return fields, err
}

//func (r *RmsBlackWhiteItem) BatchCreate(ctx context.Context, session *gorm.DB, m []*RmsBlackWhiteItem) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		if innerErr := tx.Model(&RmsBlackWhiteItem{}).Create(m).Error; innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//func (r *RmsBlackWhiteItem) GetParentItems(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (res map[int64]*RmsBlackWhiteItem, err error) {
//	res = make(map[int64]*RmsBlackWhiteItem)
//	var parentIDs []int64
//	var items []*RmsBlackWhiteItem
//	err = er.ConvertDBError(fn(global.DB.Model(&RmsBlackWhiteItem{})).WithContext(ctx).Pluck("parent_id", &parentIDs).Error)
//	if err != nil {
//		return
//	}
//	err = er.ConvertDBError(global.DB.Model(&RmsBlackWhiteItem{}).WithContext(ctx).Where("id in?", parentIDs).Find(&items).Error)
//	if err != nil {
//		return
//	}
//	for _, v := range items {
//		res[v.ID] = v
//	}
//	return
//}
//
//// Create 创建名单记录
//func (r *RmsBlackWhiteItem) Create(ctx context.Context, session *gorm.DB, m *RmsBlackWhiteItem) (id int64, err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		resTx := tx.Model(&RmsBlackWhiteItem{}).Create(m)
//		if resTx.Error != nil {
//			innerErr = er.ConvertDBError(resTx.Error)
//			return innerErr
//		}
//		id = m.ID
//		return nil
//	})
//	return
//}
//
//// Delete 删除名单记录
//func (r *RmsBlackWhiteItem) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(fn(tx.Model(&RmsBlackWhiteItem{})).Delete(&RmsBlackWhiteItem{}).Error)
//	})
//}
//
//// Update 更新名单记录
//func (r *RmsBlackWhiteItem) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(fn(tx.Model(&RmsBlackWhiteItem{})).Updates(data).Error)
//	})
//}
//func (r *RmsBlackWhiteItem) Remove(ctx context.Context, session *gorm.DB, id int64) (err error) {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = r.Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", id)
//		}, map[string]interface{}{"status": BlackWhiteItemStatusRemoveDraft})
//		if innerErr != nil {
//			return innerErr
//		}
//		return nil
//	})
//}
//
//// GetBlackWhiteItems 获取名单列表
//func (r *RmsBlackWhiteItem) GetBlackWhiteItems(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteItem, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsBlackWhiteItem{}))).WithContext(ctx)
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//	}
//	return int64(len(items)), items, err
//}
//
//// First 获取单条名单记录
//func (r *RmsBlackWhiteItem) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsBlackWhiteItem, error) {
//	var item RmsBlackWhiteItem
//	err := er.ConvertDBError(fn(global.DB.Model(&RmsBlackWhiteItem{})).WithContext(ctx).First(&item).Error)
//	return &item, err
//}
//
//// 审核通过
//func (r *RmsBlackWhiteItem) AuditPass(ctx context.Context, session *gorm.DB, bwlID int64) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		//旧数据，需要被替换更新的数据更新成Remove
//		var parentIds []int64
//		innerErr = global.DB.Table(r.TableName()+" as t").Select("t.parent_id").Where("t.bwl_id = ?", bwlID).Where("t.status = ?", BlackWhiteItemStatusPendingApproval).Where("t.parent_id > ?", 0).Pluck("parent_id", &parentIds).Error
//		if innerErr != nil {
//			return innerErr
//		}
//		if len(parentIds) > 0 {
//			innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//				return db.Where("bwl_id = ?", bwlID).Where("id in (?)", parentIds)
//			}, map[string]interface{}{
//				"status": BlackWhiteItemStatusRemoved,
//			})
//			if innerErr != nil {
//				return innerErr
//			}
//		}
//		//新数据，需要被替换更新的数据更新成Pass
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", bwlID).Where("status = ?", BlackWhiteItemStatusPendingApproval)
//		}, map[string]interface{}{
//			"status": BlackWhiteItemStatusPass,
//		})
//		if innerErr != nil {
//			return innerErr
//		}
//		//删除旧数据
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", bwlID).Where("status = ?", BlackWhiteItemStatusRemovePendingApproval)
//		}, map[string]interface{}{
//			"status": BlackWhiteItemStatusRemoved,
//		})
//		if innerErr != nil {
//			return innerErr
//		}
//
//		return nil
//	})
//}
//
//// 审核拒绝
//func (r *RmsBlackWhiteItem) AuditReject(ctx context.Context, session *gorm.DB, bwlID int64) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		//拒绝新增/修改待审核数据
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", bwlID).Where("status = ?", BlackWhiteItemStatusPendingApproval)
//		}, map[string]interface{}{
//			"status": BlackWhiteItemStatusReject,
//		})
//		if innerErr != nil {
//			return innerErr
//		}
//		//拒绝移除待审核数据
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", bwlID).Where("status = ?", BlackWhiteItemStatusRemovePendingApproval)
//		}, map[string]interface{}{
//			"status": BlackWhiteItemStatusPass,
//		})
//		if innerErr != nil {
//			return innerErr
//		}
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", bwlID).Where("status = ?", BlackWhiteItemStatusModified)
//		}, map[string]interface{}{
//			"status": BlackWhiteItemStatusPass,
//		})
//		return innerErr
//	})
//}
//
//func (r *RmsBlackWhiteItem) GroupCount(ctx context.Context, idColumn string, fn func(tx *gorm.DB) *gorm.DB) (res []*GroupIdCount, err error) {
//	err = fn(global.DB.Model(&RmsBlackWhiteItem{})).WithContext(ctx).Select(fmt.Sprintf("%s as id, count(*) as total", idColumn)).Group(idColumn).Find(&res).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsBlackWhiteItem) Count(ctx context.Context, fn func(tx *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = er.ConvertDBError(fn(global.DB.Model(&RmsBlackWhiteItem{})).WithContext(ctx).Count(&total).Error)
//	return
//}
