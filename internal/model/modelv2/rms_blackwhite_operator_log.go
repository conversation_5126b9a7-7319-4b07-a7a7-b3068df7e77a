package modelv2

import (
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
)

const TableNameRmsBlackWhiteOperatorLog = "rms_black_white_operator_log"

// RmsBlackWhiteOperatorLog 黑名单白名单操作日志
type RmsBlackWhiteOperatorLog struct {
	ID       int64                      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	BwlID    int64                      `gorm:"column:bwl_id;type:bigint(11);not null;index;comment:黑白名单ID" json:"bwl_id"`
	AuditID  int64                      `gorm:"column:audit_id;type:bigint(11);not null;index;comment:触发的审核ID" json:"audit_id"`
	Operator string                     `gorm:"column:operator;type:varchar(255);not null;index;comment:操作员" json:"operator"`
	Action   entity.BwOperatorLogAction `gorm:"column:action;type:varchar(100);not null;comment:操作" json:"action"`
	Log      string                     `gorm:"column:log;type:text;comment:日志" json:"log"`
	BaseModel
}

func NewRmsBlackWhiteOperatorLog() *RmsBlackWhiteOperatorLog {
	return &RmsBlackWhiteOperatorLog{}
}

// TableName RmsBlackWhiteOperatorLog's table name
func (*RmsBlackWhiteOperatorLog) TableName() string {
	return TableNameRmsBlackWhiteOperatorLog
}

//
//// Create
//func (r *RmsBlackWhiteOperatorLog) Create(ctx context.Context, session *gorm.DB, model *RmsBlackWhiteOperatorLog) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(tx.Model(&RmsBlackWhiteOperatorLog{}).Create(model).Error)
//	})
//	return
//}
//
//// List
//func (r *RmsBlackWhiteOperatorLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteOperatorLog, err error) {
//	tx := fn(global.DB.Model(&RmsBlackWhiteOperatorLog{})).WithContext(ctx)
//
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//		return
//	}
//	return
//}
