package modelv2

const TableNameRmsEventField = "rms_event_field"

type EventFieldType string

const (
	FieldTypeDate    EventFieldType = "Date"
	FieldTypeDouble  EventFieldType = "Double"
	FieldTypeInteger EventFieldType = "Integer"
	FieldTypeMoney   EventFieldType = "Money"
	FieldTypeString  EventFieldType = "String"
)

// RmsEventField 风险事件字段配置
type RmsEventField struct {
	ID                int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                        // 主键
	CheckPoint        string         `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp,priority:1;comment:归属检查点（编码）" json:"check_point"` // 归属检查点（编码）
	FieldName         string         `gorm:"column:field_name;type:varchar(16);not null;comment:字段名" json:"field_name"`                                      // 字段名
	InnerFieldName    *string        `gorm:"column:inner_field_name;type:varchar(16);comment:内部字段名" json:"inner_field_name"`                               // 内部字段名
	FieldType         EventFieldType `gorm:"column:field_type;type:varchar(16);not null;comment:字段类型，如String" json:"field_type"`                           // 字段类型，如String
	DefaultValue      *string        `gorm:"column:default_value;type:varchar(128);comment:默认值" json:"default_value"`                                        // 默认值
	Required          bool           `gorm:"column:required;type:tinyint(1);not null;comment:是否必填" json:"required"`                                         // 是否必填
	RequiredCondition string         `gorm:"column:required_condition;type:varchar(255);not null;comment:必填条件，依赖于其它字段" json:"required_condition"`    // 必填条件，依赖于其它字段
	Memo              *string        `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                             // 备注
	BaseModel
}

// TableName RmsEventField's table name
func (*RmsEventField) TableName() string {
	return TableNameRmsEventField
}

func NewRmsEventField() *RmsEventField {
	return &RmsEventField{}
}

//
//func (r *RmsEventField) Create(ctx context.Context, session *gorm.DB, model *RmsEventField) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsEventField{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsEventField) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsEventField) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEventField{})).Updates(model).Error)
//	})
//}
//
//func (r *RmsEventField) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEventField{})).Updates(data).Error)
//	})
//}
//
//func (r *RmsEventField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEventField{})).Delete(&RmsEventField{}).Error)
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsEventField) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsEventField, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsEventField{})).WithContext(ctx))
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsEventField) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsEventField{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsEventField) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsEventField, error) {
//	var item RmsEventField
//	var err error
//	err = fn(global.DB.Model(&RmsEventField{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsEventField) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsEventField, error) {
//	var items []*RmsEventField
//	err := global.DB.Model(&RmsEventField{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsEventField) FindByFieldName(ctx context.Context, fieldName string) ([]*RmsEventField, error) {
//	var items []*RmsEventField
//	err := global.DB.Model(&RmsEventField{}).Where("field_name = ?", fieldName).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//// GetIntersectionFields 获取多个检查点中都存在的字段
//func (r *RmsEventField) GetIntersectionFields(ctx context.Context, checkPoints []string) (fields []string, err error) {
//	err = global.DB.Model(&RmsEventField{}).WithContext(ctx).Where("check_point IN (?)", checkPoints).
//		Group("field_name").Having("COUNT(DISTINCT check_point) = ?", len(checkPoints)).Pluck("field_name", &fields).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return
//}
//func (r *RmsEventField) FindByCheckPointAndFieldName(ctx context.Context, checkPoint, fieldName string) (*RmsEventField, error) {
//	var item RmsEventField
//	err := global.DB.Model(&RmsEventField{}).
//		Where("check_point = ? AND field_name = ?", checkPoint, fieldName).
//		WithContext(ctx).
//		First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}
//
//func (r *RmsEventField) CheckFiledExists(c context.Context, checkPoints []string, fields []string) (exists bool, field string, err error) {
//	existsFields, err := r.GetIntersectionFields(c, checkPoints)
//	if err != nil {
//		return
//	}
//	for _, field := range fields {
//		if !slices.Contains(existsFields, field) {
//			return false, field, nil
//		}
//	}
//	return true, "", nil
//}
