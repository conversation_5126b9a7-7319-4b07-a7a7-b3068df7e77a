// Code generated by gen_model_columns.go; DO NOT EDIT.
package modelv2

func (r *RmsEvent) IDField() string { return "id" }
func (r *RmsEvent) CheckPointField() string { return "check_point" }
func (r *RmsEvent) StrField1Field() string { return "str_field1" }
func (r *RmsEvent) StrField2Field() string { return "str_field2" }
func (r *RmsEvent) StrField3Field() string { return "str_field3" }
func (r *RmsEvent) StrField4Field() string { return "str_field4" }
func (r *RmsEvent) StrField5Field() string { return "str_field5" }
func (r *RmsEvent) NumField1Field() string { return "num_field1" }
func (r *RmsEvent) NumField2Field() string { return "num_field2" }
func (r *RmsEvent) EventField() string { return "event" }
func (r *RmsEvent) ResultCodeField() string { return "result_code" }
func (r *RmsEvent) ResultMessageField() string { return "result_message" }
func (r *RmsEvent) RuleNoField() string { return "rule_no" }
func (r *RmsEvent) RulesNotPassedField() string { return "rules_not_passed" }

func (r *BaseDeletedModel) DeletedAtField() string { return "deleted_at" }

func (r *RmsBlackWhiteAudit) IDField() string { return "id" }
func (r *RmsBlackWhiteAudit) BwlIDField() string { return "bwl_id" }
func (r *RmsBlackWhiteAudit) ModeField() string { return "mode" }
func (r *RmsBlackWhiteAudit) StateField() string { return "state" }
func (r *RmsBlackWhiteAudit) SubmitterField() string { return "submitter" }
func (r *RmsBlackWhiteAudit) ApproverField() string { return "approver" }
func (r *RmsBlackWhiteAudit) NotifiedField() string { return "notified" }
func (r *RmsBlackWhiteAudit) ReasonField() string { return "reason" }

func (r *RmsBlackWhiteAuditDetail) IDField() string { return "id" }
func (r *RmsBlackWhiteAuditDetail) BwlIDField() string { return "bwl_id" }
func (r *RmsBlackWhiteAuditDetail) AuditIDField() string { return "audit_id" }
func (r *RmsBlackWhiteAuditDetail) LogField() string { return "log" }

func (r *RmsBlackWhiteItem) IDField() string { return "id" }
func (r *RmsBlackWhiteItem) BwlIDField() string { return "bwl_id" }
func (r *RmsBlackWhiteItem) StartDateField() string { return "start_date" }
func (r *RmsBlackWhiteItem) EndDateField() string { return "end_date" }
func (r *RmsBlackWhiteItem) RemainingDayField() string { return "remaining" }
func (r *RmsBlackWhiteItem) ExtendsField() string { return "extends" }
func (r *RmsBlackWhiteItem) ParentIDField() string { return "parent_id" }
func (r *RmsBlackWhiteItem) StatusField() string { return "status" }
func (r *RmsBlackWhiteItem) AuditIDField() string { return "audit_id" }
func (r *RmsBlackWhiteItem) RequiredSha256Field() string { return "required_sha256" }

func (r *RmsIndicatorVersion) IDField() string { return "id" }
func (r *RmsIndicatorVersion) IndicatorIDField() string { return "indicator_id" }
func (r *RmsIndicatorVersion) ScriptField() string { return "script" }
func (r *RmsIndicatorVersion) TimeWindowTypeField() string { return "time_window_type" }
func (r *RmsIndicatorVersion) StartTimeField() string { return "start_time" }
func (r *RmsIndicatorVersion) EndTimeField() string { return "end_time" }
func (r *RmsIndicatorVersion) RulePreviewField() string { return "rule_preview" }
func (r *RmsIndicatorVersion) TimeWindowValueField() string { return "time_window_value" }
func (r *RmsIndicatorVersion) TimeWindowUnitField() string { return "time_window_unit" }
func (r *RmsIndicatorVersion) TimeWindowColumnIDField() string { return "time_window_column_id" }
func (r *RmsIndicatorVersion) TimeWindowExcludingField() string { return "time_window_excluding" }
func (r *RmsIndicatorVersion) RemarkField() string { return "remark" }
func (r *RmsIndicatorVersion) VersionField() string { return "version" }

func (r *RmsIndicatorVersionHistory) IDField() string { return "id" }
func (r *RmsIndicatorVersionHistory) IndicatorIDField() string { return "indicator_id" }
func (r *RmsIndicatorVersionHistory) VersionIDField() string { return "version_id" }
func (r *RmsIndicatorVersionHistory) VersionField() string { return "version" }
func (r *RmsIndicatorVersionHistory) DescField() string { return "desc" }
func (r *RmsIndicatorVersionHistory) IsDraftField() string { return "is_draft" }
func (r *RmsIndicatorVersionHistory) LastModifiedField() string { return "last_modified" }

func (r *RmsRuleParameterValue) IDField() string { return "id" }
func (r *RmsRuleParameterValue) ParamIDField() string { return "param_id" }
func (r *RmsRuleParameterValue) GroupIDField() string { return "group_id" }
func (r *RmsRuleParameterValue) ParamKeyField() string { return "param_key" }
func (r *RmsRuleParameterValue) ParamValueField() string { return "param_value" }
func (r *RmsRuleParameterValue) DescriptionField() string { return "description" }

func (r *RmsBlackWhiteOperatorLog) IDField() string { return "id" }
func (r *RmsBlackWhiteOperatorLog) BwlIDField() string { return "bwl_id" }
func (r *RmsBlackWhiteOperatorLog) AuditIDField() string { return "audit_id" }
func (r *RmsBlackWhiteOperatorLog) OperatorField() string { return "operator" }
func (r *RmsBlackWhiteOperatorLog) ActionField() string { return "action" }
func (r *RmsBlackWhiteOperatorLog) LogField() string { return "log" }

func (r *PlatConfigProgress) IDField() string { return "id" }
func (r *PlatConfigProgress) CheckPointCodeField() string { return "check_point_code" }
func (r *PlatConfigProgress) ProgressField() string { return "progress" }

func (r *RmsFilterField) IDField() string { return "id" }
func (r *RmsFilterField) FieldNameField() string { return "field_name" }
func (r *RmsFilterField) FieldEnumField() string { return "field_enum" }
func (r *RmsFilterField) CheckPointField() string { return "check_point" }
func (r *RmsFilterField) MemoField() string { return "memo" }

func (r *BaseModel) CreatedAtField() string { return "created_at" }
func (r *BaseModel) UpdatedAtField() string { return "updated_at" }

func (r *RmsEventFieldBinding) IDField() string { return "id" }
func (r *RmsEventFieldBinding) CheckPointField() string { return "check_point" }
func (r *RmsEventFieldBinding) FieldNameField() string { return "field_name" }
func (r *RmsEventFieldBinding) BindTableNameField() string { return "bind_table_name" }
func (r *RmsEventFieldBinding) BindTableIDField() string { return "bind_table_id" }
func (r *RmsEventFieldBinding) CreatedAtField() string { return "created_at" }
func (r *RmsEventFieldBinding) UpdatedAtField() string { return "updated_at" }

func (r *RmsPolicy) IDField() string { return "id" }
func (r *RmsPolicy) PolicyGroupField() string { return "policy_group" }
func (r *RmsPolicy) PolicyNoField() string { return "policy_no" }
func (r *RmsPolicy) PolicyNameField() string { return "policy_name" }
func (r *RmsPolicy) CheckPointField() string { return "check_point" }
func (r *RmsPolicy) FiltersField() string { return "filters" }
func (r *RmsPolicy) PolicyRulesField() string { return "policy_rules" }
func (r *RmsPolicy) StatusField() string { return "status" }
func (r *RmsPolicy) MemoField() string { return "memo" }
func (r *RmsPolicy) SyncedAtField() string { return "synced_at" }

func (r *RmsDataSourceTable) IDField() string { return "id" }
func (r *RmsDataSourceTable) DataSourceIDField() string { return "data_source_id" }
func (r *RmsDataSourceTable) NameField() string { return "name" }
func (r *RmsDataSourceTable) AliasField() string { return "alias" }
func (r *RmsDataSourceTable) DescField() string { return "desc" }
func (r *RmsDataSourceTable) BusinessTypeField() string { return "business_type" }
func (r *RmsDataSourceTable) LastModifiedByField() string { return "last_modified_by" }

func (r *RmsEventField) IDField() string { return "id" }
func (r *RmsEventField) CheckPointField() string { return "check_point" }
func (r *RmsEventField) FieldNameField() string { return "field_name" }
func (r *RmsEventField) InnerFieldNameField() string { return "inner_field_name" }
func (r *RmsEventField) FieldTypeField() string { return "field_type" }
func (r *RmsEventField) DefaultValueField() string { return "default_value" }
func (r *RmsEventField) RequiredField() string { return "required" }
func (r *RmsEventField) RequiredConditionField() string { return "required_condition" }
func (r *RmsEventField) MemoField() string { return "memo" }

func (r *RmsIndicator) IDField() string { return "id" }
func (r *RmsIndicator) IndicatorNameField() string { return "indicator_name" }
func (r *RmsIndicator) DataSourceIDField() string { return "data_source_id" }
func (r *RmsIndicator) IsScriptField() string { return "is_script" }
func (r *RmsIndicator) AccessPointField() string { return "access_point" }
func (r *RmsIndicator) MeasureTypeField() string { return "measure_type" }
func (r *RmsIndicator) TableIDField() string { return "table_id" }
func (r *RmsIndicator) CurrentVersionIDField() string { return "current_version_id" }
func (r *RmsIndicator) CurrentVersionField() string { return "current_version" }
func (r *RmsIndicator) StatusField() string { return "status" }
func (r *RmsIndicator) DraftVersionIDField() string { return "draft_version_id" }

func (r *RmsIndicatorRule) IDField() string { return "id" }
func (r *RmsIndicatorRule) VersionIDField() string { return "version_id" }
func (r *RmsIndicatorRule) HasLeftBracketsField() string { return "has_left_brackets" }
func (r *RmsIndicatorRule) HasRightBracketsField() string { return "has_right_brackets" }
func (r *RmsIndicatorRule) ConnectorField() string { return "connector" }
func (r *RmsIndicatorRule) ColumnIDField() string { return "column_id" }
func (r *RmsIndicatorRule) OperatorField() string { return "operator" }
func (r *RmsIndicatorRule) ValueField() string { return "value" }
func (r *RmsIndicatorRule) ValueTypeField() string { return "value_type" }
func (r *RmsIndicatorRule) ParentIDField() string { return "parent_id" }

func (r *RmsOperatorLog) IDField() string { return "id" }
func (r *RmsOperatorLog) UserIDField() string { return "user_id" }
func (r *RmsOperatorLog) UserNameField() string { return "user_name" }
func (r *RmsOperatorLog) MethodField() string { return "method" }
func (r *RmsOperatorLog) ParamsField() string { return "params" }

func (r *RmsRiskLog) IDField() string { return "id" }
func (r *RmsRiskLog) PartnerIDField() string { return "partner_id" }
func (r *RmsRiskLog) ReqIPField() string { return "req_ip" }
func (r *RmsRiskLog) ProjectNameField() string { return "project_name" }
func (r *RmsRiskLog) Alias_Field() string { return "alias" }
func (r *RmsRiskLog) EventExplainField() string { return "event_explain" }
func (r *RmsRiskLog) OriParamsField() string { return "ori_params" }
func (r *RmsRiskLog) IllegalParamField() string { return "illegal_param" }

func (r *RmsRule) IDField() string { return "id" }
func (r *RmsRule) RuleGroupField() string { return "rule_group" }
func (r *RmsRule) RuleNoField() string { return "rule_no" }
func (r *RmsRule) RuleNameField() string { return "rule_name" }
func (r *RmsRule) CheckPointField() string { return "check_point" }
func (r *RmsRule) RuleTypeField() string { return "rule_type" }
func (r *RmsRule) RuleContentField() string { return "rule_content" }
func (r *RmsRule) ShortCircuitField() string { return "short_circuit" }
func (r *RmsRule) ParamsField() string { return "params" }
func (r *RmsRule) AgendaNameField() string { return "agenda_name" }
func (r *RmsRule) EngineTypeField() string { return "engine_type" }
func (r *RmsRule) DeployMethodField() string { return "deploy_method" }
func (r *RmsRule) EngineInstanceField() string { return "engine_instance" }
func (r *RmsRule) StartTimeField() string { return "start_time" }
func (r *RmsRule) EndTimeField() string { return "end_time" }
func (r *RmsRule) StatusField() string { return "status" }
func (r *RmsRule) MemoField() string { return "memo" }
func (r *RmsRule) SyncedAtField() string { return "synced_at" }
func (r *RmsRule) PriorityField() string { return "priority" }

func (r *RmsRuleParameterGroup) IDField() string { return "id" }
func (r *RmsRuleParameterGroup) CodeField() string { return "code" }
func (r *RmsRuleParameterGroup) ParamIdsField() string { return "param_ids" }
func (r *RmsRuleParameterGroup) CheckPointField() string { return "check_point" }
func (r *RmsRuleParameterGroup) DescriptionField() string { return "description" }
func (r *RmsRuleParameterGroup) DefaultFlagField() string { return "default_flag" }
func (r *RmsRuleParameterGroup) AbleField() string { return "able" }

func (r *RmsDataSource) IDField() string { return "id" }
func (r *RmsDataSource) SourceNameField() string { return "source_name" }
func (r *RmsDataSource) SourceTypeField() string { return "source_type" }
func (r *RmsDataSource) AddressField() string { return "address" }
func (r *RmsDataSource) PortField() string { return "port" }
func (r *RmsDataSource) UsernameField() string { return "username" }
func (r *RmsDataSource) PasswordField() string { return "password" }
func (r *RmsDataSource) DatabaseNameField() string { return "database_name" }
func (r *RmsDataSource) DescriptionField() string { return "description" }
func (r *RmsDataSource) ConnStatusField() string { return "conn_status" }
func (r *RmsDataSource) LastCheckTimeField() string { return "last_check_time" }

func (r *RmsEventStoreCfg) IDField() string { return "id" }
func (r *RmsEventStoreCfg) CheckPointField() string { return "check_point" }
func (r *RmsEventStoreCfg) PersistentField() string { return "persistent" }
func (r *RmsEventStoreCfg) SendToSchedulerField() string { return "send_to_scheduler" }
func (r *RmsEventStoreCfg) SaveToBusinessField() string { return "save_to_business" }
func (r *RmsEventStoreCfg) SendToIntraField() string { return "send_to_intra" }
func (r *RmsEventStoreCfg) StrField1Field() string { return "str_field1" }
func (r *RmsEventStoreCfg) StrField2Field() string { return "str_field2" }
func (r *RmsEventStoreCfg) StrField3Field() string { return "str_field3" }
func (r *RmsEventStoreCfg) StrField4Field() string { return "str_field4" }
func (r *RmsEventStoreCfg) StrField5Field() string { return "str_field5" }
func (r *RmsEventStoreCfg) NumField1Field() string { return "num_field1" }
func (r *RmsEventStoreCfg) NumField2Field() string { return "num_field2" }

func (r *WpHandleLog) HlidField() string { return "hlid" }
func (r *WpHandleLog) PuseridField() string { return "puserid" }
func (r *WpHandleLog) PartnerIDField() string { return "partner_id" }
func (r *WpHandleLog) HandleCodeField() string { return "handle_code" }
func (r *WpHandleLog) HandleTypeField() string { return "handle_type" }
func (r *WpHandleLog) OrderSnField() string { return "order_sn" }
func (r *WpHandleLog) HandleEventsField() string { return "handle_events" }
func (r *WpHandleLog) HandleParamsField() string { return "handle_params" }
func (r *WpHandleLog) HandleStatusField() string { return "handle_status" }
func (r *WpHandleLog) HandleIPField() string { return "handle_ip" }
func (r *WpHandleLog) CreatedAtField() string { return "created_at" }

func (r *RmsBlackWhiteField) IDField() string { return "id" }
func (r *RmsBlackWhiteField) BwlIDField() string { return "bwl_id" }
func (r *RmsBlackWhiteField) FieldNameField() string { return "field_name" }
func (r *RmsBlackWhiteField) RequiredField() string { return "required" }

func (r *RmsBlackWhiteList) IDField() string { return "id" }
func (r *RmsBlackWhiteList) NameField() string { return "name" }
func (r *RmsBlackWhiteList) TypeField() string { return "type" }
func (r *RmsBlackWhiteList) AccessPointsField() string { return "access_points" }
func (r *RmsBlackWhiteList) NoteField() string { return "note" }
func (r *RmsBlackWhiteList) StatusField() string { return "status" }
func (r *RmsBlackWhiteList) StateField() string { return "state" }
func (r *RmsBlackWhiteList) CreatedByField() string { return "created_by" }
func (r *RmsBlackWhiteList) ChangeDataField() string { return "change_data" }

func (r *RmsCheckPoint) IDField() string { return "id" }
func (r *RmsCheckPoint) CodeField() string { return "code" }
func (r *RmsCheckPoint) LabelField() string { return "label" }
func (r *RmsCheckPoint) FilterFieldsField() string { return "filter_fields" }
func (r *RmsCheckPoint) PreActionsField() string { return "pre_actions" }
func (r *RmsCheckPoint) BusinessTypeField() string { return "business_type" }
func (r *RmsCheckPoint) VoucherConfigsField() string { return "voucher_configs" }
func (r *RmsCheckPoint) DefaultPkFieldNameField() string { return "default_pk_field_name" }
func (r *RmsCheckPoint) CheckDuplicateField() string { return "check_duplicate" }
func (r *RmsCheckPoint) MemoField() string { return "memo" }
func (r *RmsCheckPoint) AlwaysRunField() string { return "always_run" }

func (r *RmsIndicatorMeasure) IDField() string { return "id" }
func (r *RmsIndicatorMeasure) VersionIDField() string { return "version_id" }
func (r *RmsIndicatorMeasure) AggTypeField() string { return "agg_type" }
func (r *RmsIndicatorMeasure) MeasureFieldField() string { return "measure_field" }
func (r *RmsIndicatorMeasure) ConditionNameField() string { return "condition_name" }
func (r *RmsIndicatorMeasure) ParentIDField() string { return "parent_id" }

func (r *RmsRuleParameter) IDField() string { return "id" }
func (r *RmsRuleParameter) CodeField() string { return "code" }
func (r *RmsRuleParameter) DescriptionField() string { return "description" }
func (r *RmsRuleParameter) ParamTypeField() string { return "param_type" }
func (r *RmsRuleParameter) CheckPointField() string { return "check_point" }
func (r *RmsRuleParameter) ValueTransferField() string { return "value_transfer" }
func (r *RmsRuleParameter) AccessFlagField() string { return "access_flag" }

func (r *RmsRuleParameterGroupBind) IDField() string { return "id" }
func (r *RmsRuleParameterGroupBind) GroupIDField() string { return "group_id" }
func (r *RmsRuleParameterGroupBind) CheckPointField() string { return "check_point" }
func (r *RmsRuleParameterGroupBind) TargetIDField() string { return "target_id" }
func (r *RmsRuleParameterGroupBind) TargetTypeField() string { return "target_type" }
func (r *RmsRuleParameterGroupBind) AbleField() string { return "able" }

