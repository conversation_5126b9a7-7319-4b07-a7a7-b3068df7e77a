package modelv2

// RmsBlackWhiteList

const (
	BlackWhiteListTypeBlack = 1
	BlackWhiteListTypeWhite = 2
	//banking issuing acquiring

	BlackWhiteListStatusTurnOff = 0 // 关闭
	BlackWhiteListStatusTurnOn  = 1 // 开启

	BlackWhiteListStateDraft           = 0 // 草稿：新增状态下的草稿
	BlackWhiteListStateModifyDraft     = 1 // 修改草稿：审核通过后修改数据后的草稿
	BlackWhiteListStatePendingApproval = 2 // 待审核：开关Status，删除非草稿状态的数据，Submit草稿/修改草稿的记录
	BlackWhiteListStatePass            = 3 // 审核通过：审核通过后，状态为审核通过
	BlackWhiteListStateReject          = 4 // 审核拒绝：审核拒绝后，状态为审核拒绝
	BlackWhiteListStateRemoved         = 5 // 移除：移除后，状态为移除
	BlackWhiteListStateDisable         = 6 // 失效：点击off按钮并且通过就变成当前状态
)

func GetBlackWhiteListScopeBit(scope []int) Bitmap {
	var bit Bitmap = 0
	for _, s := range scope {
		if s == -1 {
			return -1
		}
		bit |= Bitmap(1 << (s - 1))
	}
	return bit
}

func GetBlackWhiteListScope(bit Bitmap) []int {
	var scope []int
	if bit == -1 {
		return []int{-1}
	}
	for i := 0; i < 4; i++ {
		if bit&(1<<i) != 0 {
			scope = append(scope, i+1)
		}
	}
	return scope
}

const TableNameRmsBlackWhiteList = "rms_black_white_list"

// RmsBlackWhiteList 黑名单白名单
type RmsBlackWhiteList struct {
	ID           int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	Name         string `gorm:"column:name;type:varchar(100);not null;comment:名称" json:"name"`
	Type         int    `gorm:"column:type;type:tinyint(1);not null;comment:类型:1-黑名单 2-白名单" json:"type"`
	AccessPoints string `gorm:"column:access_points;type:json;comment:接入点名称" json:"access_points"`
	Note         string `gorm:"column:note;type:text;comment:备注" json:"note"` //最大500个字符
	Status       int    `gorm:"column:status;type:tinyint(1);not null;default:0;comment:状态: 1.Turn On 0.Turn Off" json:"status"`
	State        int    `gorm:"column:state;type:tinyint(1);not null;default:0;comment:状态: 0: 草稿 1: 修改草稿 2: 待审核 3: 审核通过 4: 审核拒绝 5: 移除 6: 失效" json:"state"`
	CreatedBy    string `gorm:"column:created_by;type:varchar(15);not null;comment:最新创建/编辑人" json:"created_by"`
	ChangeData   string `gorm:"column:change_data;type:text;comment:变更数据" json:"change_data"`
	BaseDeletedModel
}

func NewRmsBlackWhiteList() *RmsBlackWhiteList {
	return &RmsBlackWhiteList{}
}

// TableName RmsBlackWhiteList's table name
func (*RmsBlackWhiteList) TableName() string {
	return TableNameRmsBlackWhiteList
}

//
//// List 获取名单列表
//func (r *RmsBlackWhiteList) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteList, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsBlackWhiteList{}))).WithContext(ctx)
//
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//		return
//	}
//	return
//}
//
//// GetBlackList 获取黑名单列表
//func (r *RmsBlackWhiteList) GetBlackList(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteList, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsBlackWhiteList{})).Where("type = ?", BlackWhiteListTypeBlack)).WithContext(ctx)
//
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//		return
//	}
//	return
//}
//
//// GetWhiteList 获取白名单列表
//func (r *RmsBlackWhiteList) GetWhiteList(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteList, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsBlackWhiteList{})).Where("type = ?", BlackWhiteListTypeWhite)).WithContext(ctx)
//
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//		return
//	}
//	return
//}
//
//// First 获取单条名单记录
//func (r *RmsBlackWhiteList) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsBlackWhiteList, error) {
//	var item RmsBlackWhiteList
//	err := er.ConvertDBError(fn(global.DB.Model(&RmsBlackWhiteList{}).WithContext(ctx)).First(&item).Error)
//	return &item, err
//}
//
//// Update 更新名单记录
//func (r *RmsBlackWhiteList) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *RmsBlackWhiteList) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(fn(tx.Model(&RmsBlackWhiteList{})).Updates(data).Error)
//	})
//}
//func (r *RmsBlackWhiteList) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(fn(tx.Model(&RmsBlackWhiteList{})).Updates(data).Error)
//	})
//}
//func (r *RmsBlackWhiteList) UpdateChangeData(ctx context.Context, session *gorm.DB, id int64, submitter string, changeData string) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(tx.Model(&RmsBlackWhiteList{}).Where("id = ?", id).Updates(map[string]interface{}{
//			"change_data": changeData,
//			"state":       BlackWhiteListStateModifyDraft,
//			"created_by":  submitter,
//		}).Error)
//	})
//}
//func (r *RmsBlackWhiteList) Remove(ctx context.Context, session *gorm.DB, id int64, submitter string) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", id)
//		}, map[string]interface{}{
//			"state":       BlackWhiteListStateRemoved,
//			"change_data": "",
//			"created_by":  submitter,
//		})
//		if innerErr != nil {
//			return
//		}
//		innerErr = NewRmsBlackWhiteItem().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", id)
//		}, map[string]interface{}{
//			"status": BlackWhiteItemStatusRemoved,
//		})
//		if innerErr != nil {
//			return
//		}
//		innerErr = NewRmsEventFieldBinding().Remove(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bind_table_name = ?", r.TableName()).Where("bind_table_id = ?", id)
//		})
//		if innerErr != nil {
//			return
//		}
//		//删除现行规则
//		innerErr = entity.BlackRulesCache.RemoveByBwlID(id)
//
//		return
//	})
//}
//
//func (r *RmsBlackWhiteList) AuditPass(ctx context.Context, session *gorm.DB, id int64) (err error) {
//	var bwl *RmsBlackWhiteList
//	var updateData = map[string]interface{}{
//		"state":       BlackWhiteListStatePass,
//		"change_data": "",
//	}
//	bwl, err = NewRmsBlackWhiteList().First(ctx, func(db *gorm.DB) *gorm.DB {
//		return db.Where("id = ?", id)
//	})
//	if err != nil {
//		return err
//	}
//	if len(bwl.ChangeData) > 0 {
//		tmpBwl := json.JsonUnmarshal[RmsBlackWhiteList](bwl.ChangeData)
//		updateData["note"] = tmpBwl.Note
//		updateData["created_by"] = tmpBwl.CreatedBy
//		updateData["access_points"] = tmpBwl.AccessPoints
//	}
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", id)
//		}, updateData)
//		return
//	})
//}
//func (r *RmsBlackWhiteList) AuditReject(ctx context.Context, session *gorm.DB, id int64) (err error) {
//	var updateData = map[string]interface{}{
//		"state":       BlackWhiteListStateReject,
//		"change_data": "",
//	}
//
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = r.Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", id)
//		}, updateData)
//		return
//	})
//}
//
//// Create 创建名单记录
//func (r *RmsBlackWhiteList) Create(ctx context.Context, session *gorm.DB, model *RmsBlackWhiteList) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsBlackWhiteList{}).Create(model)
//		if resTx.Error != nil {
//			return er.ConvertDBError(resTx.Error)
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsBlackWhiteList) Fields(ctx context.Context) (fields entity.Fields, err error) {
//	var fieldsModels []*RmsBlackWhiteField
//	fieldsModels, err = NewRmsBlackWhiteField().GetBlackWhiteFieldsBriefing(ctx, func(db *gorm.DB) *gorm.DB {
//		return db.Where("bwl_id = ?", r.ID)
//	})
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	for _, field := range fieldsModels {
//		fields = append(fields, &entity.Field{
//			Name:       field.FieldName,
//			IsRequired: field.Required,
//		})
//	}
//	return
//}
