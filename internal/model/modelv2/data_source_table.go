package modelv2

const (
	TableNameRmsDataSourceTable = "rms_data_source_table"

	// BusinessType constants
	BusinessTypeBanking   = 1000 // Banking
	BusinessTypeForeign   = 2000 // Foreign
	BusinessTypeAcquiring = 3000 // Acquiring
	BusinessTypeIssuing   = 4000 // Issuing
	BusinessTypeRamp      = 5000 // Ramp

)

// RmsDataSourceTable 数据源表信息表
type RmsDataSourceTable struct {
	ID             int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	DataSourceID   int64  `gorm:"column:data_source_id;not null" json:"data_source_id"`
	Name           string `gorm:"column:name;not null" json:"name"`
	Alias          string `gorm:"column:alias" json:"alias"`
	Desc           string `gorm:"column:desc" json:"desc"`
	BusinessType   int    `gorm:"column:business_type;not null" json:"business_type"`
	LastModifiedBy string `gorm:"column:last_modified_by" json:"last_modified_by"`
	BaseModel
}

func NewRmsDataSourceTable() *RmsDataSourceTable {
	return &RmsDataSourceTable{}
}

// TableName 返回表名
func (RmsDataSourceTable) TableName() string {
	return TableNameRmsDataSourceTable
}

// GetBusinessTypeName 获取业务类型名称
func (t *RmsDataSourceTable) GetBusinessTypeName() string {
	switch t.BusinessType {
	case BusinessTypeBanking:
		return "Banking"
	case BusinessTypeForeign:
		return "Foreign"
	case BusinessTypeAcquiring:
		return "Acquiring"
	case BusinessTypeIssuing:
		return "Issuing"
	case BusinessTypeRamp:
		return "Ramp"
	default:
		return "Unknown"
	}
}
