package modelv2

import "time"

type Bitmap int

var ForeverTime = time.Unix(253402185600, 0).Local()

// RmsBlackWhiteOperatorLog

// RmsBlackWhiteField
const (
	DefaultRequiredFiledCount = 2
	//1-字符串 2-数字 3-日期 4-日期时间
	BlackWhiteFieldTypeString   = 1
	BlackWhiteFieldTypeInt      = 2
	BlackWhiteFieldTypeDate     = 3
	BlackWhiteFieldTypeDateTime = 4

	BusinessFieldStartDate      = "effective_start_date"
	BusinessFieldExpirationDate = "effective_date"
	BusinessFieldRequiredSha256 = "required_sha256"
)
