package modelv2

var BwSkipMarshalFields = map[string]bool{
	BusinessFieldStartDate:      true,
	BusinessFieldExpirationDate: true,
	BusinessFieldRequiredSha256: true,
}

const TableNameRmsBlackWhiteField = "rms_black_white_field"

// RmsBlackWhiteField 黑名单白名单字段列表
type RmsBlackWhiteField struct {
	ID        int64  `gorm:"column:id;type:bigint(11);not null;primaryKey;autoIncrement:true;comment:主键" json:"id"`
	BwlID     int64  `gorm:"column:bwl_id;type:bigint(11);not null;index;comment:黑白名单ID" json:"bwl_id"`
	FieldName string `gorm:"column:field_name;type:varchar(50);not null;index;comment:field_name" json:"field_name"`
	Required  bool   `gorm:"column:required;type:tinyint(1);not null;default:0;comment:是否必填" json:"required"`
	BaseModel
}

func NewRmsBlackWhiteField() *RmsBlackWhiteField {
	return &RmsBlackWhiteField{}
}

// TODO 后期再说
// TableName RmsBlackWhiteField's table name
func (*RmsBlackWhiteField) TableName() string {
	return TableNameRmsBlackWhiteField
}

//func (r *RmsBlackWhiteField) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteField, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsBlackWhiteField{})).WithContext(ctx))
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//		return
//	}
//	return
//}
//
//func (r *RmsBlackWhiteField) BatchCreate(ctx context.Context, session *gorm.DB, m []*RmsBlackWhiteField) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		if innerErr := tx.Model(&RmsBlackWhiteField{}).Create(m).Error; innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//// Delete
//func (r *RmsBlackWhiteField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(fn(tx).Delete(&RmsBlackWhiteField{}).Error)
//	})
//}
//func (r *RmsBlackWhiteField) GetBlackWhiteFieldsBriefing(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (res []*RmsBlackWhiteField, err error) {
//	err = er.ConvertDBError(fn(global.DB.Model(&RmsBlackWhiteField{}).WithContext(ctx)).Find(&res).Error)
//	return
//}
