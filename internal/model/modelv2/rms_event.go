package modelv2

const TableNameRmsEvent = "rms_event"

// RmsEvent 风险事件
type RmsEvent struct {
	ID             int64    `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                            // 主键
	CheckPoint     string   `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp_rcode,priority:1;comment:联系人姓名" json:"check_point"`     // 联系人姓名
	StrField1      *string  `gorm:"column:str_field1;type:varchar(64);comment:重要字符串类型字段1（建议主键），字段名见对应存储配置" json:"str_field1"`       // 重要字符串类型字段1（建议主键），字段名见对应存储配置
	StrField2      *string  `gorm:"column:str_field2;type:varchar(64);comment:重要字符串类型字段2，字段名见对应存储配置" json:"str_field2"`                 // 重要字符串类型字段2，字段名见对应存储配置
	StrField3      *string  `gorm:"column:str_field3;type:varchar(64);comment:重要字符串类型字段3，字段名见对应存储配置" json:"str_field3"`                 // 重要字符串类型字段3，字段名见对应存储配置
	StrField4      *string  `gorm:"column:str_field4;type:varchar(64);comment:重要字符串类型字段4，字段名见对应存储配置" json:"str_field4"`                 // 重要字符串类型字段4，字段名见对应存储配置
	StrField5      *string  `gorm:"column:str_field5;type:varchar(64);comment:重要字符串类型字段5，字段名见对应存储配置" json:"str_field5"`                 // 重要字符串类型字段5，字段名见对应存储配置
	NumField1      *float64 `gorm:"column:num_field1;type:decimal(16,2);comment:重要数值类型字段1(值=原始值*1000)，字段名见对应存储配置" json:"num_field1"` // 重要数值类型字段1(值=原始值*1000)，字段名见对应存储配置
	NumField2      *float64 `gorm:"column:num_field2;type:decimal(16,2);comment:重要数值类型字段2(值=原始值*1000)，字段名见对应存储配置" json:"num_field2"` // 重要数值类型字段2(值=原始值*1000)，字段名见对应存储配置
	Event          string   `gorm:"column:event;type:text;not null;comment:json显示存储的事件完整内容" json:"event"`                                       // json显示存储的事件完整内容
	ResultCode     *string  `gorm:"column:result_code;type:varchar(16);index:idx_ckp_rcode,priority:2;comment:规则结果代码" json:"result_code"`            // 规则结果代码
	ResultMessage  string   `gorm:"column:result_message;type:varchar(64);not null;comment:规则结果内容" json:"result_message"`                            // 规则结果内容
	RuleNo         *string  `gorm:"column:rule_no;type:varchar(32);comment:规则编号" json:"rule_no"`                                                       // 规则编号
	RulesNotPassed *string  `gorm:"column:rules_not_passed;type:varchar(255);comment:未通过规则" json:"rules_not_passed"`                                  // 未通过规则
	BaseModel
}

// TableName RmsEvent's table name
func (*RmsEvent) TableName() string {
	return TableNameRmsEvent
}

func NewRmsEvent() *RmsEvent {
	return &RmsEvent{}
}

//
//func (r *RmsEvent) Create(ctx context.Context, session *gorm.DB, model *RmsEvent) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsEvent{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsEvent) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsEvent) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEvent{})).Updates(model).Error)
//	})
//}
//
//func (r *RmsEvent) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEvent{})).Updates(data).Error)
//	})
//}
//
//func (r *RmsEvent) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEvent{})).Delete(&RmsEvent{}).Error)
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsEvent) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsEvent, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsEvent{})).WithContext(ctx))
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsEvent) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsEvent{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsEvent) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsEvent, error) {
//	var item RmsEvent
//	var err error
//	err = fn(global.DB.Model(&RmsEvent{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsEvent) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsEvent, error) {
//	var items []*RmsEvent
//	err := global.DB.Model(&RmsEvent{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsEvent) FindByResultCode(ctx context.Context, resultCode string) ([]*RmsEvent, error) {
//	var items []*RmsEvent
//	err := global.DB.Model(&RmsEvent{}).Where("result_code = ?", resultCode).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsEvent) FindByCheckPointAndResultCode(ctx context.Context, checkPoint, resultCode string) ([]*RmsEvent, error) {
//	var items []*RmsEvent
//	err := global.DB.Model(&RmsEvent{}).
//		Where("check_point = ? AND result_code = ?", checkPoint, resultCode).
//		WithContext(ctx).
//		Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
