package modelv2

import (
	"encoding/json"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

const TableNameRmsFilterField = "rms_filter_field"

// RmsFilterField 事件过滤字段配置（亦即枚举字段配置）
type RmsFilterField struct {
	ID         int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                        // 主键
	FieldName  string  `gorm:"column:field_name;type:varchar(16);not null;uniqueIndex:idx_fname,priority:1;comment:字段名" json:"field_name"`     // 字段名
	FieldEnum  string  `gorm:"column:field_enum;type:varchar(1024);not null;comment:枚举类型列表的json，各枚举由编码和名称构成" json:"field_enum"` // 枚举类型列表的json，各枚举由编码和名称构成
	CheckPoint string  `gorm:"column:check_point;type:varchar(256);not null;comment:检查点" json:"check_point"`                                   // 检查点
	Memo       *string `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                             // 备注
	BaseModel
}

// TableName RmsFilterField's table name
func (*RmsFilterField) TableName() string {
	return TableNameRmsFilterField
}

func NewRmsFilterField() *RmsFilterField {
	return &RmsFilterField{}
}

//
//func (r *RmsFilterField) Create(ctx context.Context, session *gorm.DB, model *RmsFilterField) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsFilterField{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsFilterField) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsFilterField) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsFilterField{})).Updates(model).Error)
//	})
//}
//
//func (r *RmsFilterField) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsFilterField{})).Updates(data).Error)
//	})
//}
//
//func (r *RmsFilterField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsFilterField{})).Delete(&RmsFilterField{}).Error)
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsFilterField) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsFilterField, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsFilterField{})).WithContext(ctx))
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsFilterField) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsFilterField{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsFilterField) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsFilterField, error) {
//	var item RmsFilterField
//	var err error
//	err = fn(global.DB.Model(&RmsFilterField{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsFilterField) FindByFieldName(ctx context.Context, fieldName string) ([]*RmsFilterField, error) {
//	var items []*RmsFilterField
//	err := global.DB.Model(&RmsFilterField{}).Where("field_name = ?", fieldName).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsFilterField) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsFilterField, error) {
//	var items []*RmsFilterField
//	err := global.DB.Model(&RmsFilterField{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsFilterField) FindByFieldNameAndCheckPoint(ctx context.Context, fieldName, checkPoint string) (*RmsFilterField, error) {
//	var item RmsFilterField
//	err := global.DB.Model(&RmsFilterField{}).Where("field_name = ? AND check_point = ?", fieldName, checkPoint).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}

type RmsFilterFieldEnum []map[string]string

func (m RmsFilterFieldEnum) AllCode() []string {
	li := []string{}
	for _, v := range m {
		li = append(li, v["code"])
	}
	return li
}

func (m *RmsFilterField) GetFieldEnum() (RmsFilterFieldEnum, error) {
	data := RmsFilterFieldEnum{}
	err := json.Unmarshal([]byte(m.FieldEnum), &data)
	return data, er.WSEF(err, zap.String("FieldEnum", m.FieldEnum))
}

func (m *RmsFilterField) SetFieldEnum(data RmsFilterFieldEnum) error {
	f, err := json.Marshal(data)
	if err != nil {
		return er.WSEF(err)
	}
	m.FieldEnum = string(f)
	return nil
}
