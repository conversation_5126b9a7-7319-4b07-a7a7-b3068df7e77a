package modelv2

const TableNameRmsRuleParameter = "rms_rule_parameter"

// RmsRuleParameter 规则参数
type RmsRuleParameter struct {
	ID            int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                                             // 主键
	Code          string  `gorm:"column:code;type:varchar(64);not null;uniqueIndex:uidx_code,priority:1;comment:参数编码" json:"code"`                                                    // 参数编码
	Description   string  `gorm:"column:description;type:varchar(256);not null;comment:参数描述" json:"description"`                                                                      // 参数描述
	ParamType     string  `gorm:"column:param_type;type:varchar(32);comment:参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes" json:"param_type"`                                      // 参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes
	CheckPoint    string  `gorm:"column:check_point;type:varchar(256);not null;comment:检查点(可多个,逗号分割)" json:"check_point"`                                                       // 检查点(可多个,逗号分割)
	ValueTransfer *string `gorm:"column:value_transfer;type:varchar(32);comment:值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer" json:"value_transfer"` // 值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer
	AccessFlag    *string `gorm:"column:access_flag;type:varchar(16);not null;default:apex;comment:允许接口添加值，默认值apex" json:"access_flag"`                                         // 允许接口添加值，默认值apex.旧系统有 bug，无论是否有效数据库中的值都是 apex。经商议从前端去掉。
	BaseModel
}

func NewRmsRuleParameter() *RmsRuleParameter {
	return &RmsRuleParameter{}
}

// TableName RmsRuleParameter's table name
func (*RmsRuleParameter) TableName() string {
	return TableNameRmsRuleParameter
}

//
//func (r *RmsRuleParameter) Create(ctx context.Context, session *gorm.DB, model *RmsRuleParameter) (id int64, err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsRuleParameter{}).Create(model)
//		if resTx.Error != nil {
//			return er.ConvertDBError(resTx.Error)
//		}
//		id = model.ID
//		return nil
//	})
//	return
//}
//
//func (r *RmsRuleParameter) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsRuleParameter) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameter{})).Omit("access_flag").Updates(model).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//func (r *RmsRuleParameter) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameter{})).Delete(&RmsRuleParameter{}).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsRuleParameter) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsRuleParameter, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsRuleParameter{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsRuleParameter) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsRuleParameter{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsRuleParameter) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsRuleParameter, error) {
//	var item RmsRuleParameter
//	var err error
//	err = fn(global.DB.Model(&RmsRuleParameter{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsRuleParameter) FindByCode(ctx context.Context, code string) (*RmsRuleParameter, error) {
//	var item RmsRuleParameter
//	err := global.DB.Model(&RmsRuleParameter{}).Where("code = ?", code).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}
//
//func (r *RmsRuleParameter) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsRuleParameter, error) {
//	var items []*RmsRuleParameter
//	err := global.DB.Model(&RmsRuleParameter{}).Where("check_point LIKE ?", "%"+checkPoint+"%").WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
