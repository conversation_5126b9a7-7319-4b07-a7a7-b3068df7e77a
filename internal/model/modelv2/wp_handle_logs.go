package modelv2

import (
	"time"
)

const TableNameWpHandleLog = "wp_handle_logs"

// WpHandleLog 用户操作日志
type WpHandleLog struct {
	EmptyModel
	Hlid         int32      `gorm:"column:hlid;type:int;primaryKey;autoIncrement:true" json:"hlid"`
	Puserid      *string    `gorm:"column:puserid;type:varchar(32);index:IDX_CODE,priority:1;index:IDX_PUID_PARTNERID,priority:1;index:IDX_STATUS,priority:1;comment:操作员ID" json:"puserid"`                                                                                                                             // 操作员ID
	PartnerID    *string    `gorm:"column:partner_id;type:varchar(48);index:IDX_CODE,priority:2;index:IDX_PUID_PARTNERID,priority:2;index:IDX_STATUS,priority:2;comment:商户ID" json:"partner_id"`                                                                                                                         // 商户ID
	HandleCode   string     `gorm:"column:handle_code;type:varchar(48);not null;index:IDX_CODE,priority:3;comment:日志类型: 前台商户=TYPE_PARTNER_HANDLE，后台管理=TYPE_SYSADMIN_HANDLE，网关日志=TYPE_GATEWAY_HANDLE，计划任务日志=TYPE_TASK_HANDLE" json:"handle_code"`                                                     // 日志类型: 前台商户=TYPE_PARTNER_HANDLE，后台管理=TYPE_SYSADMIN_HANDLE，网关日志=TYPE_GATEWAY_HANDLE，计划任务日志=TYPE_TASK_HANDLE
	HandleType   string     `gorm:"column:handle_type;type:varchar(48);not null;index:IDX_CODE,priority:4;comment:日志类型编码 PARTNER_LOGIN = 商户登陆 PARTNER_PAYOUT = 商户提现  PARTNER_PAYIN = 商户充值  PARTNER_SAFETY = 商户安全设置 SYSADMIN_LOG 后台设置日志 TASK_HANDLE_LOG=定时任务设置日志" json:"handle_type"` // 日志类型编码 PARTNER_LOGIN = 商户登陆 PARTNER_PAYOUT = 商户提现  PARTNER_PAYIN = 商户充值  PARTNER_SAFETY = 商户安全设置 SYSADMIN_LOG 后台设置日志 TASK_HANDLE_LOG=定时任务设置日志
	OrderSn      *string    `gorm:"column:order_sn;type:varchar(32);comment:订单号：针对订单流程" json:"order_sn"`                                                                                                                                                                                                          // 订单号：针对订单流程
	HandleEvents *string    `gorm:"column:handle_events;type:varchar(240);comment:操作事件" json:"handle_events"`                                                                                                                                                                                                          // 操作事件
	HandleParams *string    `gorm:"column:handle_params;type:text;comment:动作参数[内部使用]" json:"handle_params"`                                                                                                                                                                                                        // 动作参数[内部使用]
	HandleStatus int32      `gorm:"column:handle_status;type:int;not null;index:IDX_STATUS,priority:3;comment:事件状态 0=失败 1=成功" json:"handle_status"`                                                                                                                                                                // 事件状态 0=失败 1=成功
	HandleIP     string     `gorm:"column:handle_ip;type:varchar(128);not null;comment:操作IP" json:"handle_ip"`                                                                                                                                                                                                           // 操作IP
	CreatedAt    *time.Time `gorm:"column:created_at;type:datetime;index:IDX_DATELINE,priority:1;comment:创建时间" json:"created_at"`                                                                                                                                                                                      // 创建时间
}

func NewWpHandleLog() *WpHandleLog {
	return &WpHandleLog{}
}

// TableName WpHandleLog's table name
func (*WpHandleLog) TableName() string {
	return TableNameWpHandleLog
}

//
//func (r *WpHandleLog) Create(ctx context.Context, session *gorm.DB, model *WpHandleLog) (id int32, err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&WpHandleLog{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		id = model.Hlid
//		return nil
//	})
//	return
//}
//func (r *WpHandleLog) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *WpHandleLog) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&WpHandleLog{})).Updates(model).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//func (r *WpHandleLog) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&WpHandleLog{})).Delete(&WpHandleLog{}).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//
//func (r *WpHandleLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*WpHandleLog, err error) {
//	tx := fn(global.DB.Model(&WpHandleLog{})).WithContext(ctx)
//
//	// 获取列表数据
//	err = r.DoDefaultFilter(defaultFilter, tx).Order("hlid desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *WpHandleLog) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&WpHandleLog{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//func (r *WpHandleLog) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*WpHandleLog, error) {
//	var item WpHandleLog
//	var err error
//	err = fn(global.DB.Model(&RmsBlackWhiteList{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
