package modelv2

import (
	"encoding/json"
	"fmt"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
)

const TableNameRmsPolicy = "rms_policy"
const PolicyStatusOnline = "Online"
const PolicyStatusOffline = "Offline"

// RmsPolicy 风控规则策略（即规则组）
type RmsPolicy struct {
	ID           int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                                                                    // 主键
	PolicyGroup  string     `gorm:"column:policy_group;type:varchar(32);not null;index:idx_pg,priority:1;default:CMF;comment:策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup" json:"policy_group"` // 策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup
	PolicyNo     string     `gorm:"column:policy_no;type:varchar(32);not null;comment:策略编号" json:"policy_no"`                                                                                                  // 策略编号
	PolicyName   *string    `gorm:"column:policy_name;type:varchar(64);comment:规则组名称" json:"policy_name"`                                                                                                     // 规则组名称
	CheckPoint   string     `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp,priority:1;comment:检查点" json:"check_point"`                                                                       // 检查点
	Filters      *string    `gorm:"column:filters;type:varchar(1024);comment:策略匹配的过滤条件，需事先设置检查点的过滤字段" json:"filters"`                                                                        // 策略匹配的过滤条件，需事先设置检查点的过滤字段
	WhiteListIDs string     `gorm:"column:white_list_ids;type:varchar(225);comment:白名单id列表json" json:"white_list_ids"`
	PolicyRules  string     `gorm:"column:policy_rules;type:varchar(1024);not null;comment:策略中需要执行的规则id列表json" json:"policy_rules"` // 策略中需要执行的规则id列表json
	Status       string     `gorm:"column:status;type:varchar(16);not null;comment:状态，Online/Offline" json:"status"`                          // 状态，Online/Offline
	Memo         *string    `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                      // 备注
	SyncedAt     *time.Time `gorm:"column:synced_at;type:datetime;comment:同步时间" json:"synced_at"`                                           // 同步时间
	BaseModel
}

// TableName RmsPolicy's table name
func (*RmsPolicy) TableName() string {
	return TableNameRmsPolicy
}

type RmsPolicyFilters map[string][]string

func NewRmsPolicy() *RmsPolicy {
	return &RmsPolicy{}
}

func (r *RmsPolicy) ToCacheData() (res map[string]interface{}) {
	res = map[string]interface{}{
		r.IDField():          r.ID,
		r.PolicyGroupField(): r.PolicyGroup,
		r.PolicyNoField():    r.PolicyNo,
		r.PolicyNameField():  "",
		r.CheckPointField():  r.CheckPoint,
		r.PolicyRulesField(): r.PolicyRules,
		r.StatusField():      r.Status,
		r.SyncedAtField():    time.Time{},
		r.FiltersField():     "",
		r.MemoField():        "",
	}

	if r.PolicyName != nil {
		res["policy_name"] = *r.PolicyName
	}
	if r.SyncedAt != nil {
		res["synced_at"] = *r.SyncedAt
	}
	if r.Filters != nil {
		res["filters"] = *r.Filters
	}
	if r.Memo != nil {
		res["memo"] = *r.Memo
	}

	return

}

//
//func (r *RmsPolicy) Create(ctx context.Context, session *gorm.DB, model *RmsPolicy) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsPolicy{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsPolicy) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsPolicy) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsPolicy{})).Updates(model).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//func (r *RmsPolicy) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsPolicy{})).Updates(data).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//func (r *RmsPolicy) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsPolicy{})).Delete(&RmsPolicy{}).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsPolicy) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsPolicy, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsPolicy{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsPolicy) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsPolicy{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsPolicy) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsPolicy, error) {
//	var item RmsPolicy
//	var err error
//	err = fn(global.DB.Model(&RmsPolicy{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsPolicy) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsPolicy, error) {
//	var items []*RmsPolicy
//	err := global.DB.Model(&RmsPolicy{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsPolicy) FindByPolicyNo(ctx context.Context, policyNo string) (*RmsPolicy, error) {
//	var item RmsPolicy
//	err := global.DB.Model(&RmsPolicy{}).Where("policy_no = ?", policyNo).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}

func (m *RmsPolicy) GetFilters() (RmsPolicyFilters, error) {
	data := RmsPolicyFilters{}
	if m.Filters == nil {
		return data, nil
	}
	err := json.Unmarshal([]byte(*m.Filters), &data)
	if err != nil {
		return nil, er.WSEF(err, zap.String("Filters", fmt.Sprintln(m.Filters)))
	}
	return data, nil
}

func (m *RmsPolicy) SetFilters(data RmsPolicyFilters) error {
	v, err := json.Marshal(data)
	if err != nil {
		return er.WSEF(err)
	}
	f := string(v)
	m.Filters = &f
	return nil
}
