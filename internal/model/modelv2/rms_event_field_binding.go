package modelv2

import (
	"time"
)

const TableNameRmsEventFieldBinding = "rms_event_field_binding"

// RmsEventFieldBinding 风险字段绑定情况
type RmsEventFieldBinding struct {
	ID            int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                         // 主键
	CheckPoint    string     `gorm:"column:check_point;type:varchar(255);not null;index:idx_ckp,priority:1;comment:归属检查点（编码）" json:"check_point"` // 归属检查点（编码）
	FieldName     string     `gorm:"column:field_name;type:varchar(255);not null;comment:字段名" json:"field_name"`                                      // 字段名
	BindTableName string     `gorm:"column:bind_table_name;type:varchar(50);not null;comment:绑定记录表名" json:"bind_table_name"`                       // 绑定记录表名
	BindTableID   int64      `gorm:"column:bind_table_id;type:bigint;not null;comment:绑定记录表记录ID" json:"bind_table_id"`                            // 绑定记录表记录ID
	CreatedAt     *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`              // 创建时间
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`              // 更新时间
	EmptyModel
}

// TableName RmsEventField's table name
func (r *RmsEventFieldBinding) TableName() string {
	return TableNameRmsEventFieldBinding
}
func NewRmsEventFieldBinding() *RmsEventFieldBinding {
	return &RmsEventFieldBinding{}
}

//
//func (r *RmsEventFieldBinding) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsEventFieldBinding, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsEventFieldBinding{})).Where("type = ?", BlackWhiteListTypeBlack)).WithContext(ctx)
//
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return
//	}
//	if len(withTotal) > 0 && withTotal[0] {
//		err = er.ConvertDBError(tx.Count(&total).Error)
//		return
//	}
//	return
//}
//
//func (r *RmsEventFieldBinding) Remove(ctx context.Context, session *gorm.DB, fn func(db2 *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsEventFieldBinding{})).WithContext(ctx).Delete(&RmsEventFieldBinding{}).Error
//		if innerErr != nil {
//			return
//		}
//		return
//	})
//}
//
//// Create 创建记录
//func (r *RmsEventFieldBinding) Create(ctx context.Context, session *gorm.DB, model *RmsEventFieldBinding) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsEventFieldBinding{}).Create(model)
//		if resTx.Error != nil {
//			return er.ConvertDBError(resTx.Error)
//		}
//		return nil
//	})
//	return
//}
//
//// BatchCreate 批量创建记录
//func (r *RmsEventFieldBinding) BatchCreate(ctx context.Context, session *gorm.DB, models []*RmsEventFieldBinding) (err error) {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(tx.Model(&RmsEventFieldBinding{}).Create(models).Error)
//	})
//}
//
//func (r *RmsEventFieldBinding) Bound(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (bound bool, err error) {
//	var total int64
//	err = fn(global.DB.Model(&RmsEventFieldBinding{})).WithContext(ctx).Count(&total).Error
//	return total > 0, er.ConvertDBError(err)
//}
