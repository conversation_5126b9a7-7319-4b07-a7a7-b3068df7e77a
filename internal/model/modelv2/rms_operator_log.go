package modelv2

const TableNameRmsOperatorLog = "rms_operator_log"

// RmsOperatorLog 风控操作日志
type RmsOperatorLog struct {
	ID       int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                        // 主键
	UserID   string  `gorm:"column:user_id;type:varchar(32);not null;index:idx_uid,priority:1;comment:操作员id" json:"user_id"` // 操作员id
	UserName *string `gorm:"column:user_name;type:varchar(16);comment:操作员姓名" json:"user_name"`                             // 操作员姓名
	Method   *string `gorm:"column:method;type:varchar(128);index:idx_method,priority:1;comment:动作" json:"method"`            // 动作
	Params   string  `gorm:"column:params;type:varchar(1024);not null;comment:参数" json:"params"`                              // 参数
	BaseModel
}

// TableName RmsOperatorLog's table name
func (*RmsOperatorLog) TableName() string {
	return TableNameRmsOperatorLog
}

func NewRmsOperatorLog() *RmsOperatorLog {
	return &RmsOperatorLog{}
}

//
//func (r *RmsOperatorLog) Create(ctx context.Context, session *gorm.DB, model *RmsOperatorLog) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsOperatorLog{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsOperatorLog) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsOperatorLog) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsOperatorLog{})).Updates(model).Error)
//	})
//}
//
//func (r *RmsOperatorLog) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsOperatorLog{})).Updates(data).Error)
//	})
//}
//
//func (r *RmsOperatorLog) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsOperatorLog{})).Delete(&RmsOperatorLog{}).Error)
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsOperatorLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsOperatorLog, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsOperatorLog{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsOperatorLog) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsOperatorLog{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsOperatorLog) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsOperatorLog, error) {
//	var item RmsOperatorLog
//	var err error
//	err = fn(global.DB.Model(&RmsOperatorLog{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsOperatorLog) FindByUserID(ctx context.Context, userID string) ([]*RmsOperatorLog, error) {
//	var items []*RmsOperatorLog
//	err := global.DB.Model(&RmsOperatorLog{}).Where("user_id = ?", userID).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsOperatorLog) FindByMethod(ctx context.Context, method string) ([]*RmsOperatorLog, error) {
//	var items []*RmsOperatorLog
//	err := global.DB.Model(&RmsOperatorLog{}).Where("method = ?", method).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
