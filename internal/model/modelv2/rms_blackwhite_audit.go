package modelv2

const TableNameRmsBlackWhiteAudit = "rms_black_white_audit"

// RmsBlackWhiteAudit
const (
	BlackWhiteAuditModeCreate  = 1 // 创建操作
	BlackWhiteAuditModeEdit    = 2 // 编辑操作, 包括删除名单项
	BlackWhiteAuditModeDelete  = 3 // 删除名单操作
	BlackWhiteAuditModeTurnOn  = 4 // 开启状态操作
	BlackWhiteAuditModeTurnOff = 5 // 关闭状态操作

	BlackWhiteAuditStatePending = 1 // 待审核
	BlackWhiteAuditStatePass    = 2 // 审核通过
	BlackWhiteAuditStateReject  = 3 // 审核拒绝
)

// RmsBlackWhiteAudit 黑名单白名单审核
type RmsBlackWhiteAudit struct {
	ID        int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	BwlID     int64  `gorm:"column:bwl_id;type:bigint(11);not null;index;comment:黑白名单ID" json:"bwl_id"`
	Mode      int    `gorm:"column:mode;type:tinyint(1);not null;comment:审核模式:1.创建操作 2.编辑操作 3.删除名单操作 4.开启状态操作 5.关闭状态操作" json:"mode"`
	State     int    `gorm:"column:state;type:tinyint(1);not null;comment:审核状态:1.待审核 2.审核通过 3.审核拒绝" json:"state"`
	Submitter string `gorm:"column:submitter;type:varchar(255);not null;comment:提交人" json:"submitter"`
	Approver  string `gorm:"column:approver;type:varchar(255);not null;comment:审核人" json:"approver"`
	Notified  bool   `gorm:"column:notified;type:tinyint(0);not null;default(0);comment:是否通知:1.是 0.否" json:"is_notify"`
	Reason    string `gorm:"column:reason;type:text;comment:拒绝理由" json:"reason"`
	BaseModel
}

func NewRmsBlackWhiteAudit() *RmsBlackWhiteAudit {
	return &RmsBlackWhiteAudit{}
}

// TableName RmsBlackWhiteAudit's table name
func (*RmsBlackWhiteAudit) TableName() string {
	return TableNameRmsBlackWhiteAudit
}

//
//// 创建审核任务
//func (r *RmsBlackWhiteAudit) Create(ctx context.Context, session *gorm.DB, model *RmsBlackWhiteAudit) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = er.ConvertDBError(tx.WithContext(ctx).Create(model).Error)
//		if innerErr != nil {
//			return innerErr
//		}
//		innerErr = er.ConvertDBError(NewRmsBlackWhiteList().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", model.BwlID)
//		}, map[string]interface{}{"state": BlackWhiteListStatePendingApproval}))
//		if innerErr != nil {
//			return innerErr
//		}
//		innerErr = er.ConvertDBError(NewRmsBlackWhiteItem().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", model.BwlID).Where("status = ?", BlackWhiteItemStatusDraft)
//		}, map[string]interface{}{"status": BlackWhiteItemStatusPendingApproval, "audit_id": model.ID}))
//		if innerErr != nil {
//			return innerErr
//		}
//		innerErr = er.ConvertDBError(NewRmsBlackWhiteItem().Updates(ctx, tx, func(db *gorm.DB) *gorm.DB {
//			return db.Where("bwl_id = ?", model.BwlID).Where("status = ?", BlackWhiteItemStatusRemoveDraft)
//		}, map[string]interface{}{"status": BlackWhiteItemStatusRemovePendingApproval, "audit_id": model.ID}))
//		if innerErr != nil {
//			return innerErr
//		}
//		return
//	})
//}
//func (r *RmsBlackWhiteAudit) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsBlackWhiteAudit, error) {
//	var item RmsBlackWhiteAudit
//	err := er.ConvertDBError(fn(global.DB.Model(&RmsBlackWhiteAudit{})).WithContext(ctx).First(&item).Error)
//	return &item, err
//}
//func (r *RmsBlackWhiteAudit) Updates(ctx context.Context, session *gorm.DB, fn func(tx *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return er.ConvertDBError(fn(tx.Model(&RmsBlackWhiteAudit{})).Updates(data).Error)
//	})
//}
//func (r *RmsBlackWhiteAudit) Audit(ctx context.Context, session *gorm.DB, auditModel *RmsBlackWhiteAudit, state int, approver string, reason string) error {
//
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = r.Updates(ctx, tx, func(tx *gorm.DB) *gorm.DB {
//			return tx.Where("id = ?", auditModel.ID)
//		}, map[string]interface{}{
//			"state":    state,
//			"reason":   reason,
//			"approver": approver,
//		})
//		if innerErr != nil {
//			return innerErr
//		}
//		switch auditModel.Mode {
//		case BlackWhiteAuditModeDelete:
//			return auditModel.auditRemoveList(ctx, tx, state)
//		case BlackWhiteAuditModeTurnOn:
//			return auditModel.auditChangeStatus(ctx, tx, BlackWhiteListStatusTurnOn, state)
//		case BlackWhiteAuditModeTurnOff:
//			return auditModel.auditChangeStatus(ctx, tx, BlackWhiteListStatusTurnOff, state)
//		case BlackWhiteAuditModeCreate:
//			return auditModel.auditSave(ctx, tx, state)
//		case BlackWhiteAuditModeEdit:
//			return auditModel.auditSave(ctx, tx, state)
//		}
//		return nil
//	})
//
//}
//func (r *RmsBlackWhiteAudit) auditRemoveList(ctx context.Context, session *gorm.DB, state int) error {
//	switch state {
//	case BlackWhiteAuditStatePass:
//		return NewRmsBlackWhiteList().Remove(ctx, session, r.BwlID, r.Submitter)
//	case BlackWhiteAuditStateReject:
//		return NewRmsBlackWhiteList().Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", r.BwlID)
//		}, map[string]interface{}{
//			"state":       BlackWhiteListStateReject,
//			"change_data": "",
//		})
//	}
//	return nil
//
//}
//func (r *RmsBlackWhiteAudit) auditChangeStatus(ctx context.Context, session *gorm.DB, auditModel *modelv2.RmsBlackWhiteAudit, status int, state int) error {
//
//	switch state {
//	case BlackWhiteAuditStatePass:
//		//删除现行规则
//		entity.BlackRulesCache.SetStatus(auditModel.BwlID, status == BlackWhiteListStatusTurnOn)
//		return NewRmsBlackWhiteList().Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", auditModel.BwlID)
//		}, map[string]interface{}{
//			"state":       BlackWhiteListStatePass,
//			"status":      status,
//			"change_data": "",
//		})
//	case BlackWhiteAuditStateReject:
//		return NewRmsBlackWhiteList().Updates(ctx, session, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", auditModel.BwlID)
//		}, map[string]interface{}{
//			"state":       BlackWhiteListStateReject,
//			"change_data": "",
//		})
//	}
//	return nil
//}
//
//func (r *RmsBlackWhiteAudit) auditSave(ctx context.Context, session *gorm.DB, auditModel *modelv2.RmsBlackWhiteAudit, state int) (err error) {
//	switch state {
//	case BlackWhiteAuditStatePass:
//		err = NewRmsBlackWhiteList().AuditPass(ctx, session, auditModel.BwlID)
//		if err != nil {
//			return err
//		}
//		return NewRmsBlackWhiteItem().AuditPass(ctx, session, auditModel.BwlID)
//	case BlackWhiteAuditStateReject:
//		err = NewRmsBlackWhiteList().AuditReject(ctx, session, auditModel.BwlID)
//		if err != nil {
//			return err
//		}
//		return NewRmsBlackWhiteItem().AuditReject(ctx, session, auditModel.BwlID)
//	}
//	return nil
//}
//
//func (r *RmsBlackWhiteAudit) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsBlackWhiteAudit, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsBlackWhiteAudit{})).WithContext(ctx))
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
