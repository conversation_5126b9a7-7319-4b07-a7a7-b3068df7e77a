package modelv2

import "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/encrypt"

const TableNameRmsDataSource = "rms_data_source"

const (
	ConnStatusConnected    = "Connected"
	ConnStatusDisconnected = "Disconnected"
)

// RmsDataSource 数据源配置
type RmsDataSource struct {
	ID           int64             `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                     // 主键
	SourceName   string            `gorm:"column:source_name;type:varchar(50);not null;uniqueIndex;comment:数据源名称" json:"source_name"` // 数据源名称
	SourceType   string            `gorm:"column:source_type;type:varchar(50);not null;comment:数据源类型" json:"source_type"`             // 数据源类型
	Address      string            `gorm:"column:address;type:varchar(255);not null;comment:服务器地址" json:"address"`                    // 服务器地址
	Port         int               `gorm:"column:port;type:int;not null;comment:端口" json:"port"`                                         // 端口
	Username     string            `gorm:"column:username;type:varchar(50);not null;comment:用户名" json:"username"`                       // 用户名
	Password     encrypt.Encrypted `gorm:"column:password;type:varchar(255);not null;comment:密码" json:"password"`                        // 密码
	DatabaseName string            `gorm:"column:database_name;type:varchar(50);not null;comment:数据库名称" json:"database_name"`         // 数据库名称
	Description  *string           `gorm:"column:description;type:varchar(255);comment:描述" json:"description"`                           // 描述
	ConnStatus   string            `gorm:"column:conn_status;type:varchar(20);default:Disconnected;comment:连接状态" json:"conn_status"`   // 连接状态
	//失败理由
	FailReason    string `gorm:"fail_reason;type:text;comment:失败理由" json:"failReason"`
	LastCheckTime int64  `gorm:"column:last_check_time;type:bigint;comment:最后检查时间" json:"last_check_time"` // 最后检查时间
	BaseModel
}

// TableName RmsDataSource's table name
func (*RmsDataSource) TableName() string {
	return TableNameRmsDataSource
}

// NewRmsDataSource creates a new RmsDataSource instance
func NewRmsDataSource() *RmsDataSource {
	return &RmsDataSource{}
}

// 密码加密
func (ds *RmsDataSource) PasswordEncrypt() (pwd string) {
	return string(encrypt.Encrypt(encrypt.Raw(ds.Password)))
}
