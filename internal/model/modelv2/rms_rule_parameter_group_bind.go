package modelv2

const TableNameRmsRuleParameterGroupBind = "rms_rule_parameter_group_bind"

// RmsRuleParameterGroupBind 规则参数组绑定关系
type RmsRuleParameterGroupBind struct {
	ID         int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                  // 主键
	GroupID    int64   `gorm:"column:group_id;type:bigint;not null;index:idx_tid_gid,priority:2;comment:参数组id" json:"group_id"`          // 参数组id
	CheckPoint *string `gorm:"column:check_point;type:varchar(256);comment:检查点" json:"check_point"`                                      // 检查点
	TargetID   string  `gorm:"column:target_id;type:varchar(64);not null;index:idx_tid_gid,priority:1;comment:绑定目标id" json:"target_id"` // 绑定目标id
	TargetType *string `gorm:"column:target_type;type:varchar(32);comment:绑定目标类型，MERCHANT-商户，CHANNEL-渠道" json:"target_type"`      // 绑定目标类型，MERCHANT-商户，CHANNEL-渠道
	Able       int32   `gorm:"column:able;type:int;not null;comment:状态，1-有效，0-失效" json:"able"`                                        // 状态，1-有效，0-失效
	BaseModel
}

func NewRmsRuleParameterGroupBind() *RmsRuleParameterGroupBind {
	return &RmsRuleParameterGroupBind{}
}

// TableName RmsRuleParameterGroupBind's table name
func (*RmsRuleParameterGroupBind) TableName() string {
	return TableNameRmsRuleParameterGroupBind
}

//
//func (r *RmsRuleParameterGroupBind) Create(ctx context.Context, session *gorm.DB, models ...*RmsRuleParameterGroupBind) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsRuleParameterGroupBind{}).Create(models)
//		if resTx.Error != nil {
//			return er.ConvertDBError(resTx.Error)
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsRuleParameterGroupBind) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsRuleParameterGroupBind) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsRuleParameterGroupBind{})).Updates(model).Error)
//	})
//}
//
//func (r *RmsRuleParameterGroupBind) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//
//		return er.ConvertDBError(fn(tx.Model(&RmsRuleParameterGroupBind{})).Delete(&RmsRuleParameterGroupBind{}).Error)
//
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsRuleParameterGroupBind) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsRuleParameterGroupBind, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsRuleParameterGroupBind{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsRuleParameterGroupBind) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsRuleParameterGroupBind{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsRuleParameterGroupBind) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsRuleParameterGroupBind, error) {
//	var item RmsRuleParameterGroupBind
//	var err error
//	err = fn(global.DB.Model(&RmsRuleParameterGroupBind{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsRuleParameterGroupBind) GetByGroupID(ctx context.Context, groupID int64) (*RmsRuleParameterGroupBind, error) {
//	var item RmsRuleParameterGroupBind
//	err := global.DB.Model(&RmsRuleParameterGroupBind{}).Where("group_id = ?", groupID).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}
//
//func (r *RmsRuleParameterGroupBind) ListByTargetType(ctx context.Context, targetType string) ([]*RmsRuleParameterGroupBind, error) {
//	var items []*RmsRuleParameterGroupBind
//	err := global.DB.Model(&RmsRuleParameterGroupBind{}).Where("target_type = ?", targetType).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsRuleParameterGroupBind) ListByTargetID(ctx context.Context, targetID string) ([]*RmsRuleParameterGroupBind, error) {
//	var items []*RmsRuleParameterGroupBind
//	err := global.DB.Model(&RmsRuleParameterGroupBind{}).Where("target_id = ?", targetID).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
