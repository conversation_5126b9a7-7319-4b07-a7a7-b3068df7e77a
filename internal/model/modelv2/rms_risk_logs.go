package modelv2

const TableNameRmsRiskLog = "rms_risk_logs"

// RmsRiskLog 风控日志
type RmsRiskLog struct {
	ID           int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                            // 主键
	PartnerID    string  `gorm:"column:partner_id;type:varchar(16);not null;index:idx_pid,priority:1;comment:商户号" json:"partner_id"` // 商户号
	ReqIP        string  `gorm:"column:req_ip;type:varchar(32);not null;comment:请求IP" json:"req_ip"`                                  // 请求IP
	ProjectName  string  `gorm:"column:project_name;type:varchar(32);not null;comment:工程名" json:"project_name"`                      // 工程名
	Alias_       string  `gorm:"column:alias;type:varchar(16);not null;comment:拦截别名 sql,xss,signature" json:"alias"`                // 拦截别名 sql,xss,signature
	EventExplain *string `gorm:"column:event_explain;type:varchar(64);comment:事件说明" json:"event_explain"`                           // 事件说明
	OriParams    string  `gorm:"column:ori_params;type:varchar(256);not null;comment:源参数" json:"ori_params"`                         // 源参数
	IllegalParam int32   `gorm:"column:illegal_param;type:int;not null;comment:非法参数. 1-是，0-否" json:"illegal_param"`               // 非法参数. 1-是，0-否
	BaseModel
}

// NewRmsRiskLog creates a new RmsRiskLog instance
func NewRmsRiskLog() *RmsRiskLog {
	return &RmsRiskLog{}
}

// TableName RmsRiskLog's table name
func (*RmsRiskLog) TableName() string {
	return TableNameRmsRiskLog
}

//
//func (r *RmsRiskLog) Create(ctx context.Context, session *gorm.DB, model *RmsRiskLog) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsRiskLog{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsRiskLog) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsRiskLog) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsRiskLog{})).Updates(model).Error)
//	})
//}
//
//func (r *RmsRiskLog) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsRiskLog{})).Delete(&RmsRiskLog{}).Error)
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsRiskLog) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsRiskLog, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsRuleParameter{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsRiskLog) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsRiskLog{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsRiskLog) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsRiskLog, error) {
//	var item RmsRiskLog
//	var err error
//	err = fn(global.DB.Model(&RmsRiskLog{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsRiskLog) FindByPartnerID(ctx context.Context, partnerID string) ([]*RmsRiskLog, error) {
//	var items []*RmsRiskLog
//	err := global.DB.Model(&RmsRiskLog{}).Where("partner_id = ?", partnerID).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
