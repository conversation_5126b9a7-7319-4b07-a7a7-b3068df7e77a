package modelv2

const TableNameRmsBlackWhiteAuditDetail = "rms_black_white_audit_detail"

// RmsBlackWhiteAuditDetail 黑名单白名单审核明细
type RmsBlackWhiteAuditDetail struct {
	ID      int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"` // 主键
	BwlID   int64  `gorm:"column:bwl_id;type:bigint(11);not null;index;comment:黑白名单ID" json:"bwl_id"`
	AuditID int64  `gorm:"column:audit_id;type:bigint;not null;comment:审核ID" json:"audit_id"`
	Log     string `gorm:"column:log;type:text;comment:日志" json:"log"`
	BaseModel
}

func NewRmsBlackWhiteAuditDetail() *RmsBlackWhiteAuditDetail {
	return &RmsBlackWhiteAuditDetail{}
}

// TableName RmsBlackWhiteAuditDetail's table name
func (*RmsBlackWhiteAuditDetail) TableName() string {
	return TableNameRmsBlackWhiteAuditDetail
}
