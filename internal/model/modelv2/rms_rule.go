package modelv2

import (
	"time"
)

// AP acquiring CP:issuing BP:banking
const TableNameRmsRule = "rms_rule"

// RmsRule 风控规则
type RmsRule struct {
	ID             int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                                                                         // 主键
	RuleGroup      *string    `gorm:"column:rule_group;type:varchar(32);not null;index:idx_rg,priority:1;default:CMF;comment:规则组，即策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup" json:"rule_group"` // 规则组，即策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup
	RuleNo         string     `gorm:"column:rule_no;type:varchar(32);not null;uniqueIndex:uidx_rno,priority:1;comment:规则编号" json:"rule_no"`                                                                           // 规则编号
	RuleName       string     `gorm:"column:rule_name;type:varchar(64);not null;comment:规则描述" json:"rule_name"`                                                                                                       // 规则描述
	CheckPoint     string     `gorm:"column:check_point;type:varchar(256);not null;comment:检查点" json:"check_point"`                                                                                                    // 检查点
	RuleType       string     `gorm:"column:rule_type;type:varchar(16);not null;comment:规则类型" json:"rule_type"`                                                                                                       // 规则类型
	RuleContent    string     `gorm:"column:rule_content;type:text;not null;comment:规则内容" json:"rule_content"`                                                                                                        // 规则内容
	ShortCircuit   bool       `gorm:"column:short_circuit;type:tinyint(1);not null;comment:是否支持短路" json:"short_circuit"`                                                                                            // 是否支持短路
	Params         *string    `gorm:"column:params;type:varchar(1024);comment:参数，注意与规则参数区分" json:"params"`                                                                                                     // 参数，注意与规则参数区分
	AgendaName     string     `gorm:"column:agenda_name;type:varchar(128);not null;comment:Agenda，和规则内容中的package一致" json:"agenda_name"`                                                                          // Agenda，和规则内容中的package一致
	EngineType     string     `gorm:"column:engine_type;type:varchar(16);not null;comment:引擎类型，有状态/无状态" json:"engine_type"`                                                                                     // 引擎类型，有状态/无状态
	DeployMethod   string     `gorm:"column:deploy_method;type:varchar(8);not null;comment:上下线方式，manual/auto" json:"deploy_method"`                                                                                  // 上下线方式，manual/auto
	EngineInstance *string    `gorm:"column:engine_instance;type:varchar(128);comment:有状态类型情况下选择的引擎实例" json:"engine_instance"`                                                                             // 有状态类型情况下选择的引擎实例
	StartTime      *time.Time `gorm:"column:start_time;type:datetime;index:idx_start_end,priority:1;comment:生效时间" json:"start_time"`                                                                                  // 生效时间
	EndTime        *time.Time `gorm:"column:end_time;type:datetime;index:idx_start_end,priority:2;comment:失效时间" json:"end_time"`                                                                                      // 失效时间
	Status         string     `gorm:"column:status;type:varchar(16);not null;comment:状态，Online/Offline" json:"status"`                                                                                                  // 状态，Online/Offline
	Memo           *string    `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                                                                                              // 备注
	SyncedAt       *time.Time `gorm:"column:synced_at;type:datetime;comment:同步时间" json:"synced_at"`
	Priority       int        `gorm:"column:priority;type:int"`
	BaseModel
}

// TableName RmsRule's table name
func (*RmsRule) TableName() string {
	return TableNameRmsRule
}

func NewRmsRule() *RmsRule {
	return &RmsRule{}
}

func (r *RmsRule) ToCacheData() (res map[string]interface{}) {
	res = map[string]interface{}{
		r.RuleGroupField():      "",
		r.SyncedAtField():       time.Time{},
		r.ParamsField():         "",
		r.EngineInstanceField(): "",
		r.StartTimeField():      time.Time{},
		r.EndTimeField():        time.Time{},
		r.MemoField():           "",
	}
	res["rule_group"] = ""
	res["params"] = ""
	res["engine_instance"] = ""
	res["id"] = r.ID
	if r.RuleGroup != nil {
		res["rule_group"] = *r.RuleGroup
	}
	res["rule_no"] = r.RuleNo
	res["rule_name"] = r.RuleName
	res["check_point"] = r.CheckPoint
	res["rule_type"] = r.RuleType
	res["rule_content"] = r.RuleContent
	res["short_circuit"] = r.ShortCircuit
	if r.Params != nil {
		res["params"] = *r.Params
	}
	res["agenda_name"] = r.AgendaName
	res["engine_type"] = r.EngineType
	res["deploy_method"] = r.DeployMethod
	if r.EngineInstance != nil {
		res["engine_instance"] = *r.EngineInstance
	}
	if r.StartTime != nil {
		res["start_time"] = *r.StartTime
	}
	if r.EndTime != nil {
		res["end_time"] = *r.EndTime
	}
	res["status"] = r.Status
	if r.Memo != nil {
		res["memo"] = *r.Memo
	}
	if r.SyncedAt != nil {
		res["synced_at"] = *r.SyncedAt
	}
	res["priority"] = r.Priority
	return

}

//
//func (r *RmsRule) Create(ctx context.Context, session *gorm.DB, models ...*RmsRule) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsRule{}).Create(models)
//		if resTx.Error != nil {
//			return er.ConvertDBError(resTx.Error)
//		}
//		return nil
//	})
//	return
//}
//
//func (r *RmsRule) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsRule) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsRule{})).Select(
//			"check_point",
//			"rule_no",
//			"rule_name",
//			"start_time",
//			"end_time",
//			"agenda_name",
//			"rule_content",
//			"memo",
//			"status",
//			"short_circuit",
//			"deploy_method",
//			"synced_at",
//			"updated_at",
//			"priority",
//		).Updates(model).Error)
//	})
//}
//
//func (r *RmsRule) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsRule{})).Delete(&RmsRule{}).Error)
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsRule) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsRule, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsRule{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsRule) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsRule{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsRule) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsRule, error) {
//	var item RmsRule
//	var err error
//	err = fn(global.DB.Model(&RmsRule{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsRule) GetByRuleNo(ctx context.Context, ruleNo string) (*RmsRule, error) {
//	var item RmsRule
//	err := global.DB.Model(&RmsRule{}).Where("rule_no = ?", ruleNo).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}
//
//func (r *RmsRule) ListByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsRule, error) {
//	var items []*RmsRule
//	err := global.DB.Model(&RmsRule{}).Where("check_point = ?", checkPoint).Order("id DESC").WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsRule) ListByStatus(ctx context.Context, status string) ([]*RmsRule, error) {
//	var items []*RmsRule
//	err := global.DB.Model(&RmsRule{}).Where("status = ?", status).Order("id DESC").WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsRule) ListByRuleGroup(ctx context.Context, ruleGroup string) ([]*RmsRule, error) {
//	var items []*RmsRule
//	err := global.DB.Model(&RmsRule{}).Where("rule_group = ?", ruleGroup).Order("id DESC").WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//func (r *RmsRule) SetStatus(ctx context.Context, session *gorm.DB, id int64, cp *RmsRule) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return tx.Select("start_time", "end_time", "deploy_method", "status", "synced_at").Where("id =?", id).Updates(cp).Error
//	})
//
//}
