package modelv2

const TableNameRmsRuleParameterGroup = "rms_rule_parameter_group"

// RmsRuleParameterGroup 规则参数组
type RmsRuleParameterGroup struct {
	ID          int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                             // 主键
	Code        string `gorm:"column:code;type:varchar(64);not null;uniqueIndex:uidx_code,priority:1;comment:参数组编码" json:"code"`                  // 参数组编码
	ParamIds    string `gorm:"column:param_ids;type:varchar(256);comment:下属规则参数id(可多个,逗号分割)" json:"param_ids"`                            // 下属规则参数id(可多个,逗号分割)
	CheckPoint  string `gorm:"column:check_point;type:varchar(256);not null;comment:检查点" json:"check_point"`                                        // 检查点
	Description string `gorm:"column:description;type:varchar(256);not null;comment:参数组描述" json:"description"`                                    // 参数组描述
	DefaultFlag int32  `gorm:"column:default_flag;type:int;not null;comment:默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否" json:"default_flag"` // 默认参数组，一个接入点仅允许一个默认参数组. 1-是，0-否
	Able        int32  `gorm:"column:able;type:int;not null;comment:状态，1-有效，0-失效" json:"able"`                                                   // 状态，1-有效，0-失效。
	BaseModel
}

func NewRmsRuleParameterGroup() *RmsRuleParameterGroup {
	return &RmsRuleParameterGroup{}
}

// TableName RmsRuleParameterGroup's table name
func (*RmsRuleParameterGroup) TableName() string {
	return TableNameRmsRuleParameterGroup
}

//
//func (r *RmsRuleParameterGroup) Create(ctx context.Context, session *gorm.DB, model *RmsRuleParameterGroup) (id int64, err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsRuleParameterGroup{}).Create(model)
//		if resTx.Error != nil {
//			return er.ConvertDBError(resTx.Error)
//		}
//		id = model.ID
//		return nil
//	})
//	return
//}
//
//func (r *RmsRuleParameterGroup) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsRuleParameterGroup) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameterGroup{})).Updates(model).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//func (r *RmsRuleParameterGroup) UpdateField(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, field string, value interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameterGroup{})).Update(field, value).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//func (r *RmsRuleParameterGroup) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameterGroup{})).Delete(&RmsRuleParameterGroup{}).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsRuleParameterGroup) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsRuleParameterGroup, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsRuleParameterGroup{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsRuleParameterGroup) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsRuleParameterGroup{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsRuleParameterGroup) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsRuleParameterGroup, error) {
//	var item RmsRuleParameterGroup
//	var err error
//	err = fn(global.DB.Model(&RmsRuleParameterGroup{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsRuleParameterGroup) FindByCode(ctx context.Context, code string) (*RmsRuleParameterGroup, error) {
//	var item RmsRuleParameterGroup
//	err := global.DB.Model(&RmsRuleParameterGroup{}).Where("code = ?", code).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}
//
//func (r *RmsRuleParameterGroup) FindByCheckPoint(ctx context.Context, checkPoint string) (*RmsRuleParameterGroup, error) {
//	var item RmsRuleParameterGroup
//	err := global.DB.Model(&RmsRuleParameterGroup{}).Where("check_point = ?", checkPoint).WithContext(ctx).First(&item).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return &item, nil
//}
//
//func (r *RmsRuleParameterGroup) ListAll(ctx context.Context) ([]*RmsRuleParameterGroup, error) {
//	var items []*RmsRuleParameterGroup
//	err := global.DB.Model(&RmsRuleParameterGroup{}).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsRuleParameterGroup) ListExcludeDefault(ctx context.Context) ([]*RmsRuleParameterGroup, error) {
//	var items []*RmsRuleParameterGroup
//	err := global.DB.Model(&RmsRuleParameterGroup{}).Where("default_flag = ?", 0).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
//
//func (r *RmsRuleParameterGroup) UpdateParamIds(ctx context.Context, session *gorm.DB, id int64, paramIds string) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		return r.UpdateField(ctx, global.DB, func(db *gorm.DB) *gorm.DB {
//			return db.Where("id = ?", id)
//		}, "param_ids", paramIds)
//	})
//}
