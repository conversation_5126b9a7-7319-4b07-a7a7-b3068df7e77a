package modelv2

import (
	"slices"
)

type BlackWhiteCache struct {
	IsBlack          bool
	AccessPointRules map[string][]int64 //access_point => bwl_id
	BwlIDs           map[int64]*BlackWhiteCacheMetadata
}

func NewBwCacheMap(isBlack bool) *BlackWhiteCache {
	return &BlackWhiteCache{
		IsBlack:          isBlack,
		AccessPointRules: make(map[string][]int64),                 //通过接入点获取黑白名单信息，通过接入点进行规则校验,int64为黑白名单列表数据
		BwlIDs:           make(map[int64]*BlackWhiteCacheMetadata), //通过bwl_id获取黑白名单信息，方便后面增量更新
	}
}

type BlackWhiteCacheMetadata struct {
	BwlID                int64                    `json:"bwl_id"`
	Fields               []string                 `json:"fields"`
	AccessPoints         []string                 `json:"access_points"` //接入点
	RequiredFields       []string                 `json:"required_fields"`
	Name                 string                   `json:"name"`                   //黑白名单名称
	Status               bool                     `json:"status"`                 //黑白名单状态
	Rules                []map[string]interface{} `json:"rules"`                  //黑白名单用于逻辑判断的数据
	RequiredFieldsSha256 map[string]int           `json:"required_fields_sha256"` //黑白名单必填字段sha256 => Rules的索引
}

func (c *BlackWhiteCache) Set(metadata *BlackWhiteCacheMetadata) (err error) {
	for _, accessPoint := range metadata.AccessPoints {
		var tmpCache, _ = c.AccessPointRules[accessPoint]
		tmpCache = append(tmpCache, metadata.BwlID)
		c.AccessPointRules[accessPoint] = tmpCache
	}
	c.BwlIDs[metadata.BwlID] = metadata
	return
}

func (c *BlackWhiteCache) ChangeAccessPoints(bwlID int64, accessPoints []string) {
	var metadata *BlackWhiteCacheMetadata
	var ok bool
	metadata, ok = c.BwlIDs[bwlID]
	if !ok {
		return
	}
	for _, accessPoint := range metadata.AccessPoints {
		var metas []int64
		metas, ok = c.AccessPointRules[accessPoint]
		if !ok {
			continue
		}
		for i, id := range metas {
			if id == bwlID {
				metas = slices.Delete(metas, i, i+1)
			}
		}
		c.AccessPointRules[accessPoint] = metas
	}
	metadata.AccessPoints = accessPoints

	for _, accessPoint := range accessPoints {
		var tmpCache, _ = c.AccessPointRules[accessPoint]
		tmpCache = append(tmpCache, metadata.BwlID)
		c.AccessPointRules[accessPoint] = tmpCache
	}
}
func (c *BlackWhiteCache) RemoveByBwlID(bwlID int64) (err error) {
	var metadata *BlackWhiteCacheMetadata
	var ok bool
	metadata, ok = c.BwlIDs[bwlID]
	if !ok {
		return
	}
	for _, accessPoint := range metadata.AccessPoints {
		var metas []int64
		metas, ok = c.AccessPointRules[accessPoint]
		if !ok {
			continue
		}
		for i, id := range metas {
			if id == bwlID {
				metas = slices.Delete(metas, i, i+1)
			}
		}
	}
	delete(c.BwlIDs, bwlID)
	return
}
func (c *BlackWhiteCache) SetStatus(bwlID int64, status bool) *BlackWhiteCache {
	var metadata *BlackWhiteCacheMetadata
	var ok bool
	metadata, ok = c.BwlIDs[bwlID]
	if !ok {
		return c
	}
	metadata.Status = status
	return c
}
