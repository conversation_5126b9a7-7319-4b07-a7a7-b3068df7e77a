package modelv2

import (
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/global"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"

	"gorm.io/gorm"
)

type BaseModel struct {
	EmptyModel

	CreatedAt time.Time `gorm:"column:created_at;type:datetime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;comment:修改时间" json:"updated_at"`
}
type BaseDeletedModel struct {
	BaseModel
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime;comment:删除时间" json:"deleted_at"`
}
type EmptyModel struct{}

func (b *EmptyModel) DoDefaultFilter(filter *filter.DefaultFilter, tx *gorm.DB) *gorm.DB {
	if filter == nil {
		return tx
	}
	if filter.Page != nil {
		tx = filter.Page.Filter(tx)
	}
	if filter.Time != nil {
		tx = filter.Time.Filter(tx)
	}
	return tx
}
func (b *EmptyModel) FilterPage(filter *filter.DefaultFilter, tx *gorm.DB) *gorm.DB {
	if filter == nil {
		return tx
	}
	if filter.Page != nil {
		tx = filter.Page.Filter(tx)
	}
	return tx
}
func (b *EmptyModel) FilterTime(filter *filter.DefaultFilter, tx *gorm.DB) *gorm.DB {
	if filter == nil {
		return tx
	}
	if filter.Time != nil {
		tx = filter.Time.Filter(tx)
	}
	return tx
}

func InitModel() {
	global.DB.AutoMigrate(
		// 已有的表结构
		&RmsCheckPoint{}, &RmsDataSourceTable{}, &RmsIndicator{}, &RmsIndicatorVersion{},
		&RmsIndicatorVersionHistory{}, &RmsIndicatorMeasure{}, &RmsIndicatorRule{},
		&RmsDataSource{}, &RmsBlackWhiteList{}, &RmsBlackWhiteItem{}, &RmsBlackWhiteAudit{},
		&RmsEventFieldBinding{}, &RmsBlackWhiteField{}, &RmsBlackWhiteOperatorLog{},
		&RmsBlackWhiteAuditDetail{},


		// 添加其他model目录下的表结构...
	)
}

type GroupIdCount struct {
	Id    int64 `json:"id"`
	Total int64 `json:"total"`
}
