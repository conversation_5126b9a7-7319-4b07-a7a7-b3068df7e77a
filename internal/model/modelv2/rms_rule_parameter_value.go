package modelv2

const TableNameRmsRuleParameterValue = "rms_rule_parameter_value"

// RmsRuleParameterValue 规则参数值
type RmsRuleParameterValue struct {
	ID          int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                         // 主键
	ParamID int64 `gorm:"column:param_id;type:bigint;not null;index:idx_pid,priority:1;comment:RMS_RULE_PARAMETER外键" json:"param_id"` // RMS_RULE_PARAMETER外键
	GroupID     int64   `gorm:"column:group_id;type:bigint;not null;index:idx_gid,priority:1;comment:RMS_RULE_PARAMETER_GROUP外键" json:"group_id"` // RMS_RULE_PARAMETER_GROUP外键
	ParamKey    string  `gorm:"column:param_key;type:varchar(16);comment:健，非键值对类型可空" json:"param_key"`                                     // 健，非键值对类型可空
	ParamValue  string  `gorm:"column:param_value;type:varchar(1024);not null;comment:值" json:"param_value"`                                       // 值
	Description *string `gorm:"column:description;type:varchar(256);comment:描述" json:"description"`                                               // 描述
	BaseModel
}

func NewRmsRuleParameterValue() *RmsRuleParameterValue {
	return &RmsRuleParameterValue{}
}

// TableName RmsRuleParameterValue's table name
func (*RmsRuleParameterValue) TableName() string {
	return TableNameRmsRuleParameterValue
}

//
//func (r *RmsRuleParameterValue) Create(ctx context.Context, session *gorm.DB, model *RmsRuleParameterValue) (id int64, err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsRuleParameterValue{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		id = model.ID
//		return nil
//	})
//	return
//}
//
//func (r *RmsRuleParameterValue) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsRuleParameterValue) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameterValue{})).Updates(model).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//func (r *RmsRuleParameterValue) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		innerErr = fn(tx.Model(&RmsRuleParameterValue{})).Delete(&RmsRuleParameterValue{}).Error
//		if innerErr != nil {
//			return er.ConvertDBError(innerErr)
//		}
//		return nil
//	})
//}
//
//// List 获取列表数据，可选择是否返回总数
//// withTotal: true 返回总数，false 只返回列表数据
//func (r *RmsRuleParameterValue) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsRuleParameterValue, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsRuleParameterValue{})).WithContext(ctx))
//
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//func (r *RmsRuleParameterValue) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsRuleParameterValue{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//func (r *RmsRuleParameterValue) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsRuleParameterValue, error) {
//	var item RmsRuleParameterValue
//	var err error
//	err = fn(global.DB.Model(&RmsRuleParameterValue{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//func (r *RmsRuleParameterValue) AllByGroupID(ctx context.Context, groupID int64) ([]*RmsRuleParameterValue, error) {
//	var items []*RmsRuleParameterValue
//	err := global.DB.Model(&RmsRuleParameterValue{}).Where("group_id = ?", groupID).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
