package modelv2

const TableNameRmsEventStoreCfg = "rms_event_store_cfg"

// RmsEventStoreCfg 事件存储配置
type RmsEventStoreCfg struct {
	ID              int64   `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                              // 主键
	CheckPoint      string  `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp,priority:1;comment:检查点" json:"check_point"` // 检查点
	Persistent      *bool   `gorm:"column:persistent;type:tinyint(1);not null;default:1;comment:是否存储" json:"persistent"`                 // 是否存储
	SendToScheduler bool    `gorm:"column:send_to_scheduler;type:tinyint(1);not null;comment:暂不使用" json:"send_to_scheduler"`             // 暂不使用
	SaveToBusiness  bool    `gorm:"column:save_to_business;type:tinyint(1);not null;comment:暂不使用" json:"save_to_business"`               // 暂不使用
	SendToIntra     bool    `gorm:"column:send_to_intra;type:tinyint(1);not null;default:1;comment:是否发送到风控后台" json:"send_to_intra"` // 是否发送到风控后台
	StrField1       *string `gorm:"column:str_field1;type:varchar(16);comment:重要字符串类型字段1的字段名（建议主键）" json:"str_field1"`      // 重要字符串类型字段1的字段名（建议主键）
	StrField2       *string `gorm:"column:str_field2;type:varchar(16);comment:重要字符串类型字段2的字段名" json:"str_field2"`                // 重要字符串类型字段2的字段名
	StrField3       *string `gorm:"column:str_field3;type:varchar(16);comment:重要字符串类型字段3的字段名" json:"str_field3"`                // 重要字符串类型字段3的字段名
	StrField4       *string `gorm:"column:str_field4;type:varchar(16);comment:重要字符串类型字段4的字段名" json:"str_field4"`                // 重要字符串类型字段4的字段名
	StrField5       *string `gorm:"column:str_field5;type:varchar(16);comment:重要字符串类型字段5的字段名" json:"str_field5"`                // 重要字符串类型字段5的字段名
	NumField1       *string `gorm:"column:num_field1;type:varchar(16);comment:重要数值类型字段1的字段名" json:"num_field1"`                  // 重要数值类型字段1的字段名
	NumField2       *string `gorm:"column:num_field2;type:varchar(16);comment:重要数值类型字段2的字段名" json:"num_field2"`                  // 重要数值类型字段2的字段名
	BaseModel
}

// TableName RmsEventStoreCfg's table name
func (*RmsEventStoreCfg) TableName() string {
	return TableNameRmsEventStoreCfg
}

// NewRmsEventStoreCfg creates a new RmsEventStoreCfg instance
func NewRmsEventStoreCfg() *RmsEventStoreCfg {
	return &RmsEventStoreCfg{}
}

//
//// Create creates a new RmsEventStoreCfg record
//func (r *RmsEventStoreCfg) Create(ctx context.Context, session *gorm.DB, model *RmsEventStoreCfg) (err error) {
//	err = db.Transaction(ctx, session, func(tx *gorm.DB) error {
//		resTx := tx.Model(&RmsEventStoreCfg{}).Create(model)
//		if resTx.Error != nil {
//			return resTx.Error
//		}
//		return nil
//	})
//	return
//}
//
//// Update updates an existing RmsEventStoreCfg record
//func (r *RmsEventStoreCfg) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, model *RmsEventStoreCfg) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEventStoreCfg{})).Updates(model).Error)
//	})
//}
//
//// Updates updates specific fields in an existing RmsEventStoreCfg record
//func (r *RmsEventStoreCfg) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEventStoreCfg{})).Updates(data).Error)
//	})
//}
//
//// Delete deletes a RmsEventStoreCfg record
//func (r *RmsEventStoreCfg) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {
//	return db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {
//		return er.ConvertDBError(fn(tx.Model(&RmsEventStoreCfg{})).Delete(&RmsEventStoreCfg{}).Error)
//	})
//}
//
//// List retrieves a list of RmsEventStoreCfg records with optional total count
//// withTotal: true returns total count, false returns only list data
//func (r *RmsEventStoreCfg) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*RmsEventStoreCfg, err error) {
//	tx := r.FilterTime(defaultFilter, fn(global.DB.Model(&RmsEventStoreCfg{})).WithContext(ctx))
//	// 获取列表数据
//	err = r.FilterPage(defaultFilter, tx).Order("id desc").Find(&items).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//		return 0, nil, err
//	}
//
//	// 是否需要获取总数
//	if len(withTotal) > 0 && withTotal[0] {
//		err = tx.Count(&total).Error
//		if err != nil {
//			err = er.ConvertDBError(err)
//			return 0, items, err
//		}
//		return total, items, nil
//	}
//
//	return 0, items, nil
//}
//
//// Count counts RmsEventStoreCfg records based on the condition
//func (r *RmsEventStoreCfg) Count(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (total int64, err error) {
//	err = fn(global.DB.Model(&RmsEventStoreCfg{})).WithContext(ctx).Count(&total).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return
//}
//
//// Retrieve retrieves a single RmsEventStoreCfg record
//func (r *RmsEventStoreCfg) Retrieve(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*RmsEventStoreCfg, error) {
//	var item RmsEventStoreCfg
//	var err error
//	err = fn(global.DB.Model(&RmsEventStoreCfg{})).WithContext(ctx).First(&item).Error
//	if err != nil {
//		err = er.ConvertDBError(err)
//	}
//	return &item, err
//}
//
//// FindByCheckPoint retrieves RmsEventStoreCfg records by check point
//func (r *RmsEventStoreCfg) FindByCheckPoint(ctx context.Context, checkPoint string) ([]*RmsEventStoreCfg, error) {
//	var items []*RmsEventStoreCfg
//	err := global.DB.Model(&RmsEventStoreCfg{}).Where("check_point = ?", checkPoint).WithContext(ctx).Find(&items).Error
//	if err != nil {
//		return nil, er.ConvertDBError(err)
//	}
//	return items, nil
//}
