package modelv2

type PlatConfigProgress struct {
	ID             int64  `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CheckPointCode string `gorm:"column:check_point_code;type:varchar(100);not null;uniqueIndex:plat_config_progress_unique,priority:1;comment:接入点 code" json:"check_point_code"`
	Progress       int32  `gorm:"column:progress;type:int;not null;comment:填写进度，格式：[1,2,3]，数字为 接入点 填写引导中步骤的编号，每完成一步的填写增加一条" json:"progress"`
}

func NewPlatConfigProgress() *PlatConfigProgress {
	return &PlatConfigProgress{}
}
func (PlatConfigProgress) TableName() string {
	return "plat_config_progress"
}
