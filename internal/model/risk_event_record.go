package model

import (
	"github.com/shopspring/decimal"
	"time"
)

type RiskEventRecord struct {
	ID               uint64          `json:"id" gorm:"primaryKey;autoIncrement"`
	CheckPoint       string          `json:"check_point" gorm:"size:32;index"`
	Str1             string          `json:"str1" gorm:"str1"`
	Str2             string          `json:"str2" gorm:"str2"`
	Str3             string          `json:"str3" gorm:"str3"`
	Str4             string          `json:"str4" gorm:"str4"`
	Str5             string          `json:"str5" gorm:"str5"`
	Num1             decimal.Decimal `json:"num1" gorm:"num1"`
	Num2             decimal.Decimal `json:"num2" gorm:"num2"`
	ResultCode       string          `json:"result_code" gorm:"result_code"`
	ResultMsg        string          `json:"result_msg" gorm:"result_msg"`
	FailedRule       string          `json:"failed_rule" gorm:"failed_rule"`
	CreateTime       time.Time       `json:"create_time" gorm:"create_time"`
	RequestParameter string          `json:"request_parameter" gorm:"request_parameter"`
}

func (RiskEventRecord) TableName() string {
	return "risk_event_record"
}
