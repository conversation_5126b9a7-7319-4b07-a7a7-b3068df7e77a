package model

import (
	"time"
)

const TableNameRmsAlarmContact = "rms_alarm_contacts"

// RmsAlarmContact 风控预警联系人
type RmsAlarmContact struct {
	ID                int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`            // 主键
	Name              string    `gorm:"column:name;type:varchar(32);not null;comment:联系人姓名" json:"name"`                     // 联系人姓名
	Mobile            *string   `gorm:"column:mobile;type:varchar(16);comment:手机号" json:"mobile"`                            // 手机号
	Email             *string   `gorm:"column:email;type:varchar(64);comment:邮箱" json:"email"`                               // 邮箱
	Status            int32     `gorm:"column:status;type:int;not null;comment:状态" json:"status"`                            // 状态
	AlarmTimeConfigID *int64    `gorm:"column:alarm_time_config_id;type:bigint;comment:时间配置表外键" json:"alarm_time_config_id"` // 时间配置表外键
	NoticeTime        string    `gorm:"column:notice_time;type:varchar(4);not null;comment:通知类型" json:"notice_time"`         // 通知类型
	OpenID            *string   `gorm:"column:open_id;type:varchar(32);comment:操作员" json:"open_id"`                          // 操作员
	CreatedAt         time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`             // 创建时间
	UpdatedAt         time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`             // 更新时间
}

// TableName RmsAlarmContact's table name
func (*RmsAlarmContact) TableName() string {
	return TableNameRmsAlarmContact
}
