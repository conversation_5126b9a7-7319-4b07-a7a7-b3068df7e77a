package model

import (
	"time"
)

const TableNameWpHandleLog = "wp_handle_logs"

// WpHandleLog 用户操作日志
type WpHandleLog struct {
	Hlid         int32      `gorm:"column:hlid;type:int;primaryKey;autoIncrement:true" json:"hlid"`
	Puserid      *string    `gorm:"column:puserid;type:varchar(32);index:IDX_CODE,priority:1;index:IDX_PUID_PARTNERID,priority:1;index:IDX_STATUS,priority:1;comment:操作员ID" json:"puserid"`                                                                                          // 操作员ID
	PartnerID    *string    `gorm:"column:partner_id;type:varchar(48);index:IDX_CODE,priority:2;index:IDX_PUID_PARTNERID,priority:2;index:IDX_STATUS,priority:2;comment:商户ID" json:"partner_id"`                                                                                     // 商户ID
	HandleCode   string     `gorm:"column:handle_code;type:varchar(48);not null;index:IDX_CODE,priority:3;comment:日志类型: 前台商户=TYPE_PARTNER_HANDLE，后台管理=TYPE_SYSADMIN_HANDLE，网关日志=TYPE_GATEWAY_HANDLE，计划任务日志=TYPE_TASK_HANDLE" json:"handle_code"`                                     // 日志类型: 前台商户=TYPE_PARTNER_HANDLE，后台管理=TYPE_SYSADMIN_HANDLE，网关日志=TYPE_GATEWAY_HANDLE，计划任务日志=TYPE_TASK_HANDLE
	HandleType   string     `gorm:"column:handle_type;type:varchar(48);not null;index:IDX_CODE,priority:4;comment:日志类型编码 PARTNER_LOGIN = 商户登陆 PARTNER_PAYOUT = 商户提现  PARTNER_PAYIN = 商户充值  PARTNER_SAFETY = 商户安全设置 SYSADMIN_LOG 后台设置日志 TASK_HANDLE_LOG=定时任务设置日志" json:"handle_type"` // 日志类型编码 PARTNER_LOGIN = 商户登陆 PARTNER_PAYOUT = 商户提现  PARTNER_PAYIN = 商户充值  PARTNER_SAFETY = 商户安全设置 SYSADMIN_LOG 后台设置日志 TASK_HANDLE_LOG=定时任务设置日志
	OrderSn      *string    `gorm:"column:order_sn;type:varchar(32);comment:订单号：针对订单流程" json:"order_sn"`                                                                                                                                                                             // 订单号：针对订单流程
	HandleEvents *string    `gorm:"column:handle_events;type:varchar(240);comment:操作事件" json:"handle_events"`                                                                                                                                                                        // 操作事件
	HandleParams *string    `gorm:"column:handle_params;type:text;comment:动作参数[内部使用]" json:"handle_params"`                                                                                                                                                                          // 动作参数[内部使用]
	HandleStatus int32      `gorm:"column:handle_status;type:int;not null;index:IDX_STATUS,priority:3;comment:事件状态 0=失败 1=成功" json:"handle_status"`                                                                                                                                  // 事件状态 0=失败 1=成功
	HandleIP     string     `gorm:"column:handle_ip;type:varchar(128);not null;comment:操作IP" json:"handle_ip"`                                                                                                                                                                       // 操作IP
	CreatedAt    *time.Time `gorm:"column:created_at;type:datetime;index:IDX_DATELINE,priority:1;comment:创建时间" json:"created_at"`                                                                                                                                                    // 创建时间
}

// TableName WpHandleLog's table name
func (*WpHandleLog) TableName() string {
	return TableNameWpHandleLog
}
