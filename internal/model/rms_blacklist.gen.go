package model

import (
	"time"
)

const TableNameRmsBlacklist = "rms_blacklist"

// RmsBlacklist 黑名单
type RmsBlacklist struct {
	RcID      int64     `gorm:"column:rc_id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"rc_id"`                                             // 主键
	RiskKey   string    `gorm:"column:risk_key;type:varchar(16);not null;uniqueIndex:idx_rk_rv,priority:1;comment:键，BANK_CARD_NO/TRADE_IP" json:"risk_key"` // 键，BANK_CARD_NO/TRADE_IP
	RiskValue string    `gorm:"column:risk_value;type:varchar(64);not null;uniqueIndex:idx_rk_rv,priority:2;comment:值，如卡号/ip" json:"risk_value"`            // 值，如卡号/ip
	Remark    *string   `gorm:"column:remark;type:varchar(256);comment:备注" json:"remark"`                                                                   // 备注
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`                                                    // 创建时间
}

// TableName RmsBlacklist's table name
func (*RmsBlacklist) TableName() string {
	return TableNameRmsBlacklist
}
