package model

import (
	"time"
)

const TableNameRmsOperatorLog = "rms_operator_log"

// RmsOperatorLog 风控操作日志
type RmsOperatorLog struct {
	ID        int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                       // 主键
	UserID    string    `gorm:"column:user_id;type:varchar(32);not null;index:idx_uid,priority:1;comment:操作员id" json:"user_id"` // 操作员id
	UserName  *string   `gorm:"column:user_name;type:varchar(16);comment:操作员姓名" json:"user_name"`                               // 操作员姓名
	Method    *string   `gorm:"column:method;type:varchar(128);index:idx_method,priority:1;comment:动作" json:"method"`           // 动作
	Params    string    `gorm:"column:params;type:varchar(1024);not null;comment:参数" json:"params"`                             // 参数
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`                        // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`                        // 更新时间
}

// TableName RmsOperatorLog's table name
func (*RmsOperatorLog) TableName() string {
	return TableNameRmsOperatorLog
}
