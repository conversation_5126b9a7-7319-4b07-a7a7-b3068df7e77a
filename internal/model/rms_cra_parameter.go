package model

import "time"

// RmsCraParameter Cra—参数列表
type RmsCraParameter struct {
	ID                 int64     `json:"id" gorm:"id"`                                     // 主键
	CraFrameworkId     string    `json:"cra_framework_id" gorm:"cra_framework_id"`         // 唯一ID
	CraParameterId     string    `json:"cra_parameter_id" gorm:"cra_parameter_id"`         // 唯一ID
	CraParameterName   string    `json:"cra_parameter_name" gorm:"cra_parameter_name"`     // 参数名
	CraParameterStatus int8      `json:"cra_parameter_status" gorm:"cra_parameter_status"` // 状态：1=active, 0=默认, -1=inactive
	CraParameterType   string    `json:"cra_parameter_type" gorm:"cra_parameter_type"`     // AML; COUNTRY; CUSTOM
	Description        string    `json:"description" gorm:"description"`                   // 描述
	Weight             float64   `json:"weight" gorm:"weight"`                             // 重量，权重
	CreateTime         time.Time `json:"create_time" gorm:"create_time"`
	UpdateTime         time.Time `json:"update_time" gorm:"update_time"`
	ParamInfo          string    `json:"param_info" gorm:"param_info"` // 关联字段详细信息
}

// TableName 表名称
func (*RmsCraParameter) TableName() string {
	return "rms_cra_parameter"
}
