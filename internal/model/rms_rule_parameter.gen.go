package model

import (
	"time"
)

const TableNameRmsRuleParameter = "rms_rule_parameter"

// RmsRuleParameter 规则参数
type RmsRuleParameter struct {
	ID            int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                          // 主键
	Code          string     `gorm:"column:code;type:varchar(64);not null;uniqueIndex:uidx_code,priority:1;comment:参数编码" json:"code"`                                   // 参数编码
	Description   string     `gorm:"column:description;type:varchar(256);not null;comment:参数描述" json:"description"`                                                     // 参数描述
	ParamType     string     `gorm:"column:param_type;type:varchar(32);comment:参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes" json:"param_type"`                           // 参数类型，可选值在RMS_FILTER_FIELD定义-ruleParamTypes
	CheckPoint    string     `gorm:"column:check_point;type:varchar(256);not null;comment:检查点(可多个,逗号分割)" json:"check_point"`                                            // 检查点(可多个,逗号分割)
	ValueTransfer *string    `gorm:"column:value_transfer;type:varchar(32);comment:值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer" json:"value_transfer"` // 值转换（出于安全将卡号原文转换为Ticket），可选值在RMS_FILTER_FIELD定义-valueTransfer
	AccessFlag    *string    `gorm:"column:access_flag;type:varchar(16);not null;default:apex;comment:允许接口添加值，默认值apex" json:"access_flag"`                              // 允许接口添加值，默认值apex.旧系统有 bug，无论是否有效数据库中的值都是 apex。经商议从前端去掉。
	CreatedAt     *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                 // 创建时间
	UpdatedAt     *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                 // 更新时间
}

// TableName RmsRuleParameter's table name
func (*RmsRuleParameter) TableName() string {
	return TableNameRmsRuleParameter
}
