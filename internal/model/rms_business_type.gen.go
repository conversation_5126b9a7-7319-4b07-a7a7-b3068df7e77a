package model

import (
	"time"
)

const TableNameRmsBusinessType = "rms_business_type"

// RmsBusinessType 业务类型
type RmsBusinessType struct {
	ID           int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`         // 主键
	BusinessCode string    `gorm:"column:business_code;type:varchar(16);not null;comment:业务编码" json:"business_code"` // 业务编码
	BusinessName string    `gorm:"column:business_name;type:varchar(16);not null;comment:业务名称" json:"business_name"` // 业务名称
	TableName_   *string   `gorm:"column:table_name;type:varchar(16);comment:与businessField相关，废弃" json:"table_name"` // 与businessField相关，废弃
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`          // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`          // 更新时间
}

// TableName RmsBusinessType's table name
func (*RmsBusinessType) TableName() string {
	return TableNameRmsBusinessType
}
