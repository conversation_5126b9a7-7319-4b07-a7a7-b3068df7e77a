package model

import (
	"github.com/shopspring/decimal"
	"time"
)

// RmsCraScoreReference Cra—结果概览
type RmsCraScoreReference struct {
	ID                  int64           `json:"id" gorm:"id"`                                         // 主键
	CraScoreReferenceId string          `json:"cra_score_reference_id" gorm:"cra_score_reference_id"` // 唯一ID
	CustomerId          string          `json:"customer_id" gorm:"customer_id"`                       // 客户ID
	EntityId            string          `json:"entity_id" gorm:"entity_id"`                           // 实体ID（PERSONAL,BUSINESS）
	BusinessCode        int16           `json:"business_code" gorm:"business_code"`                   // 1000=Banking，2000=Foreign，3000=Acquiring，4000=Issuing，5000=Ramp
	Info                string          `json:"info" gorm:"info"`                                     // Cra字段详情
	AssessmentScore     decimal.Decimal `json:"assessment_score" gorm:"assessment_score"`             // 分数
	AssessmentGrade     int8            `json:"assessment_grade" gorm:"assessment_grade"`             // 等级：0=prohibit, 1=high, 2=medium, 3=low
	CreateTime          time.Time       `json:"create_time" gorm:"create_time"`
	Uid                 string          `json:"uid" gorm:"uid"`
	Nickname            string          `json:"nickname" gorm:"nickname"`
	Email               string          `json:"email" gorm:"email"`
}

// TableName 表名称
func (*RmsCraScoreReference) TableName() string {
	return "rms_cra_score_reference"
}
