package model

import "time"

// RmsCraParameterVal Cra—参考列表
type RmsCraParameterVal struct {
	ID             int64     `json:"-" gorm:"id"`                              // 主键
	ParamValId     string    `json:"param_val_id" gorm:"param_val_id"`         // 参考id
	CraFrameworkId string    `json:"cra_framework_id" gorm:"cra_framework_id"` // 唯一ID
	ParamValName   string    `json:"param_val_name" gorm:"param_val_name"`     // 参考名
	Description    string    `json:"description" gorm:"description"`           // 描述
	NoInformation  int8      `json:"no_information" gorm:"no_information"`     // 无数据结果：0=prohibit, 1=high, 2=medium, 3=low
	HitFactor      string    `json:"hit_factor" gorm:"hit_factor"`             // 命中结果：JSON参数
	NoHitMatch     int8      `json:"no_hit_match" gorm:"no_hit_match"`         // 没命中结果：0=prohibit, 1=high, 2=medium, 3=low
	Status         int8      `json:"status" gorm:"status"`                     // 状态：1=active, 0=默认, -1=inactive
	CreateTime     time.Time `json:"create_time" gorm:"create_time"`
	UpdateTime     time.Time `json:"update_time" gorm:"update_time"`
}

// TableName 表名称
func (*RmsCraParameterVal) TableName() string {
	return "rms_cra_parameter_val"
}
