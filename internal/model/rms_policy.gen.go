package model

import (
	"encoding/json"
	"fmt"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"time"
)

const TableNameRmsPolicy = "rms_policy"

// RmsPolicy 风控规则策略（即规则组）
type RmsPolicy struct {
	ID           int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                                                                    // 主键
	PolicyGroup  string     `gorm:"column:policy_group;type:varchar(32);not null;index:idx_pg,priority:1;default:CMF;comment:策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup" json:"policy_group"` // 策略组，可选值为在RMS_FILTER_FIELD定义的枚举字段-rulePolicyGroup
	PolicyNo     string     `gorm:"column:policy_no;type:varchar(32);not null;comment:策略编号" json:"policy_no"`                                                                                                  // 策略编号
	PolicyName   *string    `gorm:"column:policy_name;type:varchar(64);comment:规则组名称" json:"policy_name"`                                                                                                     // 规则组名称
	CheckPoint   string     `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp,priority:1;comment:检查点" json:"check_point"`                                                                       // 检查点
	Filters      *string    `gorm:"column:filters;type:varchar(1024);comment:策略匹配的过滤条件，需事先设置检查点的过滤字段" json:"filters"`                                                                        // 策略匹配的过滤条件，需事先设置检查点的过滤字段
	WhiteListIDs string     `gorm:"column:white_list_ids;type:varchar(225);comment:白名单id列表json" json:"white_list_ids"`                                                                                        // 策略匹配的过滤条件，需事先设置检查点的过滤字段
	PolicyRules  string     `gorm:"column:policy_rules;type:varchar(1024);not null;comment:策略中需要执行的规则id列表json" json:"policy_rules"`                                                                    // 策略中需要执行的规则id列表json
	Status       string     `gorm:"column:status;type:varchar(16);not null;comment:状态，Online/Offline" json:"status"`                                                                                             // 状态，Online/Offline
	Memo         *string    `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                                                                                         // 备注
	SyncedAt     *time.Time `gorm:"column:synced_at;type:datetime;comment:同步时间" json:"synced_at"`                                                                                                              // 同步时间
	CreatedAt    *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                                                         // 创建时间
	UpdatedAt    *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                                                         // 更新时间
}

// TableName RmsPolicy's table name
func (*RmsPolicy) TableName() string {
	return TableNameRmsPolicy
}

type RmsPolicyFilters map[string][]string

func (m *RmsPolicy) GetFilters() (RmsPolicyFilters, error) {
	data := RmsPolicyFilters{}
	if m.Filters == nil {
		return data, nil
	}
	err := json.Unmarshal([]byte(*m.Filters), &data)
	if err != nil {
		return nil, er.WSEF(err, zap.String("Filters", fmt.Sprintln(m.Filters)))
	}
	return data, nil
}

func (m *RmsPolicy) SetFilters(data RmsPolicyFilters) error {
	v, err := json.Marshal(data)
	if err != nil {
		return er.WSEF(err)
	}
	f := string(v)
	m.Filters = &f
	return nil
}
