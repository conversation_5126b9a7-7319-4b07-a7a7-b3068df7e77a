package model

import "time"

// RmsFileExtra undefined
type RmsFileExtra struct {
	ID             int64     `json:"id" gorm:"id"`                             // 主键
	FileId         string    `json:"file_id" gorm:"file_id"`                   // uuid
	FileName       string    `json:"file_name" gorm:"file_name"`               // 文件名
	FileType       int64     `json:"file_type" gorm:"file_type"`               // 1000 event
	CreatorId      string    `json:"creator_id" gorm:"creator_id"`             // 创建人id
	CreatorName    string    `json:"creator_name" gorm:"creator_name"`         // 创建人名
	CreateTime     time.Time `json:"create_time" gorm:"create_time"`           // 创建时间
	UpdateTime     time.Time `json:"update_time" gorm:"update_time"`           // 修改时间（文件生成成功时间）
	ExpireTime     time.Time `json:"expire_time" gorm:"expire_time"`           // 文件过期时间默认30天
	FileParam      string    `json:"file_param" gorm:"file_param"`             // 文件参数
	FileStatus     int8      `json:"file_status" gorm:"file_status"`           // -1=failed, 0=pending,1=success
	StartEventTime time.Time `json:"start_event_time" gorm:"start_event_time"` // 开始时间
	EndEventTime   time.Time `json:"end_event_time" gorm:"end_event_time"`     // 结束时间
}

// TableName 表名称
func (*RmsFileExtra) TableName() string {
	return "rms_file_extra"
}
