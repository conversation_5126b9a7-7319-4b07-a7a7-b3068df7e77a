package model

import "time"

// RmsFileS3 undefined
type RmsFileS3 struct {
	ID         int64     `json:"id" gorm:"id"`                   // 主键
	CreateTime time.Time `json:"create_time" gorm:"create_time"` // 创建时间
	FileName   string    `json:"file_name" gorm:"file_name"`
	FileFormat string    `json:"file_format" gorm:"file_format"` // 文件类型 csv excel pdf
	FilePath   string    `json:"file_path" gorm:"file_path"`     // 文件存储路径
	Region     string    `json:"region" gorm:"region"`           // 文件区域
	ExtraId    string    `json:"extra_id" gorm:"extra_id"`       // 唯一关联ID
}

// TableName 表名称
func (*RmsFileS3) TableName() string {
	return "rms_file_s3"
}
