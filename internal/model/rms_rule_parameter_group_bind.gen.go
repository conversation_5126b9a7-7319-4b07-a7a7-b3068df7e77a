package model

import (
	"time"
)

const TableNameRmsRuleParameterGroupBind = "rms_rule_parameter_group_bind"

// RmsRuleParameterGroupBind 规则参数组绑定关系
type RmsRuleParameterGroupBind struct {
	ID         int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                // 主键
	GroupID    int64      `gorm:"column:group_id;type:bigint;not null;index:idx_tid_gid,priority:2;comment:参数组id" json:"group_id"`         // 参数组id
	CheckPoint *string    `gorm:"column:check_point;type:varchar(256);comment:检查点" json:"check_point"`                                     // 检查点
	TargetID   string     `gorm:"column:target_id;type:varchar(64);not null;index:idx_tid_gid,priority:1;comment:绑定目标id" json:"target_id"` // 绑定目标id
	TargetType *string    `gorm:"column:target_type;type:varchar(32);comment:绑定目标类型，MERCHANT-商户，CHANNEL-渠道" json:"target_type"`            // 绑定目标类型，MERCHANT-商户，CHANNEL-渠道
	Able       int32      `gorm:"column:able;type:int;not null;comment:状态，1-有效，0-失效" json:"able"`                                          // 状态，1-有效，0-失效
	CreatedAt  *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`       // 创建时间
	UpdatedAt  *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`       // 更新时间
}

// TableName RmsRuleParameterGroupBind's table name
func (*RmsRuleParameterGroupBind) TableName() string {
	return TableNameRmsRuleParameterGroupBind
}
