package model

import (
	"encoding/json"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"time"
)

const TableNameRmsFilterField = "rms_filter_field"

// RmsFilterField 事件过滤字段配置（亦即枚举字段配置）
type RmsFilterField struct {
	ID         int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                   // 主键
	FieldName  string     `gorm:"column:field_name;type:varchar(16);not null;uniqueIndex:idx_fname,priority:1;comment:字段名" json:"field_name"` // 字段名
	FieldEnum  string     `gorm:"column:field_enum;type:varchar(1024);not null;comment:枚举类型列表的json，各枚举由编码和名称构成" json:"field_enum"`            // 枚举类型列表的json，各枚举由编码和名称构成
	CheckPoint string     `gorm:"column:check_point;type:varchar(256);not null;comment:检查点" json:"check_point"`                               // 检查点
	Memo       *string    `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                        // 备注
	CreatedAt  *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`          // 创建时间
	UpdatedAt  *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`          // 更新时间
}

// TableName RmsFilterField's table name
func (*RmsFilterField) TableName() string {
	return TableNameRmsFilterField
}

type RmsFilterFieldEnum []map[string]string

func (m RmsFilterFieldEnum) AllCode() []string {
	li := []string{}
	for _, v := range m {
		li = append(li, v["code"])
	}
	return li
}

func (m *RmsFilterField) GetFieldEnum() (RmsFilterFieldEnum, error) {
	data := RmsFilterFieldEnum{}
	err := json.Unmarshal([]byte(m.FieldEnum), &data)
	return data, er.WSEF(err, zap.String("FieldEnum", m.FieldEnum))
}

func (m *RmsFilterField) SetFieldEnum(data RmsFilterFieldEnum) error {
	f, err := json.Marshal(data)
	if err != nil {
		return er.WSEF(err)
	}
	m.FieldEnum = string(f)
	return nil
}
