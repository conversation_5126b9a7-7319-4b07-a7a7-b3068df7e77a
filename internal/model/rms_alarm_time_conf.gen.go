package model

import (
	"time"
)

const TableNameRmsAlarmTimeConf = "rms_alarm_time_conf"

// RmsAlarmTimeConf 风控预警时间配置
type RmsAlarmTimeConf struct {
	ID         int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`           // 主键
	ResultCode string    `gorm:"column:result_code;type:varchar(32);not null;comment:ResultCode" json:"result_code"` // ResultCode
	Status     int32     `gorm:"column:status;type:int;not null;comment:状态" json:"status"`                           // 状态
	StartTime  *string   `gorm:"column:start_time;type:varchar(64);comment:开始时间" json:"start_time"`                  // 开始时间
	EndTime    int32     `gorm:"column:end_time;type:int;not null;comment:截止时间" json:"end_time"`                     // 截止时间
	OpenID     *string   `gorm:"column:open_id;type:varchar(32);comment:操作员" json:"open_id"`                         // 操作员
	CreatedAt  time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`            // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`            // 更新时间
}

// TableName RmsAlarmTimeConf's table name
func (*RmsAlarmTimeConf) TableName() string {
	return TableNameRmsAlarmTimeConf
}
