package model

import (
	"time"
)

const TableNameRmsEventField = "rms_event_field"

type EventFieldType string

const (
	FieldTypeDate    EventFieldType = "Date"
	FieldTypeDouble  EventFieldType = "Double"
	FieldTypeInteger EventFieldType = "Integer"
	FieldTypeMoney   EventFieldType = "Money"
	FieldTypeString  EventFieldType = "String"
)

// RmsEventField 风险事件字段配置
type RmsEventField struct {
	ID                int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                   // 主键
	CheckPoint        string         `gorm:"column:check_point;type:varchar(16);not null;index:idx_ckp,priority:1;comment:归属检查点（编码）" json:"check_point"` // 归属检查点（编码）
	FieldName         string         `gorm:"column:field_name;type:varchar(16);not null;comment:字段名" json:"field_name"`                                  // 字段名
	InnerFieldName    *string        `gorm:"column:inner_field_name;type:varchar(16);comment:内部字段名" json:"inner_field_name"`                             // 内部字段名
	FieldType         EventFieldType `gorm:"column:field_type;type:varchar(16);not null;comment:字段类型，如String" json:"field_type"`                         // 字段类型，如String
	DefaultValue      *string        `gorm:"column:default_value;type:varchar(128);comment:默认值" json:"default_value"`                                    // 默认值
	Required          bool           `gorm:"column:required;type:tinyint(1);not null;comment:是否必填" json:"required"`                                      // 是否必填
	RequiredCondition string         `gorm:"column:required_condition;type:varchar(255);not null;comment:必填条件，依赖于其它字段" json:"required_condition"`        // 必填条件，依赖于其它字段
	Memo              *string        `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                        // 备注
	CreatedAt         *time.Time     `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`          // 创建时间
	UpdatedAt         *time.Time     `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`          // 更新时间
}

// TableName RmsEventField's table name
func (*RmsEventField) TableName() string {
	return TableNameRmsEventField
}
