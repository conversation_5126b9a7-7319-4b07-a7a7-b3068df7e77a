package model

import (
	"time"
)

const TableNameRmsRiskLog = "rms_risk_logs"

// RmsRiskLog 风控日志
type RmsRiskLog struct {
	ID           int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                           // 主键
	PartnerID    string    `gorm:"column:partner_id;type:varchar(16);not null;index:idx_pid,priority:1;comment:商户号" json:"partner_id"` // 商户号
	ReqIP        string    `gorm:"column:req_ip;type:varchar(32);not null;comment:请求IP" json:"req_ip"`                                 // 请求IP
	ProjectName  string    `gorm:"column:project_name;type:varchar(32);not null;comment:工程名" json:"project_name"`                      // 工程名
	Alias_       string    `gorm:"column:alias;type:varchar(16);not null;comment:拦截别名 sql,xss,signature" json:"alias"`                 // 拦截别名 sql,xss,signature
	EventExplain *string   `gorm:"column:event_explain;type:varchar(64);comment:事件说明" json:"event_explain"`                            // 事件说明
	OriParams    string    `gorm:"column:ori_params;type:varchar(256);not null;comment:源参数" json:"ori_params"`                         // 源参数
	IllegalParam int32     `gorm:"column:illegal_param;type:int;not null;comment:非法参数. 1-是，0-否" json:"illegal_param"`                  // 非法参数. 1-是，0-否
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`                            // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;not null;comment:更新时间" json:"updated_at"`                            // 更新时间
}

// TableName RmsRiskLog's table name
func (*RmsRiskLog) TableName() string {
	return TableNameRmsRiskLog
}
