package model

import "time"

// RmsCraFramework Cra—框架
type RmsCraFramework struct {
	ID                       int64     `json:"-" gorm:"id"`                                                  // 主键
	CraFrameworkId           string    `json:"cra_framework_id" gorm:"cra_framework_id"`                     // 唯一ID
	CraType                  string    `json:"cra_type" gorm:"cra_type"`                                     // cra业务：KYC，KYB
	LowHighestPercentage     float64   `json:"low_highest_percentage" gorm:"low_highest_percentage"`         // low最高分数（百分比）
	LowLowestPercentage      float64   `json:"low_lowest_percentage" gorm:"low_lowest_percentage"`           // low最低分数（百分比）
	LowGradeScore            int64     `json:"low_grade_score" gorm:"low_grade_score"`                       // low分数
	MediumHighestPercentage  float64   `json:"medium_highest_percentage" gorm:"medium_highest_percentage"`   // medium最高分数（百分比）
	MediumLowestPercentage   float64   `json:"medium_lowest_percentage" gorm:"medium_lowest_percentage"`     // medium最低分数（百分比）
	MediumGradeScore         int64     `json:"medium_grade_score" gorm:"medium_grade_score"`                 // medium分数
	HighHighestPercentage    float64   `json:"high_highest_percentage" gorm:"high_highest_percentage"`       // high最高分数（百分比）
	HighLowestPercentage     float64   `json:"high_lowest_percentage" gorm:"high_lowest_percentage"`         // high最低分数（百分比）
	HighGradeScore           int64     `json:"high_grade_score" gorm:"high_grade_score"`                     // high分数
	ProhibitLowestPercentage float64   `json:"prohibit_lowest_percentage" gorm:"prohibit_lowest_percentage"` // prohibit最低分数（百分比）
	Status                   int8      `json:"status" gorm:"status"`                                         // 状态：1=active, 0=默认, -1=inactive
	CreateTime               time.Time `json:"create_time" gorm:"create_time"`
	UpdateTime               time.Time `json:"update_time" gorm:"update_time"`
}

// TableName 表名称
func (*RmsCraFramework) TableName() string {
	return "rms_cra_framework"
}
