package model

import (
	"encoding/json"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"time"
)

const TableNameRmsCheckPoint = "rms_check_point"

// RmsCheckPoint 检查点
type RmsCheckPoint struct {
	ID                 int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                                       // 主键
	Code               string    `gorm:"column:code;type:varchar(16);not null;uniqueIndex:uidx_code,priority:1;comment:编码" json:"code"`                                  // 编码
	Label              string    `gorm:"column:label;type:varchar(16);not null;comment:名称" json:"label"`                                                                 // 名称
	FilterFields       *string   `gorm:"column:filter_fields;type:varchar(255);default:[];comment:过滤字段配置，区分更细化的业务数据（policy选取有关）" json:"filter_fields"` // 过滤字段配置，区分更细化的业务数据（policy选取有关）
	PreActions         *string   `gorm:"column:pre_actions;type:varchar(255);default:[];comment:事件预处理配置，如根据ip获取省市" json:"pre_actions"`                       // 事件预处理配置，如根据ip获取省市
	BusinessType       int64     `gorm:"column:business_type;type:int;comment:业务类型配置，用于对接入业务（事件）的细分" json:"business_type"`                               // 业务类型配置，用于对接入业务（事件）的细分
	VoucherConfigs     *string   `gorm:"column:voucher_configs;type:varchar(255);comment:凭证读取配置，不启用" json:"voucher_configs"`                                      // 凭证读取配置，不启用
	DefaultPkFieldName *string   `gorm:"column:default_pk_field_name;type:varchar(32);comment:默认主键字段，与filterField相关" json:"default_pk_field_name"`                // 默认主键字段，与filterField相关
	CheckDuplicate     *bool     `gorm:"column:check_duplicate;type:tinyint(1);default:1;comment:数据重复检查" json:"check_duplicate"`                                     // 数据重复检查
	Memo               *string   `gorm:"column:memo;type:varchar(64);comment:备注" json:"memo"`                                                                            // 备注
	AlwaysRun          int       `gorm:"column:always_run;type:tinyint(1);default:0" json:"always_run"`
	CreatedAt          time.Time `gorm:"created_at" json:"created_at"` // 创建时间
	UpdatedAt          time.Time `gorm:"updated_at" json:"updated_at"` // 更新时间
}

func (m *RmsCheckPoint) AddFilterField(f string) error {
	fli := []string{}
	if m.FilterFields != nil && *m.FilterFields != "" {
		if err := json.Unmarshal([]byte(*m.FilterFields), &fli); err != nil {
			return er.WSEF(err, zap.String("FilterFields", *m.FilterFields))
		}
	}
	fli = append(fli, f)
	flbyte, err := json.Marshal(fli)
	if err != nil {
		return er.WSEF(err, zap.Any("fli", fli))
	}
	t := string(flbyte)
	m.FilterFields = &t
	return nil
}

func (m *RmsCheckPoint) RemoveFilterField(f string) error {
	fli := []string{}
	if m.FilterFields != nil && *m.FilterFields != "" {
		if err := json.Unmarshal([]byte(*m.FilterFields), &fli); err != nil {
			return er.WSEF(err, zap.String("FilterFields", *m.FilterFields))
		}
	}
	newFli := []string{}
	for _, i := range fli {
		if f == i {
			continue
		}
		newFli = append(newFli, i)
	}
	flbyte, err := json.Marshal(newFli)
	if err != nil {
		return er.WSEF(err, zap.Any("newFli", newFli))
	}
	t := string(flbyte)
	m.FilterFields = &t
	return nil
}

// CheckPointBusinessConfig 接入点的业务配置
type CheckPointBusinessConfig struct {
	BusinessCode string `json:"businessCode" example:"AUTH" binding:"required"`
	// 主键字段
	PkFieldName string `json:"pkFieldName" example:"ip"`
	// 第二匹配条件
	SecondFieldName  string `json:"secondFieldName" example:"num"`
	SecondFieldValue string `json:"secondFieldValue" example:"zz"`
}

type RmsCheckPointBusinessTypes []*CheckPointBusinessConfig

// TableName RmsCheckPoint's table name
func (*RmsCheckPoint) TableName() string {
	return TableNameRmsCheckPoint
}
