package model

import (
	"time"
)

const TableNameRmsRuleParameterValue = "rms_rule_parameter_value"

// RmsRuleParameterValue 规则参数值
type RmsRuleParameterValue struct {
	ID          int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                                         // 主键
	ParamID     int64      `gorm:"column:param_id;type:bigint;not null;index:idx_pid,priority:1;comment:RMS_RULE_PARAMETER外键" json:"param_id"`       // RMS_RULE_PARAMETER外键
	GroupID     int64      `gorm:"column:group_id;type:bigint;not null;index:idx_gid,priority:1;comment:RMS_RULE_PARAMETER_GROUP外键" json:"group_id"` // RMS_RULE_PARAMETER_GROUP外键
	ParamKey    string     `gorm:"column:param_key;type:varchar(16);comment:健，非键值对类型可空" json:"param_key"`                                            // 健，非键值对类型可空
	ParamValue  string     `gorm:"column:param_value;type:varchar(1024);not null;comment:值" json:"param_value"`                                      // 值
	Description *string    `gorm:"column:description;type:varchar(256);comment:描述" json:"description"`                                               // 描述
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                // 创建时间
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                // 更新时间
}

// TableName RmsRuleParameterValue's table name
func (*RmsRuleParameterValue) TableName() string {
	return TableNameRmsRuleParameterValue
}
