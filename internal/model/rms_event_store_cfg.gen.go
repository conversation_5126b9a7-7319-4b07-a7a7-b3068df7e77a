package model

import (
	"time"
)

const TableNameRmsEventStoreCfg = "rms_event_store_cfg"

// RmsEventStoreCfg 事件存储配置
type RmsEventStoreCfg struct {
	ID              int64      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:主键" json:"id"`                             // 主键
	CheckPoint      string     `gorm:"column:check_point;type:varchar(32);not null;index:idx_ckp,priority:1;comment:检查点" json:"check_point"` // 检查点
	Persistent      *bool      `gorm:"column:persistent;type:tinyint(1);not null;default:1;comment:是否存储" json:"persistent"`                  // 是否存储
	SendToScheduler bool       `gorm:"column:send_to_scheduler;type:tinyint(1);not null;comment:暂不使用" json:"send_to_scheduler"`              // 暂不使用
	SaveToBusiness  bool       `gorm:"column:save_to_business;type:tinyint(1);not null;comment:暂不使用" json:"save_to_business"`                // 暂不使用
	SendToIntra     bool       `gorm:"column:send_to_intra;type:tinyint(1);not null;default:1;comment:是否发送到风控后台" json:"send_to_intra"`       // 是否发送到风控后台
	StrField1       *string    `gorm:"column:str_field1;type:varchar(32);comment:重要字符串类型字段1的字段名（建议主键）" json:"str_field1"`                    // 重要字符串类型字段1的字段名（建议主键）
	StrField2       *string    `gorm:"column:str_field2;type:varchar(32);comment:重要字符串类型字段2的字段名" json:"str_field2"`                          // 重要字符串类型字段2的字段名
	StrField3       *string    `gorm:"column:str_field3;type:varchar(32);comment:重要字符串类型字段3的字段名" json:"str_field3"`                          // 重要字符串类型字段3的字段名
	StrField4       *string    `gorm:"column:str_field4;type:varchar(32);comment:重要字符串类型字段4的字段名" json:"str_field4"`                          // 重要字符串类型字段4的字段名
	StrField5       *string    `gorm:"column:str_field5;type:varchar(32);comment:重要字符串类型字段5的字段名" json:"str_field5"`                          // 重要字符串类型字段5的字段名
	NumField1       *string    `gorm:"column:num_field1;type:varchar(32);comment:重要数值类型字段1的字段名" json:"num_field1"`                           // 重要数值类型字段1的字段名
	NumField2       *string    `gorm:"column:num_field2;type:varchar(32);comment:重要数值类型字段2的字段名" json:"num_field2"`                           // 重要数值类型字段2的字段名
	CreatedAt       *time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`    // 创建时间
	UpdatedAt       *time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`    // 更新时间
}

// TableName RmsEventStoreCfg's table name
func (*RmsEventStoreCfg) TableName() string {
	return TableNameRmsEventStoreCfg
}
