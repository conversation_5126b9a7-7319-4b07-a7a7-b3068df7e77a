{"language": "go", "projectName": "uqpay-risk-portal-api", "architecture": {"description": "分层架构，新功能主要基于v2版本实现", "layers": [{"name": "API", "description": "处理HTTP请求、路由和控制器", "path": "api/", "components": [{"name": "routers", "description": "API路由定义, 按版本(v1/v2)组织，新功能主要在v2中实现"}, {"name": "handle", "description": "请求处理逻辑, 按版本(v1/v2)组织，新功能主要在handlev2中实现，负责业务性数据校验"}, {"name": "server", "description": "服务器启动和初始化逻辑"}, {"name": "cron", "description": "定时任务"}, {"name": "engine", "description": "服务引擎配置"}]}, {"name": "Internal", "description": "业务核心逻辑层", "path": "internal/", "components": [{"name": "service", "description": "业务服务层, 实现业务逻辑，新功能主要在servicev2中实现，负责逻辑性数据校验"}, {"name": "domain", "description": "领域模型, 核心业务概念和规则", "subComponents": [{"name": "request_parameters", "description": "定义接收前端请求的结构体，包含业务校验规则"}, {"name": "response_parameters", "description": "定义返回给前端的响应结构体"}]}, {"name": "model", "description": "数据模型定义", "subComponents": [{"name": "modelv2", "description": "定义数据库表对应的结构体及其自身方法，仅处理结构体内部数据转换"}]}, {"name": "data", "description": "数据访问层, 实现数据持久化，新功能主要在datav2中实现，负责数据库表的增删改查操作"}, {"name": "repo", "description": "数据仓库, 处理数据存储和检索"}, {"name": "consts", "description": "常量定义"}]}, {"name": "Pkg", "description": "通用工具包和库", "path": "pkg/", "components": [{"name": "config", "description": "配置管理"}, {"name": "db", "description": "数据库连接和管理"}, {"name": "logger", "description": "日志管理"}, {"name": "jwt", "description": "JWT认证"}, {"name": "global", "description": "全局变量和常量"}, {"name": "version", "description": "版本信息"}, {"name": "tools", "description": "通用工具函数"}, {"name": "snowflake", "description": "雪花ID生成"}, {"name": "er", "description": "错误处理"}, {"name": "compress", "description": "压缩工具"}]}]}, "codeRules": {"requestParsing": {"jsonRequest": "使用request.GetRequestBodyJson[T]接收POST请求中的JSON数据", "queryRequest": "使用request.GetRequestQueryBind[T]接收GET请求中的URL查询参数"}, "naming": {"variables": "camelCase", "functions": "camelCase", "types": "PascalCase", "interfaces": "PascalCase", "constants": "ALL_CAPS", "packages": "lowercase"}, "layout": {"fileStructure": ["package declaration", "imports", "constants", "types", "global variables", "functions"], "importGroups": ["standard library", "third-party packages", "internal packages"]}, "requestStructure": {"listRequest": "列表请求结构体内嵌PageRequest结构体用于分页功能", "pageRequest": "PageRequest通常包含PageNum、PageSize和Option字段"}, "responseStructure": {"listResponse": "列表响应结构体命名为[实体]ListResp，包含Total和Items字段", "itemsType": "Items字段类型为[*[实体]List]，即结构体名去掉Resp后缀"}, "documentation": {"requiredForExported": true, "style": "// 函数说明", "examplePrefix": "// 例如:"}, "errorHandling": {"approach": "explicit error checking", "wrapErrors": true, "useCustomErrors": true, "datav2Layer": "使用er.ConvertDBError()处理数据库错误", "servicev2Layer": {"errorPropagation": "错误处理遵循'要么记录并返回nil，要么不记录直接返回错误'的原则，避免重复记录同一错误", "datav2Errors": "直接返回datav2层的错误，除了gorm.ErrRecordNotFound需使用errors.Is()特殊处理", "otherErrors": "使用er.[ErrorType].WithMsg().WithErr().WithStack()封装错误，不使用er.NewBusinessError", "validationErrors": "仅使用er.[ErrorType].WithMsg()，无需WithErr()和WithStack()"}, "errorTypes": "优先使用pkg/er/error_list.go中已定义的错误类型，相同模块相近错误可复用", "errorMessages": "确保返回给前端的错误信息有意义且面向用户友好，如'Data does not exist'而非'数据源不存在'"}, "validation": {"handleLayer": "业务性校验，如字段长度、格式等表面验证", "serviceLayer": "逻辑性校验，如数据一致性、业务规则等深层验证"}, "databaseOperations": {"query": "在servicev2层调用datav2层的查询方法，可传入条件函数进行过滤", "transaction": "使用db.Transaction构建闭包函数进行事务操作，所有数据库操作使用闭包函数生成的tx参数", "errorHandling": "在事务或匿名函数中，使用innerErr作为返回错误变量名，确保内部错误传递一致性"}, "dataLayerStructure": {"baseStruct": "继承Empty结构体", "initialization": "使用New[ModelName]函数实例化，返回对应的repo接口类型", "requiredMethods": ["TableName() - 返回对应的表名", "List() - 获取列表数据，支持过滤和分页", "First() - 获取单条记录", "Create() - 创建记录", "Update() - 更新记录", "Updates() - 批量更新记录", "Delete() - 删除记录", "BatchCreate() - 批量创建记录(可选)"]}, "handleStructure": {"baseStruct": "必须内嵌BaseHandle结构体", "responseSuccess": "使用response.ReturnSuccess(c, data, [message])返回成功结果", "responseFailure": "使用response.ReturnFailure(c, err)返回错误结果"}, "routerStructure": {"naming": "[功能名]Router，例如EventFieldRouter", "fields": "包含对应handlev2结构体的指针", "requiredMethods": ["Register(group *gin.RouterGroup) - 注册路由"], "routeFormat": "HTTP方法(POST/GET) + 操作(create/update/delete/list/retrieve)", "middlewares": "根据需要添加中间件，如权限验证、日志记录等"}, "swaggerAnnotation": {"methodCommentFormat": "// [方法名] [方法描述]\n// @Summary [简短描述]\n// @Tags risk-portal-api/[大类名称] [大类描述]/[具体功能名称] [功能描述]", "paramFormat": "// @Param [参数名] [参数位置] [参数类型] [是否必须] \"[参数描述]\"", "securityFormat": "// @Security x-auth-token\n// @Param x-auth-token header string true \"jwt token\"", "successFormat": "// @Success [状态码] {[返回类型]} [返回结构体] \"[返回描述]\"", "listResponseFormat": "// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.XXX]", "routerFormat": "// @Router [路由路径] [HTTP方法]"}}, "dependencies": {"database": [{"name": "gorm", "description": "ORM框架"}, {"name": "mysql", "description": "MySQL数据库驱动"}], "web": [{"name": "gin", "description": "Web框架"}], "utilities": [{"name": "viper", "description": "配置管理"}, {"name": "zap", "description": "日志管理"}, {"name": "jwt-go", "description": "JWT认证"}, {"name": "decimal", "description": "高精度数值计算"}, {"name": "uuid", "description": "UUID生成"}, {"name": "snowflake", "description": "分布式ID生成"}], "internal": [{"name": "uqpay-common-sdk", "description": "内部通用SDK"}, {"name": "uqpay-risk-sdk", "description": "风控SDK"}]}, "conventions": {"api": {"versioning": "URL path (v1, v2)，新功能主要在v2中实现", "responseFormat": {"success": {"code": 0, "message": "success", "data": {}}, "error": {"code": "[错误码]", "message": "[错误信息]"}}, "authentication": "JWT", "validation": "validator/v10"}, "database": {"migration": "手动SQL脚本", "naming": "snake_case", "tablePrefix": "rms_", "operations": {"query": "在datav2层定义查询方法，支持传入条件函数进行过滤", "create": "使用Repo的Create方法创建记录", "update": "使用Repo的Update方法更新记录", "delete": "使用Repo的Delete方法删除记录", "transaction": "使用db.Transaction方法包装事务操作"}, "dataLayer": {"structNaming": "Rms[ModelName]，例如RmsBlackWhiteList", "factoryFunction": "New[ModelName]，例如NewRmsBlackWhiteList", "methodParameters": {"context": "ctx context.Context", "session": "session *gorm.DB", "filter": "fn func(db *gorm.DB) *gorm.DB", "data": "对应的模型指针或map[string]interface{}"}}}, "logging": {"framework": "zap", "levels": ["debug", "info", "warn", "error", "panic"], "rotation": "lumberjack"}, "dataFlow": {"request": "API请求 -> handlev2(业务校验) -> servicev2(逻辑校验) -> data(数据操作) -> 返回结果", "response": "data返回数据 -> servicev2处理 -> 通过response_parameters结构体返回给前端"}, "router": {"grouping": "按功能分组", "naming": "使用 RESTful 风格命名路由，尽量避免在路径后添加 /create、/update 等动词后缀", "middleware": "使用middleware包中的中间件进行权限验证和日志记录", "registration": "在wire_gen.go中实例化和注册所有路由"}, "responseHandling": {"success": "使用response.ReturnSuccess返回成功结果", "failure": "使用response.ReturnFailure返回错误结果", "listResponse": "使用response_parameters.ListResponsePointer或response_parameters.ListResponse包装列表结果", "detailResponse": "直接构建响应结构体，而不是使用FromModel等转换方法", "errorMessages": "错误消息应面向用户友好，使用英文描述，如'Data does not exist'而非'数据源不存在'"}, "errorHandling": {"datav2Layer": "数据库错误通过er.ConvertDBError()转换为自定义错误", "servicev2Layer": "根据错误来源和类型使用不同的封装方式", "errorTypes": "在pkg/er/error_list.go中定义和复用错误类型", "errorLogging": "通过WithErr()和WithStack()记录错误详情，但不暴露给前端"}, "handleLayer": {"structBase": "handle结构体必须内嵌BaseHandle", "responseHandling": "使用统一的response.ReturnSuccess和response.HeadErr方法"}}, "buildProcess": {"tools": ["go build", "make"], "containerization": "<PERSON>er", "cicd": "GitLab CI"}, "linting": {"rules": ["go fmt", "go vet", "go lint"]}, "bestPractices": {"modelDesign": ["在数据模型中包含必要的错误信息字段，如FailReason，以便记录操作失败的具体原因", "模型字段命名应与JSON响应字段保持一致，或遵循明确的命名转换规则", "避免在模型中使用过多的转换方法，优先在service层直接构建响应结构体"], "requestResponseStructures": ["列表请求结构体内嵌PageRequest结构体处理分页功能", "列表响应结构体命名为[实体]ListResp，包含Total和Items字段", "Items字段类型为[*[实体]List]，即结构体名去掉Resp后缀", "servicev2层列表方法返回(total int64, res []*response_parameters.XXXList, err error)格式"], "security": ["使用JWT进行身份验证", "数据库连接使用参数化查询", "敏感配置通过环境变量注入"], "performance": ["使用连接池管理数据库连接", "实现数据缓存机制", "异步处理长时间运行的任务"], "maintenance": ["遵循模块化和分层设计", "编写单元测试确保代码质量", "新功能优先在v2版本中实现"], "validation": ["在request_parameters中定义请求数据结构及基础校验规则", "在handlev2中进行业务性校验(如字段长度、格式等)", "在servicev2中进行逻辑性校验(如数据一致性、业务规则等)", "使用response_parameters定义统一的返回数据结构"], "databaseOperations": ["查询操作在servicev2中调用datav2层的方法，可传入条件函数进行过滤", "增删改操作如需事务支持，使用db.Transaction构建闭包函数", "事务操作中，所有数据库操作使用闭包函数生成的tx参数", "批量操作优先使用BatchCreate、BatchUpdate等方法", "事务或匿名函数中，应定义innerErr作为返回错误变量，在函数内统一使用该变量接收和返回错误", "在编写where条件时，将多个条件拆分为独立的Where调用，以提高可读性和可维护性"], "dataLayerImplementation": ["datav2层结构体应继承Empty", "提供New[ModelName]函数实例化datav2对象", "实现TableName()方法返回对应的表名", "实现标准的CRUD方法(List、First、Create、Update、Updates、Delete等)", "所有数据库错误应通过er.ConvertDBError转换为自定义错误"], "handleImplementation": ["handle结构体必须内嵌BaseHandle", "成功响应使用response.ReturnSuccess(c, data, [message])方法", "错误响应使用response.ReturnFailure(c, err)方法", "处理请求前进行参数校验", "适当使用BaseHandle提供的辅助方法", "对于POST请求中的JSON数据，使用request.GetRequestBodyJson[T]方法接收", "对于GET请求中的URL查询参数，使用request.GetRequestQueryBind[T]方法接收"], "routerImplementation": ["创建结构体包含所有需要的handlev2结构体指针", "实现Register方法将路由注册到gin路由组", "使用适当的中间件处理权限验证和日志记录", "路由命名遵循RESTful风格，尽量避免在路径后添加动词后缀", "使用HTTP方法区分操作类型：POST用于创建，PUT用于更新，DELETE用于删除，GET用于查询", "在wire_gen.go中完成所有层级的实例化和依赖注入"], "swaggerDocumentation": ["所有API接口必须添加Swagger注释", "Tags必须以'risk-portal-api/'为前缀，后跟大类名称和具体功能名称", "每个接口必须包含Summary、Tags、Produce、Accept、Security等基本信息", "返回成功状态使用@Success注释，格式为状态码+返回类型+返回结构体+描述", "列表接口返回使用response_parameters.ListResponsePointer或response_parameters.ListResponse泛型结构体"], "responseHandling": ["使用response.ReturnSuccess函数返回成功结果", "使用response.ReturnFailure函数返回错误结果", "列表类接口使用ListResponsePointer或ListResponse包装返回结果", "在ListResponsePointer中包含TotalNum、Items和TotalPage字段", "直接构建响应结构体，而不是使用FromModel等转换方法", "确保模型中包含必要的错误信息字段，如FailReason，以便向用户提供明确的错误原因"], "errorHandling": ["在datav2层使用er.ConvertDBError()处理所有数据库错误", "在servicev2层直接返回来自datav2层的错误，除了需要特殊处理的gorm.ErrRecordNotFound", "对于servicev2层内部产生的错误，使用er.[ErrorType].WithMsg().WithErr().WithStack()封装，不使用er.NewBusinessError", "对于参数校验等用户可修复的错误，仅使用er.[ErrorType].WithMsg()，不需要WithErr()和WithStack()", "错误信息应面向用户友好且有指导意义，如'Data does not exist'而非'数据源不存在'", "优先使用pkg/er/error_list.go中已定义的错误类型，按模块分组相近错误", "通过WithErr()记录原始错误，通过WithStack()记录堆栈信息，方便问题排查", "错误传递时遵循单一责任原则：要么在当前位置记录错误并返回nil，要么不记录直接将错误传递到上层", "避免在检查记录是否存在后再执行删除等操作，可以直接执行操作并处理返回的错误"]}, "filePatterns": {"requestParams": "internal/domain/request_parameters/*.go", "responseParams": "internal/domain/response_parameters/*.go", "service": {"v1": "internal/service/*_*.go", "v2": "internal/service/servicev2/*.go"}, "model": {"v1": "internal/model/*.go", "v2": "internal/model/modelv2/*.go"}, "handler": {"v1": "api/handle/*_*.go", "v2": "api/handle/handlev2/*.go"}, "router": {"v1": "api/routers/v1/*.go", "v2": "api/routers/v2/*.go"}, "repository": "internal/repo/*.go", "data": {"v1": "internal/data/*.go", "v2": "internal/data/datav2/*.go"}}, "versionStrategy": {"current": "v2", "description": "新功能和需求主要在v2版本中实现，v1版本主要用于维护旧功能"}, "layerResponsibilities": {"handlev2": "处理HTTP请求，进行业务性数据校验(如字段长度、格式等表面验证)，调用servicev2层处理业务逻辑", "servicev2": "实现核心业务逻辑，进行逻辑性数据校验(如数据一致性、业务规则等深层验证)，调用data层操作数据，列表方法返回(total int64, res []*response_parameters.XXXList, err error)格式", "request_parameters": "定义接收前端请求的结构体，包含业务校验规则和标签，列表请求结构体内嵌PageRequest", "response_parameters": "定义返回给前端的响应结构体，列表响应包含Total和Items字段，规范API返回格式", "data": "实现数据库操作，负责增删改查等基础数据操作", "modelv2": "定义数据库表对应的结构体，以及结构体自身的数据处理方法，不涉及外部数据源", "routerv2": "定义API路由，将请求映射到对应的handlev2方法"}, "codeExamples": {"requestParsing": {"jsonRequest": {"description": "接收POST请求中的JSON数据", "example": "var req, err = request.GetRequestBodyJson[request_parameters.GetIndicatorList](c)\nif err != nil {\n\tresponse.ReturnFailure(c, err)\n\treturn\n}"}, "queryRequest": {"description": "接收GET请求中的URL查询参数", "example": "var req, err = request.GetRequestQueryBind[request_parameters.GetBlackWhiteRecord](c)\nif err != nil {\n\tresponse.ReturnFailure(c, err)\n\treturn\n}"}}, "requestResponseStructures": {"listRequest": {"description": "列表请求结构体示例", "example": "type GetBlackWhiteList struct {\n\tPageRequest request_parameters.PageRequest `json:\"page_request\" binding:\"required\"`\n\tName string `json:\"name\" binding:\"-\"`\n\tType int `json:\"type\" binding:\"required\"`\n\tAccessPoints []string `json:\"access_points\" binding:\"-\"`\n\tRequiredFields []string `json:\"required_fields\" binding:\"-\"`\n}"}, "listResponse": {"description": "列表响应结构体示例", "example": "type DataSourceListResp struct {\n\tTotal int64 `json:\"total\"` // 总数\n\tItems []*DataSourceList `json:\"items\"` // 数据源列表\n}"}, "serviceListMethod": {"description": "服务层列表方法示例", "example": "func (s *BlackWhiteListService) List(ctx context.Context, req *request_parameters.GetBlackWhiteList) (total int64, res []*response_parameters.BlackWhiteList, err error) {\n\t// 数据库查询获取total和items\n\t// 处理数据并填充res\n\treturn\n}"}}, "databaseQuery": {"description": "在servicev2中查询数据库示例", "example": "count, err := s.DataSourceTableRepo.Count(ctx, func(db *gorm.DB) *gorm.DB {\n\treturn db.Where(\"data_source_id = ?\", req.DataSourceID).Where(\"name = ?\", req.Name)\n})"}, "databaseTransaction": {"description": "在servicev2中使用事务进行数据库操作示例", "example": "err = db.Transaction(c, nil, func(tx *gorm.DB) (innerErr error) {\n\t// 在事务中创建记录\n\tinnerErr = s.BlackWhiteListRepo.Create(c, tx, bwl)\n\tif innerErr != nil {\n\t\treturn\n\t}\n\t// 在事务中批量创建记录\n\tif len(fieldsModels) > 0 {\n\t\tinnerErr = s.BlackWhiteFieldRepo.BatchCreate(c, tx, fieldsModels)\n\t}\n\treturn\n})"}, "closureErrorHandling": {"description": "在事务或闭包函数中处理错误", "example": "err = someFunction(c, func() (innerErr error) {\n\t// 在闭包函数中的操作\n\tinnerErr = someOperation()\n\tif innerErr != nil {\n\t\treturn // 直接返回innerErr\n\t}\n\t// 继续其他操作\n\tinnerErr = anotherOperation()\n\treturn // 最后返回innerErr，可能是nil或错误值\n})"}, "dataLayerImplementation": {"newFunction": {"description": "datav2层的实例化函数", "example": "func NewRmsBlackWhiteList() repo.BlackWhiteListRepo {\n\treturn &RmsBlackWhiteList{}\n}"}, "tableNameMethod": {"description": "返回表名的方法", "example": "func (r *RmsBlackWhiteList) TableName() string {\n\treturn modelv2.TableNameRmsBlackWhiteList\n}"}, "listMethod": {"description": "获取列表数据的方法", "example": "func (r *RmsBlackWhiteList) List(ctx context.Context, defaultFilter *filter.DefaultFilter, fn func(db *gorm.DB) *gorm.DB, withTotal ...bool) (total int64, items []*modelv2.RmsBlackWhiteList, err error) {\n\ttx := r.FilterTime(defaultFilter, fn(global.DB.Model(&modelv2.RmsBlackWhiteList{}))).WithContext(ctx)\n\n\terr = r.FilterPage(defaultFilter, tx).Find(&items).Error\n\tif err != nil {\n\t\terr = er.ConvertDBError(err)\n\t\treturn\n\t}\n\tif len(withTotal) > 0 && withTotal[0] {\n\t\terr = er.ConvertD<PERSON>r(tx.Count(&total).Error)\n\t\treturn\n\t}\n\treturn\n}"}, "firstMethod": {"description": "获取单条记录的方法", "example": "func (r *RmsBlackWhiteList) First(ctx context.Context, fn func(db *gorm.DB) *gorm.DB) (*modelv2.RmsBlackWhiteList, error) {\n\tvar item modelv2.RmsBlackWhiteList\n\terr := er.Convert<PERSON>r(fn(global.DB.Model(&modelv2.RmsBlackWhiteList{}).WithContext(ctx)).First(&item).Error)\n\treturn &item, err\n}"}, "updateMethod": {"description": "更新记录的方法", "example": "func (r *RmsBlackWhiteList) Update(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data *modelv2.RmsBlackWhiteList) error {\n\treturn db.Transaction(ctx, session, func(tx *gorm.DB) error {\n\t\treturn er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteList{})).Updates(data).Error)\n\t})\n}"}, "updatesMethod": {"description": "批量更新记录的方法", "example": "func (r *RmsBlackWhiteList) Updates(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB, data map[string]interface{}) error {\n\treturn db.Transaction(ctx, session, func(tx *gorm.DB) error {\n\t\treturn er.ConvertDBError(fn(tx.Model(&modelv2.RmsBlackWhiteList{})).Updates(data).Error)\n\t})\n}"}, "deleteMethod": {"description": "删除记录的方法", "example": "func (r *RmsFilterField) Delete(ctx context.Context, session *gorm.DB, fn func(db *gorm.DB) *gorm.DB) error {\n\treturn db.Transaction(ctx, session, func(tx *gorm.DB) (innerErr error) {\n\t\treturn er.ConvertDBError(fn(tx.Model(&modelv2.RmsFilterField{})).Delete(&modelv2.RmsFilterField{}).Error)\n\t})\n}"}}, "handleImplementation": {"handleStruct": {"description": "handle结构体定义", "example": "type BlackWhiteListHandle struct {\n\tBaseHandle\n\tBlackWhiteListService  *servicev2.BlackWhiteListService\n\tBlackWhiteItemService  *servicev2.BlackWhiteItemService\n\tBlackWhiteAuditService *servicev2.BlackWhiteAuditService\n}"}, "successResponse": {"description": "成功响应示例", "example": "response.ReturnSuccess(c, response_parameters.NormalCreateResponse{\n\tId: id,\n})"}, "failureResponse": {"description": "错误响应示例", "example": "if response.ReturnFailure(c, err) {\n\treturn\n}"}}, "routerImplementation": {"routerStruct": {"description": "路由结构体定义", "example": "type EventFieldRouter struct {\n\tEventFieldHandle *handlev2.EventFieldHandle\n}"}, "registerMethod": {"description": "路由注册方法", "example": "func (a *EventFieldRouter) Register(group *gin.RouterGroup) {\n\tg := group.Group(\"/event_field\")\n\tg.POST(\"\", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, \"Create event_field\"), a.EventFieldHandle.Create)\n\tg.PUT(\"\", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, \"UPDATE event_field\"), a.EventFieldHandle.Update)\n\tg.DELETE(\"/:id\", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, \"DELETE event_field\"), a.EventFieldHandle.Delete)\n\tg.GET(\"\", a.EventFieldHandle.List)\n\tg.GET(\"/:id\", a.EventFieldHandle.Retrieve)\n\tg.POST(\"/check_next\", a.EventFieldHandle.CheckNext)\n}"}, "wireGenExample": {"description": "在wire_gen.go中注册路由", "example": "// V2Router初始化代码\neventFieldRouterV2 := &v3.EventFieldRouter{EventFieldHandle: eventFieldHandleV2}\n\n// 添加到路由集合\nroutersV1 := engine.NewRouters().\n\tSetV1Routers(...).\n\tSetV2Routers(..., eventFieldRouterV2, ...)"}}, "swaggerAnnotation": {"normalMethod": {"description": "普通方法的Swagger注释", "example": "// CreateBlackWhiteList 创建黑白名单\n// @Summary Create BlackWhiteList 创建黑白名单\n// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理\n// @Produce json\n// @Accept json\n// @Security x-auth-token\n// @Param x-auth-token header string true \"jwt token\"\n// @Param data body request_parameters.CreateBlackWhiteRecord true \"Request body\"\n// @Success 200 {object} response_parameters.NormalCreateResponse \"黑白名单保存成功\"\n// @Router /api/v2/black_white/record [post]"}, "listMethod": {"description": "列表方法的Swagger注释", "example": "// GetBlackWhiteList 获取黑白名单列表\n// @Summary Get BlackWhiteList 获取黑白名单列表\n// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理\n// @Produce json\n// @Accept json\n// @Security x-auth-token\n// @Param x-auth-token header string true \"jwt token\"\n// @Param data body request_parameters.GetBlackWhiteList true \"Request body\"\n// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.BlackWhiteList]\n// @Router /api/v2/black_white/list [post]"}, "listResponse": {"description": "列表响应示例", "example": "response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.BlackWhiteList]{\n\tTotalNum:  total,\n\tItems:     res,\n\tTotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.<PERSON>um, req.PageSize).PageSize))),\n})"}}, "errorHandling": {"datav2Layer": {"description": "datav2层处理数据库错误", "example": "err := er.Convert<PERSON><PERSON>r(fn(global.DB.Model(&modelv2.RmsBlackWhiteList{}).WithContext(ctx)).First(&item).Error)"}, "servicev2Layer": {"datav2Errors": {"description": "servicev2层处理datav2层返回的错误", "example": "// 处理First返回的记录不存在错误\nif errors.Is(err, gorm.ErrRecordNotFound) {\n\treturn nil, er.NotFound.WithMsg(\"Data does not exist.\")\n}\n// 其他datav2错误直接返回\nif err != nil {\n\treturn nil, err\n}"}, "otherErrors": {"description": "servicev2层处理内部错误", "example": "return nil, er.Internal.WithMsg(\"Database exception\").WithErr(err).WithStack()"}, "validationErrors": {"description": "servicev2层处理参数校验错误", "example": "return er.AlreadyExists.WithMsg(\"The current data source already exists.\")"}}, "errorPropagation": {"description": "错误处理示例 - 记录并返回nil", "example": "if err != nil {\n\twlog.Error(\"failed to process data\", zap.Error(err))\n\treturn nil\n}"}, "errorPassthrough": {"description": "错误处理示例 - 不记录直接返回", "example": "if err != nil {\n\treturn nil, err\n}"}, "deleteOperation": {"description": "删除操作的错误处理", "example": "err := s.DataSourceRepo.Delete(ctx, nil, func(db *gorm.DB) *gorm.DB {\n\treturn db.Where(\"id = ?\", id)\n})\nif err != nil {\n\treturn er.Internal.WithMsg(\"Database exception\").WithErr(err)\n}\nreturn nil"}}}}