#!/bin/bash

# 从 DEPLOYMENT_GROUP_NAME 确定环境
case ${DEPLOYMENT_GROUP_NAME} in
  "develop-group") export ENVIRONMENT="develop" ;;
  "testing-group") export ENVIRONMENT="testing" ;;
  "sandbox-group") export ENVIRONMENT="sandbox" ;;
  "release-group") export ENVIRONMENT="release" ;;
  *) 
    echo "Error: Unknown deployment group ${DEPLOYMENT_GROUP_NAME}" 
    exit 1 
    ;;
esac

echo "Determined environment: ${ENVIRONMENT}"

echo "Stopping uqpay-risk-admincp-api for ${ENVIRONMENT} environment..."

# 切换到应用目录
cd /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api

# 检查PID文件是否存在
if [ -f ./uqpay-risk-admincp-api.pid ]; then
    PID=$(cat ./uqpay-risk-admincp-api.pid)
    if ps -p $PID > /dev/null; then
        echo "Stopping process $PID"
        kill $PID
        sleep 5
        if ps -p $PID > /dev/null; then
            echo "Process did not stop gracefully. Force killing."
            kill -9 $PID
        fi
    else
        echo "Process $PID not found. It may have already stopped."
    fi
    rm -rf ./uqpay-risk-admincp-api.pid
else
    echo "PID file not found. Application may not be running."
fi

echo "Stop script completed"
