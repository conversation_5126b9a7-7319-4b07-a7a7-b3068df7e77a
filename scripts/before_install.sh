#!/bin/bash

# 从 DEPLOYMENT_GROUP_NAME 确定环境
case ${DEPLOYMENT_GROUP_NAME} in
  "develop-group") export ENVIRONMENT="develop" ;;
  "testing-group") export ENVIRONMENT="testing" ;;
  "sandbox-group") export ENVIRONMENT="sandbox" ;;
  "release-group") export ENVIRONMENT="release" ;;
  *) 
    echo "Error: Unknown deployment group ${DEPLOYMENT_GROUP_NAME}" 
    exit 1 
    ;;
esac

echo "Determined environment: ${ENVIRONMENT}"

echo "Preparing for uqpay-risk-admincp-api installation in ${ENVIRONMENT} environment..."

# 创建必要的目录结构
sudo mkdir -p /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api
sudo mkdir -p /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api/logs
sudo mkdir -p /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api/configs

# 设置适当的权限
sudo chown -R root:root /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api

echo "Preparation completed for ${ENVIRONMENT} environment"
