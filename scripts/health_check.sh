#!/bin/bash

# 从 DEPLOYMENT_GROUP_NAME 确定环境
case ${DEPLOYMENT_GROUP_NAME} in
  "develop-group") export ENVIRONMENT="develop" ;;
  "testing-group") export ENVIRONMENT="testing" ;;
  "sandbox-group") export ENVIRONMENT="sandbox" ;;
  "release-group") export ENVIRONMENT="release" ;;
  *) 
    echo "Error: Unknown deployment group ${DEPLOYMENT_GROUP_NAME}" 
    exit 1 
    ;;
esac

echo "Determined environment: ${ENVIRONMENT}"

echo "Performing health check for uqpay-risk-admincp-api in ${ENVIRONMENT} environment..."

# 切换到应用目录
cd /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api

# 检查PID文件
if [ ! -f ./uqpay-risk-admincp-api.pid ]; then
    echo "PID file not found. Application may not be running."
    exit 1
fi

PID=$(cat ./uqpay-risk-admincp-api.pid)
if ! ps -p $PID > /dev/null; then
    echo "Process with PID $PID not found. Application may have crashed."
    exit 1
fi

# 这里可以添加更多的健康检查逻辑
# 例如，检查应用的HTTP端点
# curl -f http://localhost:8080/health || exit 1

echo "Health check passed for ${ENVIRONMENT} environment"
