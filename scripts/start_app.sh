#!/bin/bash

# 从 DEPLOYMENT_GROUP_NAME 确定环境
case ${DEPLOYMENT_GROUP_NAME} in
  "develop-group") export ENVIRONMENT="develop" ;;
  "testing-group") export ENVIRONMENT="testing" ;;
  "sandbox-group") export ENVIRONMENT="sandbox" ;;
  "release-group") export ENVIRONMENT="release" ;;
  *) 
    echo "Error: Unknown deployment group ${DEPLOYMENT_GROUP_NAME}" 
    exit 1 
    ;;
esac

echo "Determined environment: ${ENVIRONMENT}"

echo "Starting uqpay-risk-admincp-api for ${ENVIRONMENT} environment..."

# 创建环境特定的目录
ENVIRONMENT_DIR="/opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api"
mkdir -p "$ENVIRONMENT_DIR"


# 复制文件到环境特定的目录
cp -R /opt/source/${ENVIRONMENT}/uqpay-risk-admincp-api/* "$ENVIRONMENT_DIR/"

# 切换到环境特定的目录
cd "$ENVIRONMENT_DIR"

# 确保日志目录存在
mkdir -p logs

# 启动应用，使用对应的环境参数
nohup ./uqpay-risk-admincp-api -env=${ENVIRONMENT} > ./logs/output.log 2>&1 &

# 保存PID到文件中
echo $! > ./uqpay-risk-admincp-api.pid

echo "uqpay-risk-admincp-api started with PID: $!"
