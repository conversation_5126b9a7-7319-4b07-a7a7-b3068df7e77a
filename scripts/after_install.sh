#!/bin/bash

# 从 DEPLOYMENT_GROUP_NAME 确定环境
case ${DEPLOYMENT_GROUP_NAME} in
  "develop-group") export ENVIRONMENT="develop" ;;
  "testing-group") export ENVIRONMENT="testing" ;;
  "sandbox-group") export ENVIRONMENT="sandbox" ;;
  "release-group") export ENVIRONMENT="release" ;;
  *) 
    echo "Error: Unknown deployment group ${DEPLOYMENT_GROUP_NAME}" 
    exit 1 
    ;;
esac

echo "Determined environment: ${ENVIRONMENT}"

echo "Configuring uqpay-risk-admincp-api for ${ENVIRONMENT} environment..."

# 切换到应用目录
cd /opt/www/${ENVIRONMENT}/uqpay-risk-admincp-api

# 设置执行权限23
chmod +x ./uqpay-risk-admincp-api

# 确保配置文件就绪
if [ ! -f "./configs/${ENVIRONMENT}_config.yml" ]; then
    echo "Config file for ${ENVIRONMENT} not found. Deployment might fail."
fi

echo "Configuration completed for ${ENVIRONMENT} environment"
