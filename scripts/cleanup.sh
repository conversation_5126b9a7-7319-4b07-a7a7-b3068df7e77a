#!/bin/bash

# 设置变量
DEPLOYMENT_ROOT="/opt/codedeploy-agent/deployment-root"
KEEP_DEPLOYMENTS=5  # 保留最近5次部署

# 获取部署组ID
DEPLOYMENT_GROUP_ID=$(ls -1 $DEPLOYMENT_ROOT | grep -v 'deployment-instructions' | head -n 1)

if [ -z "$DEPLOYMENT_GROUP_ID" ]; then
    echo "No deployment group found."
    exit 0
fi

DEPLOYMENT_GROUP_ROOT="$DEPLOYMENT_ROOT/$DEPLOYMENT_GROUP_ID"

# 获取所有部署ID，按时间排序
DEPLOYMENTS=$(ls -1t $DEPLOYMENT_GROUP_ROOT | grep -v 'deployment-group-id')

# 计算要删除的部署数量
TOTAL_DEPLOYMENTS=$(echo "$DEPLOYMENTS" | wc -l)
TO_DELETE=$((TOTAL_DEPLOYMENTS - KEEP_DEPLOYMENTS))

if [ $TO_DELETE -le 0 ]; then
    echo "No old deployments to delete."
    exit 0
fi

echo "Deleting $TO_DELETE old deployment(s)..."

# 删除旧的部署
echo "$DEPLOYMENTS" | tail -n $TO_DELETE | while read deployment; do
    echo "Deleting deployment: $deployment"
    rm -rf "$DEPLOYMENT_GROUP_ROOT/$deployment"
done

echo "Cleanup completed."
