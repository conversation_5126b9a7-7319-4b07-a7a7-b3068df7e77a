//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"reflect"
	"strings"
)

const (
	modelDir = "internal/model/modelv2"
	outFile  = "internal/model/modelv2/column.go"
)

func main() {
	structs := map[string][]struct {
		FieldName string
		ColName   string
	}{}

	// 遍历 modelv2 目录下所有 go 文件
	err := filepath.Walk(modelDir, func(path string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() || !strings.HasSuffix(path, ".go") || strings.HasSuffix(path, "_test.go") {
			return nil
		}
		fset := token.NewFileSet()
		node, err := parser.ParseFile(fset, path, nil, parser.ParseComments)
		if err != nil {
			return err
		}
		for _, decl := range node.Decls {
			gen, ok := decl.(*ast.GenDecl)
			if !ok {
				continue
			}
			for _, spec := range gen.Specs {
				ts, ok := spec.(*ast.TypeSpec)
				if !ok {
					continue
				}
				st, ok := ts.Type.(*ast.StructType)
				if !ok {
					continue
				}
				structName := ts.Name.Name
				for _, field := range st.Fields.List {
					if len(field.Names) == 0 {
						continue
					}
					fieldName := field.Names[0].Name
					tag := ""
					if field.Tag != nil {
						tag = strings.Trim(field.Tag.Value, "`")
					}
					col := ""
					if tag != "" {
						col = parseGormColumn(reflect.StructTag(tag).Get("gorm"))
					}
					if col == "" {
						continue
					}
					structs[structName] = append(structs[structName], struct {
						FieldName string
						ColName   string
					}{fieldName, col})
				}
			}
		}
		return nil
	})
	if err != nil {
		panic(err)
	}

	// 生成代码
	var b strings.Builder
	b.WriteString("// Code generated by gen_model_columns.go; DO NOT EDIT.\n")
	b.WriteString("package modelv2\n\n")
	for structName, fields := range structs {
		for _, f := range fields {
			b.WriteString(fmt.Sprintf("func (r *%s) %sField() string { return \"%s\" }\n", structName, f.FieldName, f.ColName))
		}
		b.WriteString("\n")
	}

	// 写入文件
	if err := os.WriteFile(outFile, []byte(b.String()), 0644); err != nil {
		panic(err)
	}
	fmt.Printf("Generated: %s\n", outFile)
}

func parseGormColumn(tag string) string {
	for _, part := range strings.Split(tag, ";") {
		if strings.HasPrefix(part, "column:") {
			return strings.TrimPrefix(part, "column:")
		}
	}
	return ""
}
