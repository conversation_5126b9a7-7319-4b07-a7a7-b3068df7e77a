[2025-02-21 14:34:18] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:34:29] ERROR [middleware/error.go:17] runtime error: invalid memory address or nil pointer dereference {"Stack": "git.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/pkg/er.(*BuiltInError).WithStackSkip\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/pkg/er/built_in_error.go:101\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1.1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:16\nruntime.gopanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:785\nruntime.panicmem\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:262\nruntime.sigpanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/signal_unix.go:917\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service.(*CraParameterService).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/service/rms_cra_parameter.go:85\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.(*CraParameterHandle).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/rms_cra_parameter.go:69\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.JwtAuth.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/auth.go:45\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:25\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.GinCors.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/cors.go:28\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210\nnet/http.(*conn).serve\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092\nruntime.goexit\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223\n"}

[2025-02-21 14:35:06] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:35:26] ERROR [middleware/error.go:17] runtime error: invalid memory address or nil pointer dereference {"Stack": "git.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/pkg/er.(*BuiltInError).WithStackSkip\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/pkg/er/built_in_error.go:101\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1.1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:16\nruntime.gopanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:785\nruntime.panicmem\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:262\nruntime.sigpanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/signal_unix.go:917\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service.(*CraParameterService).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/service/rms_cra_parameter.go:85\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.(*CraParameterHandle).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/rms_cra_parameter.go:69\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.JwtAuth.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/auth.go:45\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:25\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.GinCors.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/cors.go:28\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210\nnet/http.(*conn).serve\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092\nruntime.goexit\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223\n"}

[2025-02-21 14:35:46] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:36:27] ERROR [middleware/error.go:17] runtime error: invalid memory address or nil pointer dereference {"Stack": "git.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/pkg/er.(*BuiltInError).WithStackSkip\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/pkg/er/built_in_error.go:101\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1.1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:16\nruntime.gopanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:785\nruntime.panicmem\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:262\nruntime.sigpanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/signal_unix.go:917\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service.(*CraParameterService).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/service/rms_cra_parameter.go:85\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.(*CraParameterHandle).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/rms_cra_parameter.go:69\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.JwtAuth.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/auth.go:45\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:25\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.GinCors.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/cors.go:28\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210\nnet/http.(*conn).serve\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092\nruntime.goexit\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223\n"}

[2025-02-21 14:37:02] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:37:34] ERROR [middleware/error.go:17] runtime error: invalid memory address or nil pointer dereference {"Stack": "git.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/pkg/er.(*BuiltInError).WithStackSkip\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/pkg/er/built_in_error.go:101\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1.1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:16\nruntime.gopanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:785\nruntime.panicmem\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:262\nruntime.sigpanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/signal_unix.go:917\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service.(*CraParameterService).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/service/rms_cra_parameter.go:85\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.(*CraParameterHandle).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/rms_cra_parameter.go:69\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.JwtAuth.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/auth.go:45\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:25\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.GinCors.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/cors.go:28\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:633\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/gin.go:589\nnet/http.serverHandler.ServeHTTP\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210\nnet/http.(*conn).serve\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092\nruntime.goexit\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223\n"}

[2025-02-21 14:45:07] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:45:18] ERROR [handle/handle.go:102] unknown {"code": 2, "name": "Unknown", "msg": "unknown", "level": "error", "sendToClient": true, "stack": "\n\t:0\n", "error": "Error:Unknown, msg:unknown, code:2, level:error, sendToClient:true", "error": "unsupported data type: &[]: Table not set, please set it like: db.Model(&user) or db.Table(\"users\")"}

[2025-02-21 14:45:41] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:46:24] ERROR [handle/handle.go:102] unknown {"code": 2, "name": "Unknown", "msg": "unknown", "level": "error", "sendToClient": true, "stack": "\n\t:0\n", "error": "Error:Unknown, msg:unknown, code:2, level:error, sendToClient:true", "error": "unsupported data type: &[]: Table not set, please set it like: db.Model(&user) or db.Table(\"users\")"}

[2025-02-21 14:46:28] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:46:57] ERROR [handle/handle.go:102] unknown {"code": 2, "name": "Unknown", "msg": "unknown", "level": "error", "sendToClient": true, "stack": "\n\t:0\n", "error": "Error:Unknown, msg:unknown, code:2, level:error, sendToClient:true", "error": "sql: converting argument $1 type: unsupported type []string, a slice of string"}

[2025-02-21 14:47:01] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:47:52] ERROR [handle/handle.go:102] unknown {"code": 2, "name": "Unknown", "msg": "unknown", "level": "error", "sendToClient": true, "stack": "\n\t:0\n", "error": "Error:Unknown, msg:unknown, code:2, level:error, sendToClient:true", "error": "Error 1146 (42S02): Table 'uqpay_risk_developv10.rms_cra_parameter_vals' doesn't exist"}

[2025-02-21 14:47:55] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:48:08] ERROR [middleware/error.go:17] reflect: call of reflect.Value.Field on string Value {"Stack": "git.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/pkg/er.(*BuiltInError).WithStackSkip\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/pkg/er/built_in_error.go:101\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1.1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:16\nruntime.gopanic\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/runtime/panic.go:785\nreflect.Value.Field\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/golang.org/<EMAIL>-arm64/src/reflect/value.go:1269\ngorm.io/gorm/schema.(*Field).setupValuerAndSetter.func5\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/schema/field.go:502\ngorm.io/gorm/schema.(*Field).setupValuerAndSetter.func8\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/schema/field.go:605\ngorm.io/gorm.(*DB).scanIntoStruct\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/scan.go:75\ngorm.io/gorm.Scan\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/scan.go:307\ngorm.io/gorm/callbacks.Query\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/callbacks/query.go:28\ngorm.io/gorm.(*processor).Execute\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/callbacks.go:130\ngorm.io/gorm.(*DB).Find\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/gorm.io/gorm@v1.25.0/finisher_api.go:172\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data.(*RmsCraParameterVal).GetParameterName\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/data/rms_cra_parameter_val.go:42\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service.(*CraParameterService).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/service/rms_cra_parameter.go:85\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.(*CraParameterHandle).List\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/rms_cra_parameter.go:69\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.JwtAuth.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/auth.go:45\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.ErrorHandler.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/error.go:25\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/engine/middleware.GinCors.func1\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/engine/middleware/cors.go:28\n"}

[2025-02-21 14:48:34] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:49:44] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:57:03] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 14:58:06] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 15:07:54] INFO [handle/handle.go:102] Invalid argument: CraFrameworkId is required {"code": 3, "name": "InvalidArgument", "msg": "Invalid argument: CraFrameworkId is required", "level": "info", "sendToClient": true, "stack": "git.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/pkg/er.ValidatorErrorHandler\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/internal/pkg/er/validator.go:59\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.BaseHandle.BindJSON\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/handle.go:141\ngit.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle.(*CraParameterHandle).Create\n\t/Users/<USER>/project/uqpay/risk/uqpay-risk-portal-api/api/handle/rms_cra_parameter.go:18\ngithub.com/gin-gonic/gin.(*Context).Next\n\t/Users/<USER>/gopath/bin/path/to/go/mod/cache/github.com/gin-gonic/gin@v1.10.0/context.go:185\n", "error": "Error:InvalidArgument, msg:invalid_argument, code:3, level:info, sendToClient:true"}

[2025-02-21 15:08:27] ERROR [handle/handle.go:102] unknown {"code": 2, "name": "Unknown", "msg": "unknown", "level": "error", "sendToClient": true}

[2025-02-21 15:09:32] ERROR [handle/handle.go:102] unknown {"code": 2, "name": "Unknown", "msg": "unknown", "level": "error", "sendToClient": true}

[2025-02-21 15:09:35] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

[2025-02-21 15:12:55] INFO [engine/engine.go:164] bind: 0.0.0.0:6102

