# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
logs/
assets/
bin/
.DS_Store

configs/*_config.yml
api.logz
api*.log.gz
api.log
/configs/config.yaml
.gitlab-ci.yml
out.json
docs/swagger.json
docs/swagger.yaml
