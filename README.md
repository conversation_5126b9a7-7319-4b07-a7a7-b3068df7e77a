# Uqpay Risk Admin Api

## 快速开始

快速运行开发环境

```sh
make init		# 安装依赖工具
make gen-config	# 生成配置文件到 configs/config.yaml
make run-dev	# 运行开发服务
```

访问 http://localhost:8080/ 查看和测试接口



如果以上步骤出现问题，尝试运行

```sh
make all		# 按循序生成依赖
make run-dev	# 运行开发服务
```



## 线上部署

### 编译和运行

```sh
make build-pro # 编译线上服务，编译输出路径在 bin/uqpay-risk-portal-api
./bin/uqpay-risk-portal-api # 使用默认配置文件 configs/config.yaml 运行
./bin/uqpay-risk-portal-api -c configs/config.yaml # -c 执行配置文件运行
```

### 配置文件

```sh
# 如果没有配置文件，生成配置文件
make gen-config

# gen-config 可以作为二进制发布，使用以下命令编译所有命令，输出路径在 bin 中
make build
./bin/gen-config
```



## 开发

uqpay-risk-portal-api 使用 swagger 生成接口文档，运行 `make swagger` 生成文档代码，输出路径在 doc 目录中。swagger 可以导入到如 postman 之类的测试平台中，为了让文档在 postman 中表现的更好，需要详细写明 swagger 的 @Tags 和 @Summary。在导入 postman 时，点击 Import Settings，Naming requests 选择 Fallback，Folder organization 选择 Tags。

dev 服务提供免 jwt 和权限验证的接口，方便调试。同时托管了 swagger 的 web 页面。运行 `make run-dev` 启动 dev 服务，访问 `http://localhost:8080` 自动重定向到文档页面。swagger 页面支持接口调试，每次更改文档后重启 `make run-dev` 即可，run-dev 集成了 swagger 生成。

uqpay-risk-portal-api 使用 wire 生成依赖注入代码。每次变更依赖需要执行 `make generate` 重新生成初始化逻辑，否则可能会出现没改过的地方空指针。为了项目能在快速开发下保障稳定不出bug，为单元测试做准备，进行了大量解耦和依赖注入。但由于初期业务设计不明朗，目前单元测试和依赖注入并不完善。

更多内容请运行 `make help` 查看帮助信息。
