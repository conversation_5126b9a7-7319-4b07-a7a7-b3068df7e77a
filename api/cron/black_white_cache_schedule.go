package cron

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"go.uber.org/zap"
	"time"
)

type BlackWhiteCacheTask struct {
	BlackWhiteListService *servicev2.BlackWhiteListService
}

func NewBlackWhiteCacheTask() *BlackWhiteCacheTask {
	return &BlackWhiteCacheTask{
		BlackWhiteListService: &servicev2.BlackWhiteListService{
			BlackWhiteListRepo:       &datav2.RmsBlackWhiteList{},
			BlackWhiteItemRepo:       &datav2.RmsBlackWhiteItem{},
			BlackWhiteFieldRepo:      &datav2.RmsBlackWhiteField{},
			BlackWhiteAuditRepo:      &datav2.RmsBlackWhiteAudit{},
			RmsEventFieldRepo:        &datav2.RmsEventField{},
			RmsEventFieldBindingRepo: &datav2.RmsEventFieldBinding{},
		},
	}
}
func (c *BlackWhiteCacheTask) Init() (err error) {
	initFn := func() (innerErr error) {
		var blackRulesCache *modelv2.BlackWhiteCache
		var whiteRulesCache *modelv2.BlackWhiteCache
		blackRulesCache, innerErr = datav2.NewRmsBlackListCache().GetBlackWhiteCache(context.Background(), time.Now().Local().Format("2006-01-02"))
		if innerErr != nil {
			wlog.Error("BlackCacheTask init", zap.Any("innerErr", innerErr))
		}
		whiteRulesCache, innerErr = datav2.NewRmsWhiteListCache().GetBlackWhiteCache(context.Background(), time.Now().Local().Format("2006-01-02"))
		if innerErr != nil {
			wlog.Error("WhiteCacheTask init", zap.Any("innerErr", innerErr))
		}
		innerErr = datav2.NewRmsBlackListCache().Flush(blackRulesCache)
		if innerErr != nil {
			wlog.Error("BlackCacheTask flush", zap.Any("innerErr", innerErr))
		}
		innerErr = datav2.NewRmsBlackListCache().Flush(whiteRulesCache)
		if innerErr != nil {
			wlog.Error("WhiteCacheTask flush", zap.Any("innerErr", innerErr))
		}
		return
	}
	err = initFn()
	if err != nil {
		return
	}
	_, err = CronTasker.AddFunc("0 0 0 * * *", func() {
		innerErr := initFn()
		if innerErr != nil {
			wlog.Error("BlackCacheTask flush", zap.Any("innerErr", innerErr))
		}
	})
	return
}

//func BlackCheck(accessPoint string, data map[string]interface{}) {
//	entity.BlackRulesCache.GetAccessPointRules()
//
//}
