package cron

import (
	"github.com/robfig/cron/v3"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"go.uber.org/zap"
	"time"
)

var CronTasker *cron.Cron

func init() {
	//设置时区为utc+8时区
	location := time.FixedZone("UTC+8", 8*60*60)

	CronTasker = cron.New(cron.WithSeconds(), cron.WithLocation(location))
	CronTasker.Start()
}

func Start() {
	err := NewBlackWhiteCacheTask().Init()
	if err != nil {
		wlog.Error("init black white list cache fail", zap.Any("err", err))
		panic(err)
	}
	err = NewIndicatorCacheTask().Init()
	if err != nil {
		wlog.Error("init indicator list cache fail", zap.Any("err", err))
		panic(err)
	}
}
