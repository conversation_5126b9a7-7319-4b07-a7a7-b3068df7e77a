package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type EventHandle struct {
	*BaseHandle
	EventService *service.EventService
}

// @Tags Event 风险事件 事件查询
// @Security x-auth-token
// @Summary List Event
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventList true "Request body"
// @Success 200 {object} model.RmsEvent
// @Router /event/list [post]
func (h *EventHandle) List(c *gin.Context) {
	req := new(domain.EventList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.EventService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags Event 风险事件 事件查询
// @Security x-auth-token
// @Summary Retrieve Event
// @Success 200 {object} model.RmsEvent
// @Router /event/retrieve/{id} [get]
func (h *EventHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.EventService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
