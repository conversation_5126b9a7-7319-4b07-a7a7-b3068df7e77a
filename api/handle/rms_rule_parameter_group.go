package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type RuleParameterGroupHandle struct {
	*BaseHandle
	RuleParameterGroupService *service.RuleParameterGroupService
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Create RuleParameterGroup
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupCreate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /rule_parameter_group/create [post]
func (h *RuleParameterGroupHandle) Create(c *gin.Context) {
	req := new(domain.RuleParameterGroupCreate)
	if h.BindJ<PERSON>N(c, req) {
		return
	}
	data := new(model.RmsRuleParameterGroup)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleParameterGroupService.Create(c, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleParameterGroupResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Update RuleParameterGroup
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupUpdate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /rule_parameter_group/update [post]
func (h *RuleParameterGroupHandle) Update(c *gin.Context) {
	req := new(domain.RuleParameterGroupUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsRuleParameterGroup)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleParameterGroupService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleParameterGroupResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /rule_parameter_group/delete/{id} [get]
func (h *RuleParameterGroupHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.RuleParameterGroupService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary List RuleParameterGroup
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupList true "Request body"
// @Success 200 {object} domain.RuleParameterGroupListResp
// @Router /rule_parameter_group/list [post]
func (h *RuleParameterGroupHandle) List(c *gin.Context) {
	req := new(domain.RuleParameterGroupList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleParameterGroupService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleParameterGroupListResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Retrieve RuleParameterGroup
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /rule_parameter_group/retrieve/{id} [get]
func (h *RuleParameterGroupHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.RuleParameterGroupService.Retrieve(c, id)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleParameterGroupResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary All RuleParameterGroup
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /rule_parameter_group/all [get]
func (h *RuleParameterGroupHandle) All(c *gin.Context) {
	data, err := h.RuleParameterGroupService.All(c)
	h.FeedBack(c, data, err)
}

// @Tags RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary all_exclude_default RuleParameterGroup
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /rule_parameter_group/all_exclude_default [get]
func (h *RuleParameterGroupHandle) AllExcludeDefault(c *gin.Context) {
	data, err := h.RuleParameterGroupService.AllExcludeDefault(c)
	h.FeedBack(c, data, err)
}
