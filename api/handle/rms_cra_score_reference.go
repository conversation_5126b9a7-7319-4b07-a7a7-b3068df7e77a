package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type CraScoreReferenceHandle struct {
	*BaseHandle
	CraScoreReferenceService *service.CraScoreReferenceService
}

func (h *CraScoreReferenceHandle) Retrieve(c *gin.Context) {
	req := new(domain.RmsCraScoreReferenceRetrieveReq)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCraScoreReference)
	if h.Copy(c, req, data) {
		return
	}
	info, err := h.CraScoreReferenceService.Retrieve(c, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, info)
	}

}
func (h *CraScoreReferenceHandle) List(c *gin.Context) {
	req := new(domain.RmsCraScoreReferenceListReq)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCraScoreReference)
	if h.Copy(c, req, data) {
		return
	}
	list, err := h.CraScoreReferenceService.List(c, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, list)
	}

}
