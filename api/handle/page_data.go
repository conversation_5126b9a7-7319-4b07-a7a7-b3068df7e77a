package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

// 组装返回某个页面需要的所有数据

type PageDataHandle struct {
	*BaseHandle
	PolicyService   *service.PolicyService
	PageDataService *service.PageDataService
}

// @Tags PageData 对应前端页面
// @Security x-auth-token
// @Summary PolicyConfigPage 规则组配置所需数据
// @Param x-auth-token header string true "jwt token"
// @Param access_point_code path string true "access point code"
// @Success 200 {object} domain.PolicyConfigPageResp
// @Router /page_data/policy_config_page/{rules_group_code} [get]
func (h *PageDataHandle) PolicyConfigPage(c *gin.Context) {
	var rules_group_code string
	if h.ParamStrId(c, "rules_group_code", &rules_group_code) {
		return
	}
	resp, err := h.PageDataService.PolicyConfigPage(c, rules_group_code)
	h.FeedBack(c, resp, err)
}

// @Tags PageData 对应前端页面
// @Security x-auth-token
// @Summary PolicyConfigPageCreate 创建规则组配置所需数据
// @Param x-auth-token header string true "jwt token"
// @Param access_point_code path string true "access point code"
// @Success 200 {object} domain.PolicyConfigPageResp
// @Router /page_data/policy_config_page_create/{access_point_code} [get]
func (h *PageDataHandle) PolicyConfigPageCreate(c *gin.Context) {
	var access_point_code string
	if h.ParamStrId(c, "access_point_code", &access_point_code) {
		return
	}
	resp, err := h.PageDataService.PolicyConfigPageCreate(c, access_point_code)
	h.FeedBack(c, resp, err)
}

// @Tags PageData 对应前端页面
// @Security x-auth-token
// @Summary BusinessConfig 接入点配置-> 业务配置页面
// @Param x-auth-token header string true "jwt token"
// @Param access_point_code path string true "access point code"
// @Success 200 {object} domain.PolicyConfigPageResp
// @Router /page_data/business_config/{access_point_code} [get]
func (h *PageDataHandle) BusinessConfig(c *gin.Context) {
	var access_point_code string
	if h.ParamStrId(c, "access_point_code", &access_point_code) {
		return
	}
	resp, err := h.PageDataService.BusinessConfig(c, access_point_code)
	h.FeedBack(c, resp, err)
}

// @Tags PageData 对应前端页面
// @Security x-auth-token
// @Summary RmsRuleParameterGroupBind 规则配置-> 参数组 -> 参数组绑定
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyConfigPageList true "Request body"
// @Success 200 {object} domain.RmsRuleParameterGroupBindResp
// @Router /page_data/rule_parameter_group_bind [post]
func (h *PageDataHandle) RmsRuleParameterGroupBind(c *gin.Context) {
	req := new(domain.PolicyConfigPageList)
	if h.BindJSON(c, req) {
		return
	}
	resp, err := h.PageDataService.RmsRuleParameterGroupBind(c, req)
	h.FeedBack(c, resp, err)
}
