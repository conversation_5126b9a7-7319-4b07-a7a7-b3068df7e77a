package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type HandleLogHandle struct {
	*BaseHandle
	HandleLogService *service.HandleLogService
}

// @Tags HandleLog 用户操作日志
// @Security x-auth-token
// @Summary List HandleLog
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.HandleLogList true "Request body"
// @Success 200 {object} model.WpHandleLog
// @Router /handle_log/list [post]
func (h *HandleLogHandle) List(c *gin.Context) {
	req := new(domain.HandleLogList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.HandleLogService.List(c, req)
	h.Feed<PERSON>ack(c, data, err)
}

// @Tags HandleLog 用户操作日志
// @Security x-auth-token
// @Summary Retrieve HandleLog
// @Success 200 {object} model.WpHandleLog
// @Router /handle_log/retrieve/{id} [get]
func (h *HandleLogHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.HandleLogService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
