package handlev2

import (
	"github.com/gin-gonic/gin"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type BlackWhiteFieldHandle struct {
	BaseHandle
	BlackWhiteFieldService *servicev2.BlackWhiteFieldService
}

// ListBlackWhiteField 创建黑白名单字段列表
// @Summary 黑白名单字段列表
// @Tags risk-portal-api/BlackWhite 黑白名单/Field 黑白名单字段管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param body body request_parameters.ListBlackWhiteField true "黑白名单字段列表"
// @Success 200 {array} string "黑白名单字段列表"
// @Router /api/v2/black_white/fields [post]
func (h *BlackWhiteFieldHandle) ListBlackWhiteField(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.ListBlackWhiteField](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	fields, err := h.BlackWhiteFieldService.List(c, req.AccessPoints)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, fields)
}
