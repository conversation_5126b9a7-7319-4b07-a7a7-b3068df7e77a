package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type RuleParameterGroupBindHandle struct {
	RuleParameterGroupBindService *servicev2.RuleParameterGroupBindService
}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Create RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindCreate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupBindResp
// @Router /api/v2/rule_parameter_group_bind/create [post]
func (h *RuleParameterGroupBindHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupBindCreate](c)
	if err != nil {
		response.HeadErr(c, err)
		return
	}
	data, err := h.RuleParameterGroupBindService.Create(c, &req)
	if err != nil {
		response.HeadErr(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.RuleParameterGroupBindResp{
		RuleParameterGroupBindUpdate: response_parameters.RuleParameterGroupBindUpdate{
			ID: data.ID,
			RuleParameterGroupBindCreate: response_parameters.RuleParameterGroupBindCreate{
				TargetType: data.TargetType,
				GroupID:    data.GroupID,
				Able:       data.Able,
				TargetID:   data.TargetID,
			},
		},
	})

}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Create_list Add multiple RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindCreateList true "Request body"
// @Success 200 {array} domain.RuleParameterGroupBindResp
// @Router /api/v2/rule_parameter_group_bind/create_list [post]
func (h *RuleParameterGroupBindHandle) CreateList(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupBindCreateList](c)
	if err != nil {
		response.HeadErr(c, err)
		return
	}
	data, err := h.RuleParameterGroupBindService.CreateList(c, &req)
	if err != nil {
		response.HeadErr(c, err)
		return
	}
	var resp []*response_parameters.RuleParameterGroupBindResp
	for _, v := range data {
		resp = append(resp, &response_parameters.RuleParameterGroupBindResp{
			RuleParameterGroupBindUpdate: response_parameters.RuleParameterGroupBindUpdate{
				ID: v.ID,
				RuleParameterGroupBindCreate: response_parameters.RuleParameterGroupBindCreate{
					TargetType: v.TargetType,
					GroupID:    v.GroupID,
					Able:       v.Able,
					TargetID:   v.TargetID,
				},
			},
		})
	}
	response.FeedBack(c, resp, err)
}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Update RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindUpdate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupBindResp
// @Router /api/v2/rule_parameter_group_bind/update [post]
func (h *RuleParameterGroupBindHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupBindUpdate](c)
	if err != nil {
		response.HeadErr(c, err)
	}
	// 前端的组件不支持 bool 值
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	var data *modelv2.RmsRuleParameterGroupBind
	data, err = h.RuleParameterGroupBindService.Update(c, req.ID, &request_parameters.RuleParameterGroupBindUpdate{
		ID: req.ID,
		RuleParameterGroupBindCreate: request_parameters.RuleParameterGroupBindCreate{
			TargetType: req.TargetType,
			GroupID:    req.GroupID,
			Able:       req.Able,
			State:      req.State,
			TargetID:   req.TargetID,
		},
	})
	response.FeedBack(c, data, err)
}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule_parameter_group_bind/delete/{id} [get]
func (h *RuleParameterGroupBindHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	err = h.RuleParameterGroupBindService.Delete(c, id)
	response.FeedBack(c, nil, err)
}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary List RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindList true "Request body"
// @Success 200 {object} domain.RuleParameterGroupBindListResp
// @Router /api/v2/rule_parameter_group_bind/list [post]
func (h *RuleParameterGroupBindHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupBindList](c)
	if err != nil {
		response.HeadErr(c, err)
		return
	}

	total, data, err := h.RuleParameterGroupBindService.List(c, &req)
	response.FeedBack(c, response_parameters.NewListResponsePointer[modelv2.RmsRuleParameterGroupBind](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		data,
	), err)
}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary DeleteList batch deletion RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param values body []int64 true "Request body"
// @Success 200 {object} nil
// @Router /api/v2/rule_parameter_group_bind/delete_list [post]
func (h *RuleParameterGroupBindHandle) DeleteList(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupBindDeleteList](c)
	if err != nil {
		response.HeadErr(c, err)
		return
	}
	response.ReturnFailure(c, h.RuleParameterGroupBindService.DeleteList(c, req.Value))
}

// @Tags V2 RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Retrieve RuleParameterGroupBind
// @Param id path string true "id"
// @Success 200 {object} domain.RuleParameterGroupBindResp
// @Router /api/v2/rule_parameter_group_bind/retrieve/{id} [get]
func (h *RuleParameterGroupBindHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	resp, err := h.RuleParameterGroupBindService.Retrieve(c, id)
	response.FeedBack(c, resp, err)
}
