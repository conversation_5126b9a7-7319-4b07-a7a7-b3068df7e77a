package handlev2

import (
	"github.com/gin-gonic/gin"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	response_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
	"math"
)

type EventFieldHandle struct {
	EventFieldService *servicev2.EventFieldService
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Create EventField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldCreate true "Request body"
// @Success 200 {object} domain.EventFieldResp
// @Router /event_field/create [post]
func (h *EventFieldHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventFieldCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	response.FeedBack(c, nil, h.EventFieldService.Create(c, &req))
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Update EventField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldUpdate true "Request body"
// @Success 200 {object} domain.EventFieldResp
// @Router /event_field/update [post]
func (h *EventFieldHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventFieldUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.EventFieldService.Update(c, req.ID, &req))
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /event_field/delete/{id} [get]
func (h *EventFieldHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.EventFieldService.Delete(c, id))
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary List EventField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldList true "Request body"
// @Success 200 {object} domain.EventFieldListResp
// @Router /event_field/list [post]
func (h *EventFieldHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventFieldList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	total, li, err := h.EventFieldService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var resLi []*response_parameters.EventFieldResp
	for _, v := range li {
		resLi = append(resLi, &response_parameters.EventFieldResp{
			EventFieldUpdate: response_parameters.EventFieldUpdate{
				ID: v.ID,
				EventFieldCreate: response_parameters.EventFieldCreate{
					CheckPoint: v.CheckPoint,
					FieldName:  v.FieldName,
					FieldType:  v.FieldType,
					Memo:       v.Memo,
					Required:   v.Required,
					Primary:    v.Primary,
					Filter:     v.Filter,
				},
			},
		})
	}
	response.FeedBack(c, &response_parameters.ListResponsePointer[response_parameters.EventFieldResp]{
		TotalNum:  total,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
		Items:     resLi,
	}, nil)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Retrieve EventField
// @Param id path string true "EventField Id"
// @Success 200 {object} domain.EventFieldResp
// @Router /event_field/retrieve/{id} [get]
func (h *EventFieldHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var data *response_parameters.EventFieldResp
	data, err = h.EventFieldService.Retrieve(c, id)
	response.FeedBack(c, data, err)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary CheckNext Check if you can click Next
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldCheckNext true "Request body"
// @Success 200 {object} domain.EventFieldListResp
// @Router /event_field/check_next [post]
func (h *EventFieldHandle) CheckNext(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventFieldCheckNext](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.EventFieldService.CheckNext(c, &req))
}
