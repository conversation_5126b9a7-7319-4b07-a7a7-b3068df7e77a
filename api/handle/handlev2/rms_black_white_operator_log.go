package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type BlackWhiteOperatorLogHandle struct {
	BaseHandle
	BlackWhiteOperatorLogService *servicev2.BlackWhiteOperatorLogService
}

// List 获取黑白名单操作日志列表
// @Summary List 获取黑白名单操作日志列表
// @Tags risk-portal-api/BlackWhite 黑白名单/OperatorLog 操作日志
// @Produce json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param page_num query int true "页码"
// @Param page_size query int false "每页条数"
// @Param bwl_id query int true "黑白名单列表ID"
// @Router /api/v2/black_white/operator_logs [get]
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.BlackWhiteOperatorLogs]
func (h *BlackWhiteOperatorLogHandle) List(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.BlackWhiteOperatorLogsRequest](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	total, respLi, err := h.BlackWhiteOperatorLogService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var resp []*response_parameters.BlackWhiteOperatorLogs
	for _, item := range respLi {
		resp = append(resp, &response_parameters.BlackWhiteOperatorLogs{
			Operator:  item.Operator,
			CreatedAt: item.CreatedAt.Local().Unix(),
			Log:       item.Log,
		})
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer(
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		resp,
	), err)
}
