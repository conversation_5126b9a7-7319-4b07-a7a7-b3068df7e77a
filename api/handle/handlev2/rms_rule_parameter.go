package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type RuleParameterHandle struct {
	RuleParameterService *servicev2.RuleParameterService
}

// @Tags V2 RuleParameter 规则参数
// @Security x-auth-token
// @Summary Create RuleParameter
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterCreate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /api/v2/rule_param/create [post]
func (h *RuleParameterHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsRuleParameter{
		Code:          req.Code,
		Description:   req.Description,
		ParamType:     *req.ParamType,
		CheckPoint:    req.CheckPoint,
		ValueTransfer: req.ValueTransfer,
	}
	err = h.RuleParameterService.Create(c, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	// TODO 疑问
	resp := &response_parameters.PolicyResp{
		PolicyUpdate: response_parameters.PolicyUpdate{
			ID: data.ID,
			PolicyCreate: response_parameters.PolicyCreate{
				CheckPoint: data.CheckPoint,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// @Tags V2 RuleParameter 规则参数
// @Security x-auth-token
// @Summary Update RuleParameter
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterUpdate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /api/v2/rule_param/update [post]
func (h *RuleParameterHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsRuleParameter{
		ID:            req.ID,
		Code:          req.Code,
		Description:   req.Description,
		ParamType:     *req.ParamType,
		CheckPoint:    req.CheckPoint,
		ValueTransfer: req.ValueTransfer,
	}
	err = h.RuleParameterService.Update(c, req.ID, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := &response_parameters.PolicyResp{
		PolicyUpdate: response_parameters.PolicyUpdate{
			ID: data.ID,
			PolicyCreate: response_parameters.PolicyCreate{
				CheckPoint: data.CheckPoint,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// @Tags V2 RuleParameter 规则参数
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule_param/delete/{id} [get]
func (h *RuleParameterHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	response.FeedBack(c, nil, h.RuleParameterService.Delete(c, id))
}

// @Tags V2 RuleParameter 规则参数
// @Security x-auth-token
// @Summary List RuleParameter
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterList true "Request body"
// @Success 200 {object} domain.RuleParameterListResp
// @Router /api/v2/rule_param/list [post]
func (h *RuleParameterHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var (
		total int64
		data  []*modelv2.RmsRuleParameter
	)
	total, data, err = h.RuleParameterService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var res []*response_parameters.RuleParameterResp
	for _, datum := range data {
		res = append(res, &response_parameters.RuleParameterResp{
			RuleParameterUpdate: response_parameters.RuleParameterUpdate{
				ID: datum.ID,
				RuleParameterCreate: response_parameters.RuleParameterCreate{
					CheckPoint:    datum.CheckPoint,
					ParamType:     &datum.ParamType,
					Code:          datum.Code,
					Description:   datum.Description,
					ValueTransfer: datum.ValueTransfer,
				},
			},
		})
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer[response_parameters.RuleParameterResp](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		res,
	), err)

}

// @Tags V2 RuleParameter 规则参数
// @Security x-auth-token
// @Summary Retrieve RuleParameter
// @Success 200 {object} domain.PolicyResp
// @Router /api/v2/rule_param/retrieve/{id} [get]
func (h *RuleParameterHandle) Retrieve(c *gin.Context) {
	var data *modelv2.RmsRuleParameter
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err = h.RuleParameterService.Retrieve(c, id)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := &response_parameters.PolicyResp{
		PolicyUpdate: response_parameters.PolicyUpdate{
			ID: data.ID,
			PolicyCreate: response_parameters.PolicyCreate{
				CheckPoint: data.CheckPoint,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}
