package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type HandleLogHandle struct {
	HandleLogService *servicev2.HandleLogService
}

// @Tags HandleLog 用户操作日志
// @Security x-auth-token
// @Summary List HandleLog
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.HandleLogList true "Request body"
// @Success 200 {object} model.WpHandleLog
// @Router /handle_log/list [post]
func (h *HandleLogHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.HandleLogList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	filterPage := filter.NewPaging(req.PageNum, req.PageSize)
	total, data, err := h.HandleLogService.List(c,
		filter.NewDefaultFilterByRequest(req.TimeRequest.Option, req.PageRequest.Option), &req)
	response.FeedBack(c, response_parameters.NewListResponsePointer[modelv2.WpHandleLog](total, filterPage.PageSize, data), err)
}

// @Tags HandleLog 用户操作日志
// @Security x-auth-token
// @Summary Retrieve HandleLog
// @Success 200 {object} model.WpHandleLog
// @Router /handle_log/retrieve/{id} [get]
func (h *HandleLogHandle) Retrieve(c *gin.Context) {

	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err := h.HandleLogService.Retrieve(c, id)
	response.FeedBack(c, data, err)
}
