package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type EventHandle struct {
	EventService *servicev2.EventService
}

// @Tags Event 风险事件 事件查询
// @Security x-auth-token
// @Summary List Event
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventList true "Request body"
// @Success 200 {object} model.RmsEvent
// @Router /event/list [post]
func (h *EventHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var (
		total int64
		data  []*modelv2.RmsEvent
	)
	total, data, err = h.EventService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer[modelv2.RmsEvent](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		data,
	), err)
}

// @Tags Event 风险事件 事件查询
// @Security x-auth-token
// @Summary Retrieve Event
// @Success 200 {object} model.RmsEvent
// @Router /event/retrieve/{id} [get]
func (h *EventHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err := h.EventService.Retrieve(c, id)
	response.FeedBack(c, data, err)
}
