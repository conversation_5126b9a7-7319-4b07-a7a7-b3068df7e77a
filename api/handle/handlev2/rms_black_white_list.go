package handlev2

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/wlog"
	"go.uber.org/zap"
	"math"
	"strconv"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	response_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type BlackWhiteListHandle struct {
	BaseHandle
	BlackWhiteListService  *servicev2.BlackWhiteListService
	BlackWhiteItemService  *servicev2.BlackWhiteItemService
	BlackWhiteAuditService *servicev2.BlackWhiteAuditService
}

// CreateBlackWhiteList 创建黑白名单
// @Summary Create BlackWhiteList 创建黑白名单
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CreateBlackWhiteRecord true "Request body"
// @Success 200 {object} response_parameters.NormalCreateResponse "黑白名单保存成功"
// @Router /api/v2/black_white/record [post]
func (h *BlackWhiteListHandle) CreateBlackWhiteList(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.CreateBlackWhiteRecord](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var id int64
	id, err = h.BlackWhiteListService.Save(c, &entity.SaveBlackWhiteRecord{
		Name:         req.Name,
		Type:         req.Type,
		AccessPoints: req.AccessPoints,
		Note:         req.Note,
		Fields:       req.Fields,
		Submitter:    h.GetUsername(c),
	})
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, response_parameters.NormalCreateResponse{
		Id: id,
	})
}

// UpdateBlackWhiteList 更新黑白名单
// @Summary Update BlackWhiteList 更新黑白名单
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.UpdateBlackWhiteRecord true "Request body"
// @Success 200 {object} response_parameters.NormalCreateResponse "黑白名单成功"
// @Router /api/v2/black_white/record [put]
func (h *BlackWhiteListHandle) UpdateBlackWhiteList(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.UpdateBlackWhiteRecord](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var id int64
	id, err = h.BlackWhiteListService.Save(c, &entity.SaveBlackWhiteRecord{
		ID:           req.ID,
		AccessPoints: req.AccessPoints,
		Note:         req.Note,
		Submitter:    h.GetUsername(c),
	})
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, response_parameters.NormalCreateResponse{
		Id: id,
	})
}

// GetBlackWhiteList 获取黑白名单列表
// @Summary Get BlackWhiteList 获取黑白名单列表
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.GetBlackWhiteList true "Request body"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.BlackWhiteList]
// @Router /api/v2/black_white/list [post]
func (h *BlackWhiteListHandle) GetBlackWhiteList(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.GetBlackWhiteList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var total int64
	var res []*response_parameters.BlackWhiteList
	total, res, err = h.BlackWhiteListService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.BlackWhiteList]{
		TotalNum:  total,
		Items:     res,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// RemoveBlackWhiteList 删除黑白名单
// @Summary Remove BlackWhiteList 删除黑白名单
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Security x-auth-token
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param id query int true "黑白名单记录ID"
// @Success 200 {string} string "黑白名单删除成功"
// @Router /api/v2/black_white/record [delete]
func (h *BlackWhiteListHandle) RemoveBlackWhiteList(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.GetBlackWhiteRecord](c)
	if err != nil {
		response.ReturnFailure(c, err)
	}
	err = h.BlackWhiteListService.Remove(c, req.ID, h.GetUsername(c))
	if err != nil {
		response.ReturnFailure(c, err)
	}
	c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), entity.BwOperatorLogSvc, []servicev2.BwLogAction{servicev2.NewBwLogDelList(req.ID)}))
	response.ReturnSuccess(c, "黑白名单删除成功")
}

// GetBlackWhiteListDetail 获取黑白名单详情
// @Summary Get BlackWhiteList 获取黑白名单详情
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Security x-auth-token
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param id query int true "黑白名单记录ID"
// @Success 200 {object} response_parameters.GetBlackWhiteRecord
// @Router /api/v2/black_white/info [get]
func (h *BlackWhiteListHandle) GetBlackWhiteListDetail(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.GetBlackWhiteRecord](c)
	var listEffectiveTime int64
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var bwlEntity *entity.BlackWhiteListDetail
	bwlEntity, err = h.BlackWhiteListService.Detail(c, int64(req.ID))
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	if bwlEntity.State == modelv2.BlackWhiteListStateReject || bwlEntity.State == modelv2.BlackWhiteListStatePass {
		var auditModel *modelv2.RmsBlackWhiteAudit
		auditModel, err = h.BlackWhiteAuditService.LastAuditRecord(c, int64(bwlEntity.ID))
		if err != nil {
			response.ReturnFailure(c, err)
			return
		}
		if auditModel.State == modelv2.BlackWhiteAuditStatePass || auditModel.State == modelv2.BlackWhiteAuditStateReject {
			listEffectiveTime = auditModel.UpdatedAt.Unix()

		}
	}
	response.ReturnSuccess(c, &response_parameters.GetBlackWhiteRecord{
		ID:                int64(bwlEntity.ID),
		Name:              bwlEntity.Name,
		Type:              bwlEntity.Type,
		AccessPoints:      bwlEntity.AccessPoints,
		Note:              bwlEntity.Note,
		Status:            bwlEntity.Status,
		State:             bwlEntity.State,
		Fields:            bwlEntity.Fields,
		Submitter:         bwlEntity.CreatedBy,
		LastAuditState:    bwlEntity.LastAuditState,
		ListEffectiveTime: listEffectiveTime,
	}, "Record detail retrieved successfully.")
}

// SetStatus 设置黑白名单状态
// @Summary SetStatus 设置黑白名单状态
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Security x-auth-token
// @Produce json
// @Accept json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.SetBlackWhiteStatus true "Request body"
// @Success 200 {object} response_parameters.NormalCreateResponse "黑白名单状态设置成功"
// @Router /api/v2/black_white/status [put]
func (h *BlackWhiteListHandle) SetStatus(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.SetBlackWhiteStatus](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var auditModel *modelv2.RmsBlackWhiteAudit
	auditModel, err = h.BlackWhiteListService.SetStatus(c, req.ID, req.Status, h.GetUsername(c))
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	if auditModel != nil {
		if req.Status == 1 {
			c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), entity.BwOperatorLogSvc, []servicev2.BwLogAction{servicev2.NewBwLogTurnOn(req.ID, auditModel.ID)}))
		} else {
			c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), entity.BwOperatorLogSvc, []servicev2.BwLogAction{servicev2.NewBwLogTurnOff(req.ID, auditModel.ID)}))
		}
	}

	response.ReturnSuccess(c, "黑白名单状态设置成功")
}

// 提交审核
// @Summary SubmitAudit 提交审核
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Security x-auth-token
// @Produce json
// @Accept json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.SubmitAudit true "Request body"
// @Success 200 {object} nil "黑白名单提交审核成功"
// @Router /api/v2/black_white/submit [post]
func (h *BlackWhiteListHandle) SubmitAudit(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.SubmitAudit](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	err = h.BlackWhiteListService.SubmitAudit(c, req.ID, h.GetUsername(c))
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), entity.BwOperatorLogSvc, []servicev2.BwLogAction{servicev2.NewBwLogCreate(req.ID), servicev2.NewBwLogEdit(req.ID)}))

	response.ReturnSuccess(c, nil, "")
}

// 黑白名单进度
// @Summary Progress 黑白名单进度
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Security x-auth-token
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param bwi_id query int true "黑白名单记录ID"
// @Success 200 {array} entity.Progress "黑白名单进度"
// @Router /api/v2/black_white/progress/{bwi_id} [get]
func (h *BlackWhiteListHandle) Progress(c *gin.Context) {
	var progress []*entity.Progress
	idStr := c.Param("bwi_id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Invalid ID"))
		return
	}
	progress, err = h.BlackWhiteListService.Progress(c, id)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, progress, "")
}

// Print 黑白名单数据打印
// @Summary Print 黑白名单数据打印
// @Tags risk-portal-api/BlackWhite 黑白名单/List 黑白名单管理
// @Security x-auth-token
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param bwi_id query int true "黑白名单记录ID"
// @Success 200 {array} entity.Progress "黑白名单进度"
// @Router /api/v2/black_white/print [get]
func (h *BlackWhiteListHandle) Print(c *gin.Context) {
	var res map[string]interface{}
	var err error
	res, err = h.BlackWhiteListService.Print(c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, res, "")
}

// Flush 黑白名数据刷新环境
// @Summary risk-portal-api/BlackWhite 黑白名单/Flush 黑白名数据刷新环境
// @Security x-auth-token
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Tags risk-portal-api/BlackWhite 黑白名单/Flush 黑白名数据刷新环境
// @Success 200 {array} entity.Progress "黑白名单进度"
// @Router /api/v2/black_white/flush [get]
func (h *BlackWhiteListHandle) Flush(c *gin.Context) {
	var blackRulesCache *modelv2.BlackWhiteCache
	var whiteRulesCache *modelv2.BlackWhiteCache
	var innerErr error
	blackRulesCache, innerErr = datav2.NewRmsBlackListCache().GetBlackWhiteCache(context.Background(), time.Now().Local().Format("2006-01-02"))
	if innerErr != nil {
		wlog.Error("BlackCacheTask init", zap.Any("innerErr", innerErr))
	}
	whiteRulesCache, innerErr = datav2.NewRmsWhiteListCache().GetBlackWhiteCache(context.Background(), time.Now().Local().Format("2006-01-02"))
	if innerErr != nil {
		wlog.Error("WhiteCacheTask init", zap.Any("innerErr", innerErr))
	}
	innerErr = datav2.NewRmsBlackListCache().Flush(blackRulesCache)
	if innerErr != nil {
		wlog.Error("BlackCacheTask flush", zap.Any("innerErr", innerErr))
	}
	innerErr = datav2.NewRmsBlackListCache().Flush(whiteRulesCache)
	if innerErr != nil {
		wlog.Error("WhiteCacheTask flush", zap.Any("innerErr", innerErr))
	}
	response.ReturnSuccess(c, nil, "")
}
