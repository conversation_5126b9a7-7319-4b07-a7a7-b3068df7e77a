package handlev2

import (
	"math"

	"github.com/gin-gonic/gin"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type FilterFieldHandle struct {
	BaseHandle
	FilterFieldService *servicev2.FilterFieldService
}

// CreateFilterField 创建过滤字段
// @Summary Create FilterField 创建过滤字段
// @Tags risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.FilterFieldCreate true "Request body"
// @Success 200 {object} response_parameters.FilterFieldResp "过滤字段创建成功"
// @Router /api/v2/filter_field/create [post]
func (h *FilterFieldHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.FilterFieldCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var data = &modelv2.RmsFilterField{
		FieldName:  req.FieldName,
		FieldEnum:  req.FieldEnum,
		CheckPoint: req.AccessPoint,
		Memo:       req.Memo,
	}

	err = h.FilterFieldService.Create(c, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var resp = response_parameters.FilterFieldResp{
		FilterFieldUpdate: response_parameters.FilterFieldUpdate{
			ID: data.ID,
			FilterFieldCreate: response_parameters.FilterFieldCreate{
				CheckPoint: data.CheckPoint,
				FieldName:  data.FieldName,
				FieldEnum:  data.FieldEnum,
				Memo:       data.Memo,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// UpdateFilterField 更新过滤字段
// @Summary Update FilterField 更新过滤字段
// @Tags risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.FilterFieldUpdate true "Request body"
// @Success 200 {object} response_parameters.FilterFieldResp "过滤字段更新成功"
// @Router /api/v2/filter_field/update [post]
func (h *FilterFieldHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.FilterFieldUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	data := &modelv2.RmsFilterField{
		ID:         req.ID,
		FieldName:  req.FieldName,
		FieldEnum:  req.FieldEnum,
		CheckPoint: req.AccessPoint,
		Memo:       req.Memo,
	}

	if err = h.FilterFieldService.Update(c, req.ID, data); err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var resp = response_parameters.FilterFieldResp{
		FilterFieldUpdate: response_parameters.FilterFieldUpdate{
			ID: data.ID,
			FilterFieldCreate: response_parameters.FilterFieldCreate{
				CheckPoint: data.CheckPoint,
				FieldName:  data.FieldName,
				FieldEnum:  data.FieldEnum,
				Memo:       data.Memo,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// 过滤字段的删除在 event_field 的 delete 和 update 中实现
//func (h *FilterFieldHandle) Delete(c *gin.Context) {
//	var id int64
//	if h.ParamId(c, "id", &id) {
//		return
//	}
//	err := h.FilterFieldService.Delete(c, id)
//	h.FeedBack(c, nil, err)
//}

// GetFilterFieldList 获取过滤字段列表
// @Summary Get FilterField List 获取过滤字段列表
// @Tags risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.FilterFieldList true "Request body"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.FilterFieldResp] "过滤字段列表"
// @Router /api/v2/filter_field/list [post]
func (h *FilterFieldHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.FilterFieldList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var (
		total int64
		data  []*modelv2.RmsFilterField
	)
	total, data, err = h.FilterFieldService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var resp []*response_parameters.FilterFieldResp
	for _, v := range data {
		resp = append(resp, &response_parameters.FilterFieldResp{
			FilterFieldUpdate: response_parameters.FilterFieldUpdate{
				ID: v.ID,
				FilterFieldCreate: response_parameters.FilterFieldCreate{
					CheckPoint: v.CheckPoint,
					FieldName:  v.FieldName,
					FieldEnum:  v.FieldEnum,
					Memo:       v.Memo,
				},
			},
		})
	}

	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.FilterFieldResp]{
		TotalNum:  total,
		Items:     resp,
		TotalPage: int64(math.Ceil(float64(total) / float64(req.PageSize))),
	})
}

// RetrieveFilterField 获取过滤字段详情
// @Summary Retrieve FilterField 获取过滤字段详情
// @Tags risk-portal-api/FilterField 过滤字段/FilterField 过滤字段管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.FilterFieldRetrieve true "Request body"
// @Success 200 {object} response_parameters.FilterFieldResp "过滤字段详情"
// @Router /api/v2/filter_field/retrieve [post]
func (h *FilterFieldHandle) Retrieve(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.FilterFieldRetrieve](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var data *modelv2.RmsFilterField
	data, err = h.FilterFieldService.RetrieveByName(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var resp = response_parameters.FilterFieldResp{
		FilterFieldUpdate: response_parameters.FilterFieldUpdate{
			ID: data.ID,
			FilterFieldCreate: response_parameters.FilterFieldCreate{
				CheckPoint: data.CheckPoint,
				FieldName:  data.FieldName,
				FieldEnum:  data.FieldEnum,
				Memo:       data.Memo,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}
