package handlev2

import (
	"github.com/gin-gonic/gin"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type EventStoreCfgHandle struct {
	EventStoreCfgService *servicev2.EventStoreCfgService
}

// Create 创建事件配置
// @Summary Create 创建事件配置
// @Tags risk-portal-api/event store cfg 事件配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.EventStoreCfgCreate true "Request body"
// @Success 200 {array} response_parameters.EventStoreCfgResp
// @Router /api/v2/event_store_cfg/create [post]
func (h *EventStoreCfgHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventStoreCfgCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var data = &modelv2.RmsEventStoreCfg{
		CheckPoint: req.CheckPoint,
		Persistent: req.Persistent,
		StrField1:  &req.StrField1,
		StrField2:  &req.StrField2,
		StrField3:  &req.StrField3,
		StrField4:  &req.StrField4,
		StrField5:  &req.StrField5,
		NumField1:  &req.NumField1,
		NumField2:  &req.NumField2,
	}
	err = h.EventStoreCfgService.Create(c, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.EventStoreCfgResp{
		EventStoreCfgUpdate: response_parameters.EventStoreCfgUpdate{
			ID: data.ID,
			EventStoreCfgCreate: response_parameters.EventStoreCfgCreate{
				CheckPoint: data.CheckPoint,
				Persistent: data.Persistent,
				StrField1:  data.StrField1,
				StrField2:  data.StrField2,
				StrField3:  data.StrField3,
				StrField4:  data.StrField4,
				StrField5:  data.StrField5,
				NumField1:  data.NumField1,
				NumField2:  data.NumField2,
			},
		},
	})
}

// Update 更新事件配置
// @Summary Update 更新事件配置
// @Tags risk-portal-api/event store cfg 事件配置
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.EventStoreCfgUpdate true "Request body"
// @Success 200 {array} response_parameters.EventStoreCfgResp
// @Router /api/v2/event_store_cfg/update [post]
func (h *EventStoreCfgHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventStoreCfgUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsEventStoreCfg{
		ID:         req.ID,
		CheckPoint: req.CheckPoint,
		Persistent: req.Persistent,
		StrField1:  &req.StrField1,
		StrField2:  &req.StrField2,
		StrField3:  &req.StrField3,
		StrField4:  &req.StrField4,
		StrField5:  &req.StrField5,
		NumField1:  &req.NumField1,
		NumField2:  &req.NumField2,
	}
	err = h.EventStoreCfgService.Update(c, req.ID, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.EventStoreCfgResp{
		EventStoreCfgUpdate: response_parameters.EventStoreCfgUpdate{
			ID: data.ID,
			EventStoreCfgCreate: response_parameters.EventStoreCfgCreate{
				CheckPoint: data.CheckPoint,
				Persistent: data.Persistent,
				StrField1:  data.StrField1,
				StrField2:  data.StrField2,
				StrField3:  data.StrField3,
				StrField4:  data.StrField4,
				StrField5:  data.StrField5,
				NumField1:  data.NumField1,
				NumField2:  data.NumField2,
			},
		},
	})
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /event_store_cfg/delete/{id} [get]
func (h *EventStoreCfgHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.EventStoreCfgService.Delete(c, id))
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary List EventStoreCfg
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventStoreCfgList true "Request body"
// @Success 200 {object} domain.EventStoreCfgResp
// @Router /event_store_cfg/list [post]
func (h *EventStoreCfgHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.EventStoreCfgList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var total int64
	var data []*response_parameters.EventStoreCfgResp
	total, data, err = h.EventStoreCfgService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer[response_parameters.EventStoreCfgResp](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		data,
	), err)
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary Retrieve EventStoreCfg
// @Param access_point_code path string true "access point code"
// @Success 200 {object} domain.EventStoreCfgResp
// @Router /event_store_cfg/retrieve/{access_point_code} [get]
func (h *EventStoreCfgHandle) Retrieve(c *gin.Context) {
	var apCode, err = request.GetParam(c, "apCode")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err := h.EventStoreCfgService.RetrieveByAPCode(c, apCode)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.EventStoreCfgResp{
		EventStoreCfgUpdate: response_parameters.EventStoreCfgUpdate{
			ID: data.ID,
			EventStoreCfgCreate: response_parameters.EventStoreCfgCreate{
				CheckPoint: data.CheckPoint,
				Persistent: data.Persistent,
				StrField1:  data.StrField1,
				StrField2:  data.StrField2,
				StrField3:  data.StrField3,
				StrField4:  data.StrField4,
				StrField5:  data.StrField5,
				NumField1:  data.NumField1,
				NumField2:  data.NumField2,
			},
		},
	})
}
