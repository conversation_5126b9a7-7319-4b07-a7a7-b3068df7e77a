package handlev2

import (
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

// IndicatorScriptHandle 指标配置处理器
type IndicatorScriptHandle struct {
	BaseHandle
	IndicatorService *servicev2.IndicatorService
}

// Create 创建指标配置
// @Summary Create Indicator 创建指标配置
// @Tags risk-portal-api/Indicator 指标/Script Indicator Script指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CreateScriptIndicator true "Request body"
// @Success 200 {object} response_parameters.IndicatorCreateResponse "指标配置创建成功"
// @Router /api/v2/indicator_script [post]
func (h *IndicatorScriptHandle) Create(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.CreateScriptIndicator](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 创建指标配置
	id, versionID, err := h.IndicatorService.CreateScript(c, &req)
	if response.HeadErr(c, err) {
		return
	}
	err = h.IndicatorService.GenerateScriptVersionLog(c, h.GetUsername(c), id)
	if response.HeadErr(c, err) {
		return
	}
	// 返回创建结果
	response.ReturnSuccess(c, response_parameters.IndicatorCreateResponse{
		IndicatorId: id,
		VersionID:   versionID,
	})
}

// Update 更新Script指标配置
// @Summary Update Indicator 更新指标配置
// @Tags risk-portal-api/Indicator 指标/Script Indicator Script指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.UpdateScriptIndicator true "Request body"
// @Success 200 {object} nil "指标配置更新成功"
// @Router /api/v2/indicator_script [put]
func (h *IndicatorScriptHandle) Update(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.UpdateScriptIndicator](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 更新指标配置
	err = h.IndicatorService.UpdateScript(c, &req)
	if response.HeadErr(c, err) {
		return
	}
	err = h.IndicatorService.GenerateScriptVersionLog(c, h.GetUsername(c), req.ID)
	if response.HeadErr(c, err) {
		return
	}
	// 返回更新结果
	response.ReturnSuccess(c, nil)
}

// List 获取Script指标配置列表
// @Summary Get Indicator List 获取指标配置列表
// @Tags risk-portal-api/Indicator 指标/Script Indicator Script指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.GetIndicatorScriptList true "Request body"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.IndicatorScriptList]
// @Router /api/v2/indicator_script/list [post]
func (h *IndicatorScriptHandle) List(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.GetIndicatorScriptList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 获取指标配置列表
	total, items, err := h.IndicatorService.ListScript(c, &req)
	if response.HeadErr(c, err) {
		return
	}

	// 返回列表结果
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.IndicatorScriptList]{
		TotalNum:  total,
		Items:     items,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// Delete 删除指标配置
// @Summary Delete Indicator 删除指标配置
// @Tags risk-portal-api/Indicator 指标/Script Indicator Script指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "指标ID"
// @Success 200 {object} nil "指标配置删除成功"
// @Router /api/v2/indicator_script/:id [delete]
func (h *IndicatorScriptHandle) Delete(c *gin.Context) {
	// 解析请求参数
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 删除指标配置
	err = h.IndicatorService.Delete(c, id)
	if response.HeadErr(c, err) {
		return
	}

	// 返回删除结果
	response.ReturnSuccess(c, nil)
}

// Retrieve 获取Script指标配置详情
// @Summary Get Indicator  Detail 获取指标配置详情
// @Tags risk-portal-api/Indicator 指标/Script Indicator Script指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id query int true "指标ID"
// @Param version_id query int true "版本短ID"
// @Success 200 {object} response_parameters.IndicatorScriptDetail
// @Router /api/v2/indicator_script [get]
func (h *IndicatorScriptHandle) Retrieve(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestQueryBind[request_parameters.GetIndicatorDetail](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 获取指标配置详情
	detail, err := h.IndicatorService.RetrieveScript(c, req.ID, req.VersionID)
	if response.HeadErr(c, err) {
		return
	}

	// 返回详情结果
	response.ReturnSuccess(c, detail)
}

// UpdateStatus 更新指标状态
// @Summary Update Indicator Status 更新指标状态
// @Tags risk-portal-api/Indicator 指标/Script Indicator Script指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.UpdateIndicatorStatus true "Request body"
// @Success 200 {object} nil "指标状态更新成功"
// @Router /api/v2/indicator_script/status [post]
func (h *IndicatorScriptHandle) UpdateStatus(c *gin.Context) {
	// 解析请求参数
	var req, err = request.GetRequestBodyJson[request_parameters.UpdateIndicatorStatus](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 更新指标状态
	err = h.IndicatorService.UpdateStatus(c, &req)
	if response.HeadErr(c, err) {
		return
	}
	if req.Disable {
		//禁用，删除缓存
		// 刷新指标缓存
		err = h.IndicatorService.DeleteIndicatorCache(c, req.ID)
		if err != nil {
			response.ReturnFailure(c, err)
			return
		}
	} else {
		// 刷新指标缓存
		err = h.IndicatorService.FlushIndicatorCache(c, req.ID)
		if err != nil {
			response.ReturnFailure(c, err)
			return
		}
	}
	// 返回更新结果
	response.ReturnSuccess(c, nil)
}

// TestScriptIndicatorSql 测试Script指标SQL
// @Summary Test Script Indicator SQL 测试Script指标SQL
// @Tags risk-portal-api/Indicator 指标/Visual Indicator 可视化指标
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.TestScriptIndicatorSql true "Request body"
// @Success 200 {object} map[string]interface{} "指标SQL测试成功"
// @Router /api/v2/indicator_script/testing [post]
func (h *IndicatorScriptHandle) TestScriptIndicatorSql(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.TestScriptIndicatorSql](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var res map[string]interface{}
	// 生成指标SQL
	res, err = h.IndicatorService.TestScriptIndicatorSql(c, req.IndicatorID, req.VersionID)
	if response.HeadErr(c, err) {
		return
	}
	response.ReturnSuccess(c, res)
}
