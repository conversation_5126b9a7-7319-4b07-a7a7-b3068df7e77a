package handlev2

import (
	"github.com/gin-gonic/gin"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	response_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type OperatorLogHandle struct {
	OperatorLogService *servicev2.OperatorLogService
}

// @Tags OperatorLog 风控操作日志
// @Security x-auth-token
// @Summary List OperatorLog
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.OperatorLogList true "Request body"
// @Success 200 {object} modelv2.RmsOperatorLog
// @Router /api/v2/operator_log/list [post]
func (h *OperatorLogHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.OperatorLogList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var (
		total int64
		items []*modelv2.RmsOperatorLog
	)
	total, items, err = h.OperatorLogService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer[modelv2.RmsOperatorLog](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		items,
	), err)
}

// @Tags OperatorLog 风控操作日志
// @Security x-auth-token
// @Summary Retrieve OperatorLog
// @Success 200 {object} modelv2.RmsOperatorLog
// @Router /api/v2/operator_log/retrieve/{id} [get]
func (h *OperatorLogHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var data *modelv2.RmsOperatorLog
	data, err = h.OperatorLogService.Retrieve(c, id)

	response.FeedBack(c, data, err)
}
