package handlev2

import (
	"math"
	"strconv"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"

	"github.com/gin-gonic/gin"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
)

// DataSourceTableHandle 数据源表信息处理器
type DataSourceTableHandle struct {
	BaseHandle
	DataSourceTableService *servicev2.DataSourceTableService
}

// Create 创建数据源表信息
// @Summary Create DataSourceTable 创建数据源表信息
// @Tags risk-portal-api/DataSource 数据源管理/Table 表信息管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CreateDataSourceTable true "Request body"
// @Success 200 {object} response_parameters.NormalCreateResponse "获取成功"
// @Router /api/v2/data_source_table [post]
func (h *DataSourceTableHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.CreateDataSourceTable](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var id int64
	id, err = h.DataSourceTableService.Create(c, h.GetUsername(c), req)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, response_parameters.NormalCreateResponse{Id: id})
}

// Delete 删除数据源表信息
// @Summary Delete DataSourceTable 删除数据源表信息
// @Tags risk-portal-api/DataSource 数据源管理/Table 表信息管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "数据源表信息ID"
// @Router /api/v2/data_source_table/{id} [delete]
func (h *DataSourceTableHandle) Delete(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	err = h.DataSourceTableService.Delete(c, id)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, nil)
}

// List 获取数据源表信息列表
// @Summary Get DataSourceTable List 获取数据源表信息列表
// @Tags risk-portal-api/DataSource 数据源管理/Table 表信息管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.GetDataSourceTableList true "Request body"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.DataSourceTableList] "获取成功"
// @Router /api/v2/data_source_table/list [post]
func (h *DataSourceTableHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.GetDataSourceTableList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	total, res, err := h.DataSourceTableService.List(c, req)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.DataSourceTableList]{
		TotalNum:  total,
		Items:     res,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// Retrieve 获取数据源表信息详情
// @Summary Get DataSourceTable Detail 获取数据源表信息详情
// @Tags risk-portal-api/DataSource 数据源管理/Table 表信息管理
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "数据源表信息ID"
// @Success 200 {object} response_parameters.DataSourceTableDetail "获取成功"
// @Router /api/v2/data_source_table/{id} [get]
func (h *DataSourceTableHandle) Retrieve(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	res, err := h.DataSourceTableService.Retrieve(c, id)
	if response.HeadErr(c, err) {
		return
	}

	response.ReturnSuccess(c, res)
}

// Columns 获取数据源表字段列表
// @Summary Get DataSource Columns 获取数据源表字段列表
// @Tags risk-portal-api/DataSource 数据源管理/Table 表信息管理
// @Param table_id query string true "表ID"
// @Param column_type query string true "字段类型 DateTime：时间类型"
// @Security x-auth-token
// @Success 200 {array} entity.DataSourceColumn  "字段列表"
// @Router /api/v2/data_source_table/columns [get]
func (h *DataSourceTableHandle) Columns(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.DataSourceColumns](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var res []*entity.DataSourceColumn
	res, err = h.DataSourceTableService.Columns(c.Request.Context(), req.TableID, req.ColumnType)
	if response.HeadErr(c, err) {
		return
	}
	response.ReturnSuccess(c, res)
}
