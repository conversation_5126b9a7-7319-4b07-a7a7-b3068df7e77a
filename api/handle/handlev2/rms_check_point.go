package handlev2

import (
	"math"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type CheckPointHandle struct {
	BaseHandle
	CheckPointService *servicev2.CheckPointService
}

func NewCheckPointHandle() *CheckPointHandle {
	return &CheckPointHandle{
		CheckPointService: servicev2.NewCheckPointService(),
	}
}

// Create 创建检查点
// @Summary Create CheckPoint 创建检查点
// @Tags risk-portal-api/CheckPoint 接入点
// @Accept json
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CheckPointCreate true "请求体"
// @Success 200 {object} response_parameters.CheckPointResp "创建成功"
// @Router /api/v2/access_point/create [post]
func (h *CheckPointHandle) Create(c *gin.Context) {
	req, err := request.GetRequestBodyJson[request_parameters.CheckPointCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	model := &modelv2.RmsCheckPoint{
		Code:           req.Code,
		Label:          req.Name,
		CheckDuplicate: req.CheckDuplicate,
		Memo:           req.Remark,
		AlwaysRun:      req.AlwaysRun,
		BusinessType:   req.BusinessType,
	}
	err = h.CheckPointService.Create(c.Request.Context(), model)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := response_parameters.CheckPointResp{
		ID:             model.ID,
		Code:           model.Code,
		Name:           model.Label,
		BusinessType:   model.BusinessType,
		CheckDuplicate: model.CheckDuplicate,
		Remark:         model.Memo,
		AlwaysRun:      model.AlwaysRun,
	}
	response.ReturnSuccess(c, resp)
}

// Update 更新检查点
// @Summary Update CheckPoint 更新检查点
// @Tags risk-portal-api/CheckPoint 接入点
// @Accept json
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CheckPointUpdate true "请求体"
// @Success 200 {object} response_parameters.CheckPointResp "更新成功"
// @Router /api/v2/access_point/update [post]
func (h *CheckPointHandle) Update(c *gin.Context) {
	req, err := request.GetRequestBodyJson[request_parameters.CheckPointUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var cp *modelv2.RmsCheckPoint
	cp, err = h.CheckPointService.Update(c.Request.Context(), &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := response_parameters.CheckPointResp{
		ID:             req.ID,
		Code:           req.Code,
		Name:           req.Name,
		BusinessType:   cp.BusinessType,
		CheckDuplicate: req.CheckDuplicate,
		Remark:         req.Remark,
		AlwaysRun:      req.AlwaysRun,
	}
	response.ReturnSuccess(c, resp)
}

// Delete 删除检查点
// @Summary Delete CheckPoint 删除检查点
// @Tags risk-portal-api/CheckPoint 接入点
// @Accept json
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param id path int true "检查点ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Router /api/v2/access_point/delete/{id} [get]
func (h *CheckPointHandle) Delete(c *gin.Context) {
	id, err := request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	err = h.CheckPointService.Delete(c.Request.Context(), id)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, gin.H{"id": id})
}

// List 检查点列表
// @Summary List CheckPoint 检查点列表
// @Tags risk-portal-api/CheckPoint 接入点
// @Accept json
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.CheckPointList true "请求体"
// @Success 200 {object} response_parameters.CheckPointListResp "列表数据"
// @Router /api/v2/access_point/list [post]
func (h *CheckPointHandle) List(c *gin.Context) {
	req, err := request.GetRequestBodyJson[request_parameters.CheckPointList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	filterObj := &filter.DefaultFilter{}
	if req.PageRequest.PageNum > 0 && req.PageRequest.PageSize > 0 {
		filterObj.Page = filter.NewPaging(req.PageRequest.PageNum, req.PageRequest.PageSize)
	}
	total, items, err := h.CheckPointService.List(c.Request.Context(), filterObj, req.Code, req.Label)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 返回列表结果
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.CheckPointResp]{
		TotalNum:  total,
		Items:     items,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// Retrieve 获取单个检查点
// @Summary Retrieve CheckPoint 获取单个检查点
// @Tags risk-portal-api/CheckPoint 接入点
// @Accept json
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param code path string true "检查点编码"
// @Success 200 {object} response_parameters.CheckPointResp "详情数据"
// @Router /api/v2/access_point/retrieve/{code} [get]
func (h *CheckPointHandle) Retrieve(c *gin.Context) {
	code := c.Param("code")
	item, err := h.CheckPointService.RetrieveByCode(c.Request.Context(), code)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	response.ReturnSuccess(c, item)
}

// AllName 获取所有检查点名称和编码
// @Summary AllName CheckPoint 获取所有检查点名称和编码
// @Tags risk-portal-api/CheckPoint 接入点
// @Accept json
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Success 200 {array} response_parameters.CheckPointNameLabel "名称列表"
// @Router /api/v2/access_point/all_name [post]
func (h *CheckPointHandle) AllName(c *gin.Context) {
	_, items, err := h.CheckPointService.List(c.Request.Context(), nil, "", "")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := make([]response_parameters.CheckPointNameLabel, 0, len(items))
	for _, item := range items {
		resp = append(resp, response_parameters.CheckPointNameLabel{
			Code:  item.Code,
			Label: item.Name,
		})
	}
	response.ReturnSuccess(c, resp)
}
