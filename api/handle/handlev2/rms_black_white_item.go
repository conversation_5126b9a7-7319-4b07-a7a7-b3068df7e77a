package handlev2

import (
	"errors"
	"math"
	"strconv"
	"strings"
	"time"

	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/checkfile"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/ztime"
)

type BlackWhiteItemHandle struct {
	BaseHandle
	BlackWhiteListService *servicev2.BlackWhiteListService
	BlackWhiteItemService *servicev2.BlackWhiteItemService
}

// DownloadBlackWhiteListItemTemplate 下载黑白名单明细Excel模板
// @Summary Download BlackWhiteListItemTemplate 下载黑白名单明细Excel模板
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Security x-auth-token
// @Produce json
// @Param x-auth-token header string true "jwt token"
// @Param bwl_id path int true "模板ID"
// @Router /api/v2/black_white/template/{bwl_id} [get]
func (h *BlackWhiteItemHandle) DownloadBlackWhiteListItemTemplate(c *gin.Context) {
	// 从URL路径中获取id参数
	idStr := c.Param("bwl_id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Invalid ID"))
		return
	}
	filename, res, err := h.BlackWhiteItemService.DownloadBlackWhiteListItemTemplate(c, id)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnFileSuccess(c, res, filename)
}

// UploadBlackWhiteItemFile 上传黑白名单明细文件
// @Summary Upload BlackWhiteListItemFile 上传黑白名单明细文件
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Security x-auth-token
// @Produce json
// @Accept multipart/form-data
// @Param x-auth-token header string true "jwt token"
// @Param bwl_id formData int true "黑白名单ID"
// @Param file formData file true "Excel or CSV file"
// @Param recover formData bool false "是否覆盖重复项"
// @Router /api/v2/black_white/item/upload [post]
func (h *BlackWhiteItemHandle) UploadBlackWhiteItemFile(c *gin.Context) {
	// 获取文件和参数
	var fileType string
	var fileMaxSize int64 = 20 * 1024 * 1024 // 20MB

	excelFile, filename, err := request.GetRequestFormFile(c, "file", fileMaxSize)

	if errors.Is(err, er.RequestFileTooLarge) {
		err = er.RequestFileTooLarge.WithMsg("The file size exceeds the limit of [20MB].Please upload a smaller file.")
		response.ReturnFailure(c, err)
		return
	}
	if err != nil {
		err = er.BlackWhiteFileUnsupportedFormat.WithMsg("Unsupported file format. \nPlease upload Excel (.xlsx, .xis) or CSV files.").WithErr(err)
		response.ReturnFailure(c, err)
		return
	}
	// 获取bwl_id参数并转换为int64
	bwlID, err := strconv.ParseInt(c.PostForm("bwl_id"), 10, 64)
	if err != nil {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Invalid bwl_id"))
		return
	}

	// 获取recover参数并转换为bool
	rewrite, _ := strconv.ParseBool(c.PostForm("recover"))
	// 验证文件格式
	if checkfile.Excel(excelFile) {
		fileType = "excel"
	}
	if checkfile.CSV(excelFile) {
		fileType = "csv"
	}
	if fileType == "" {
		if strings.Contains(filename, ".xlsx") || strings.Contains(filename, ".xls") || strings.Contains(filename, ".csv") {
			err = er.BlackWhiteFileUnsupportedFormat.WithMsg("Error - The file is corrupted or cannot be read.")
		} else {
			err = er.BlackWhiteFileUnsupportedFormat.WithMsg("Unsupported file format. \nPlease upload Excel (.xlsx, .xis) or CSV files.").WithErr(err)
		}
		response.ReturnFailure(c, err)
		return
	}
	// 调用service处理上传
	err = h.BlackWhiteItemService.UploadBlackWhiteItems(c, &entity.UploadBlackWhiteItemFile{
		FileType:  fileType,
		BwlID:     bwlID,
		File:      excelFile,
		Recover:   rewrite,
		Submitter: h.GetUsername(c),
	})

	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	response.ReturnSuccess(c, nil, "File uploaded successfully")
}

// AddBlackWhiteItem 新增黑白名单记录明细
// @Summary Create BlackWhiteListItem 创建黑白名单记录明细
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Security x-auth-token
// @Produce json
// @Accept json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.AddBlackWhiteItem true "Request body"
// @Success 200 {object} response_parameters.CreateBlackWhiteItem
// @Router /api/v2/black_white/item [post]
func (h *BlackWhiteItemHandle) AddBlackWhiteItem(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.AddBlackWhiteItem](c)
	var now = ztime.StartOfDay(time.Now()).Unix()
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	if req.StartDate > 0 {
		req.StartDate = ztime.StartOfDay(time.Unix(req.StartDate, 0)).Unix()
	}
	if req.StartDate == 0 {
		req.StartDate = now
	}
	if req.EndDate > 0 {
		req.EndDate = ztime.EndOfDay(time.Unix(req.EndDate, 0)).Unix()
	}
	if req.EndDate == 0 || req.Permanent {
		req.EndDate = modelv2.ForeverTime.Unix()
	}
	if req.EndDate <= req.StartDate {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("End date must be greater than start date"))
		return
	}
	if req.StartDate < now {
		// 如果开始日期小于当前时间，则返回错误
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Start date must be greater than current time"))
		return
	}
	var id int64
	id, err = h.BlackWhiteItemService.Add(c, h.GetUsername(c), &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.CreateBlackWhiteItem{
		BlackWhiteItemID: id,
	}, "Item created successfully.")
}

// UpdateBlackWhiteItem 更新黑白名单记录明细
// @Summary Update BlackWhiteListItem 更新黑白名单记录明细
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Security x-auth-token
// @Produce json
// @Accept json
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.UpdateBlackWhiteItem true "Request body"
// @Success 200 {object} response_parameters.CreateBlackWhiteItem
// @Router /api/v2/black_white/item [put]
func (h *BlackWhiteItemHandle) UpdateBlackWhiteItem(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.UpdateBlackWhiteItem](c)
	var now = ztime.StartOfDay(time.Now()).Unix()
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	if req.StartDate > 0 {
		req.StartDate = ztime.StartOfDay(time.Unix(req.StartDate, 0)).Unix()
	}
	if req.StartDate == 0 {
		req.StartDate = now
	}
	if req.EndDate > 0 {
		req.EndDate = ztime.EndOfDay(time.Unix(req.EndDate, 0)).Unix()
	}
	if req.EndDate == 0 || req.Permanent {
		req.EndDate = modelv2.ForeverTime.Unix()
	}
	if req.EndDate <= req.StartDate {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("End date must be greater than start date"))
		return
	}
	if req.StartDate < now {
		// 如果开始日期小于当前时间，则返回错误
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Start date must be greater than current time"))
		return
	}
	var id int64
	id, err = h.BlackWhiteItemService.Update(c, h.GetUsername(c), &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.CreateBlackWhiteItem{
		BlackWhiteItemID: id,
	}, "Item update successfully.")
}

// RemoveBlackWhiteItem 删除黑白名单记录明细
// @Summary Delete BlackWhiteListItem 删除黑白名单记录明细
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Security x-auth-token
// @Produce json
// @Accept json
// @Param x-auth-token header string true "jwt token"
// @Param bwi_id path int true "黑白名单记录明细ID"
// @Success 200 {object} response_parameters.CreateBlackWhiteItem
// @Router /api/v2/black_white/item/{bwi_id} [delete]
func (h *BlackWhiteItemHandle) RemoveBlackWhiteItem(c *gin.Context) {
	// 从URL路径中获取id参数
	idStr := c.Param("bwi_id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Invalid ID"))
		return
	}
	err = h.BlackWhiteItemService.Delete(c, nil, h.GetUsername(c), id)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, nil, "Item deleted successfully.")

}

// GetBlackWhiteItems 获取黑白名单明细列表(编辑页调用的明细列表)
// @Summary Get BlackWhiteItems 获取黑白名单明细列表
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param page_num query int true "页码"
// @Param page_size query int false "每页条数"
// @Param bwl_id query int true "黑白名单列表ID"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.BlackWhiteItems]
// @Router /api/v2/black_white/items [get]
func (h *BlackWhiteItemHandle) GetBlackWhiteItems(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.GetBlackWhiteItems](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var total int64
	var res []*modelv2.RmsBlackWhiteItem
	total, res, err = h.BlackWhiteItemService.GetBlackWhiteItems(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var items []*response_parameters.BlackWhiteItems
	for _, v := range res {
		tmpExtends, _ := v.Extends.ToFields()
		tmpItem := &response_parameters.BlackWhiteItems{
			BlackWhiteItemID: v.ID,
			StartDate:        v.StartDate,
			ExpirationDate:   v.EndDate,
			RemainingDay:     v.RemainingDay,
			Extends:          tmpExtends,
		}
		if v.EndDate >= modelv2.ForeverTime.Unix() {
			tmpItem.Permanent = true
			tmpItem.ExpirationDate = 0
			tmpItem.RemainingDay = 0
		}
		items = append(items, tmpItem)
	}
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.BlackWhiteItems]{
		TotalNum:  total,
		Items:     items,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}

// GetBlackWhiteItemsView 获取黑白名单明细列表（查看页调用的明细列表）
// @Summary Get BlackWhiteItemsView 获取黑白名单明细列表
// @Tags risk-portal-api/BlackWhite 黑白名单/Item 黑白名单明细管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param page_num query int true "页码"
// @Param page_size query int false "每页条数"
// @Param bwl_id query int true "黑白名单列表ID"
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.BlackWhiteItems]
// @Router /api/v2/black_white/items/view [get]
func (h *BlackWhiteItemHandle) GetBlackWhiteItemsView(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.GetBlackWhiteItems](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var total int64
	var res []*modelv2.RmsBlackWhiteItem
	total, res, err = h.BlackWhiteItemService.GetBlackWhiteItemsView(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var items []*response_parameters.BlackWhiteItems
	for _, v := range res {
		tmpExtends, _ := v.Extends.ToFields()
		tmpItem := &response_parameters.BlackWhiteItems{
			BlackWhiteItemID: v.ID,
			StartDate:        v.StartDate,
			ExpirationDate:   v.EndDate,
			RemainingDay:     v.RemainingDay,
			Extends:          tmpExtends,
		}
		if v.EndDate == modelv2.ForeverTime.Unix() {
			tmpItem.Permanent = true
			tmpItem.ExpirationDate = 0
			tmpItem.RemainingDay = 0
		}
		items = append(items, tmpItem)
	}
	response.ReturnSuccess(c, response_parameters.ListResponsePointer[response_parameters.BlackWhiteItems]{
		TotalNum:  total,
		Items:     items,
		TotalPage: int64(math.Ceil(float64(total) / float64(filter.NewPaging(req.PageNum, req.PageSize).PageSize))),
	})
}
