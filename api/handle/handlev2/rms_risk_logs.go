package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type RiskLogHandle struct {
	RiskLogService *servicev2.RiskLogService
}

// @Tags RiskLog 风控日志
// @Security x-auth-token
// @Summary List RiskLog
// @Param x-auth-token header string true "jwt token"
// @Param body body request_parameters.RiskLogList true "Request body"
// @Success 200 {object} modelv2.RmsRiskLog
// @Router /risk_log/list [post]
func (h *RiskLogHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RiskLogList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var (
		total int64
		data  []*modelv2.RmsRiskLog
	)
	total, data, err = h.RiskLogService.List(c, &req)
	response.FeedBack(c, response_parameters.NewListResponsePointer[modelv2.RmsRiskLog](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		data,
	), err)
}

// @Tags RiskLog 风控日志
// @Security x-auth-token
// @Summary Retrieve RiskLog
// @Success 200 {object} model.RmsRiskLog
// @Router /risk_log/retrieve/{id} [get]
func (h *RiskLogHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err := h.RiskLogService.Retrieve(c, id)
	response.FeedBack(c, data, err)
}
