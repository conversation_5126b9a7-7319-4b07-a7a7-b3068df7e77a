package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type RuleParameterGroupHandle struct {
	RuleParameterGroupService *servicev2.RuleParameterGroupService
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Create RuleParameterGroup
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupCreate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /api/v2/rule_parameter_group/create [post]
func (h *RuleParameterGroupHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsRuleParameterGroup{
		Code:        req.Code,
		ParamIds:    *req.ParamIds,
		CheckPoint:  req.CheckPoint,
		Description: req.Description,
		DefaultFlag: req.DefaultFlag,
		Able:        req.Able,
	}
	err = h.RuleParameterGroupService.Create(c, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := &response_parameters.RuleParameterGroupResp{
		RuleParameterGroupUpdate: response_parameters.RuleParameterGroupUpdate{
			ID: data.ID,
			RuleParameterGroupCreate: response_parameters.RuleParameterGroupCreate{
				CheckPoint:  data.CheckPoint,
				Code:        data.Code,
				Able:        data.Able,
				Description: data.Description,
				DefaultFlag: data.DefaultFlag,
				ParamIds:    &data.ParamIds,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Update RuleParameterGroup
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupUpdate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /api/v2/rule_parameter_group/update [post]
func (h *RuleParameterGroupHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsRuleParameterGroup{
		ID:          req.ID,
		Code:        req.Code,
		ParamIds:    *req.ParamIds,
		CheckPoint:  req.CheckPoint,
		Description: req.Description,
		DefaultFlag: req.DefaultFlag,
		Able:        req.Able,
	}

	if err = h.RuleParameterGroupService.Update(c, req.ID, data); err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp := &response_parameters.RuleParameterGroupResp{
		RuleParameterGroupUpdate: response_parameters.RuleParameterGroupUpdate{
			ID: data.ID,
			RuleParameterGroupCreate: response_parameters.RuleParameterGroupCreate{
				CheckPoint:  data.CheckPoint,
				Code:        data.Code,
				Able:        data.Able,
				Description: data.Description,
				DefaultFlag: data.DefaultFlag,
				ParamIds:    &data.ParamIds,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule_parameter_group/delete/{id} [get]
func (h *RuleParameterGroupHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.RuleParameterGroupService.Delete(c, id))
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary List RuleParameterGroup
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupList true "Request body"
// @Success 200 {object} domain.RuleParameterGroupListResp
// @Router /api/v2/rule_parameter_group/list [post]
func (h *RuleParameterGroupHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterGroupList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var total int64
	var data []*modelv2.RmsRuleParameterGroup
	total, data, err = h.RuleParameterGroupService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var resItems []*response_parameters.RuleParameterGroupResp
	for _, datum := range data {
		resItems = append(resItems, &response_parameters.RuleParameterGroupResp{
			RuleParameterGroupUpdate: response_parameters.RuleParameterGroupUpdate{
				ID: datum.ID,
				RuleParameterGroupCreate: response_parameters.RuleParameterGroupCreate{
					CheckPoint:  datum.CheckPoint,
					Code:        datum.Code,
					Able:        datum.Able,
					Description: datum.Description,
					DefaultFlag: datum.DefaultFlag,
					ParamIds:    &datum.ParamIds,
				},
			},
		})
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer[response_parameters.RuleParameterGroupResp](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		resItems,
	), err)
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary Retrieve RuleParameterGroup
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /api/v2/rule_parameter_group/retrieve/{id} [get]
func (h *RuleParameterGroupHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var data *modelv2.RmsRuleParameterGroup
	data, err = h.RuleParameterGroupService.Retrieve(c, id)
	if err != nil {
		return
	}
	resp := &response_parameters.RuleParameterGroupResp{
		RuleParameterGroupUpdate: response_parameters.RuleParameterGroupUpdate{
			ID: data.ID,
			RuleParameterGroupCreate: response_parameters.RuleParameterGroupCreate{
				CheckPoint:  data.CheckPoint,
				Code:        data.Code,
				Able:        data.Able,
				Description: data.Description,
				DefaultFlag: data.DefaultFlag,
				ParamIds:    &data.ParamIds,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary All RuleParameterGroup
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /api/v2/rule_parameter_group/all [get]
func (h *RuleParameterGroupHandle) All(c *gin.Context) {
	data, err := h.RuleParameterGroupService.All(c)
	response.FeedBack(c, data, err)
}

// @Tags V2 RuleParameterGroup 规则参数组
// @Security x-auth-token
// @Summary all_exclude_default RuleParameterGroup
// @Success 200 {object} domain.RuleParameterGroupResp
// @Router /api/v2/rule_parameter_group/all_exclude_default [get]
func (h *RuleParameterGroupHandle) AllExcludeDefault(c *gin.Context) {
	data, err := h.RuleParameterGroupService.AllExcludeDefault(c)
	response.FeedBack(c, data, err)
}
