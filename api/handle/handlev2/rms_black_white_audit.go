package handlev2

import (
	"context"

	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type BlackWhiteAuditHandle struct {
	BaseHandle
	BlackWhiteAuditService *servicev2.BlackWhiteAuditService
}

// Detail 获取审核详情
// @Summary Detail 获取审核详情
// @Tags risk-portal-api/BlackWhite 黑白名单/Audit 审核管理
// @Produce json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Success 200 {array} response_parameters.BlackWhiteAuditDetail
// @Router /api/v2/black_white/audit/{id} [get]
func (h *BlackWhiteAuditHandle) Detail(c *gin.Context) {
	var res response_parameters.BlackWhiteAuditDetail
	id, err := request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	res, err = h.BlackWhiteAuditService.Detail(c, id)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, res)
}

// Audit 审核操作
// @Summary Audit 审核操作
// @Tags risk-portal-api/BlackWhite 黑白名单/Audit 审核管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.BlackWhiteAudit true "Request body"
// @Router /api/v2/black_white/audit [post]
func (h *BlackWhiteAuditHandle) Audit(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.BlackWhiteAudit](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	// 分配具体操作日志类型

	if !req.Pass && req.RejectReason == "" {
		response.ReturnFailure(c, er.InvalidParameter.WithMsg("The reason for rejection cannot be empty."))
		return
	}
	if req.Pass {
		c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), entity.BwOperatorLogSvc, []servicev2.BwLogAction{servicev2.NewBwLogApprove(req.ID)}))
	} else {
		c.Request = c.Request.WithContext(context.WithValue(c.Request.Context(), entity.BwOperatorLogSvc, []servicev2.BwLogAction{servicev2.NewBwLogReject(req.ID)}))
	}

	err = h.BlackWhiteAuditService.Audit(c, h.GetUsername(c), &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, nil)
	return

}

// List 获取审核列表
// @Summary List 获取审核列表
// @Tags risk-portal-api/BlackWhite 黑白名单/Audit 审核管理
// @Produce json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param page_num query int true "页码,type=3时不生效"
// @Param page_size query int false "每页条数,type=3时不生效"
// @Param type query int true "类型 0:所有 1.未处理审核 2.已处理审核 3.待提醒"
// @Router /api/v2/black_white/audit/list [get]
// @Success 200 {object} response_parameters.ListResponsePointer[response_parameters.BlackWhiteAuditList]
func (h *BlackWhiteAuditHandle) List(c *gin.Context) {
	var req, err = request.GetRequestQueryBind[request_parameters.BlackWhiteAuditRequest](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	total, respLi, err := h.BlackWhiteAuditService.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer(
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		respLi,
	), err)
}

// Mark 标记审核提醒为已提醒
// @Summary Mark 标记审核提醒为已提醒
// @Tags risk-portal-api/BlackWhite 黑白名单/Audit 审核管理
// @Produce json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Router /api/v2/black_white/audit/mark [get]
func (h *BlackWhiteAuditHandle) Mark(c *gin.Context) {
	err := h.BlackWhiteAuditService.Mark(c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, nil)
}
