package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type RuleHandle struct {
	RuleServiceV2          *servicev2.RuleService
	PolicyServiceV2        *servicev2.PolicyService
	EventStoreCfgServiceV2 *servicev2.EventStoreCfgService
	EventFieldServiceV2    *servicev2.EventFieldService
	CheckPointServiceV2    *servicev2.CheckPointService
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Create Rule
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleCreate true "Request body"
// @Success 200 {object} domain.RuleResp
// @Router /api/v2/rule/create [post]
func (h *RuleHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsRule{
		RuleNo:       req.RuleNo,
		RuleName:     req.RuleName,
		CheckPoint:   req.CheckPoint,
		RuleContent:  req.RuleContent,
		ShortCircuit: req.ShortCircuit,
		DeployMethod: req.DeployMethod,
		StartTime:    domain.NullableTime2Time(req.StartTime),
		EndTime:      domain.NullableTime2Time(req.EndTime),
		Status:       req.Status,
		Memo:         req.Memo,
		Priority:     req.Priority,
	}
	if err = h.RuleServiceV2.Create(c, data); err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.RuleResp{
		SyncedAt: data.SyncedAt,
		RuleUpdate: response_parameters.RuleUpdate{
			ID: data.ID,
			RuleCreate: response_parameters.RuleCreate{
				RuleNo:       data.RuleNo,
				RuleName:     data.RuleName,
				CheckPoint:   data.CheckPoint,
				ShortCircuit: data.ShortCircuit,
				Memo:         data.Memo,
				RuleContent:  data.RuleContent,
				DeployMethod: data.DeployMethod,
				StartTime:    domain.Time2NullableTime(data.StartTime),
				EndTime:      domain.Time2NullableTime(data.EndTime),
				Status:       data.Status,
				Priority:     data.Priority,
			},
		},
	})
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Update Rule
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleUpdate true "Request body"
// @Success 200 {object} domain.RuleResp
// @Router /api/v2/rule/update [post]
func (h *RuleHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data := &modelv2.RmsRule{
		ID:           req.ID,
		RuleNo:       req.RuleNo,
		RuleName:     req.RuleName,
		CheckPoint:   req.CheckPoint,
		RuleContent:  req.RuleContent,
		ShortCircuit: req.ShortCircuit,
		DeployMethod: req.DeployMethod,
		StartTime:    domain.NullableTime2Time(req.StartTime),
		EndTime:      domain.NullableTime2Time(req.EndTime),
		Status:       req.Status,
		Memo:         req.Memo,
		Priority:     req.Priority,
	}
	if err = h.RuleServiceV2.Update(c, req.ID, data); err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.RuleResp{
		SyncedAt: data.SyncedAt,
		RuleUpdate: response_parameters.RuleUpdate{
			ID: data.ID,
			RuleCreate: response_parameters.RuleCreate{
				RuleNo:       data.RuleNo,
				RuleName:     data.RuleName,
				CheckPoint:   data.CheckPoint,
				ShortCircuit: data.ShortCircuit,
				Memo:         data.Memo,
				RuleContent:  data.RuleContent,
				DeployMethod: data.DeployMethod,
				StartTime:    domain.Time2NullableTime(data.StartTime),
				EndTime:      domain.Time2NullableTime(data.EndTime),
				Status:       data.Status,
				Priority:     data.Priority,
			},
		},
	})

}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule/delete/{id} [get]
func (h *RuleHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.RuleServiceV2.Delete(c, id))
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary List Rule
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleList true "Request body"
// @Success 200 {object} domain.RuleResp
// @Router /api/v2/rule/list [post]
func (h *RuleHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var (
		total int64
		data  []*modelv2.RmsRule
	)
	total, data, err = h.RuleServiceV2.List(c, &req)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var res []*response_parameters.RuleResp
	for _, datum := range data {
		res = append(res, &response_parameters.RuleResp{
			SyncedAt: datum.SyncedAt,
			RuleUpdate: response_parameters.RuleUpdate{
				ID: datum.ID,
				RuleCreate: response_parameters.RuleCreate{
					RuleNo:       datum.RuleNo,
					RuleName:     datum.RuleName,
					CheckPoint:   datum.CheckPoint,
					ShortCircuit: datum.ShortCircuit,
					Memo:         datum.Memo,
					RuleContent:  datum.RuleContent,
					DeployMethod: datum.DeployMethod,
					StartTime:    domain.Time2NullableTime(datum.StartTime),
					EndTime:      domain.Time2NullableTime(datum.EndTime),
					Status:       datum.Status,
					Priority:     datum.Priority,
				},
			},
		})
	}
	response.FeedBack(c, response_parameters.NewListResponsePointer[response_parameters.RuleResp](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		res,
	), err)

}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Retrieve Rule
// @Param id path string true "17"
// @Success 200 {object} domain.RuleResp
// @Router /api/v2/rule/retrieve/{id} [get]
func (h *RuleHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err := h.RuleServiceV2.Retrieve(c, id)
	response.FeedBack(c, data, err)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Compile Rule, Returns whether the rule has errors.
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleCompile true "Request body"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule/compile [post]
func (h *RuleHandle) Compile(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleCompile](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.RuleServiceV2.Compile(req.RuleContent, req.RuleCode, req.RuleName, req.Priority))
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary SetStatus Rule, one of Online Offline
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleSetStatus true "Request body"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule/set_status [post]
func (h *RuleHandle) SetStatus(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleSetStatus](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.RuleServiceV2.SetStatus(c, req.ID, req.Status))

}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Refresh Rule Cache
// @Param x-auth-token header string true "jwt token"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule/refresh [get]
func (h *RuleHandle) Refresh(c *gin.Context) {
	err := h.RuleServiceV2.UpdateRuleCache(c.Request.Context())
	if response.HeadErr(c, err) {
		return
	}
	err = h.EventStoreCfgServiceV2.UpdateEventFieldCache(c.Request.Context())
	if response.HeadErr(c, err) {
		return
	}
	err = h.EventFieldServiceV2.UpdateEventFieldCache(c.Request.Context())
	if response.HeadErr(c, err) {
		return
	}
	err = h.CheckPointServiceV2.UpdateCheckPointCache(c.Request.Context())
	if response.HeadErr(c, err) {
		return
	}
	err = h.PolicyServiceV2.UpdatePolicyCache(c.Request.Context())
	if response.HeadErr(c, err) {
		return
	}
	err = h.RuleServiceV2.Refresh()
	response.FeedBack(c, nil, err)

}
