package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	response_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type RuleParameterValueHandle struct {
	RuleParameterValueService *servicev2.RuleParameterValueService
}

// @Tags V2 RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Create RuleParameterValue
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RmsRuleParameterValueCreate true "Request body"
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /api/v2/rule_parameter_value/create [post]
func (h *RuleParameterValueHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RmsRuleParameterValueCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	err = h.RuleParameterValueService.Create(c, &req)
	response.FeedBack(c, nil, err)
}

// @Tags V2 RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Update RuleParameterValue
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RmsRuleParameterValueUpdate true "Request body"
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /api/v2/rule_parameter_value/update [post]
func (h *RuleParameterValueHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RmsRuleParameterValueUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
	}
	resModel, err := h.RuleParameterValueService.Update(c, req.ID, &req)
	response.FeedBack(c, resModel, err)
}

// @Tags V2 RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule_parameter_value/delete/{id} [get]
func (h *RuleParameterValueHandle) Delete(c *gin.Context) {
	id, err := request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
	}
	err = h.RuleParameterValueService.Delete(c, id)
	response.FeedBack(c, nil, err)
}

// @Tags V2 RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary List RuleParameterValue
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterValueList true "Request body"
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /api/v2/rule_parameter_value/list [post]
func (h *RuleParameterValueHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RuleParameterValueList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	total, data, err := h.RuleParameterValueService.List(c, &req)
	response.FeedBack(c, response_parameters.NewListResponsePointer[modelv2.RmsRuleParameterValue](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		data,
	), err)
}

// @Tags V2 RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Retrieve RuleParameterValue
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /api/v2/rule_parameter_value/retrieve/{id} [get]
func (h *RuleParameterValueHandle) Retrieve(c *gin.Context) {
	var id int64
	var err error
	if id, err = request.GetParamInt64(c, "id"); err != nil {
		response.ReturnFailure(c, err)
		return
	}
	data, err := h.RuleParameterValueService.Retrieve(c, id)
	response.FeedBack(c, data, err)
}

// @Tags V2 RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary UpdateMultiple RuleParameterValue 更新多个
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RmsRuleParameterValueUpdateMultiple true "Request body"
// @Success 200 {object} domain.RmsRuleParameterValueUpdateMultipleResp
// @Router /api/v2/rule_parameter_value/update_multiple [post]
func (h *RuleParameterValueHandle) UpdateMultiple(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.RmsRuleParameterValueUpdateMultiple](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	resp, err := h.RuleParameterValueService.UpdateMultiple(c, &req)
	response.FeedBack(c, resp, err)
}
