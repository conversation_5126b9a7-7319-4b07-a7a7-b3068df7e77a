package handlev2

import (
	"github.com/gin-gonic/gin"
	request_parameters "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type ConfigProgressHandle struct {
	BaseHandle
	ConfigProgressService *servicev2.ConfigProgressService
}

// CreateOrUpdateConfigProgress 创建或更新配置进度
// @Summary CreateOrUpdate ConfigProgress 创建或更新配置进度
// @Tags risk-portal-api/ConfigProgress 配置进度/ConfigProgress 配置进度管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param data body request_parameters.ConfigProgressCreate true "Request body"
// @Router /api/v2/config_progress/create_or_update [post]
func (h *ConfigProgressHandle) CreateOrUpdate(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.ConfigProgressCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var data = &modelv2.PlatConfigProgress{
		CheckPointCode: req.CheckPointCode,
		Progress:       req.Progress,
	}

	err = h.ConfigProgressService.CreateOrUpdate(c, data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	// 重新获取数据以确保 ID 正确
	updatedData, err := h.ConfigProgressService.RetrieveByCode(c, data.CheckPointCode)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var resp = response_parameters.ConfigProgressResp{
		ConfigProgressUpdate: response_parameters.ConfigProgressUpdate{
			ID: updatedData.ID,
			ConfigProgressCreate: response_parameters.ConfigProgressCreate{
				CheckPointCode: updatedData.CheckPointCode,
				Progress:       updatedData.Progress,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}

// RetrieveConfigProgress 获取配置进度详情
// @Summary Retrieve ConfigProgress 获取配置进度详情
// @Tags risk-portal-api/ConfigProgress 配置进度/ConfigProgress 配置进度管理
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param code path string true "access_point code"
// @Success 200 {object} response_parameters.ConfigProgressResp "配置进度详情"
// @Router /api/v2/config_progress/retrieve/{code} [get]
func (h *ConfigProgressHandle) Retrieve(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		response.ReturnFailure(c, er.InvalidArgument.WithMsg("Code parameter is required"))
		return
	}

	var data *modelv2.PlatConfigProgress
	data, err := h.ConfigProgressService.RetrieveByCode(c, code)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var resp = response_parameters.ConfigProgressResp{
		ConfigProgressUpdate: response_parameters.ConfigProgressUpdate{
			ID: data.ID,
			ConfigProgressCreate: response_parameters.ConfigProgressCreate{
				CheckPointCode: data.CheckPointCode,
				Progress:       data.Progress,
			},
		},
	}
	response.ReturnSuccess(c, resp)
}
