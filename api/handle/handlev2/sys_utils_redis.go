package handlev2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type SysUtilsRedisHandle struct {
}

func (h *SysUtilsRedisHandle) GetKeys(c *gin.Context) {
	var key, err = request.GetParam(c, "key")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	match := c.Query("match")
	if key == "uqpay" {
		result, err := redisutils.GetAllKeys(match)
		response.FeedBack(c, result, err)
	} else {
		response.FeedBack(c, nil, nil)
	}
}

func (h *SysUtilsRedisHandle) GetString(c *gin.Context) {
	var key, err = request.GetParam(c, "key")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	result, err := redisutils.Get(key)
	response.FeedBack(c, result, err)
}

func (h *SysUtilsRedisHandle) GetMapAll(c *gin.Context) {
	var key, err = request.GetParam(c, "key")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	result, err := redisutils.HGetAll(key)
	response.FeedBack(c, result, err)
}
func (h *SysUtilsRedisHandle) GetMapByKey(c *gin.Context) {
	key := c.Query("key")
	field := c.Query("field")
	result, err := redisutils.HMGet(key, field)
	response.FeedBack(c, result, err)
}
