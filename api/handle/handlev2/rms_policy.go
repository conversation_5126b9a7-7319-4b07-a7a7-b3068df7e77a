package handlev2

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/request_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/filter"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/request"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/utils/response"
)

type PolicyHandle struct {
	PolicyService *servicev2.PolicyService
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Create Policy
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyCreate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /api/v2/rule_group/create [post]
func (h *PolicyHandle) Create(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.PolicyCreate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	var data = modelv2.RmsPolicy{
		PolicyGroup: *req.PolicyGroup,
		PolicyNo:    req.PolicyNo,
		PolicyName:  req.PolicyName,
		CheckPoint:  req.CheckPoint,
		Status:      req.Status,
		Memo:        req.Memo,
	}
	Filters := string(req.FilterFieldMap)
	data.Filters = &Filters
	data.PolicyRules = string(req.RuleIdList)
	err = h.PolicyService.Create(c, &data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}

	response.ReturnSuccess(c, &response_parameters.PolicyResp{
		SyncedAt: data.SyncedAt,
		PolicyUpdate: response_parameters.PolicyUpdate{
			ID: data.ID,
			PolicyCreate: response_parameters.PolicyCreate{
				CheckPoint:  data.CheckPoint,
				PolicyName:  data.PolicyName,
				PolicyNo:    data.PolicyNo,
				Memo:        data.Memo,
				Status:      data.Status,
				PolicyGroup: &data.PolicyGroup,
				Filters:     data.Filters,
				PolicyRules: data.PolicyRules,
			},
		},
	})
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Update Policy
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyUpdate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /api/v2/rule_group/update [post]
func (h *PolicyHandle) Update(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.PolicyUpdate](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var data = modelv2.RmsPolicy{
		ID:          req.ID,
		PolicyGroup: *req.PolicyGroup,
		PolicyNo:    req.PolicyNo,
		PolicyName:  req.PolicyName,
		CheckPoint:  req.CheckPoint,
		Filters:     req.Filters,
		PolicyRules: req.PolicyRules,
		Status:      req.Status,
		Memo:        req.Memo,
	}
	Filters := string(req.FilterFieldMap)
	data.Filters = &Filters
	data.PolicyRules = string(req.RuleIdList)

	err = h.PolicyService.Update(c, req.ID, &data)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.ReturnSuccess(c, &response_parameters.PolicyResp{
		SyncedAt: data.SyncedAt,
		PolicyUpdate: response_parameters.PolicyUpdate{
			ID: data.ID,
			PolicyCreate: response_parameters.PolicyCreate{
				CheckPoint:  data.CheckPoint,
				PolicyName:  data.PolicyName,
				PolicyNo:    data.PolicyNo,
				Memo:        data.Memo,
				Status:      data.Status,
				PolicyGroup: &data.PolicyGroup,
				Filters:     data.Filters,
				PolicyRules: data.PolicyRules,
			},
		},
	})
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule_group/delete/{id} [get]
func (h *PolicyHandle) Delete(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.PolicyService.Delete(c, id))
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary List Policy
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyList true "Request body"
// @Success 200 {object} domain.PolicyListResp
// @Router /api/v2/rule_group/list [post]
func (h *PolicyHandle) List(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.PolicyList](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var (
		total int64
		resp  []*response_parameters.PolicyResp
	)
	total, resp, err = h.PolicyService.List(c, &req)
	response.FeedBack(c, response_parameters.NewListResponsePointer[response_parameters.PolicyResp](
		total,
		filter.NewPaging(req.PageNum, req.PageSize).PageSize,
		resp,
	), err)
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Retrieve Policy
// @Success 200 {object} domain.PolicyResp
// @Router /api/v2/rule_group/retrieve/{id} [get]
func (h *PolicyHandle) Retrieve(c *gin.Context) {
	var id, err = request.GetParamInt64(c, "id")
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var data *modelv2.RmsPolicy
	if data, err = h.PolicyService.Retrieve(c, id); err != nil {
		response.ReturnFailure(c, err)
		return
	}
	var resp = &response_parameters.PolicyResp{
		SyncedAt: data.SyncedAt,
		PolicyUpdate: response_parameters.PolicyUpdate{
			ID: data.ID,
			PolicyCreate: response_parameters.PolicyCreate{
				CheckPoint:  data.CheckPoint,
				PolicyName:  data.PolicyName,
				PolicyNo:    data.PolicyNo,
				Memo:        data.Memo,
				Status:      data.Status,
				PolicyGroup: &data.PolicyGroup,
				Filters:     data.Filters,
				PolicyRules: data.PolicyRules,
			},
		},
	}

	resp.FilterFieldMap = json.RawMessage(*data.Filters)
	resp.RuleIdList = json.RawMessage(data.PolicyRules)
	response.ReturnSuccess(c, resp)
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary SetStatus Policy, one of Online Offline
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicySetStatus true "Request body"
// @Success 200 {object} domain.Base
// @Router /api/v2/rule_group/set_status [post]
func (h *PolicyHandle) SetStatus(c *gin.Context) {
	var req, err = request.GetRequestBodyJson[request_parameters.PolicySetStatus](c)
	if err != nil {
		response.ReturnFailure(c, err)
		return
	}
	response.FeedBack(c, nil, h.PolicyService.SetStatus(c, req.ID, req.Status))
}
