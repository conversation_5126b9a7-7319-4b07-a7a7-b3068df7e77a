package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type CraFrameworkHandle struct {
	*BaseHandle
	CraFrameworkService *service.CraFrameWorkService
}

func (h *CraFrameworkHandle) Update(c *gin.Context) {
	req := new(domain.CraFrameworkUpdate)
	if h.BindJSON(c, req) {
		return
	}
	err := h.CraFrameworkService.Update(c, req.CraFrameworkId, req)
	if err != nil {
		h.SendErr(c, err)
		return
	} else {
		h.Success(c, nil)

	}
}
func (h *CraFrameworkHandle) Retrieve(c *gin.Context) {
	var id string
	if h.ParamStrId(c, "id", &id) {
		return
	}
	data, err := h.CraFrameworkService.Retrieve(c, id)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, data)
	}
}
func (h *CraFrameworkHandle) RiskScoreCount(c *gin.Context) {
	req := new(domain.CraFrameworkCount)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.CraFrameworkService.RiskScoreCount(c, req)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, data)
	}
}
func (h *CraFrameworkHandle) List(c *gin.Context) {
	data, err := h.CraFrameworkService.List(c)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, data)
	}
}
