package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type EventFieldHandle struct {
	*BaseHandle
	EventFieldService *service.EventFieldService
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Create EventField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldCreate true "Request body"
// @Success 200 {object} domain.EventFieldResp
// @Router /event_field/create [post]
func (h *EventFieldHandle) Create(c *gin.Context) {
	req := new(domain.EventFieldCreate)
	if h.BindJSON(c, req) {
		return
	}
	err := h.EventFieldService.Create(c, req)
	if h.<PERSON>rr(c, err) {
		return
	}
	h.Feed<PERSON>ack(c, req, err)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Update EventField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldUpdate true "Request body"
// @Success 200 {object} domain.EventFieldResp
// @Router /event_field/update [post]
func (h *EventFieldHandle) Update(c *gin.Context) {
	req := new(domain.EventFieldUpdate)
	if h.BindJSON(c, req) {
		return
	}
	err := h.EventFieldService.Update(c, req.ID, req)
	h.FeedBack(c, req, err)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /event_field/delete/{id} [get]
func (h *EventFieldHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.EventFieldService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary List EventField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldList true "Request body"
// @Success 200 {object} domain.EventFieldListResp
// @Router /event_field/list [post]
func (h *EventFieldHandle) List(c *gin.Context) {
	req := new(domain.EventFieldList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.EventFieldService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.EventFieldListResp)
	h.CopyAndSend(c, &data, &resp)
}

func (h *EventFieldHandle) NotEventStoreCfgList(c *gin.Context) {
	req := new(domain.EventFieldList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.EventFieldService.NotEventStoreCfgList(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.NotEventStoreCfgListResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary Retrieve EventField
// @Param id path string true "EventField Id"
// @Success 200 {object} domain.EventFieldResp
// @Router /event_field/retrieve/{id} [get]
func (h *EventFieldHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.EventFieldService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}

// @Tags EventField 事件字段
// @Security x-auth-token
// @Summary CheckNext Check if you can click Next
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventFieldCheckNext true "Request body"
// @Success 200 {object} domain.EventFieldListResp
// @Router /event_field/check_next [post]
func (h *EventFieldHandle) CheckNext(c *gin.Context) {
	req := new(domain.EventFieldCheckNext)
	if h.BindJSON(c, req) {
		return
	}
	err := h.EventFieldService.CheckNext(c, req)
	h.FeedBack(c, nil, err)
}
