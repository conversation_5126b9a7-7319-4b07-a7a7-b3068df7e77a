package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type EventStoreCfgHandle struct {
	*BaseHandle
	EventStoreCfgService *service.EventStoreCfgService
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary Create EventStoreCfg
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventStoreCfgCreate true "Request body"
// @Success 200 {object} domain.EventStoreCfgResp
// @Router /event_store_cfg/create [post]
func (h *EventStoreCfgHandle) Create(c *gin.Context) {
	req := new(domain.EventStoreCfgCreate)
	if h.BindJ<PERSON>N(c, req) {
		return
	}
	data := new(model.RmsEventStoreCfg)
	if h.Co<PERSON>(c, req, data) {
		return
	}
	err := h.EventStoreCfgService.Create(c, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.EventStoreCfgResp)
	h.CopyAndSend(c, &data, &resp)
}

// Deprecated: 使用v2版本接口
// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary Update EventStoreCfg
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventStoreCfgUpdate true "Request body"
// @Success 200 {object} domain.EventStoreCfgResp
// @Router /event_store_cfg/update [post]
func (h *EventStoreCfgHandle) Update(c *gin.Context) {
	req := new(domain.EventStoreCfgUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsEventStoreCfg)
	if h.Copy(c, req, data) {
		return
	}
	err := h.EventStoreCfgService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.EventStoreCfgResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /event_store_cfg/delete/{id} [get]
func (h *EventStoreCfgHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.EventStoreCfgService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary List EventStoreCfg
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.EventStoreCfgList true "Request body"
// @Success 200 {object} domain.EventStoreCfgResp
// @Router /event_store_cfg/list [post]
func (h *EventStoreCfgHandle) List(c *gin.Context) {
	req := new(domain.EventStoreCfgList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.EventStoreCfgService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.EventStoreCfgListResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags EventStoreCfg 事件存储配置
// @Security x-auth-token
// @Summary Retrieve EventStoreCfg
// @Param access_point_code path string true "access point code"
// @Success 200 {object} domain.EventStoreCfgResp
// @Router /event_store_cfg/retrieve/{access_point_code} [get]
func (h *EventStoreCfgHandle) Retrieve(c *gin.Context) {
	var apCode string
	if h.ParamStrId(c, "apCode", &apCode) {
		return
	}
	data, err := h.EventStoreCfgService.RetrieveByAPCode(c, apCode)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.EventStoreCfgResp)
	h.CopyAndSend(c, &data, &resp)
}
