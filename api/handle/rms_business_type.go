package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type BusinessTypeHandle struct {
	*BaseHandle
	BusinessTypeService *service.BusinessTypeService
}

// @Tags BusinessType 业务类型
// @Security x-auth-token
// @Summary Create BusinessType
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.BusinessTypeCreate true "Request body"
// @Success 200 {object} model.RmsBusinessType
// @Router /business_type/create [post]
func (h *BusinessTypeHandle) Create(c *gin.Context) {
	req := new(domain.BusinessTypeCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsBusinessType)
	if h.Copy(c, req, data) {
		return
	}
	err := h.BusinessTypeService.Create(c, data)
	h.FeedBack(c, data, err)
}

// @Tags BusinessType 业务类型
// @Security x-auth-token
// @Summary Update BusinessType
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.BusinessTypeUpdate true "Request body"
// @Success 200 {object} model.RmsBusinessType
// @Router /business_type/update [post]
func (h *BusinessTypeHandle) Update(c *gin.Context) {
	req := new(domain.BusinessTypeUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsBusinessType)
	if h.Copy(c, req, data) {
		return
	}
	err := h.BusinessTypeService.Update(c, req.ID, data)
	h.FeedBack(c, req, err)
}

// @Tags BusinessType 业务类型
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /business_type/delete/{id} [get]
func (h *BusinessTypeHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.BusinessTypeService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags BusinessType 业务类型
// @Security x-auth-token
// @Summary List BusinessType
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.BusinessTypeList true "Request body"
// @Success 200 {object} model.RmsBusinessType
// @Router /business_type/list [post]
func (h *BusinessTypeHandle) List(c *gin.Context) {
	req := new(domain.BusinessTypeList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.BusinessTypeService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags BusinessType 业务类型
// @Security x-auth-token
// @Summary Retrieve BusinessType
// @Success 200 {object} model.RmsBusinessType
// @Router /business_type/retrieve/{id} [get]
func (h *BusinessTypeHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.BusinessTypeService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
