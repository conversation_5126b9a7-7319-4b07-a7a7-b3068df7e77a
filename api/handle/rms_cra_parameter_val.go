package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type CraParameterValHandle struct {
	*BaseHandle
	CraParameterValService *service.CraParameterValService
}

func (h *CraParameterValHandle) Create(c *gin.Context) {
	req := new(domain.CraParameterValCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCraParameterVal)
	if h.Copy(c, req, data) {
		return
	}
	id, err := h.CraParameterValService.Create(c, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, id)
	}

}

func (h *CraParameterValHandle) Update(c *gin.Context) {
	req := new(domain.CraParameterValUpdate)
	if h.<PERSON>d<PERSON>(c, req) {
		return
	}
	data := new(model.RmsCraParameterVal)
	if h.Copy(c, req, data) {
		return
	}
	err := h.CraParameterValService.Update(c, req.ParamValId, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, nil)
	}
}

func (h *CraParameterValHandle) Delete(c *gin.Context) {
	var id string
	if h.ParamStrId(c, "id", &id) {
		return
	}
	err := h.CraParameterValService.Delete(c, id)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, nil)
	}
}
func (h *CraParameterValHandle) List(c *gin.Context) {

	req := new(domain.CraParameterValList)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCraParameterVal)
	if h.Copy(c, req, data) {
		return
	}
	list, err := h.CraParameterValService.List(c, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, list)
	}
}
func (h *CraParameterValHandle) Retrieve(c *gin.Context) {
	var id string
	if h.ParamStrId(c, "id", &id) {
		return
	}
	data, err := h.CraParameterValService.Retrieve(c, id)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, data)
	}
}
