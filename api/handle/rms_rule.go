package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
)

type RuleHandle struct {
	*BaseHandle
	RuleService            *service.RuleService
	RuleServiceV2          *servicev2.RuleService
	PolicyServiceV2        *servicev2.PolicyService
	EventStoreCfgServiceV2 *servicev2.EventStoreCfgService
	EventFieldServiceV2    *servicev2.EventFieldService
	CheckPointServiceV2    *servicev2.CheckPointService
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Create Rule
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleCreate true "Request body"
// @Success 200 {object} domain.RuleResp
// @Router /rule/create [post]
func (h *RuleHandle) Create(c *gin.Context) {
	req := new(domain.RuleCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsRule)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleService.Create(c, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Update Rule
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleUpdate true "Request body"
// @Success 200 {object} domain.RuleResp
// @Router /rule/update [post]
func (h *RuleHandle) Update(c *gin.Context) {
	req := new(domain.RuleUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsRule)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /rule/delete/{id} [get]
func (h *RuleHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.RuleService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary List Rule
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleList true "Request body"
// @Success 200 {object} domain.RuleResp
// @Router /rule/list [post]
func (h *RuleHandle) List(c *gin.Context) {
	req := new(domain.RuleList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	h.FeedBack(c, data, err)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Retrieve Rule
// @Param id path string true "17"
// @Success 200 {object} domain.RuleResp
// @Router /rule/retrieve/{id} [get]
func (h *RuleHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.RuleService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Compile Rule, Returns whether the rule has errors.
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleCompile true "Request body"
// @Success 200 {object} domain.Base
// @Router /rule/compile [post]
func (h *RuleHandle) Compile(c *gin.Context) {
	req := new(domain.RuleCompile)
	if h.BindJSON(c, req) {
		return
	}
	err := h.RuleService.Compile(req.RuleContent, req.RuleCode, req.RuleName, req.Priority)
	h.FeedBack(c, nil, err)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary SetStatus Rule, one of Online Offline
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleSetStatus true "Request body"
// @Success 200 {object} domain.Base
// @Router /rule/set_status [post]
func (h *RuleHandle) SetStatus(c *gin.Context) {
	req := new(domain.RuleSetStatus)
	if h.BindJSON(c, req) {
		return
	}
	err := h.RuleService.SetStatus(c, req.ID, req.Status)
	h.FeedBack(c, nil, err)
}

// @Tags Rule 风控规则 规则配置
// @Security x-auth-token
// @Summary Refresh Rule Cache
// @Param x-auth-token header string true "jwt token"
// @Success 200 {object} domain.Base
// @Router /rule/refresh [get]
func (h *RuleHandle) Refresh(c *gin.Context) {
	err := h.RuleServiceV2.UpdateRuleCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.EventStoreCfgServiceV2.UpdateEventFieldCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.EventFieldServiceV2.UpdateEventFieldCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.CheckPointServiceV2.UpdateCheckPointCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.PolicyServiceV2.UpdatePolicyCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.RuleService.Refresh(c)
	h.FeedBack(c, nil, err)
}
