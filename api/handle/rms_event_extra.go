package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type EventExtraHandle struct {
	*BaseHandle
	EventExtartService *service.EventExtartService
}

func (h *EventExtraHandle) List(c *gin.Context) {
	req := new(domain.FilterExtraListReq)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.EventExtartService.List(c, req)
	if err != nil {
		h.SendErr(c, err)
		return
	} else {
		h.Success(c, data)

	}
}
func (h *EventExtraHandle) GetUrl(c *gin.Context) {
	var id string
	if h.ParamStrId(c, "id", &id) {
		return
	}
	data, err := h.EventExtartService.GetUrl(c, id)
	if err != nil {
		h.SendErr(c, err)
		return
	} else {
		h.Success(c, data)

	}
}
