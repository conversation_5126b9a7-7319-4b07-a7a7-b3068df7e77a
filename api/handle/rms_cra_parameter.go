package handle

import (
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type CraParameterHandle struct {
	*BaseHandle
	CraParameterService *service.CraParameterService
}

func (h *CraParameterHandle) Create(c *gin.Context) {

	req := new(domain.CraParameterCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := &model.RmsCraParameter{
		CraFrameworkId:     req.CraFrameworkId,                                       // 唯一ID
		CraParameterName:   req.CraParameterName,                                     // 参数名
		CraParameterStatus: req.CraParameterStatus,                                   // 状态：1=active, 0=默认, -1=inactive
		CraParameterType:   req.CraParameterType,                                     // AML; COUNTRY; CUSTOM
		Description:        req.Description,                                          // 描述
		Weight:             req.Weight.Div(decimal.NewFromInt(100)).InexactFloat64(), // 重量，权重
		ParamInfo:          req.ParamInfo,
	}
	id, err := h.CraParameterService.Create(c, data)

	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, id)
	}
}
func (h *CraParameterHandle) Update(c *gin.Context) {
	req := new(domain.CraParameterUpdate)
	if h.BindJSON(c, req) {
		return
	}

	data := &model.RmsCraParameter{
		CraParameterId:     req.CraParameterId,
		CraParameterStatus: req.CraParameterStatus,                                   // 状态：1=active, 0=默认, -1=inactive
		CraParameterType:   req.CraParameterType,                                     // AML; COUNTRY; CUSTOM
		Description:        req.Description,                                          // 描述
		Weight:             req.Weight.Div(decimal.NewFromInt(100)).InexactFloat64(), // 重量，权重
		ParamInfo:          req.ParamInfo,
		CraFrameworkId:     req.CraFrameworkId,
	}
	err := h.CraParameterService.Update(c, data.CraParameterId, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, nil)
	}
}
func (h *CraParameterHandle) Delete(c *gin.Context) {
	var id string
	if h.ParamStrId(c, "id", &id) {
		return
	}
	err := h.CraParameterService.Delete(c, id)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, nil)
	}
}
func (h *CraParameterHandle) List(c *gin.Context) {
	req := new(domain.CraParameterListReq)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCraParameter)
	if h.Copy(c, req, data) {
		return
	}
	list, err := h.CraParameterService.List(c, data)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, list)
	}
}

func (h *CraParameterHandle) Retrieve(c *gin.Context) {
	var id string
	if h.ParamStrId(c, "id", &id) {
		return
	}
	data, err := h.CraParameterService.Retrieve(c, id)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, data)
	}
}

func (h *CraParameterHandle) BatchUpdate(c *gin.Context) {
	req := new(domain.CraParameterBatchUpdateReq)
	if h.BindJSON(c, req) {
		return
	}
	for i, _ := range req.CraParameterBatchUpdateList {
		req.CraParameterBatchUpdateList[i].Weight = req.CraParameterBatchUpdateList[i].Weight.Div(decimal.NewFromInt(100))
	}
	err := h.CraParameterService.BatchUpdate(c, req)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, nil)
	}
}

func (h *CraParameterHandle) GetParameter(c *gin.Context) {
	list, err := h.CraParameterService.GetParameter(c)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, list)
	}
}

func (h *CraParameterHandle) CraAuditing(c *gin.Context) {
	req := new(domain.CraAuditingReq)
	if h.BindJSON(c, req) {
		return
	}
	info, err := h.CraParameterService.CraAuditing(c, req)
	if err != nil {
		h.SendErr(c, err)
	} else {
		h.Success(c, info)
	}
}
