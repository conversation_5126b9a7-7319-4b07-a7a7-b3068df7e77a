package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type FilterFieldHandle struct {
	*BaseHandle
	FilterFieldService *service.FilterFieldService
}

// @Tags FilterField 过滤字段
// @Security x-auth-token
// @Summary Create FilterField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.FilterFieldCreate true "Request body"
// @Success 200 {object} domain.FilterFieldResp
// @Router /filter_field/create [post]
func (h *FilterFieldHandle) Create(c *gin.Context) {
	req := new(domain.FilterFieldCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsFilterField)
	if h.Copy(c, req, data) {
		return
	}
	err := h.FilterFieldService.Create(c, data)
	if h.<PERSON>rr(c, err) {
		return
	}
	resp := new(domain.FilterFieldResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags FilterField 过滤字段
// @Security x-auth-token
// @Summary Update FilterField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.FilterFieldUpdate true "Request body"
// @Success 200 {object} domain.FilterFieldResp
// @Router /filter_field/update [post]
func (h *FilterFieldHandle) Update(c *gin.Context) {
	req := new(domain.FilterFieldUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsFilterField)
	if h.Copy(c, req, data) {
		return
	}
	err := h.FilterFieldService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.FilterFieldResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags FilterField 过滤字段
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /filter_field/delete/{id} [get]
// 过滤字段的删除在 event_field 的 delete 和 update 中中实现
//func (h *FilterFieldHandle) Delete(c *gin.Context) {
//	var id int64
//	if h.ParamId(c, "id", &id) {
//		return
//	}
//	err := h.FilterFieldService.Delete(c, id)
//	h.FeedBack(c, nil, err)
//}

// @Tags FilterField 过滤字段
// @Security x-auth-token
// @Summary List FilterField
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.FilterFieldList true "Request body"
// @Success 200 {object} domain.FilterFieldListResp
// @Router /filter_field/list [post]
func (h *FilterFieldHandle) List(c *gin.Context) {
	req := new(domain.FilterFieldList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.FilterFieldService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.FilterFieldListResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags FilterField 过滤字段
// @Security x-auth-token
// @Summary Retrieve FilterField
// @Param body body domain.FilterFieldRetrieve true "Request body"
// @Success 200 {object} domain.FilterFieldResp
// @Router /filter_field/retrieve [post]
func (h *FilterFieldHandle) Retrieve(c *gin.Context) {
	req := new(domain.FilterFieldRetrieve)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.FilterFieldService.RetrieveByName(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.FilterFieldResp)
	h.CopyAndSend(c, &data, &resp)
}
