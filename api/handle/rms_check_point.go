package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type CheckPointHandle struct {
	*BaseHandle
	CheckPointService *service.CheckPointService
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Summary Create CheckPoint
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.CheckPointCreate true "Request body"
// @Success 200 {object} domain.CheckPointResp
// @Router /access_point/create [post]
func (h *CheckPointHandle) Create(c *gin.Context) {
	req := new(domain.CheckPointCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCheckPoint)
	if h.Copy(c, req, data) {
		return
	}
	err := h.CheckPointService.Create(c, data)
	if h.<PERSON>rr(c, err) {
		return
	}
	resp := new(domain.CheckPointResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Summary Update CheckPoint
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.CheckPointUpdate true "Request body"
// @Success 200 {object} domain.CheckPointResp
// @Router /access_point/update [post]
func (h *CheckPointHandle) Update(c *gin.Context) {
	req := new(domain.CheckPointUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsCheckPoint)
	if h.Copy(c, req, data) {
		return
	}
	err := h.CheckPointService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.CheckPointResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "17"
// @Success 200 {object} domain.Base
// @Router /access_point/delete/{id} [get]
func (h *CheckPointHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.CheckPointService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Summary List CheckPoint
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.CheckPointList true "Request body"
// @Success 200 {object} domain.CheckPointListResp
// @Router /access_point/list [post]
func (h *CheckPointHandle) List(c *gin.Context) {
	req := new(domain.CheckPointList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.CheckPointService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Summary Retrieve CheckPoint
// @Param x-auth-token header string true "jwt token"
// @Param code path string true "access_point code"
// @Success 200 {object} domain.CheckPointResp
// @Router /access_point/retrieve/{code} [get]
func (h *CheckPointHandle) Retrieve(c *gin.Context) {
	var code string
	if h.ParamStrId(c, "code", &code) {
		return
	}
	data, err := h.CheckPointService.RetrieveByCode(c, code)
	h.FeedBack(c, data, err)
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Summary AllName is list of all names of CheckPoint
// @Param x-auth-token header string true "jwt token"
// @Success 200 {array} domain.CheckPointNameLabel
// @Router /access_point/all_name [post]
func (h *CheckPointHandle) AllName(c *gin.Context) {
	data, err := h.CheckPointService.AllNameCodeList(c)
	if h.HeadErr(c, err) {
		return
	}
	resp := new([]domain.CheckPointNameLabel)
	h.CopyAndSend(c, data, resp)
}

// @Tags CheckPoint 接入点
// @Security x-auth-token
// @Param body body domain.CheckPointBusinessConfigReq true "Request body"
// @Summary UpdateBusinessConfig update CheckPoint.BusinessTypesLi
// @Success 200 {array} domain.CheckPointNameLabel
// @Router /access_point/update_business_config [post]
func (h *CheckPointHandle) UpdateBusinessConfig(c *gin.Context) {
	req := new(domain.CheckPointBusinessConfigReq)
	if h.BindJSON(c, req) {
		return
	}
	err := h.CheckPointService.UpdateBusinessConfig(c, req)
	h.FeedBack(c, nil, err)
}
