package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
)

type SysUtilsRedisHandle struct {
	*BaseHandle
}

func (h *SysUtilsRedisHandle) GetKeys(c *gin.Context) {
	var key string
	if h.ParamStrId(c, "key", &key) {
		return
	}
	match := c.Query("match")
	if key == "uqpay" {
		result, err := redisutils.GetAllKeys(match)
		h.FeedBack(c, result, err)
	} else {
		h.FeedBack(c, nil, nil)
	}
}

func (h *SysUtilsRedisHandle) GetString(c *gin.Context) {
	var key string
	if h.ParamStrId(c, "key", &key) {
		return
	}
	result, err := redisutils.Get(key)
	h.FeedBack(c, result, err)
}

func (h *SysUtilsRedisHandle) GetMapAll(c *gin.Context) {
	var key string
	if h.ParamStrId(c, "key", &key) {
		return
	}
	result, err := redisutils.HGetAll(key)
	h.Feed<PERSON>ack(c, result, err)
}
func (h *SysUtilsRedisHandle) GetMapByKey(c *gin.Context) {
	key := c.Query("key")
	field := c.Query("field")
	result, err := redisutils.HMGet(key, field)
	h.FeedBack(c, result, err)
}
