package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
)

type SysUtilsRedisHandle struct {
	*BaseHandle
}

// GetKeys 获取Redis键列表
// @Summary Get Redis Keys 获取Redis键列表
// @Tags risk-portal-api/SysUtils 系统工具/Redis Redis操作
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param key path string true "Redis键前缀，仅支持'uqpay'"
// @Param match query string false "匹配模式，用于过滤键名"
// @Success 200 {object} []string "Redis键列表获取成功"
// @Router /api/v1/sys_utils_redis/get_keys/{key} [get]
func (h *SysUtilsRedisHandle) GetKeys(c *gin.Context) {
	var key string
	if h.ParamStrId(c, "key", &key) {
		return
	}
	match := c.Query("match")
	if key == "uqpay" {
		result, err := redisutils.GetAllKeys(match)
		h.FeedBack(c, result, err)
	} else {
		h.FeedBack(c, nil, nil)
	}
}

// GetString 获取Redis字符串值
// @Summary Get Redis String Value 获取Redis字符串值
// @Tags risk-portal-api/SysUtils 系统工具/Redis Redis操作
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param key path string true "Redis键名"
// @Success 200 {object} string "Redis字符串值获取成功"
// @Router /api/v1/sys_utils_redis/get_string/{key} [get]
func (h *SysUtilsRedisHandle) GetString(c *gin.Context) {
	var key string
	if h.ParamStrId(c, "key", &key) {
		return
	}
	result, err := redisutils.Get(key)
	h.FeedBack(c, result, err)
}

// GetMapAll 获取Redis哈希表所有字段和值
// @Summary Get Redis Hash All Fields 获取Redis哈希表所有字段和值
// @Tags risk-portal-api/SysUtils 系统工具/Redis Redis操作
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param key path string true "Redis哈希表键名"
// @Success 200 {object} map[string]string "Redis哈希表所有字段和值获取成功"
// @Router /api/v1/sys_utils_redis/get_map_all/{key} [get]
func (h *SysUtilsRedisHandle) GetMapAll(c *gin.Context) {
	var key string
	if h.ParamStrId(c, "key", &key) {
		return
	}
	result, err := redisutils.HGetAll(key)
	h.FeedBack(c, result, err)
}
// GetMapByKey 根据字段名获取Redis哈希表字段值
// @Summary Get Redis Hash Field Value 根据字段名获取Redis哈希表字段值
// @Tags risk-portal-api/SysUtils 系统工具/Redis Redis操作
// @Produce json
// @Accept json
// @Security x-auth-token
// @Param x-auth-token header string true "jwt token"
// @Param key query string true "Redis哈希表键名"
// @Param field query string true "哈希表字段名"
// @Success 200 {object} []interface{} "Redis哈希表字段值获取成功"
// @Router /api/v1/sys_utils_redis/get_map_by_key [get]
func (h *SysUtilsRedisHandle) GetMapByKey(c *gin.Context) {
	key := c.Query("key")
	field := c.Query("field")
	result, err := redisutils.HMGet(key, field)
	h.FeedBack(c, result, err)
}
