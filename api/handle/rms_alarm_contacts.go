package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type AlarmContactHandle struct {
	*BaseHandle
	AlarmContactService *service.AlarmContactService
}

// @Tags AlarmContact 风控预警联系人
// @Security x-auth-token
// @Summary Create AlarmContact
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.AlarmContactCreate true "Request body"
// @Success 200 {object} model.RmsAlarmContact
// @Router /alarm_contact/create [post]
func (h *AlarmContactHandle) Create(c *gin.Context) {
	req := new(domain.AlarmContactCreate)
	if h.<PERSON>d<PERSON>(c, req) {
		return
	}
	data := new(model.RmsAlarmContact)
	if h.Co<PERSON>(c, req, data) {
		return
	}
	err := h.AlarmContactService.Create(c, data)
	h.FeedBack(c, data, err)
}

// @Tags AlarmContact 风控预警联系人
// @Security x-auth-token
// @Summary Update AlarmContact
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.AlarmContactUpdate true "Request body"
// @Success 200 {object} model.RmsAlarmContact
// @Router /alarm_contact/update [post]
func (h *AlarmContactHandle) Update(c *gin.Context) {
	req := new(domain.AlarmContactUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsAlarmContact)
	if h.Copy(c, req, data) {
		return
	}
	err := h.AlarmContactService.Update(c, req.ID, data)
	h.FeedBack(c, req, err)
}

// @Tags AlarmContact 风控预警联系人
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /alarm_contact/delete/{id} [get]
func (h *AlarmContactHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.AlarmContactService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags AlarmContact 风控预警联系人
// @Security x-auth-token
// @Summary List AlarmContact
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.AlarmContactList true "Request body"
// @Success 200 {object} model.RmsAlarmContact
// @Router /alarm_contact/list [post]
func (h *AlarmContactHandle) List(c *gin.Context) {
	req := new(domain.AlarmContactList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.AlarmContactService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags AlarmContact 风控预警联系人
// @Security x-auth-token
// @Summary Retrieve AlarmContact
// @Success 200 {object} model.RmsAlarmContact
// @Router /alarm_contact/retrieve/{id} [get]
func (h *AlarmContactHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.AlarmContactService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
