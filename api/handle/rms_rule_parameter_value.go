package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type RuleParameterValueHandle struct {
	*BaseHandle
	RuleParameterValueService *service.RuleParameterValueService
}

// @Tags RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Create RuleParameterValue
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RmsRuleParameterValueCreate true "Request body"
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /rule_parameter_value/create [post]
func (h *RuleParameterValueHandle) Create(c *gin.Context) {
	req := new(domain.RmsRuleParameterValueCreate)
	if h.Bind<PERSON>(c, req) {
		return
	}
	data := new(model.RmsRuleParameterValue)
	if h.<PERSON><PERSON>(c, req, data) {
		return
	}
	err := h.RuleParameterValueService.Create(c, data)
	h.FeedBack(c, data, err)
}

// @Tags RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Update RuleParameterValue
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RmsRuleParameterValueUpdate true "Request body"
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /rule_parameter_value/update [post]
func (h *RuleParameterValueHandle) Update(c *gin.Context) {
	req := new(domain.RmsRuleParameterValueUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsRuleParameterValue)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleParameterValueService.Update(c, req.ID, data)
	h.FeedBack(c, req, err)
}

// @Tags RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /rule_parameter_value/delete/{id} [get]
func (h *RuleParameterValueHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.RuleParameterValueService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary List RuleParameterValue
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterValueList true "Request body"
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /rule_parameter_value/list [post]
func (h *RuleParameterValueHandle) List(c *gin.Context) {
	req := new(domain.RuleParameterValueList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleParameterValueService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary Retrieve RuleParameterValue
// @Success 200 {object} model.RmsRuleParameterValue
// @Router /rule_parameter_value/retrieve/{id} [get]
func (h *RuleParameterValueHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.RuleParameterValueService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}

// @Tags RuleParameterValue 规则参数值
// @Security x-auth-token
// @Summary UpdateMultiple RuleParameterValue 更新多个
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RmsRuleParameterValueUpdateMultiple true "Request body"
// @Success 200 {object} domain.RmsRuleParameterValueUpdateMultipleResp
// @Router /rule_parameter_value/update_multiple [post]
func (h *RuleParameterValueHandle) UpdateMultiple(c *gin.Context) {
	req := new(domain.RmsRuleParameterValueUpdateMultiple)
	if h.BindJSON(c, req) {
		return
	}
	resp, err := h.RuleParameterValueService.UpdateMultiple(c, req)
	h.FeedBack(c, resp, err)
}
