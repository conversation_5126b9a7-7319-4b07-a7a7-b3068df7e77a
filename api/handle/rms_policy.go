package handle

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type PolicyHandle struct {
	*BaseHandle
	PolicyService *service.PolicyService
}

// Create 创建策略组
// @Tags risk-portal-api/Policy 策略/风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Create Policy 创建策略组
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyCreate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /api/v1/rule_group/create [post]
func (h *PolicyHandle) Create(c *gin.Context) {
	req := new(domain.PolicyCreate)
	if h.Bind<PERSON>(c, req) {
		return
	}
	data := new(model.RmsPolicy)
	if h.<PERSON><PERSON>(c, req, data) {
		return
	}
	Filters := string(req.FilterFieldMap)
	data.Filters = &Filters
	if len(req.WhiteListIDList) > 0 {
		ids, _ := jsoniter.MarshalToString(req.WhiteListIDList)
		data.WhiteListIDs = ids
	}
	data.PolicyRules = string(req.RuleIdList)
	err := h.PolicyService.Create(c, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.PolicyResp)
	h.CopyAndSend(c, &data, &resp)
}

// Update 更新策略组
// @Tags risk-portal-api/Policy 策略/风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Update Policy 更新策略组
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyCreate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /api/v1/rule_group/update [post]
func (h *PolicyHandle) Update(c *gin.Context) {
	req := new(domain.PolicyUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsPolicy)
	if h.Copy(c, req, data) {
		return
	}
	Filters := string(req.FilterFieldMap)
	data.Filters = &Filters
	if req.WhiteListIDList == nil {
		req.WhiteListIDList = []int64{}
	}
	ids, _ := jsoniter.MarshalToString(req.WhiteListIDList)
	data.WhiteListIDs = ids
	data.PolicyRules = string(req.RuleIdList)
	err := h.PolicyService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.PolicyResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /rule_group/delete/{id} [get]
func (h *PolicyHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.PolicyService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary List Policy
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicyList true "Request body"
// @Success 200 {object} domain.PolicyListResp
// @Router /rule_group/list [post]
func (h *PolicyHandle) List(c *gin.Context) {
	req := new(domain.PolicyList)
	if h.BindJSON(c, req) {
		return
	}
	resp, err := h.PolicyService.List(c, req)
	h.FeedBack(c, resp, err)
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary Retrieve Policy
// @Success 200 {object} domain.PolicyResp
// @Router /rule_group/retrieve/{id} [get]
func (h *PolicyHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.PolicyService.Retrieve(c, id)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.PolicyResp)
	if h.Copy(c, &data, &resp) {
		return
	}
	resp.FilterFieldMap = json.RawMessage(*data.Filters)
	resp.RuleIdList = json.RawMessage(data.PolicyRules)
	if data.WhiteListIDs != "" {
		_ = jsoniter.UnmarshalFromString(data.WhiteListIDs, &resp.WhiteListIDList)
	}
	h.Success(c, resp)
}

// @Tags Policy 风控规则策略（即规则组）
// @Security x-auth-token
// @Summary SetStatus Policy, one of Online Offline
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.PolicySetStatus true "Request body"
// @Success 200 {object} domain.Base
// @Router /rule_group/set_status [post]
func (h *PolicyHandle) SetStatus(c *gin.Context) {
	req := new(domain.PolicySetStatus)
	if h.BindJSON(c, req) {
		return
	}
	err := h.PolicyService.SetStatus(c, req.ID, req.Status)
	h.FeedBack(c, nil, err)
}
