package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type OperatorLogHandle struct {
	*BaseHandle
	OperatorLogService *service.OperatorLogService
}

// @Tags OperatorLog 风控操作日志
// @Security x-auth-token
// @Summary List OperatorLog
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.OperatorLogList true "Request body"
// @Success 200 {object} model.RmsOperatorLog
// @Router /operator_log/list [post]
func (h *OperatorLogHandle) List(c *gin.Context) {
	req := new(domain.OperatorLogList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.OperatorLogService.List(c, req)
	h.Feed<PERSON>ack(c, data, err)
}

// @Tags OperatorLog 风控操作日志
// @Security x-auth-token
// @Summary Retrieve OperatorLog
// @Success 200 {object} model.RmsOperatorLog
// @Router /operator_log/retrieve/{id} [get]
func (h *OperatorLogHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.OperatorLogService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
