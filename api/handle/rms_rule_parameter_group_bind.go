package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type RuleParameterGroupBindHandle struct {
	*BaseHandle
	RuleParameterGroupBindService *service.RuleParameterGroupBindService
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Create RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindCreate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupBindResp
// @Router /rule_parameter_group_bind/create [post]
func (h *RuleParameterGroupBindHandle) Create(c *gin.Context) {
	req := new(domain.RuleParameterGroupBindCreate)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleParameterGroupBindService.Create(c, req)
	if err != nil {
		h.SendErr(c, err)
	}
	resp := new(domain.RuleParameterGroupBindResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Create_list Add multiple RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindCreateList true "Request body"
// @Success 200 {array} domain.RuleParameterGroupBindResp
// @Router /rule_parameter_group_bind/create_list [post]
func (h *RuleParameterGroupBindHandle) CreateList(c *gin.Context) {
	req := new(domain.RuleParameterGroupBindCreateList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleParameterGroupBindService.CreateList(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := []*domain.RuleParameterGroupBindResp{}
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Update RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindUpdate true "Request body"
// @Success 200 {object} domain.RuleParameterGroupBindResp
// @Router /rule_parameter_group_bind/update [post]
func (h *RuleParameterGroupBindHandle) Update(c *gin.Context) {
	req := new(domain.RuleParameterGroupBindUpdate)
	if h.BindJSON(c, req) {
		return
	}
	// 前端的组件不支持 bool 值
	if req.State == "Yes" {
		req.Able = 1
	} else {
		req.Able = 0
	}
	data := new(model.RmsRuleParameterGroupBind)
	if h.Copy(c, req, data) {
		return
	}
	data, err := h.RuleParameterGroupBindService.Update(c, req.ID, req)
	if h.HeadErr(c, err) {
		return
	}
	h.CopyAndSend(c, &data, &req)
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /rule_parameter_group_bind/delete/{id} [get]
func (h *RuleParameterGroupBindHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.RuleParameterGroupBindService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary List RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterGroupBindList true "Request body"
// @Success 200 {object} domain.RuleParameterGroupBindListResp
// @Router /rule_parameter_group_bind/list [post]
func (h *RuleParameterGroupBindHandle) List(c *gin.Context) {
	req := new(domain.RuleParameterGroupBindList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleParameterGroupBindService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary DeleteList batch deletion RuleParameterGroupBind
// @Param x-auth-token header string true "jwt token"
// @Param values body []int64 true "Request body"
// @Success 200 {object} nil
// @Router /rule_parameter_group_bind/delete_list [post]
func (h *RuleParameterGroupBindHandle) DeleteList(c *gin.Context) {
	req := new(domain.RuleParameterGroupBindDeleteList)
	if h.BindJSON(c, &req) {
		return
	}
	err := h.RuleParameterGroupBindService.DeleteList(c, req.Value)
	h.FeedBack(c, nil, err)
}

// @Tags RuleParameterGroupBind 规则参数组绑定关系
// @Security x-auth-token
// @Summary Retrieve RuleParameterGroupBind
// @Param id path string true "id"
// @Success 200 {object} domain.RuleParameterGroupBindResp
// @Router /rule_parameter_group_bind/retrieve/{id} [get]
func (h *RuleParameterGroupBindHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	resp, err := h.RuleParameterGroupBindService.Retrieve(c, id)
	h.FeedBack(c, resp, err)
}
