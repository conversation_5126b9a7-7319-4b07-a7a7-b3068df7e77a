package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type AlarmTimeConfHandle struct {
	*BaseHandle
	AlarmTimeConfService *service.AlarmTimeConfService
}

// @Tags AlarmTimeConf 风控预警时间配置
// @Security x-auth-token
// @Summary Create AlarmTimeConf
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.AlarmTimeConfCreate true "Request body"
// @Success 200 {object} model.RmsAlarmTimeConf
// @Router /alarm_time_conf/create [post]
func (h *AlarmTimeConfHandle) Create(c *gin.Context) {
	req := new(domain.AlarmTimeConfCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsAlarmTimeConf)
	if h.<PERSON><PERSON>(c, req, data) {
		return
	}
	err := h.AlarmTimeConfService.Create(c, data)
	h.FeedBack(c, data, err)
}

// @Tags AlarmTimeConf 风控预警时间配置
// @Security x-auth-token
// @Summary Update AlarmTimeConf
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.AlarmTimeConfUpdate true "Request body"
// @Success 200 {object} model.RmsAlarmTimeConf
// @Router /alarm_time_conf/update [post]
func (h *AlarmTimeConfHandle) Update(c *gin.Context) {
	req := new(domain.AlarmTimeConfUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsAlarmTimeConf)
	if h.Copy(c, req, data) {
		return
	}
	err := h.AlarmTimeConfService.Update(c, req.ID, data)
	h.FeedBack(c, req, err)
}

// @Tags AlarmTimeConf 风控预警时间配置
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /alarm_time_conf/delete/{id} [get]
func (h *AlarmTimeConfHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.AlarmTimeConfService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags AlarmTimeConf 风控预警时间配置
// @Security x-auth-token
// @Summary List AlarmTimeConf
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.AlarmTimeConfList true "Request body"
// @Success 200 {object} model.RmsAlarmTimeConf
// @Router /alarm_time_conf/list [post]
func (h *AlarmTimeConfHandle) List(c *gin.Context) {
	req := new(domain.AlarmTimeConfList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.AlarmTimeConfService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags AlarmTimeConf 风控预警时间配置
// @Security x-auth-token
// @Summary Retrieve AlarmTimeConf
// @Success 200 {object} model.RmsAlarmTimeConf
// @Router /alarm_time_conf/retrieve/{id} [get]
func (h *AlarmTimeConfHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.AlarmTimeConfService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
