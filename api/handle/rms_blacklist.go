package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type BlacklistHandle struct {
	*BaseHandle
	BlacklistService *service.BlacklistService
}

// @Tags Blacklist 黑名单
// @Security x-auth-token
// @Summary Create Blacklist
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.BlacklistCreate true "Request body"
// @Success 200 {object} domain.BlacklistResp
// @Router /black_list/create [post]
func (h *BlacklistHandle) Create(c *gin.Context) {
	req := new(domain.BlacklistCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsBlacklist)
	if h.<PERSON><PERSON>(c, req, data) {
		return
	}
	err := h.BlacklistService.Create(c, data)
	if h.<PERSON>rr(c, err) {
		return
	}
	resp := new(domain.BlacklistResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags Blacklist 黑名单
// @Security x-auth-token
// @Summary Update Blacklist
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.BlacklistUpdate true "Request body"
// @Success 200 {object} domain.BlacklistResp
// @Router /black_list/update [post]
func (h *BlacklistHandle) Update(c *gin.Context) {
	req := new(domain.BlacklistUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsBlacklist)
	if h.Copy(c, req, data) {
		return
	}
	data.RcID = req.RcID
	err := h.BlacklistService.Update(c, req.RcID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.BlacklistResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags Blacklist 黑名单
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /black_list/delete/{id} [get]
func (h *BlacklistHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.BlacklistService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags Blacklist 黑名单
// @Security x-auth-token
// @Summary List Blacklist
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.BlacklistList true "Request body"
// @Success 200 {object} domain.BlacklistListResp
// @Router /black_list/list [post]
func (h *BlacklistHandle) List(c *gin.Context) {
	req := new(domain.BlacklistList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.BlacklistService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.BlacklistListResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags Blacklist 黑名单
// @Security x-auth-token
// @Summary Retrieve Blacklist
// @Success 200 {object} domain.BlacklistResp
// @Router /black_list/retrieve/{id} [get]
func (h *BlacklistHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.BlacklistService.Retrieve(c, id)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.BlacklistResp)
	h.CopyAndSend(c, &data, &resp)
}
