package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type ConfigProgressHandle struct {
	*BaseHandle
	ConfigProgressService *service.ConfigProgressService
}

// @Tags PlatConfigProgress 配置进度
// @Security x-auth-token
// @Summary CreateOrUpdate ConfigProgress
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.ConfigProgressCreate true "Request body"
// @Success 200 {object} domain.Base
// @Router /config_progress/create_or_update [post]
func (h *ConfigProgressHandle) CreateOrUpdate(c *gin.Context) {
	req := new(domain.ConfigProgressCreate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.PlatConfigProgress)
	if h.<PERSON><PERSON>(c, req, data) {
		return
	}
	err := h.ConfigProgressService.CreateOrUpdate(c, data)
	h.FeedBack(c, nil, err)
}

// @Tags PlatConfigProgress 配置进度
// @Security x-auth-token
// @Summary Retrieve ConfigProgress
// @Param x-auth-token header string true "jwt token"
// @Param code path string true "access_point code"
// @Success 200 {object} model.PlatConfigProgress
// @Router /config_progress/retrieve/{code} [get]
func (h *ConfigProgressHandle) Retrieve(c *gin.Context) {
	var code string
	if h.ParamStrId(c, "code", &code) {
		return
	}
	data, err := h.ConfigProgressService.RetrieveByCode(c, code)
	h.FeedBack(c, data, err)
}
