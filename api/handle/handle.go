package handle

import (
	"github.com/gin-gonic/gin"
	"github.com/google/wire"
	"github.com/jinzhu/copier"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"net/http"
	"strconv"
)

// @title uqpay-risk-portal-api
// @version v0.0.1
// @description description
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @license
// @name Authorization
// @schemes http https
// @basePath /api/v1

// ProviderSet is handle providers.
var HandleProviderSet = wire.NewSet(
	wire.Struct(new(BaseHandle), "*"),
	wire.Struct(new(AlarmContactHandle), "*"),
	wire.Struct(new(AlarmTimeConfHandle), "*"),
	wire.Struct(new(BlacklistHandle), "*"),
	wire.Struct(new(BusinessTypeHandle), "*"),
	wire.Struct(new(CheckPointHandle), "*"),
	wire.Struct(new(EventHandle), "*"),
	wire.Struct(new(EventFieldHandle), "*"),
	wire.Struct(new(EventStoreCfgHandle), "*"),
	wire.Struct(new(FilterFieldHandle), "*"),
	wire.Struct(new(OperatorLogHandle), "*"),
	wire.Struct(new(PolicyHandle), "*"),
	wire.Struct(new(RiskLogHandle), "*"),
	wire.Struct(new(RuleHandle), "*"),
	wire.Struct(new(RuleParameterHandle), "*"),
	wire.Struct(new(RuleParameterGroupHandle), "*"),
	wire.Struct(new(RuleParameterGroupBindHandle), "*"),
	wire.Struct(new(RuleParameterValueHandle), "*"),
	wire.Struct(new(HandleLogHandle), "*"),
	wire.Struct(new(ConfigProgressHandle), "*"),
	wire.Struct(new(PageDataHandle), "*"),
)

type BaseHandle struct {
	Log *zap.Logger
}

type BaseHandleErrInterface interface {
	Name() string
	Level() zapcore.Level
	ToMap() map[string]interface{}
	Code() uint32
	Msg() string
	SendToClient() bool // 是否发送给调用者
	ToZapField() []zapcore.Field
}

func (h BaseHandle) ParamInt64(c *gin.Context, key string, id *int64) bool {
	var err error
	*id, err = strconv.ParseInt(c.Param(key), 10, 64)
	if err != nil {
		h.SendErr(c, er.InvalidArgument.WithErr(err))
		return true
	}
	return false
}

// ParamId 获取 Param 中的 id，在 <= 0 时抛出错误
func (h BaseHandle) ParamId(c *gin.Context, key string, id *int64) bool {
	if h.ParamInt64(c, key, id) {
		return true
	}
	if *id <= 0 {
		h.SendErr(c, er.InvalidArgument.WithStack().WithMsg("id cannot be 0"))
		return true
	}
	return false
}

// ParamStrId 获取 Param 中的 string id，在 空值 时抛出错误
func (h BaseHandle) ParamStrId(c *gin.Context, key string, p *string) bool {
	*p = c.Param(key)
	if *p == "" {
		h.SendErr(c, er.InvalidArgument)
		return true
	}
	return false
}

func (h BaseHandle) SendErr(c *gin.Context, err error) {
	var ok bool
	var errInf BaseHandleErrInterface
	if errInf, ok = err.(BaseHandleErrInterface); !ok {
		errInf = er.Unknown.WithErr(err)
	}
	h.Log.Log(errInf.Level(), errInf.Msg(), errInf.ToZapField()...)
	if errInf.SendToClient() {
		c.JSON(http.StatusBadRequest, domain.Base{
			Code: errInf.Code(),
			Msg:  errInf.Msg(),
		})
	} else {
		c.JSON(http.StatusBadRequest, domain.Base{
			Code: er.Unknown.Code(),
			Msg:  er.Unknown.Msg(),
		})
	}
}

// HeadErr 处理错误并记录log，如果返回 true，代表发生了错误并被处理，Handle层应该 return，不该继续执行。如果返回 false，说明没有错误，继续执行。
func (h BaseHandle) HeadErr(c *gin.Context, err error) bool {
	if err == nil {
		return false
	}
	h.SendErr(c, err)
	return true
}

// Copy fromValue to toValue by copier
func (h BaseHandle) Copy(c *gin.Context, fromValue interface{}, toValue interface{}) bool {
	return h.HeadErr(c, copier.Copy(toValue, fromValue))
}

// Copy fromValue to toValue by copier and send to client
func (h BaseHandle) CopyAndSend(c *gin.Context, fromValue interface{}, toValue interface{}) bool {
	if h.HeadErr(c, copier.Copy(toValue, fromValue)) {
		return true
	}
	return h.Success(c, toValue)
}

// BindJSON 解析 json，如果返回 true，代表发生了错误并被处理，Handle层应该 return，不该继续执行。如果返回 false，说明没有错误，继续执行。
func (h BaseHandle) BindJSON(c *gin.Context, ptr interface{}) bool {
	if err := c.ShouldBindJSON(&ptr); err != nil {
		InvalidArgumentErr := er.ValidatorErrorHandler(err)
		if InvalidArgumentErr != nil {
			return h.HeadErr(c, InvalidArgumentErr)
		}
	}
	return false
}

// Success 返回成功数据
func (h BaseHandle) Success(c *gin.Context, data interface{}) bool {
	c.JSON(http.StatusOK, domain.Base{
		Code: er.Ok.Code(),
		Msg:  er.Ok.Msg(),
		Data: data,
	})
	return false
}

// FeedBack 同时处理数据和错误，是 HeadErr 与 Success 的胶水方法，存在错误返回 true，成功返回 false
func (h BaseHandle) FeedBack(c *gin.Context, data interface{}, err error) bool {
	if h.HeadErr(c, err) {
		return true
	}
	h.Success(c, data)
	return false
}
