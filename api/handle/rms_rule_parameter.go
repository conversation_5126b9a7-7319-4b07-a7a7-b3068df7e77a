package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type RuleParameterHandle struct {
	*BaseHandle
	RuleParameterService *service.RuleParameterService
}

// @Tags RuleParameter 规则参数
// @Security x-auth-token
// @Summary Create RuleParameter
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterCreate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /rule_param/create [post]
func (h *RuleParameterHandle) Create(c *gin.Context) {
	req := new(domain.RuleParameterCreate)
	if h.BindJ<PERSON>(c, req) {
		return
	}
	data := new(model.RmsRuleParameter)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleParameterService.Create(c, data)
	if h.<PERSON>(c, err) {
		return
	}
	resp := new(domain.PolicyResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameter 规则参数
// @Security x-auth-token
// @Summary Update RuleParameter
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterUpdate true "Request body"
// @Success 200 {object} domain.PolicyResp
// @Router /rule_param/update [post]
func (h *RuleParameterHandle) Update(c *gin.Context) {
	req := new(domain.RuleParameterUpdate)
	if h.BindJSON(c, req) {
		return
	}
	data := new(model.RmsRuleParameter)
	if h.Copy(c, req, data) {
		return
	}
	err := h.RuleParameterService.Update(c, req.ID, data)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.PolicyResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameter 规则参数
// @Security x-auth-token
// @Summary Delete role record by ID
// @Param id path string true "unique id"
// @Success 200 {object} domain.Base
// @Router /rule_param/delete/{id} [get]
func (h *RuleParameterHandle) Delete(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	err := h.RuleParameterService.Delete(c, id)
	h.FeedBack(c, nil, err)
}

// @Tags RuleParameter 规则参数
// @Security x-auth-token
// @Summary List RuleParameter
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RuleParameterList true "Request body"
// @Success 200 {object} domain.RuleParameterListResp
// @Router /rule_param/list [post]
func (h *RuleParameterHandle) List(c *gin.Context) {
	req := new(domain.RuleParameterList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RuleParameterService.List(c, req)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.RuleParameterListResp)
	h.CopyAndSend(c, &data, &resp)
}

// @Tags RuleParameter 规则参数
// @Security x-auth-token
// @Summary Retrieve RuleParameter
// @Success 200 {object} domain.PolicyResp
// @Router /rule_param/retrieve/{id} [get]
func (h *RuleParameterHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.RuleParameterService.Retrieve(c, id)
	if h.HeadErr(c, err) {
		return
	}
	resp := new(domain.PolicyResp)
	h.CopyAndSend(c, &data, &resp)
}
