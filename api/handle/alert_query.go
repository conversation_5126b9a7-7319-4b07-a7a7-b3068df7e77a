package handle

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/response_parameters"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type AlertQueryHandle struct {
	*BaseHandle
	AlertAQueryService *service.AlertAQueryService
}

func (h *AlertQueryHandle) List(c *gin.Context) {
	req := new(domain.AlertQueryListReq)
	if h.BindJSON(c, req) {
		return
	}
	list, err := h.AlertAQueryService.List(c, req)
	if err != nil {
		fmt.Println(err.Error())
		h.SendErr(c, err)
		return
	} else {
		var res = &response_parameters.AlertQueryListRes{
			TotalItem: list.TotalItem,
			PageNum:   list.PageNum,
			PageSize:  list.PageSize,
		}
		for _, v := range list.ItemList {
			var tmpRecord = &response_parameters.RiskEventRecord{
				ID:               v.ID,
				CheckPoint:       v.CheckPoint,
				Str1:             v.Str1,
				Str2:             v.Str2,
				Str3:             v.Str3,
				Str4:             v.Str4,
				Str5:             v.Str5,
				ResultCode:       v.ResultCode,
				ResultMsg:        v.ResultMsg,
				FailedRule:       v.FailedRule,
				CreateTime:       v.CreateTime,
				RequestParameter: v.RequestParameter,
			}
			if v.Num1.IsZero() {
				tmpRecord.Num1 = "0"
			} else {
				tmpRecord.Num1 = v.Num1.String()
			}
			if v.Num2.IsZero() {
				tmpRecord.Num2 = "0"
			} else {
				tmpRecord.Num2 = v.Num2.String()
			}
			res.ItemList = append(res.ItemList, tmpRecord)

		}
		h.Success(c, res)

	}
}
func (h *AlertQueryHandle) Statistics(c *gin.Context) {
	req := new(domain.AlertQueryStatisticsReq)
	if h.BindJSON(c, req) {
		return
	}
	list, err := h.AlertAQueryService.Statistics(c, req)
	if err != nil {
		fmt.Println(err.Error())
		h.SendErr(c, err)
		return
	} else {
		h.Success(c, list)

	}
}
func (h *AlertQueryHandle) Get(c *gin.Context) {
	req := new(domain.AlertQueryGetReq)
	if h.BindJSON(c, req) {
		return
	}
	list, err := h.AlertAQueryService.Get(c, req)
	if err != nil {
		fmt.Println(err.Error())
		h.SendErr(c, err)
		return
	} else {
		h.Success(c, list)

	}
}
func (h *AlertQueryHandle) Report(c *gin.Context) {
	req := new(domain.AlertQueryReportReq)
	if h.BindJSON(c, req) {
		return
	}
	list, err := h.AlertAQueryService.Report(c, req)
	if err != nil {
		fmt.Println(err.Error())
		h.SendErr(c, err)
		return
	} else {
		h.Success(c, list)

	}
}
