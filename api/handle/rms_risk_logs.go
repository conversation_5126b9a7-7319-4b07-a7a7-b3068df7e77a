package handle

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	_ "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
)

type RiskLogHandle struct {
	*BaseHandle
	RiskLogService *service.RiskLogService
}

// @Tags RiskLog 风控日志
// @Security x-auth-token
// @Summary List RiskLog
// @Param x-auth-token header string true "jwt token"
// @Param body body domain.RiskLogList true "Request body"
// @Success 200 {object} model.RmsRiskLog
// @Router /risk_log/list [post]
func (h *RiskLogHandle) List(c *gin.Context) {
	req := new(domain.RiskLogList)
	if h.BindJSON(c, req) {
		return
	}
	data, err := h.RiskLogService.List(c, req)
	h.FeedBack(c, data, err)
}

// @Tags RiskLog 风控日志
// @Security x-auth-token
// @Summary Retrieve RiskLog
// @Success 200 {object} model.RmsRiskLog
// @Router /risk_log/retrieve/{id} [get]
func (h *RiskLogHandle) Retrieve(c *gin.Context) {
	var id int64
	if h.ParamId(c, "id", &id) {
		return
	}
	data, err := h.RiskLogService.Retrieve(c, id)
	h.FeedBack(c, data, err)
}
