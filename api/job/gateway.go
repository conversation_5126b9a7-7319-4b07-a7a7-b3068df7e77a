package cron

import (
	"context"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service"
	"sync"

	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/data/datav2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain/entity"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/model/modelv2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/service/servicev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/er"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/tools/redisutils"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

type GatewayCacheTask struct {
	RuleService            *service.RuleService
	RuleServiceV2          *servicev2.RuleService
	PolicyServiceV2        *servicev2.PolicyService
	EventStoreCfgServiceV2 *servicev2.EventStoreCfgService
	EventFieldServiceV2    *servicev2.EventFieldService
	CheckPointServiceV2    *servicev2.CheckPointService
}

func NewGatewayCacheTask(ruleService *service.RuleService,
	ruleServiceV2 *servicev2.RuleService,
	policyServiceV2 *servicev2.PolicyService,
	eventStoreCfgServiceV2 *servicev2.EventStoreCfgService,
	eventFieldServiceV2 *servicev2.EventFieldService,
	checkPointServiceV2 *servicev2.CheckPointService,
) *GatewayCacheTask {
	return &GatewayCacheTask{
		RuleService:            ruleService,
		RuleServiceV2:          ruleServiceV2,
		PolicyServiceV2:        policyServiceV2,
		EventStoreCfgServiceV2: eventStoreCfgServiceV2,
		EventFieldServiceV2:    eventFieldServiceV2,
		CheckPointServiceV2:    checkPointServiceV2,
	}
}
func (c *GatewayCacheTask) Init() (err error) {
	err := h.RuleServiceV2.UpdateRuleCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.EventStoreCfgServiceV2.UpdateEventFieldCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.EventFieldServiceV2.UpdateEventFieldCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.CheckPointServiceV2.UpdateCheckPointCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.PolicyServiceV2.UpdatePolicyCache(c.Request.Context())
	if h.HeadErr(c, err) {
		return
	}
	err = h.RuleService.Refresh(c)
	h.FeedBack(c, nil, err)
}

func FlushIndicator(visualSqlCache, scriptSqlCache map[string]map[string]string) (err error) {
	var visualIndicatorExists, scriptIndicatorExists []string
	var visualAccessPointMap = make(map[string]bool)
	var scriptAccessPointMap = make(map[string]bool)
	visualIndicatorExists, err = redisutils.GetPrefixKeys(modelv2.RedisCacheVisualIndicator)
	if err != nil {
		return
	}
	scriptIndicatorExists, err = redisutils.GetPrefixKeys(modelv2.RedisCacheScriptIndicator)
	if err != nil {
		return
	}
	for _, exist := range visualIndicatorExists {
		visualAccessPointMap[exist] = true
	}
	for _, exist := range scriptIndicatorExists {
		scriptAccessPointMap[exist] = true
	}

	_, err = redisutils.Pipeline(func(rdb redis.Pipeliner) (innerErr error) {
		ctx := context.Background()
		//innerErr = rdb.Del(ctx, modelv2.RedisCacheVisualIndicator).Err()
		//if innerErr != nil {
		//	innerErr = er.Internal.WithMsg("failed to flush indicator cache[1]").WithErr(innerErr).WithStack()
		//	return
		//}

		for accessPoint, indicators := range visualSqlCache {
			var tmpIndicators = make(map[string]string)
			for name, ind := range indicators {
				tmpIndicators[name] = ind
			}
			var key = modelv2.RedisCacheVisualIndicator + ":" + accessPoint
			if visualAccessPointMap[key] {
				innerErr = rdb.Del(ctx, key).Err()
				if innerErr != nil {
					innerErr = er.Internal.WithMsg("failed to flush indicator cache[1]").WithErr(innerErr).WithStack()
					return
				}
				delete(visualAccessPointMap, key)
			}
			innerErr = rdb.HMSet(ctx, modelv2.RedisCacheVisualIndicator+":"+accessPoint, tmpIndicators).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[2]").WithErr(innerErr).WithStack()
				return
			}
		}
		//innerErr = rdb.Del(ctx, modelv2.RedisCacheScriptIndicator).Err()
		//if innerErr != nil {
		//	innerErr = er.Internal.WithMsg("failed to flush indicator cache[3]").WithErr(innerErr).WithStack()
		//	return
		//}
		for accessPoint, indicators := range scriptSqlCache {
			var tmpIndicators = make(map[string]string)
			for name, ind := range indicators {
				tmpIndicators[name] = ind
			}
			var key = modelv2.RedisCacheScriptIndicator + ":" + accessPoint
			if scriptAccessPointMap[key] {
				innerErr = rdb.Del(ctx, key).Err()
				if innerErr != nil {
					innerErr = er.Internal.WithMsg("failed to flush indicator cache[1]").WithErr(innerErr).WithStack()
					return
				}
				delete(scriptAccessPointMap, key)
			}
			innerErr = rdb.HMSet(ctx, key, tmpIndicators).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[4]").WithErr(innerErr).WithStack()
				return
			}
		}
		for accessPoint, _ := range scriptAccessPointMap {
			innerErr = rdb.Del(ctx, accessPoint).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[7]").WithErr(innerErr).WithStack()
				return
			}
		}
		for accessPoint, _ := range visualAccessPointMap {
			innerErr = rdb.Del(ctx, accessPoint).Err()
			if innerErr != nil {
				innerErr = er.Internal.WithMsg("failed to flush indicator cache[8]").WithErr(innerErr).WithStack()
				return
			}
		}
		return nil
	})
	return
}
