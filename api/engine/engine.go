package engine

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/validator/v10"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/docs"
	middleware2 "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/config"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/pkg/jwt"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type RegisterInterface interface {
	Register(*gin.RouterGroup)
}

func NewRouters() *Routers {
	return &Routers{}
}
func (r *Routers) SetV1Routers(routers ...RegisterInterface) *Routers {
	r.RegisterLi = routers
	return r
}
func (r *Routers) SetV2Routers(routers ...RegisterInterface) *Routers {
	r.RegisterLiV2 = routers
	return r
}

type Routers struct {
	RegisterLi   []RegisterInterface
	RegisterLiV2 []RegisterInterface
}

func (a *Routers) Register(gAPI *gin.RouterGroup) {
	v1Group := gAPI.Group("v1")
	for _, r := range a.RegisterLi {
		r.Register(v1Group)
	}
	v2Group := gAPI.Group("v2")
	for _, r := range a.RegisterLiV2 {
		r.Register(v2Group)
	}
}

// EngineBase 运行 gin.Engine
type EngineBase struct {
	register   *Routers
	ginMode    string
	httpAddr   string
	log        *zap.Logger
	middleware []gin.HandlerFunc
	db         *gorm.DB
	JWT        jwt.IJWT
}

func NewEngineBase(conf config.Config, Register *Routers, log *zap.Logger, jwt jwt.IJWT) EngineBase {
	return EngineBase{
		register: Register,
		ginMode:  conf.Http.Mode,
		httpAddr: conf.Http.Addr(),
		log:      log,
		JWT:      jwt,
	}
}

func (e *EngineBase) Use(middleware ...gin.HandlerFunc) {
	e.middleware = append(e.middleware, middleware...)
}

// Run 运行服务
func (e *EngineBase) Run(options ...func(*gin.Engine)) error {
	// 设置时区为 中国
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return err
	}
	time.Local = location

	gin.SetMode(gin.DebugMode)
	switch e.ginMode {
	case gin.DebugMode:
		gin.SetMode(gin.DebugMode)
	case gin.ReleaseMode:
		gin.SetMode(gin.ReleaseMode)
	case gin.TestMode:
		gin.SetMode(gin.TestMode)
	}
	// 注册参数校验器
	validate := binding.Validator.Engine().(*validator.Validate)
	validate.RegisterValidation("alpha_num_underline", middleware2.AlphaNumUnderline)
	// 初始化 gin
	engine := gin.New()
	engine.Use(middleware2.GinCors())
	engine.Use(middleware2.ErrorHandler(e.log))
	engine.Use(e.middleware...)
	for _, o := range options {
		o(engine)
	}
	gAPI := engine.Group("/api/")
	gAPI.Use(middleware2.JwtAuth(e.JWT))

	g := engine.Group("/api/v1/")
	//g.GET("/ping", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"status": "ok"}) })
	//g.POST("/ping", func(c *gin.Context) { c.JSON(http.StatusOK, gin.H{"status": "ok"}) })
	g.GET("/ping", func(c *gin.Context) { c.Data(http.StatusOK, "text/plain", []byte("pong")) })
	g.POST("/ping", func(c *gin.Context) { c.Data(http.StatusOK, "text/plain", []byte("pong")) })

	e.register.Register(gAPI)
	e.log.Info("bind: " + e.httpAddr)
	return engine.Run(e.httpAddr)
}

// EnginePro 运行 gin.Engine
type EnginePro struct {
	EngineBase
	JWT jwt.IJWT
}

// Run 运行服务
func (en EnginePro) Run() error {
	return en.EngineBase.Run()
}

// EngineDev 带有 swag 服务，没有权限验证，开发测试用
type EngineDev struct {
	EngineBase
}

// Run 运行测试用服务
func (en EngineDev) Run() error {
	return en.EngineBase.Run(func(e *gin.Engine) {
		docs.SwaggerInfo.BasePath = "/api/v1"
		e.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))
		e.GET("/", func(c *gin.Context) { c.Redirect(http.StatusFound, "/swagger/index.html") })
	})
}
