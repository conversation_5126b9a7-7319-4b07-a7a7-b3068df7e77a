package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type RuleParameterRouter struct {
	RuleParameterHandle *handle.RuleParameterHandle
}

func (a *RuleParameterRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/rule_param")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create rule_param"), a.RuleParameterHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE rule_param"), a.RuleParameterHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE rule_param"), a.RuleParameterHandle.Delete)
	g.POST("list", a.RuleParameterHandle.List)
	g.GET("retrieve/:id", a.RuleParameterHandle.Retrieve)
}
