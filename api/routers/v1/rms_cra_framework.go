package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type CraFrameworkRouter struct {
	CraFrameworkHandle *handle.CraFrameworkHandle
}

func (a *CraFrameworkRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/cra_framework")
	//g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create cra_parameter"), a.CraFrameworkHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE cra_parameter"), a.CraFrameworkHandle.Update)
	g.GET("retrieve/:id", a.CraFrameworkHandle.Retrieve)
	g.POST("risk_score_count", a.CraFrameworkHandle.RiskScoreCount)
	//g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE cra_parameter"), a.CraFrameworkHandle.Delete)
	g.POST("list", a.CraFrameworkHandle.List)
}
