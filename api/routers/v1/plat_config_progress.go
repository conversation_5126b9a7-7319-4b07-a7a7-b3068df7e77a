package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type ConfigProgressRouter struct {
	ConfigProgressHandle *handle.ConfigProgressHandle
}

func (a *ConfigProgressRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/config_progress")
	g.POST("create_or_update", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create config_progress"), a.ConfigProgressHandle.CreateOrUpdate)
	g.GET("retrieve/:code", a.ConfigProgressHandle.Retrieve)
}
