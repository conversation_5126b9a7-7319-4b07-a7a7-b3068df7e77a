package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type AlarmTimeConfRouter struct {
	AlarmTimeConfHandle *handle.AlarmTimeConfHandle
}

func (a *AlarmTimeConfRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/alarm_time_conf")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create alarm_time_conf"), a.AlarmTimeConfHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "update alarm_time_conf"), a.AlarmTimeConfHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "update alarm_time_conf"), a.AlarmTimeConfHandle.Delete)
	g.POST("list", a.AlarmTimeConfHandle.List)
	g.GET("retrieve/:id", a.AlarmTimeConfHandle.Retrieve)
}
