package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type AlarmContactRouter struct {
	AlarmContactHandle *handle.AlarmContactHandle
}

func (a *AlarmContactRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/alarm_contact")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create alarm_contact"), a.AlarmContactHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "update alarm_contact"), a.AlarmContactHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "delete alarm_contact"), a.AlarmContactHandle.Delete)
	g.POST("list", a.AlarmContactHandle.List)
	g.GET("retrieve/:id", a.AlarmContactHandle.Retrieve)
}
