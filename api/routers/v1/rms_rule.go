package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type RuleRouter struct {
	RuleHandle *handle.RuleHandle
}

func (a *RuleRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/rule")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create rule"), a.RuleHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE rule"), a.RuleHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE rule"), a.RuleHandle.Delete)
	g.POST("list", a.RuleHandle.List)
	g.GET("retrieve/:id", a.RuleHandle.Retrieve)
	g.POST("compile", a.RuleHandle.Compile)
	g.POST("set_status", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE rule Status"), a.RuleHandle.SetStatus)
	g.GET("refresh", a.RuleHandle.Refresh)
}
