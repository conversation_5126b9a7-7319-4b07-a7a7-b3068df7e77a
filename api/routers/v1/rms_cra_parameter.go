package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type CraParameterRouter struct {
	CraParameterHandle *handle.CraParameterHandle
}

func (a *CraParameterRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/cra_parameter")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create cra_parameter"), a.CraParameterHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE cra_parameter"), a.CraParameterHandle.Update)
	g.POST("batch_update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE batch cra_parameter"), a.CraParameterHandle.BatchUpdate)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE cra_parameter"), a.CraParameterHandle.Delete)
	g.POST("list", a.CraParameterHandle.List)
	g.GET("retrieve/:id", a.CraParameterHandle.Retrieve)
	g.GET("parameter", a.CraParameterHandle.GetParameter)
	g.POST("cra_auditing", a.CraParameterHandle.CraAuditing)
}
