package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type RuleParameterGroupRouter struct {
	RuleParameterGroupHandle *handle.RuleParameterGroupHandle
}

func (a *RuleParameterGroupRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/rule_parameter_group")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create rule_parameter_group"), a.RuleParameterGroupHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE rule_parameter_group"), a.RuleParameterGroupHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE rule_parameter_group"), a.RuleParameterGroupHandle.Delete)
	g.POST("list", a.RuleParameterGroupHandle.List)
	g.GET("all", a.RuleParameterGroupHandle.All)
	g.GET("all_exclude_default", a.RuleParameterGroupHandle.AllExcludeDefault)
	g.GET("retrieve/:id", a.RuleParameterGroupHandle.Retrieve)
}
