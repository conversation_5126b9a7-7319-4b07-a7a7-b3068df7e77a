package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type BlacklistRouter struct {
	BlacklistHandle *handle.BlacklistHandle
}

func (a *BlacklistRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/black_list")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create black_list"), a.BlacklistHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "update black_list"), a.BlacklistHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "delete black_list"), a.BlacklistHandle.Delete)
	g.POST("list", a.BlacklistHandle.List)
	g.GET("retrieve/:id", a.BlacklistHandle.Retrieve)
}
