package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type EventFieldRouter struct {
	EventFieldHandle *handle.EventFieldHandle
}

func (a *EventFieldRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/event_field")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create event_field"), a.EventFieldHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE event_field"), a.EventFieldHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE event_field"), a.EventFieldHandle.Delete)
	g.POST("list", a.EventFieldHandle.List)
	g.POST("not_event_store_cfg_list", a.EventFieldHandle.NotEventStoreCfgList)

	g.GET("retrieve/:id", a.EventFieldHandle.Retrieve)
	g.POST("check_next", a.EventFieldHandle.CheckNext)
}
