package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
)

type AlertQueryRouter struct {
	AlertQueryHandle *handle.AlertQueryHandle
}

func (a *AlertQueryRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/alert_query")
	g.POST("list", a.AlertQueryHandle.List)
	g.POST("statistics", a.AlertQueryHandle.Statistics)
	g.POST("get", a.AlertQueryHandle.Get)
	g.POST("report", a.AlertQueryHandle.Report)

}
