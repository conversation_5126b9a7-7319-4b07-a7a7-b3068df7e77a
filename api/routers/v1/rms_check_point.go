package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type CheckPointRouter struct {
	CheckPointHandle *handle.CheckPointHandle
}

func (a *CheckPointRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/access_point")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create access_point"), a.CheckPointHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE access_point"), a.CheckPointHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE access_point"), a.CheckPointHandle.Delete)
	g.POST("list", a.CheckPointHandle.List)
	g.GET("retrieve/:code", a.CheckPointHandle.Retrieve)
	g.POST("all_name", a.CheckPointHandle.AllName)
	g.POST("update_business_config", a.CheckPointHandle.UpdateBusinessConfig)
}
