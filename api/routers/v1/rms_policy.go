package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type PolicyRouter struct {
	PolicyHandle *handle.PolicyHandle
}

func (a *PolicyRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/rule_group")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create rule_group"), a.PolicyHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE rule_group"), a.PolicyHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE rule_group"), a.PolicyHandle.Delete)
	g.POST("list", a.PolicyHandle.List)
	g.GET("retrieve/:id", a.PolicyHandle.Retrieve)
	g.POST("set_status", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE  access_point status"), a.PolicyHandle.SetStatus)
}
