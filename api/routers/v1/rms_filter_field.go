package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type FilterFieldRouter struct {
	FilterFieldHandle *handle.FilterFieldHandle
}

func (a *FilterFieldRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/filter_field")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create filter_field"), a.FilterFieldHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE filter_field"), a.FilterFieldHandle.Update)
	// 过滤字段的删除在 event_field 的 delete 和 update 中中实现
	//g.GET("delete/:id", a.FilterFieldHandle.Delete)
	g.POST("list", a.FilterFieldHandle.List)
	g.POST("retrieve", a.FilterFieldHandle.Retrieve)
}
