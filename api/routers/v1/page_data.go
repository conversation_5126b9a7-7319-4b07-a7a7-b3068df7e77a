package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
)

type PageDataRouter struct {
	PageDataHandle *handle.PageDataHandle
}

func (a *PageDataRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/page_data")
	g.GET("policy_config_page/:rules_group_code", a.PageDataHandle.PolicyConfigPage)
	g.GET("policy_config_page_create/:access_point_code", a.PageDataHandle.PolicyConfigPageCreate)
	g.GET("business_config/:access_point_code", a.PageDataHandle.BusinessConfig)
	g.POST("rule_parameter_group_bind", a.PageDataHandle.RmsRuleParameterGroupBind)
}
