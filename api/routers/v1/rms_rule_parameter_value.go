package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type RuleParameterValueRouter struct {
	RuleParameterValueHandle *handle.RuleParameterValueHandle
}

func (a *RuleParameterValueRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/rule_parameter_value")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create rule_parameter_value"), a.RuleParameterValueHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE rule_parameter_value"), a.RuleParameterValueHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE rule_parameter_value"), a.RuleParameterValueHandle.Delete)
	g.POST("list", a.RuleParameterValueHandle.List)
	g.GET("retrieve/:id", a.RuleParameterValueHandle.Retrieve)
	g.POST("update_multiple", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE access_point multiple"), a.RuleParameterValueHandle.UpdateMultiple)
}
