package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
)

type CraScoreReferenceRouter struct {
	CraScoreReferenceHandle *handle.CraScoreReferenceHandle
}

func (a *CraScoreReferenceRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/score_reference")

	g.POST("list", a.CraScoreReferenceHandle.List)
	g.POST("retrieve", a.CraScoreReferenceHandle.Retrieve)
}
