package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type BusinessTypeRouter struct {
	BusinessTypeHandle *handle.BusinessTypeHandle
}

func (a *BusinessTypeRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/business_type")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create business_type"), a.BusinessTypeHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE business_type"), a.BusinessTypeHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE business_type"), a.BusinessTypeHandle.Delete)
	g.POST("list", a.BusinessTypeHandle.List)
	g.GET("retrieve/:id", a.BusinessTypeHandle.Retrieve)
}
