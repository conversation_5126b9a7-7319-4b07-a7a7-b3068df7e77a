package v1

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type CraParameterValRouter struct {
	CraParameterValHandle *handle.CraParameterValHandle
}

func (a *CraParameterValRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/cra_parameter_val")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create cra_parameter_val"), a.CraParameterValHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE cra_parameter_val"), a.CraParameterValHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE cra_parameter_val"), a.CraParameterValHandle.Delete)
	g.POST("list", a.CraParameterValHandle.List)
	g.GET("retrieve/:id", a.CraParameterValHandle.Retrieve)
}
