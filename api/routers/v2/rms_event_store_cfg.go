package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type EventStoreCfgRouter struct {
	EventStoreCfgHandle *handlev2.EventStoreCfgHandle
}

func (a *EventStoreCfgRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/event_store_cfg")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create event_store_cfg"), a.EventStoreCfgHandle.Create)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE event_store_cfg"), a.EventStoreCfgHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE event_store_cfg"), a.EventStoreCfgHandle.Delete)
	g.POST("list", a.EventStoreCfgHandle.List)
	g.GET("retrieve/:apCode", a.EventStoreCfgHandle.Retrieve)
}
