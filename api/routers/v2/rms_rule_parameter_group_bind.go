package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type RuleParameterGroupBindRouter struct {
	RuleParameterGroupBindHandle *handlev2.RuleParameterGroupBindHandle
}

func (a *RuleParameterGroupBindRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/rule_parameter_group_bind")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create rule_parameter_group_bind"), a.RuleParameterGroupBindHandle.Create)
	g.POST("create_list", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "create_list rule_parameter_group_bind"), a.RuleParameterGroupBindHandle.CreateList)
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE access_point"), a.RuleParameterGroupBindHandle.Update)
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE access_point"), a.RuleParameterGroupBindHandle.Delete)
	g.POST("delete_list", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE access_point batch"), a.RuleParameterGroupBindHandle.DeleteList)
	g.POST("list", a.RuleParameterGroupBindHandle.List)
	g.GET("retrieve/:id", a.RuleParameterGroupBindHandle.Retrieve)
}
