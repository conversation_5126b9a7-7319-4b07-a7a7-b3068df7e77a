package v2

import (
	"github.com/gin-gonic/gin"
	handlev3 "gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

// BlackWhiteListRouter 黑白名单路由
type BlackWhiteListRouter struct {
	BlackWhiteListHandle        *handlev3.BlackWhiteListHandle
	BlackWhiteItemHandle        *handlev3.BlackWhiteItemHandle
	BlackWhiteAuditHandle       *handlev3.BlackWhiteAuditHandle
	BlackWhiteFieldHandle       *handlev3.BlackWhiteFieldHandle
	BlackWhiteOperatorLogHandle *handlev3.BlackWhiteOperatorLogHandle
}

func (a *BlackWhiteListRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/black_white")
	g.POST("/record", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create black white list"), a.BlackWhiteListHandle.CreateBlackWhiteList)                                               // 创建黑白名单
	g.PUT("/record", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Update black white list"), a.BlackWhiteListHandle.UpdateBlackWhiteList)                                                // 更新黑白名单
	g.POST("/list", a.BlackWhiteListHandle.GetBlackWhiteList)                                                                                                                                         // 获取黑白名单列表
	g.GET("/info", a.BlackWhiteListHandle.GetBlackWhiteListDetail)                                                                                                                                    // 获取黑白名单详情
	g.DELETE("/record", middleware.BlackWhiteActionLogger(), middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "Delete black white list"), a.BlackWhiteListHandle.RemoveBlackWhiteList)        // 删除黑白名单
	g.PUT("/status", middleware.BlackWhiteActionLogger(), middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Set black white list status"), a.BlackWhiteListHandle.SetStatus)                  // 设置黑白名单状态
	g.POST("/submit", middleware.BlackWhiteActionLogger(), middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Blacklist/Whitelist submission for review"), a.BlackWhiteListHandle.SubmitAudit) // 提交审核
	g.GET("/progress/:bwi_id", a.BlackWhiteListHandle.Progress)

	g.GET("/print", a.BlackWhiteListHandle.Print)                                                                                                                                                            // 获取黑白名单进度
	g.GET("/flush", a.BlackWhiteListHandle.Flush)                                                                                                                                                            // 获取黑白名单进度
	g.POST("/item", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Added Blacklist/Whitelist items"), a.BlackWhiteItemHandle.AddBlackWhiteItem)                                                   // 添加黑白名单
	g.PUT("/item", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Updated Blacklist/Whitelist item"), a.BlackWhiteItemHandle.UpdateBlackWhiteItem)                                                // 更新黑白名单
	g.DELETE("/item/:bwi_id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "DELETE Blacklist/Whitelist item"), middleware.BlackWhiteActionLogger(), a.BlackWhiteItemHandle.RemoveBlackWhiteItem) // 删除黑白名单
	g.GET("/items", a.BlackWhiteItemHandle.GetBlackWhiteItems)                                                                                                                                               // 获取黑白名单明细列表
	g.GET("/items/view", a.BlackWhiteItemHandle.GetBlackWhiteItemsView)                                                                                                                                      // 获取黑白名单明细列表(查看页调用的明细列表)

	//Item
	g.POST("/item/upload", a.BlackWhiteItemHandle.UploadBlackWhiteItemFile)
	g.GET("/template/:bwl_id", a.BlackWhiteItemHandle.DownloadBlackWhiteListItemTemplate)
	g.GET("/audit/:id", a.BlackWhiteAuditHandle.Detail)
	g.POST("/audit", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Review Whitelist/Blacklist"), middleware.BlackWhiteActionLogger(), a.BlackWhiteAuditHandle.Audit)
	g.GET("/audit/list", a.BlackWhiteAuditHandle.List)
	g.GET("/audit/mark", a.BlackWhiteAuditHandle.Mark)

	g.GET("/operator_logs", a.BlackWhiteOperatorLogHandle.List)

	g.POST("/fields", a.BlackWhiteFieldHandle.ListBlackWhiteField)
}
