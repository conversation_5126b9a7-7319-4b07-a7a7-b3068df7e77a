package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

// DataSourceRouter 数据源路由
type DataSourceRouter struct {
	DataSourceHandle *handlev2.DataSourceHandle
}

// Register 注册路由
func (r *DataSourceRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/data_source")

	// 创建数据源
	g.POST("", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create data source"), r.DataSourceHandle.Create)

	// 更新数据源
	g.PUT("", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Update data source"), r.DataSourceHandle.Update)

	// 删除数据源
	g.DELETE(":id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "Delete data source"), r.DataSourceHandle.Delete)

	// 获取数据源列表
	g.POST("list", r.DataSourceHandle.List)

	// 获取数据源详情
	g.GET(":id", r.DataSourceHandle.Retrieve)

	// 测试数据源连接
	g.POST("test_connection", r.DataSourceHandle.TestConnection)

	// 获取数据源表列表
	g.GET("tables/:data_source_id", r.DataSourceHandle.Tables)

}
