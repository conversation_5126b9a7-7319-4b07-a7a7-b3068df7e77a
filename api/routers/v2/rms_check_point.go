package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type CheckPointRouter struct {
	CheckPointHandle *handlev2.CheckPointHandle
}

func (a *CheckPointRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/access_point")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create access_point"), a.CheckPointHandle.Create)    // 创建检查点
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Update access_point"), a.CheckPointHandle.Update)    // 更新检查点
	g.GET("delete/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "Delete access_point"), a.CheckPointHandle.Delete) // 删除检查点
	g.POST("list", a.CheckPointHandle.List)                                                                                         // 检查点列表
	g.GET("retrieve/:code", a.CheckPointHandle.Retrieve)                                                                            // 获取单个检查点
	g.POST("all_name", a.CheckPointHandle.AllName)                                                                                  // 获取所有检查点名称和编码
}
