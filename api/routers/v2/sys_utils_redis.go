package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
)

type SysUtilsRedisRouter struct {
	RedisUtilsHandle *handlev2.SysUtilsRedisHandle
}

func (a *SysUtilsRedisRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/sys_utils_redis")
	g.GET("get_keys/:key", a.RedisUtilsHandle.GetKeys)
	g.GET("get_string/:key", a.RedisUtilsHandle.GetString)
	g.GET("get_map_all/:key", a.RedisUtilsHandle.GetMapAll)
	g.GET("get_map_by_key", a.RedisUtilsHandle.GetMapByKey)
	//g.GET("getMap", middleware.AdminActionLogger( a.AlarmContactHandle.Create)
	//g.POST("getList", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "update alarm_contact"), a.AlarmContactHandle.Update)
	//g.POST("getSet", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "update alarm_contact"), a.AlarmContactHandle.Update)

}
