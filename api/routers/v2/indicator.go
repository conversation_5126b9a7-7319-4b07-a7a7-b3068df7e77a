package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

// IndicatorRouter 指标配置路由
type IndicatorRouter struct {
	IndicatorHandle       *handlev2.IndicatorHandle
	IndicatorScriptHandle *handlev2.IndicatorScriptHandle
}

// Register 注册路由
func (r *IndicatorRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/indicator")

	// 创建指标配置
	g.POST("", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create indicator"), r.IndicatorHandle.Create)

	// 更新指标配置
	g.PUT("", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Update indicator"), r.IndicatorHandle.Update)

	// 删除指标配置
	g.DELETE("/:id", middleware.AdminActionLogger(domain.OPERATE_TYPE_DELETE, "Delete indicator"), r.IndicatorHandle.Delete)

	// 获取指标配置列表
	g.POST("/list", r.IndicatorHandle.List)

	// 获取指标配置详情
	g.GET("", r.IndicatorHandle.Retrieve)

	// 获取指标配置版本列表
	g.POST("/versions", r.IndicatorHandle.ListVersions)

	// 发布指标配置版本
	g.POST("/version/release", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Release indicator version"), r.IndicatorHandle.Release)

	// 更新指标状态
	g.POST("/status", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Update indicator status"), r.IndicatorHandle.UpdateStatus)

	// 生成指标SQL
	g.POST("/generate_sql", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "Generate indicator sql"), r.IndicatorHandle.GenerateIndicatorSql)

	// 测试指标SQL
	g.POST("/testing", r.IndicatorHandle.TestIndicatorSql)
	scriptG := group.Group("/indicator_script")

	scriptG.POST("", r.IndicatorScriptHandle.Create)
	scriptG.PUT("", r.IndicatorScriptHandle.Update)
	scriptG.POST("/list", r.IndicatorScriptHandle.List)
	scriptG.DELETE("/:id", r.IndicatorScriptHandle.Delete)
	scriptG.GET("", r.IndicatorScriptHandle.Retrieve)
	scriptG.POST("/status", r.IndicatorScriptHandle.UpdateStatus)
	scriptG.POST("/testing", r.IndicatorScriptHandle.TestScriptIndicatorSql)
}
