package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
)

type OperatorLogRouter struct {
	OperatorLogHandle *handlev2.OperatorLogHandle
}

func (a *OperatorLogRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/operator_log")
	g.POST("list", a.OperatorLogHandle.List)
	g.GET("retrieve/:id", a.OperatorLogHandle.Retrieve)
}
