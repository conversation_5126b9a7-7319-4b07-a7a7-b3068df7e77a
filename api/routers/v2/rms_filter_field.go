package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type FilterFieldRouter struct {
	FilterFieldHandle *handlev2.FilterFieldHandle
}

func (a *FilterFieldRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/filter_field")
	g.POST("create", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create filter_field"), a.FilterFieldHandle.Create) // 创建过滤字段
	g.POST("update", middleware.AdminActionLogger(domain.OPERATE_TYPE_UPDATE, "UPDATE filter_field"), a.FilterFieldHandle.Update) // 更新过滤字段
	// 过滤字段的删除在 event_field 的 delete 和 update 中中实现
	//g.GET("delete/:id", a.FilterFieldHandle.Delete)
	g.POST("list", a.FilterFieldHandle.List)         // 获取过滤字段列表
	g.POST("retrieve", a.FilterFieldHandle.Retrieve) // 获取过滤字段详情
}
