package v2

import (
	"github.com/gin-gonic/gin"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/api/handle/handlev2"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/internal/domain"
	"gitv2.uqpaytech.com/risk-group/uqpay-risk-portal-api/middleware"
)

type ConfigProgressRouter struct {
	ConfigProgressHandle *handlev2.ConfigProgressHandle
}

func (a *ConfigProgressRouter) Register(group *gin.RouterGroup) {
	g := group.Group("/config_progress")
	g.POST("create_or_update", middleware.AdminActionLogger(domain.OPERATE_TYPE_CREATE, "Create config_progress"), a.ConfigProgressHandle.CreateOrUpdate) // 创建或更新配置进度
	g.GET("retrieve/:code", a.ConfigProgressHandle.Retrieve)                                                                                              // 根据编码获取配置进度
}
